"""
GTK 廠商解析器實作
基於 VBA 原始邏輯，保持功能不變

VBA 邏輯參考：
- If InStr(1, LCase(subject), "ft hold") Or InStr(1, LCase(subject), "ft lot") > 0
- GetKeywordValue 函數：提取 "keyword: value" 格式的資料
- FindBin1Line 函數：找到包含 "BIN1:" 的行
"""

import re
from typing import List, Optional
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult


@dataclass
class GTKParsingResult(EmailParsingResult):
    """GTK 解析結果，擴展基礎解析結果"""
    bin1_data: Optional[str] = None
    in_qty: Optional[str] = None
    
    def to_dict(self) -> dict:
        """轉換為字典格式"""
        base_dict = super().to_dict()
        base_dict.update({
            'bin1_data': self.bin1_data,
            'in_qty': self.in_qty
        })
        return base_dict


class GTKParser(VendorParser):
    """
    GTK 廠商郵件解析器
    
    識別條件：主旨包含 "ft hold" 或 "ft lot"（不區分大小寫）
    提取資料：MO、LOT、YIELD、BIN1 資料等
    """
    
    def __init__(self):
        """初始化 GTK 解析器"""
        super().__init__()
        self._vendor_code = "GTK"
        self._vendor_name = "GTK"
        self._identification_patterns = [
            "ft hold",
            "ft lot"
        ]
        self.set_confidence_threshold(0.7)  # 降低閾值以適應轉發郵件
        
        # GTK 特有的關鍵字模式
        self.keyword_patterns = {
            'mo': r'mo\s*:\s*([^\s,~_]+)',
            'lot': r'lot\s+no\s*:\s*([^\s,~_]+)|lot\s*:\s*([^\s,~_]+)', 
            'yield': r'yield\s*:\s*([^\s,%~_]+)',
            'product': r'product\s*:\s*([^\s,~_]+)'
        }
        
        # BIN1 資料模式
        self.bin1_pattern = re.compile(r'bin1\s*:\s*.*', re.IGNORECASE)

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject_lower = email_data.subject.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式
        for pattern in self._identification_patterns:
            if pattern in subject_lower:
                matched_patterns.append(pattern)
                confidence_score += 0.4  # 每個模式增加信心分數
        
        # 額外的信心分數計算
        if any(keyword in subject_lower for keyword in ['mo:', 'lot:', 'yield:']):
            confidence_score += 0.3
            
        # 檢查 GTK 相關域名 (包括郵件內容中的轉發寄件者)
        sender_lower = email_data.sender.lower()
        body_lower = email_data.body.lower() if email_data.body else ""
        
        if ('gtk' in subject_lower or 
            'gtk.com' in sender_lower or 
            'greatek.com.tw' in sender_lower or
            'greatek.com.tw' in body_lower):
            confidence_score += 0.2
            
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="keyword_extraction"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        if not email_data.subject:
            raise ParsingError("Empty subject line", vendor_code=self.vendor_code)
        
        try:
            # 提取基本資料
            # 對 GTK 郵件：
            # - Device Type 是 PD（產品代碼）
            # - Mo 是 LOT（批號）
            # - Lot no 是內部批號
            mo_number = self.extract_keyword_value("MO", email_data.subject)  # 這實際上是 LOT
            device_type = self.extract_device_type(email_data.subject)  # 這是真正的 PD
            yield_value = self.extract_keyword_value("YIELD", email_data.subject)
            product = device_type  # 使用 Device Type 作為產品代碼
            
            # 提取 BIN1 資料
            bin1_data = self.find_bin1_line(email_data)
            
            # 提取入料數量
            in_qty = self.extract_in_qty(email_data)
            
            # 建立解析結果
            # 對 GTK：mo_number 實際上是批號，device_type 是產品代碼
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=mo_number if mo_number != "?" else None,  # 實際上是批號
                lot_number=mo_number if mo_number != "?" else None,  # GTK 的 Mo 是我們的 LOT
                is_success=True,
                error_message=None,
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'yield_value': yield_value,
                    'product_name': product,  # 這是 Device Type（真正的產品代碼）
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'bin1_data': bin1_data,
                    'in_qty': in_qty,
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'keyword_based'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                is_success=False,
                error_message=f"Parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def can_parse_email(self, email: EmailData, context: ParsingContext) -> 'VendorIdentificationResult':
        """
        判斷是否能解析此郵件 - 包裝方法
        基於 VBA 邏輯：If InStr(1, LCase(subject), "ft hold") Or InStr(1, LCase(subject), "ft lot") > 0
        """
        return self.identify_vendor(email)

    def extract_keyword_value(self, keyword: str, subject: str) -> str:
        """
        從主旨中提取關鍵字的值
        基於 VBA GetKeywordValue 函數邏輯
        
        VBA 邏輯：
        - 查找 "keyword:" 模式
        - 提取後面的值
        - 在遇到 "~", " ", "_" 時停止
        - 找不到時返回 "?"
        """
        keyword_lower = keyword.lower()
        subject_lower = subject.lower()
        
        # 特殊處理 LOT 關鍵字 - 支持 "Lot no:" 格式
        if keyword_lower == "lot":
            lot_patterns = ["lot no:", "lot:"]
            for pattern in lot_patterns:
                start_pos = subject_lower.find(pattern)
                if start_pos != -1:
                    value_start = start_pos + len(pattern)
                    value_text = subject[value_start:].strip()
                    if value_text:
                        # 在遇到分隔符時停止
                        separators = ["~", ","]
                        for separator in separators:
                            sep_pos = value_text.find(separator)
                            if sep_pos != -1:
                                value_text = value_text[:sep_pos]
                                break
                        return value_text.strip() if value_text.strip() else "?"
            return "?"
        
        # 查找 "keyword:" 模式
        keyword_pattern = f"{keyword_lower}:"
        start_pos = subject_lower.find(keyword_pattern)
        
        if start_pos == -1:
            return "?"
        
        # 獲取關鍵字後面的文本
        value_start = start_pos + len(keyword_pattern)
        value_text = subject[value_start:].strip()
        
        if not value_text:
            return "?"
        
        # 在遇到分隔符時停止（基於 VBA 邏輯）
        # 對於 MO，主要分隔符是空格和逗號
        if keyword_lower == "mo":
            separators = [" ", ","]
        else:
            separators = ["~", " ", "_", ","]
            
        for separator in separators:
            sep_pos = value_text.find(separator)
            if sep_pos != -1:
                value_text = value_text[:sep_pos]
                break
        
        return value_text.strip() if value_text.strip() else "?"

    def extract_device_type(self, subject: str) -> str:
        """
        從主旨中提取 Device Type
        GTK 郵件格式：Type: G2795FKL1U-K~NV 或 Device Type: G2772HJ1U-A~B1
        """
        subject_lower = subject.lower()
        
        # 支援多種模式："type:" 和 "device type:"
        device_patterns = ["type:", "device type:"]
        
        for device_pattern in device_patterns:
            start_pos = subject_lower.find(device_pattern)
            if start_pos != -1:
                # 獲取 Device Type 後面的文本
                value_start = start_pos + len(device_pattern)
                value_text = subject[value_start:].strip()
                
                if not value_text:
                    continue
                
                # 在遇到分隔符時停止 - 修改為到空格為止
                separators = [" ,", " Mo:", " mo:", " MO:"]
                for separator in separators:
                    sep_pos = value_text.find(separator)
                    if sep_pos != -1:
                        value_text = value_text[:sep_pos]
                        break
                
                # 特殊處理：如果沒有找到上述分隔符，尋找單獨的逗號和空格
                if not any(sep in value_text for sep in [" ,", " Mo:", " mo:", " MO:"]):
                    # 查找 " ," 模式（空格+逗號）
                    space_comma_pos = value_text.find(" ,")
                    if space_comma_pos != -1:
                        value_text = value_text[:space_comma_pos]
                
                return value_text.strip() if value_text.strip() else "?"
        
        return "?"

    def find_bin1_line(self, email: EmailData) -> Optional[str]:
        """
        查找郵件正文中包含 "BIN1:" 的行
        基於 VBA FindBin1Line 函數邏輯
        """
        if not email.body:
            return None
            
        lines = email.body.split('\n')
        
        for line in lines:
            if self.bin1_pattern.search(line.strip()):
                return line.strip()
        
        return None

    def extract_in_qty(self, email: EmailData) -> Optional[str]:
        """
        提取入料數量
        基於 VBA GetInQty 函數邏輯（需要參考具體實作）
        """
        # 這裡是基於常見模式的實作，具體邏輯需要參考 VBA GetInQty
        if not email.body:
            return None
            
        # 常見的入料數量模式
        qty_patterns = [
            r'input\s+quantity\s*:\s*(\d+)',
            r'in\s+qty\s*:\s*(\d+)',
            r'total\s+input\s*:\s*(\d+)',
            r'quantity\s*:\s*(\d+)\s+units'
        ]
        
        body_lower = email.body.lower()
        
        for pattern in qty_patterns:
            match = re.search(pattern, body_lower)
            if match:
                return match.group(1)
        
        return None

    def parse(self, email: EmailData, context: ParsingContext) -> GTKParsingResult:
        """
        解析 GTK 郵件
        基於 VBA 解析邏輯，提取所有相關資料
        """
        if not email.subject:
            raise ParsingError("Empty subject line", email)
        
        try:
            # 提取基本資料
            mo_number = self.extract_keyword_value("MO", email.subject)
            lot_number = self.extract_keyword_value("LOT", email.subject)
            yield_value = self.extract_keyword_value("YIELD", email.subject)
            product = self.extract_keyword_value("PRODUCT", email.subject)
            
            # 提取 BIN1 資料
            bin1_data = self.find_bin1_line(email)
            
            # 提取入料數量
            in_qty = self.extract_in_qty(email)
            
            # 建立解析結果
            result = GTKParsingResult(
                vendor="GTK",
                mo_number=mo_number,
                lot_number=lot_number,
                yield_value=yield_value,
                product=product,
                bin1_data=bin1_data,
                in_qty=in_qty,
                subject=email.subject,
                sender=email.sender,
                received_time=email.received_time,
                success=True,
                error_message=None,
                parsing_metadata={
                    'parser_version': '1.0',
                    'patterns_matched': self.can_parse(email, context).matched_patterns,
                    'extraction_method': 'keyword_based'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return GTKParsingResult(
                vendor="GTK",
                mo_number="?",
                lot_number="?", 
                yield_value="?",
                subject=email.subject,
                sender=email.sender,
                received_time=email.received_time,
                success=False,
                error_message=f"Parsing failed: {str(e)}",
                parsing_metadata={
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def validate_extracted_data(self, result: GTKParsingResult) -> bool:
        """
        驗證提取的資料是否合理
        """
        # 基本驗證：至少要有 MO 或 LOT
        if result.mo_number == "?" and result.lot_number == "?":
            return False
            
        # MO 編號格式驗證（如果有的話）
        if result.mo_number != "?" and result.mo_number:
            # GTK MO 編號通常以 F 開頭
            if not re.match(r'^[A-Z]\d+', result.mo_number.upper()):
                return False
        
        return True

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self.identification_patterns,
            'supported_keywords': list(self.keyword_patterns.keys()),
            'confidence_threshold': self.confidence_threshold,
            'based_on': 'VBA GetKeywordValue and FindBin1Line functions'
        }