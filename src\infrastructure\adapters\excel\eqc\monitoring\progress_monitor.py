#!/usr/bin/env python3
"""
智能進度監控模組
提供處理速度估算、記憶體監控和完成摘要功能
遵循 CLAUDE.md 功能替換原則，從主處理器中獨立出來
"""

import time
import psutil
from typing import Optional


class ProgressMonitor:
    """
    智能進度監控類別
    提供處理速度估算、記憶體監控和完成摘要
    """
    
    def __init__(self, start_time: Optional[float] = None, timeout: int = 1200):
        """
        初始化進度監控器
        
        Args:
            start_time: 處理開始時間，None則使用當前時間
            timeout: 處理超時時間(秒)
        """
        self.start_time = start_time or time.time()
        self.timeout = timeout
        self.successful_files = 0
        self.failed_files = 0
    
    def show_progress(self, current: int, total: int, filename: str) -> None:
        """
        顯示檔案處理進度與預估時間
        
        Args:
            current: 當前處理的檔案編號
            total: 總檔案數量
            filename: 當前處理的檔案名稱
        """
        elapsed = time.time() - self.start_time
        speed = current / elapsed if elapsed > 0 else 0
        remaining = (total - current) / speed if speed > 0 else 0
        
        print(f"[REFRESH] 處理檔案 {current}/{total}: {filename}")
        if speed > 0:
            print(f"   [TIME] 預估剩餘: {remaining:.0f}秒 | 速度: {speed:.2f} 檔案/秒")
        
        # 每50個檔案檢查記憶體使用
        if current % 50 == 0 and current > 0:
            self._check_memory_usage()
    
    def show_line_progress(self, lines_processed: int, filename: str, interval: int = 1000) -> None:
        """
        顯示行級處理進度（每interval行顯示一次）
        
        Args:
            lines_processed: 已處理的行數
            filename: 檔案名稱
            interval: 顯示間隔行數
        """
        if lines_processed % interval == 0 and lines_processed > 0:
            print(f"   [EDIT] {filename}: 已處理 {lines_processed} 行數據...")
    
    def record_success(self) -> None:
        """記錄成功處理的檔案"""
        self.successful_files += 1
    
    def record_failure(self) -> None:
        """記錄失敗處理的檔案"""
        self.failed_files += 1
    
    def show_summary(self) -> None:
        """顯示處理完成摘要"""
        elapsed = time.time() - self.start_time
        total_files = self.successful_files + self.failed_files
        speed = total_files / elapsed if elapsed > 0 else 0
        
        print(f"\n[CHART] 處理完成摘要:")
        print(f"   [OK] 成功處理: {self.successful_files}/{total_files}")
        if self.failed_files > 0:
            print(f"   [ERROR] 失敗檔案: {self.failed_files}/{total_files}")
        print(f"   [TIME] 總時間: {elapsed:.1f}秒")
        print(f"   [UP] 平均速度: {speed:.2f} 檔案/秒")
    
    def is_timeout(self) -> bool:
        """
        檢查是否已超時
        
        Returns:
            bool: True如果已超時
        """
        return (time.time() - self.start_time) > self.timeout
    
    def get_elapsed_time(self) -> float:
        """
        獲取已經過的時間
        
        Returns:
            float: 已經過的秒數
        """
        return time.time() - self.start_time
    
    def _check_memory_usage(self) -> None:
        """檢查並顯示記憶體使用情況"""
        try:
            memory = psutil.virtual_memory().percent
            print(f"   [SAVE] 記憶體使用: {memory:.1f}%")
        except Exception:
            # 如果無法獲取記憶體資訊，靜默忽略
            pass