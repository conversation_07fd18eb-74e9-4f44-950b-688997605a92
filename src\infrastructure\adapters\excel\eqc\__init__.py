#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EQC (Electronic Quality Control) 處理模組
==============================================

本模組包含所有與 EQC 處理相關的核心功能，實現 VBA 系統到 Python 的完整遷移。

核心功能:
- 雙重搜尋機制 (eqc_dual_search_corrected) 
- InsEqcRtData2 處理器 (eqc_inseqcrtdata2_processor)
- 簡單檢測器 (eqc_simple_detector)
- BIN1 統計處理器 (eqc_bin1_final_processor)

設計原則:
- 遵循 claude.md AI 設計原則
- 功能替換原則：取代舊版本，不保留向下相容
- TDD 開發方法：先測試後實作
- 極簡程式碼：每行都有存在價值
"""

from .eqc_dual_search_corrected import EQCDualSearchCorrected
from .eqc_inseqcrtdata2_processor import EQCInsEqcRtData2Processor
from .eqc_simple_detector import EQCSimpleDetector

# 版本資訊
__version__ = "1.0.0"
__author__ = "EQC Processing Team"
__date__ = "2025-06-09"

# 匯出主要類別 - 按 claude.md 功能替換原則移[EXCEPT_CHAR]重複處理器
__all__ = [
    'EQCDualSearchCorrected',
    'EQCInsEqcRtData2Processor', 
    'EQCSimpleDetector'
]

# 模組資訊
MODULE_INFO = {
    "name": "EQC Processing Module",
    "description": "Complete EQC processing system with dual search and BIN1 analysis",
    "core_features": [
        "Advanced EQC Processing",
        "Dual Search Mechanism", 
        "BIN1 Statistics Analysis",
        "Simple Detection Tools"
    ],
    "compatibility": "Python 3.8+",
    "tested_with": [
        "EQC1R0_20250523023632.csv",
        "EQCTOTALDATA.csv generation",
        "Real production data"
    ]
}