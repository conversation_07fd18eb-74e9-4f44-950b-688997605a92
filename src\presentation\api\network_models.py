"""網路共享瀏覽器 - 資料模型"""

from typing import List
from pydantic import BaseModel


class NetworkFileInfo(BaseModel):
    """網路檔案資訊模型"""
    filename: str
    size: int
    size_mb: float
    modified_time: str
    file_type: str
    is_directory: bool = False


class NetworkFileListResponse(BaseModel):
    """檔案列表回應模型"""
    status: str
    path: str
    files: List[NetworkFileInfo]
    total_count: int
    total_size_mb: float


class NetworkPathValidateRequest(BaseModel):
    """路徑驗證請求模型"""
    path: str


class NetworkPathValidateResponse(BaseModel):
    """路徑驗證回應模型"""
    status: str
    valid: bool
    path: str
    message: str
    accessible: bool = False


class NetworkCredentials(BaseModel):
    """網路認證資訊模型"""
    username: str
    password: str
    domain: str = ""
    server: str
    share: str


class NetworkConnectRequest(BaseModel):
    """網路連接請求模型"""
    path: str
    username: str
    password: str
    domain: str = ""


class NetworkConnectResponse(BaseModel):
    """網路連接回應模型"""
    status: str
    connected: bool
    message: str
    mount_point: str = ""