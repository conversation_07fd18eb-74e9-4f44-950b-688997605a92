一鍵完成程式碼對比 - 完整系統流程圖
=============================================

[BOARD] 按鈕觸發點
-----------
按鈕位置: REF\ft_eqc_grouping_ui.html (第1257行)
HTML代碼: <button class="primary-btn" onclick="processCompleteEQCWorkflow()">
觸發事件: onclick="processCompleteEQCWorkflow()"

[REFRESH] 前端函式調用鏈
================

1⃣ 主要觸發函式
┌─────────────────────────────────────────────────────────────────┐
│ 函式: processCompleteEQCWorkflow()                             │
│ 位置: REF\ft_eqc_grouping_ui.html (第3203行)                   │
│ 功能: 一鍵完成 EQC 工作流程的主控制器                          │
│ 參數: 無 (從 DOM 取得 folderPath)                              │
└─────────────────────────────────────────────────────────────────┘
                                ↓
2⃣ 模組化實作
┌─────────────────────────────────────────────────────────────────┐
│ 類別: EQCProcessor                                              │
│ 位置: src\presentation\web\static\js\business\eqc-processor.js │
│ 方法: processCompleteWorkflow()                                │
│ 功能: 執行完整的4步驟處理流程                                  │
└─────────────────────────────────────────────────────────────────┘
                                ↓
3⃣ 步驟執行器
┌─────────────────────────────────────────────────────────────────┐
│ Step 1: executeStep1() - 呼叫 /api/process_online_eqc          │
│ Step 2: executeStep2() - 呼叫 /api/process_eqc_advanced        │
│ Step 3: executeStep3() - 呼叫 /api/analyze_eqc_real_data       │
│ Step 4: executeStep4() - 前端結果整合與顯示                    │
└─────────────────────────────────────────────────────────────────┘

[GLOBE_WITH_MERIDIANS] API 調用層
=============

Step 1: 基礎資料生成
┌─────────────────────────────────────────────────────────────────┐
│ API 端點: POST /api/process_online_eqc                          │
│ 檔案位置: src\presentation\api\ft_eqc_api.py (第324行)         │
│ 函式: process_online_eqc()                                     │
│ 服務層: EQCProcessingService.process_online_eqc()              │
│ 功能: 完整 Online EQC 處理，支援三種模式                       │
│ 輸入: OnlineEQCProcessRequest                                   │
│ 輸出: OnlineEQCProcessResponse                                  │
└─────────────────────────────────────────────────────────────────┘
                                ↓
Step 2: 進階程式碼分析
┌─────────────────────────────────────────────────────────────────┐
│ API 端點: POST /api/process_eqc_advanced                        │
│ 檔案位置: src\presentation\api\ft_eqc_api.py (第278行)         │
│ 函式: process_eqc_advanced()                                   │
│ 服務層: EQCProcessingService.process_eqc_advanced()            │
│ 功能: 雙階段 EQC 處理 (程式碼區間檢測與雙重搜尋)               │
│ 輸入: EQCStandardProcessRequest                                 │
│ 輸出: EQCAdvancedProcessResponse                                │
└─────────────────────────────────────────────────────────────────┘
                                ↓
Step 3: 真實資料分析
┌─────────────────────────────────────────────────────────────────┐
│ API 端點: POST /api/analyze_eqc_real_data                       │
│ 檔案位置: src\presentation\api\ft_eqc_api.py (第367行)         │
│ 函式: analyze_eqc_real_data()                                  │
│ 服務層: EQCProcessingService.analyze_real_data()               │
│ 功能: 分析真實 EQCTOTALDATA.xlsx 資料                          │
│ 輸入: dict (包含 folder_path)                                  │
│ 輸出: EQCRealDataAnalysisResponse                               │
└─────────────────────────────────────────────────────────────────┘

[BUILDING_CONSTRUCTION] 服務層處理
==============

服務層主控制器
┌─────────────────────────────────────────────────────────────────┐
│ 類別: EQCProcessingService                                      │
│ 位置: src\presentation\api\services\eqc_processing_service.py  │
│ 主要方法:                                                       │
│ • process_online_eqc() - 11步驟完整流程 (第116行)              │
│ • process_eqc_advanced() - 第二階段處理器 (第402行)            │
│ • analyze_real_data() - 真實資料分析                           │
└─────────────────────────────────────────────────────────────────┘

[TOOL] 核心處理器層
===============

第一階段: EQC 資料整合
┌─────────────────────────────────────────────────────────────────┐
│ 類別: EQCBin1FinalProcessor                                     │
│ 位置: src\infrastructure\adapters\excel\eqc\                   │
│       eqc_bin1_final_processor.py                              │
│ 關鍵方法:                                                       │
│ • process_complete_eqc_integration() - 自動生成 EQCTOTALDATA   │
│ • one_click_complete_comparison() - 一鍵完成程式碼對比 (第464行)│
│ 功能:                                                           │
│ • 自動發現並整合目錄下所有CSV檔案                              │
│ • 生成完整 EQCTOTALDATA.csv                                    │
│ • SPD檔案自動轉換為CSV格式                                     │
└─────────────────────────────────────────────────────────────────┘
                                ↓
第二階段: 標準處理流程
┌─────────────────────────────────────────────────────────────────┐
│ 類別: StandardEQCProcessor                                      │
│ 位置: eqc_standard_processor.py                                │
│ 關鍵方法:                                                       │
│ • process_code_comparison_pipeline() - 程式碼對比流程          │
│ • process_from_stage2_only() - 從第二階段開始處理              │
│ • find_code_region() - 程式碼區間檢測                          │
│ • perform_corrected_dual_search() - 雙重搜尋機制               │
│ • _generate_standard_report() - 完整報告生成                   │
└─────────────────────────────────────────────────────────────────┘

[CHART] 處理步驟詳細流程
==================

Step 0: 預處理階段
┌─────────────────────────────────────────────────────────────────┐
│ 0A. 中文路徑處理: process_chinese_paths_in_directory()         │
│ 0B. 特定副檔名檔案自動刪[EXCEPT_CHAR]                                     │
│ 0C. SPD檔案自動轉換為CSV                                       │
└─────────────────────────────────────────────────────────────────┘
                                ↓
Step 1: 基礎資料生成 (11個子步驟)
┌─────────────────────────────────────────────────────────────────┐
│ 1. 執行 FT-EQC 配對處理                                         │
│ 2. 取得所有 EQC 檔案並按時間分類                               │
│ 3. 基於配對結果計算統計數據                                     │
│ 4. 找到 EQC BIN=1 golden IC                                    │
│ 6. 填入統計資料 (A9/B9, A10/B10)                               │
│ 8. 生成帶超連結的 FT-EQC 失敗配對資料                          │
│ 9. 添加 FT-EQC 失敗配對資料                                     │
│ 10. 添加帶超連結的 EQC RT 資料                                  │
│ 11. 生成 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv              │
└─────────────────────────────────────────────────────────────────┘
                                ↓
Step 2: 程式碼區間檢測與雙重搜尋
┌─────────────────────────────────────────────────────────────────┐
│ 2A. 程式碼區間檢測: find_code_region()                         │
│ 2B. 雙重搜尋機制: perform_corrected_dual_search()              │
│ 2C. 區間對比分析                                               │
│ 2D. 智能填入前端欄位                                           │
└─────────────────────────────────────────────────────────────────┘
                                ↓
Step 3: 真實資料分析
┌─────────────────────────────────────────────────────────────────┐
│ 3A. 檢查 EQCTOTALDATA.xlsx 是否存在                            │
│ 3B. 分析真實資料內容                                           │
│ 3C. 生成分析報告                                               │
└─────────────────────────────────────────────────────────────────┘
                                ↓
Step 4: 前端結果整合
┌─────────────────────────────────────────────────────────────────┐
│ 4A. 整合所有步驟結果                                           │
│ 4B. 生成完整報告預覽                                           │
│ 4C. 顯示處理統計資訊                                           │
│ 4D. 提供檔案下載連結                                           │
└─────────────────────────────────────────────────────────────────┘

[TARGET] 輸出結果
===========

生成檔案
┌─────────────────────────────────────────────────────────────────┐
│ • EQCTOTALDATA.csv - 主要整合資料檔案                          │
│ • EQCTOTALDATA_RAW.csv - 原始資料備份                          │
│ • EQCTOTALDATA.xlsx - Excel 格式報告                           │
│ • EQC_BIN1_WITH_STATISTICS.csv - 統計資料檔案                  │
│ • 各種分析報告和日誌檔案                                       │
└─────────────────────────────────────────────────────────────────┘

前端顯示
┌─────────────────────────────────────────────────────────────────┐
│ • 實時進度顯示 (StatusManager)                                 │
│ • 處理結果摘要                                                 │
│ • 報告預覽功能                                                 │
│ • 檔案下載按鈕                                                 │
│ • 程式碼對比檢視                                               │
└─────────────────────────────────────────────────────────────────┘

[SEARCH] 關鍵技術特點
===============

1. 模組化設計
   - 前端: 模組化 JavaScript (EQCProcessor 類別)
   - 後端: 服務層分離 (EQCProcessingService)
   - 處理器: 專用處理器類別

2. 錯誤處理
   - 每個步驟都有獨立的錯誤捕獲
   - 詳細的錯誤日誌記錄
   - 使用者友善的錯誤訊息

3. 進度追蹤
   - 實時進度更新
   - 步驟狀態顯示
   - 處理時間統計

4. 資料驗證
   - 路徑驗證和轉換
   - 檔案存在性檢查
   - 資料格式驗證

5. 結果整合
   - 多步驟結果合併
   - 統一的回應格式
   - 豐富的元數據資訊
