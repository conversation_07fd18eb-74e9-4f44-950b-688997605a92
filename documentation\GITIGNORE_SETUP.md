# Git 忽略設定說明

## [BOARD] 已設定的忽略規則

### [RED_CIRCLE] 新增的忽略項目

```gitignore
# Virtual Environment
venv_win_3_11_12/    # uv 建立的 Python 3.11.12 虛擬環境

# 郵件附件目錄（可能包含敏感資料）
attachments/         # 郵件附件儲存目錄
```

### [TARGET] 忽略原因

| 項目 | 原因 | 影響 |
|------|------|------|
| `venv_win_3_11_12/` | 虛擬環境目錄，包含大量依賴套件 | 避免上傳大型檔案，其他開發者可自行建立環境 |
| `attachments/` | 可能包含敏感的郵件附件 | 保護隱私，避免意外上傳機密資料 |

## [OK] 驗證結果

### Git 狀態確認
```bash
$ git check-ignore venv_win_3_11_12/
venv_win_3_11_12/

$ git check-ignore attachments/
attachments/
```

**狀態**: [OK] 兩個目錄都被正確忽略

## [ROCKET] 其他開發者設定指南

### 1. 克隆專案後的環境設定

```bash
# 克隆專案
git clone <repository_url>
cd outlook_summary

# 建立虛擬環境
uv venv venv_win_3_11_12 --python 3.11.12

# 安裝依賴
uv pip install --python venv_win_3_11_12 -r requirements.txt

# 安裝 Playwright 瀏覽器
./venv_win_3_11_12/Scripts/python.exe -m playwright install
```

### 2. 附件目錄設定

```bash
# 建立附件目錄
mkdir attachments

# 設定權限（如果需要）
chmod 755 attachments/
```

## [TOOL] 已存在的忽略規則

### 虛擬環境
```gitignore
venv/
env/
ENV/
.venv/
.env
venv_win_3_11_12/  # 新增
```

### 資料庫和日誌
```gitignore
*.db
*.sqlite3
*.log
logs/
```

### 測試和暫存
```gitignore
htmlcov/
.pytest_cache/
tmp/
temp/
```

### 敏感資料
```gitignore
secrets/
*.key
*.pem
.env.local
.env.production
```

## [NOTES] 維護指南

### 檢查忽略狀態
```bash
# 檢查特定檔案/目錄是否被忽略
git check-ignore <file_or_directory>

# 查看所有被忽略的檔案
git status --ignored
```

### 如果不小心已經追蹤了這些檔案
```bash
# 從 Git 中移[EXCEPT_CHAR]但保留本地檔案
git rm --cached -r venv_win_3_11_12/
git rm --cached -r attachments/

# 提交變更
git commit -m "Remove tracked directories from Git"
```

## [TARGET] 建議

1. **定期檢查**: 確保敏感資料不會被意外提交
2. **團隊共識**: 所有開發者都應了解這些忽略規則
3. **文檔更新**: 當添加新的忽略規則時，更新此文件

---
**設定時間**: 2025年7月14日  
**狀態**: [OK] 已完成  
**維護者**: 開發團隊