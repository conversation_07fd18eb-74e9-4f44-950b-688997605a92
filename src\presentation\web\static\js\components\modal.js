/**
 * 模態框組件模組
 * 處理報告預覽、倒數計時器和各種模態框功能
 */

class ModalComponent {
    constructor() {
        this.currentReportPath = '';
        this.countdownInterval = null;
        this.isModalOpen = false;
        
        this.elements = {
            reportPreviewModal: null,
            reportContent: null,
            countdownModal: null
        };
    }
    
    /**
     * 初始化模態框組件
     */
    init() {
        console.log('🪟 初始化模態框組件...');
        
        // 獲取 DOM 元素
        this.elements.reportPreviewModal = DOMManager.get('reportPreviewModal');
        this.elements.reportContent = DOMManager.get('reportContent');
        
        // 設置事件監聽器
        this.setupEventListeners();
        
        console.log('✅ 模態框組件初始化完成');
    }
    
    /**
     * 設置事件監聽器
     */
    setupEventListeners() {
        // 點擊模態框背景關閉
        document.addEventListener('click', (event) => {
            if (event.target === this.elements.reportPreviewModal) {
                this.closeReportPreview();
            }
        });
        
        // ESC 鍵關閉模態框
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.isModalOpen) {
                this.closeReportPreview();
            }
        });
    }
    
    /**
     * 顯示報告預覽
     * @param {string} reportPath - 報告路徑
     */
    async showReportPreview(reportPath) {
        this.currentReportPath = reportPath;
        const modal = this.elements.reportPreviewModal;
        const contentDiv = this.elements.reportContent;
        
        if (!modal || !contentDiv) {
            console.error('❌ 報告預覽模態框元素未找到');
            return;
        }
        
        // 顯示模態框
        modal.style.display = 'block';
        this.isModalOpen = true;
        contentDiv.textContent = '載入中...';
        
        try {
            // 讀取報告內容
            const result = await ApiClient.readReport(reportPath);
            
            if (result.status === 'success') {
                // 美化報告內容顯示
                contentDiv.innerHTML = this.formatReportContent(result.content);
                console.log('✅ 報告內容載入成功');
            } else {
                throw new Error(result.message || '讀取報告失敗');
            }
            
        } catch (error) {
            console.error('❌ 讀取報告失敗:', error);
            contentDiv.innerHTML = `<div style="color: #e74c3c; text-align: center; padding: 20px;">
                <i class="fas fa-exclamation-triangle"></i><br>
                載入報告失敗: ${error.message}
            </div>`;
        }
    }
    
    /**
     * 格式化報告內容
     * @param {string} content - 原始報告內容
     * @returns {string} 格式化後的 HTML
     */
    formatReportContent(content) {
        return content
            .replace(/([=]{40,})/g, '<div style="border-bottom: 2px solid #007bff; margin: 15px 0;"></div>')
            .replace(/^(EQC 進階完整處理系統.*)/gm, '<h2 style="color: #2c3e50; margin-bottom: 10px;">$1</h2>')
            .replace(/^(處理時間|資料夾路徑|區間檢測結果|雙重搜尋結果|處理完成時間):/gm, '<strong style="color: #17a2b8;">$1:</strong>')
            .replace(/^(✅|❌|⚠️|🔍|📊)/gm, '<span style="font-size: 1.2em;">$1</span>')
            .replace(/\n/g, '<br>')
            .replace(/(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})/g, '<code style="background: #f8f9fa; padding: 2px 4px; border-radius: 3px;">$1</code>');
    }
    
    /**
     * 關閉報告預覽
     */
    closeReportPreview() {
        const modal = this.elements.reportPreviewModal;
        if (modal) {
            modal.style.display = 'none';
            this.isModalOpen = false;
        }
    }
    
    /**
     * 複製報告內容
     */
    async copyReportContent() {
        const contentDiv = this.elements.reportContent;
        if (!contentDiv) return;
        
        const textContent = contentDiv.textContent || contentDiv.innerText;
        
        try {
            await Utils.copyToClipboard(textContent);
            
            // 臨時顯示複製成功提示
            const originalHTML = contentDiv.innerHTML;
            contentDiv.innerHTML = '<div style="text-align: center; color: #28a745; font-weight: bold; padding: 20px;"><i class="fas fa-check-circle"></i> 內容已複製到剪貼簿</div>';
            
            setTimeout(() => {
                contentDiv.innerHTML = originalHTML;
            }, 1500);
            
            StatusManager.showToast('報告內容已複製到剪貼簿', 'success');
            
        } catch (error) {
            console.error('❌ 複製失敗:', error);
            StatusManager.showToast('複製失敗，請手動選擇複製', 'error');
        }
    }
    
    /**
     * 下載報告
     */
    downloadReport() {
        if (!this.currentReportPath) {
            StatusManager.showToast('沒有可下載的報告', 'warning');
            return;
        }
        
        // 創建下載連結
        const downloadUrl = ApiClient.createReportDownloadUrl(this.currentReportPath);
        const fileName = this.currentReportPath.split('/').pop() || 'eqc_report.txt';
        
        Utils.downloadFile(downloadUrl, fileName);
        
        StatusManager.showToast('報告下載已開始', 'success');
        console.log('📥 報告下載已觸發:', fileName);
    }
    
    /**
     * 創建倒數計時器模態框
     * @param {string} extractDir - 解壓目錄
     * @param {number} seconds - 倒數秒數
     * @param {Function} onComplete - 完成回調
     * @param {Function} onCancel - 取消回調
     */
    createCountdownModal(extractDir, seconds = 5, onComplete, onCancel) {
        let countdown = seconds;
        
        // 創建倒數計時器UI元素
        const countdownModal = DOMManager.createElement('div', {
            id: 'countdownModal',
            styles: {
                position: 'fixed',
                top: '0',
                left: '0',
                width: '100%',
                height: '100%',
                background: 'rgba(0,0,0,0.8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: '1000'
            }
        });
        
        const countdownContent = DOMManager.createElement('div', {
            styles: {
                background: 'white',
                padding: '30px',
                borderRadius: '15px',
                textAlign: 'center',
                maxWidth: '500px',
                boxShadow: '0 20px 60px rgba(0,0,0,0.3)'
            },
            innerHTML: `
                <div style="margin-bottom: 20px;">
                    <i class="fas fa-magic" style="font-size: 3em; color: #667eea; margin-bottom: 15px;"></i>
                    <h3 style="color: #2c3e50; margin: 0 0 10px 0;">檔案解壓縮完成！</h3>
                    <p style="color: #6c757d; margin: 0; font-size: 14px;">解壓到: ${extractDir}</p>
                </div>
                
                <div style="background: #f8f9ff; padding: 20px; border-radius: 10px; margin: 20px 0; border-left: 4px solid #667eea;">
                    <div style="font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;">
                        <span id="countdownNumber">${countdown}</span> 秒後自動執行 EQC 流程
                    </div>
                    <div style="font-size: 14px; color: #6c757d;">
                        自動生成 EQCTOTALDATA → 程式碼區間檢測 → 雙重搜尋 → 完整報告
                    </div>
                </div>
                
                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button id="startNow" style="background: #667eea; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-play"></i> 立即開始
                    </button>
                    <button id="cancelCountdown" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 8px; cursor: pointer; font-weight: 600;">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            `
        });
        
        countdownModal.appendChild(countdownContent);
        document.body.appendChild(countdownModal);
        
        this.elements.countdownModal = countdownModal;
        
        // 更新倒數計時
        const updateCountdown = () => {
            const countdownElement = document.getElementById('countdownNumber');
            if (countdownElement) {
                countdownElement.textContent = countdown;
            }
            
            if (countdown <= 0) {
                this.closeCountdown();
                if (onComplete) onComplete();
            } else {
                countdown--;
            }
        };
        
        // 關閉倒數計時器
        const closeCountdown = () => {
            if (this.countdownInterval) {
                clearInterval(this.countdownInterval);
                this.countdownInterval = null;
            }
            if (countdownModal && countdownModal.parentNode) {
                countdownModal.parentNode.removeChild(countdownModal);
            }
        };
        
        // 綁定事件
        document.getElementById('cancelCountdown').addEventListener('click', () => {
            closeCountdown();
            if (onCancel) onCancel();
        });
        
        document.getElementById('startNow').addEventListener('click', () => {
            closeCountdown();
            if (onComplete) onComplete();
        });
        
        // 點擊模態框背景不關閉（防止意外取消）
        countdownModal.addEventListener('click', (e) => {
            if (e.target === countdownModal) {
                e.preventDefault();
            }
        });
        
        // 開始倒數計時
        this.countdownInterval = setInterval(updateCountdown, 1000);
        console.log('⏰ 倒數計時器已啟動');
        
        return {
            close: closeCountdown,
            updateCountdown
        };
    }
    
    /**
     * 關閉倒數計時器
     */
    closeCountdown() {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
        if (this.elements.countdownModal && this.elements.countdownModal.parentNode) {
            this.elements.countdownModal.parentNode.removeChild(this.elements.countdownModal);
            this.elements.countdownModal = null;
        }
    }
    
    /**
     * 顯示確認對話框
     * @param {string} title - 標題
     * @param {string} message - 訊息
     * @param {Function} onConfirm - 確認回調
     * @param {Function} onCancel - 取消回調
     */
    showConfirmDialog(title, message, onConfirm, onCancel) {
        const confirmed = confirm(`${title}\n\n${message}`);
        if (confirmed && onConfirm) {
            onConfirm();
        } else if (!confirmed && onCancel) {
            onCancel();
        }
    }
    
    /**
     * 獲取當前狀態
     * @returns {Object} 當前狀態
     */
    getCurrentStatus() {
        return {
            isModalOpen: this.isModalOpen,
            hasCountdown: !!this.countdownInterval,
            currentReportPath: this.currentReportPath
        };
    }
}

// 創建全局實例
const modal = new ModalComponent();

// 導出模組
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModalComponent;
} else if (typeof window !== 'undefined') {
    window.ModalComponent = ModalComponent;
    window.modal = modal;
}
