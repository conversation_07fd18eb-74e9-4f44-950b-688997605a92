"""
配置管理系統實作
使用 Pydantic 進行配置驗證和管理
"""

import os
from pathlib import Path
from typing import Dict, Any, Literal
from pydantic import BaseModel, Field, field_validator, ConfigDict


class DatabaseConfig(BaseModel):
    """資料庫配置"""

    driver: Literal["sqlite", "postgresql"] = "sqlite"
    host: str = "localhost"
    port: int = Field(default=5432, gt=0)
    database_name: str = "outlook_summary"
    username: str = ""
    password: str = ""

    @field_validator("driver")
    @classmethod
    def validate_driver(cls, v):
        """驗證資料庫驅動"""
        if v not in ["sqlite", "postgresql"]:
            raise ValueError(f"不支援的資料庫驅動: {v}")
        return v

    @property
    def connection_string(self) -> str:
        """生成資料庫連線字串"""
        if self.driver == "sqlite":
            return f"sqlite:///{self.database_name}"
        elif self.driver == "postgresql":
            return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database_name}"
        else:
            raise ValueError(f"不支援的資料庫驅動: {self.driver}")


class EmailConfig(BaseModel):
    """郵件配置"""

    monitor_enabled: bool = True
    check_interval: int = Field(default=30, gt=0)
    max_retries: int = Field(default=3, ge=0)
    sender_address: str = ""
    sender_password: str = ""

    @field_validator("check_interval")
    @classmethod
    def validate_check_interval(cls, v):
        """驗證檢查間隔"""
        if v <= 0:
            raise ValueError("檢查間隔必須大於 0")
        return v


class BIN1LogConfig(BaseModel):
    """BIN1 保護機制 LOG 配置"""
    
    enabled: bool = True
    save_protection_log: bool = True
    save_analysis_log: bool = True
    log_directory: str = "."
    include_timestamp: bool = True


class Settings(BaseModel):
    """主要配置設定"""

    app_name: str = "Outlook Summary System"
    debug: bool = False
    log_level: Literal["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"] = "INFO"
    temp_dir: Path = Path("/tmp/outlook_summary")

    # 子配置
    database: DatabaseConfig = Field(default_factory=DatabaseConfig)
    email: EmailConfig = Field(default_factory=EmailConfig)
    bin1_log: BIN1LogConfig = Field(default_factory=BIN1LogConfig)

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        """驗證日誌級別"""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v not in valid_levels:
            raise ValueError(f"無效的日誌級別: {v}. 有效值: {valid_levels}")
        return v

    model_config = ConfigDict(arbitrary_types_allowed=True)

    @classmethod
    def from_dict(cls, config_data: Dict[str, Any]) -> "Settings":
        """從字典建立配置"""
        # 處理巢狀配置
        if "database" in config_data:
            config_data["database"] = DatabaseConfig(**config_data["database"])
        if "email" in config_data:
            config_data["email"] = EmailConfig(**config_data["email"])
        if "bin1_log" in config_data:
            config_data["bin1_log"] = BIN1LogConfig(**config_data["bin1_log"])

        return cls(**config_data)

    @classmethod
    def from_environment(cls) -> "Settings":
        """從環境變數載入配置"""
        config_data = {}

        # 主要設定
        if os.getenv("OUTLOOK_DEBUG"):
            config_data["debug"] = os.getenv("OUTLOOK_DEBUG").lower() == "true"
        if os.getenv("OUTLOOK_LOG_LEVEL"):
            config_data["log_level"] = os.getenv("OUTLOOK_LOG_LEVEL")
        if os.getenv("OUTLOOK_TEMP_DIR"):
            config_data["temp_dir"] = Path(os.getenv("OUTLOOK_TEMP_DIR"))

        # 資料庫設定
        db_config = {}
        if os.getenv("OUTLOOK_DB_DRIVER"):
            db_config["driver"] = os.getenv("OUTLOOK_DB_DRIVER")
        if os.getenv("OUTLOOK_DB_HOST"):
            db_config["host"] = os.getenv("OUTLOOK_DB_HOST")
        if os.getenv("OUTLOOK_DB_PORT"):
            db_config["port"] = int(os.getenv("OUTLOOK_DB_PORT"))
        if os.getenv("OUTLOOK_DB_NAME"):
            db_config["database_name"] = os.getenv("OUTLOOK_DB_NAME")
        if os.getenv("OUTLOOK_DB_USER"):
            db_config["username"] = os.getenv("OUTLOOK_DB_USER")
        if os.getenv("OUTLOOK_DB_PASSWORD"):
            db_config["password"] = os.getenv("OUTLOOK_DB_PASSWORD")

        if db_config:
            config_data["database"] = DatabaseConfig(**db_config)

        # 郵件設定
        email_config = {}
        if os.getenv("OUTLOOK_EMAIL_ENABLED"):
            email_config["monitor_enabled"] = os.getenv("OUTLOOK_EMAIL_ENABLED").lower() == "true"
        if os.getenv("OUTLOOK_EMAIL_INTERVAL"):
            email_config["check_interval"] = int(os.getenv("OUTLOOK_EMAIL_INTERVAL"))
        if os.getenv("OUTLOOK_EMAIL_RETRIES"):
            email_config["max_retries"] = int(os.getenv("OUTLOOK_EMAIL_RETRIES"))
        if os.getenv("OUTLOOK_EMAIL_SENDER"):
            email_config["sender_address"] = os.getenv("OUTLOOK_EMAIL_SENDER")
        if os.getenv("OUTLOOK_EMAIL_PASSWORD"):
            email_config["sender_password"] = os.getenv("OUTLOOK_EMAIL_PASSWORD")

        if email_config:
            config_data["email"] = EmailConfig(**email_config)

        # BIN1 LOG 設定
        bin1_log_config = {}
        if os.getenv("BIN1_LOG_ENABLED"):
            bin1_log_config["enabled"] = os.getenv("BIN1_LOG_ENABLED").lower() == "true"
        if os.getenv("BIN1_LOG_SAVE_PROTECTION"):
            bin1_log_config["save_protection_log"] = os.getenv("BIN1_LOG_SAVE_PROTECTION").lower() == "true"
        if os.getenv("BIN1_LOG_SAVE_ANALYSIS"):
            bin1_log_config["save_analysis_log"] = os.getenv("BIN1_LOG_SAVE_ANALYSIS").lower() == "true"
        if os.getenv("BIN1_LOG_DIRECTORY"):
            bin1_log_config["log_directory"] = os.getenv("BIN1_LOG_DIRECTORY")
        if os.getenv("BIN1_LOG_INCLUDE_TIMESTAMP"):
            bin1_log_config["include_timestamp"] = os.getenv("BIN1_LOG_INCLUDE_TIMESTAMP").lower() == "true"

        if bin1_log_config:
            config_data["bin1_log"] = BIN1LogConfig(**bin1_log_config)

        return cls(**config_data)

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典"""
        result = self.model_dump()

        # 確保 Path 物件被序列化為字串
        if isinstance(result.get("temp_dir"), Path):
            result["temp_dir"] = str(result["temp_dir"])

        return result
