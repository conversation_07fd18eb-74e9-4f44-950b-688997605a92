"""
郵件讀取器工廠
提供統一的郵件讀取器建立介面
"""

from typing import Optional, Dict, Any, List
from enum import Enum

from src.application.interfaces.email_reader import EmailReader, EmailReaderConfig
from src.infrastructure.adapters.pop3.pop3_email_reader import POP3EmailReader
from src.infrastructure.adapters.pop3.pop3_adapter import POP3ConnectionConfig
from src.infrastructure.logging.logger_manager import LoggerManager


class EmailReaderType(Enum):
    """郵件讀取器類型"""
    POP3 = "pop3"
    OUTLOOK = "outlook"
    IMAP = "imap"


class EmailReaderFactory:
    """
    郵件讀取器工廠
    根據配置建立不同類型的郵件讀取器
    """
    
    @staticmethod
    def create_pop3_reader(
        server: str,
        port: int,
        username: str,
        password: str,
        use_ssl: bool = False,
        timeout: int = 30,
        reader_config: Optional[EmailReaderConfig] = None
    ) -> POP3EmailReader:
        """
        建立 POP3 郵件讀取器
        
        Args:
            server: POP3 伺服器地址
            port: POP3 端口
            username: 使用者名稱
            password: 密碼
            use_ssl: 是否使用 SSL
            timeout: 連接超時時間
            reader_config: 讀取器配置
            
        Returns:
            POP3 郵件讀取器實例
        """
        connection_config = POP3ConnectionConfig(
            server=server,
            port=port,
            username=username,
            password=password,
            use_ssl=use_ssl,
            timeout=timeout
        )
        
        return POP3EmailReader(connection_config, reader_config)
    
    @staticmethod
    def create_outlook_reader(
        reader_config: Optional[EmailReaderConfig] = None
    ) -> EmailReader:
        """
        建立 Outlook 郵件讀取器
        
        Args:
            reader_config: 讀取器配置
            
        Returns:
            Outlook 郵件讀取器實例
        """
        # 導入 Outlook 相關模組
        try:
            from src.infrastructure.adapters.outlook.outlook_email_reader import OutlookEmailReader
            return OutlookEmailReader(reader_config)
        except ImportError:
            raise NotImplementedError("Outlook 郵件讀取器尚未實作")
    
    @staticmethod
    def create_imap_reader(
        server: str,
        port: int,
        username: str,
        password: str,
        use_ssl: bool = True,
        timeout: int = 30,
        reader_config: Optional[EmailReaderConfig] = None
    ) -> EmailReader:
        """
        建立 IMAP 郵件讀取器
        
        Args:
            server: IMAP 伺服器地址
            port: IMAP 端口
            username: 使用者名稱
            password: 密碼
            use_ssl: 是否使用 SSL
            timeout: 連接超時時間
            reader_config: 讀取器配置
            
        Returns:
            IMAP 郵件讀取器實例
        """
        # IMAP 實作將在未來加入
        raise NotImplementedError("IMAP 郵件讀取器尚未實作")
    
    @staticmethod
    def create_from_config(
        config: Dict[str, Any],
        reader_type: EmailReaderType = EmailReaderType.POP3
    ) -> EmailReader:
        """
        根據配置建立郵件讀取器
        
        Args:
            config: 配置字典
            reader_type: 讀取器類型
            
        Returns:
            郵件讀取器實例
        """
        logger = LoggerManager().get_logger("EmailReaderFactory")
        
        try:
            if reader_type == EmailReaderType.POP3:
                return EmailReaderFactory.create_pop3_reader(
                    server=config.get('server', 'localhost'),
                    port=config.get('port', 110),
                    username=config.get('username', ''),
                    password=config.get('password', ''),
                    use_ssl=config.get('use_ssl', False),
                    timeout=config.get('timeout', 30)
                )
            elif reader_type == EmailReaderType.OUTLOOK:
                return EmailReaderFactory.create_outlook_reader()
            elif reader_type == EmailReaderType.IMAP:
                return EmailReaderFactory.create_imap_reader(
                    server=config.get('server', 'localhost'),
                    port=config.get('port', 143),
                    username=config.get('username', ''),
                    password=config.get('password', ''),
                    use_ssl=config.get('use_ssl', True),
                    timeout=config.get('timeout', 30)
                )
            else:
                raise ValueError(f"不支援的郵件讀取器類型: {reader_type}")
                
        except Exception as e:
            logger.error(f"建立郵件讀取器失敗: {e}")
            raise
    
    @staticmethod
    def create_from_env_config(reader_type: EmailReaderType = EmailReaderType.POP3) -> EmailReader:
        """
        根據環境變數配置建立郵件讀取器
        
        Args:
            reader_type: 讀取器類型
            
        Returns:
            郵件讀取器實例
        """
        try:
            # 使用現有的配置管理器
            import sys
            import os
            
            # 添加項目根目錄到 Python 路徑
            project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../..'))
            if project_root not in sys.path:
                sys.path.insert(0, project_root)
            
            from email_config import EmailConfigManager
            
            config_manager = EmailConfigManager()
            
            if reader_type == EmailReaderType.POP3:
                pop3_config = config_manager.get_pop3_config()
                account_config = config_manager.get_email_account()
                
                return EmailReaderFactory.create_pop3_reader(
                    server=pop3_config.server,
                    port=pop3_config.port,
                    username=account_config.email_address,
                    password=account_config.password,
                    use_ssl=pop3_config.use_ssl,
                    timeout=pop3_config.timeout
                )
            elif reader_type == EmailReaderType.OUTLOOK:
                return EmailReaderFactory.create_outlook_reader()
            else:
                raise ValueError(f"不支援的郵件讀取器類型: {reader_type}")
                
        except Exception as e:
            logger = LoggerManager().get_logger("EmailReaderFactory")
            logger.error(f"從環境配置建立郵件讀取器失敗: {e}")
            raise
    
    @staticmethod
    def get_available_readers() -> List[str]:
        """
        取得可用的郵件讀取器類型
        
        Returns:
            可用的讀取器類型列表
        """
        available = ['pop3']
        
        # 檢查 Outlook 是否可用
        try:
            import win32com.client
            available.append('outlook')
        except ImportError:
            pass
        
        # 檢查 IMAP 是否實作
        # available.append('imap')  # 未來實作
        
        return available