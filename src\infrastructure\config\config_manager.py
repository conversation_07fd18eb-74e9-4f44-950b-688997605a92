"""
擴展配置管理系統
TASK_002: 實現靈活的配置管理系統
支援多環境、熱重載、加密等高級功能
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, field
from pydantic import BaseModel, Field, field_validator
from cryptography.fernet import Fernet
import base64

from .settings import Settings, DatabaseConfig, EmailConfig


@dataclass
class ValidationResult:
    """配置驗證結果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


class FileStorageConfig(BaseModel):
    """檔案儲存配置"""
    
    temp_dir: Path = Field(default=Path("/tmp/outlook_summary"))
    network_base_path: str = "//server/outlook-data"
    max_file_size_mb: int = Field(default=100, gt=0)
    allowed_extensions: List[str] = Field(default=[".csv", ".xlsx", ".zip", ".txt"])
    cleanup_after_days: int = Field(default=7, gt=0)
    enable_compression: bool = True
    backup_enabled: bool = True
    backup_retention_days: int = Field(default=30, gt=0)


class OutlookConfig(BaseModel):
    """Outlook 配置"""
    
    monitor_folder: str = "Inbox"
    backup_folder: str = "Processed"
    check_interval_seconds: int = Field(default=30, gt=0)
    auto_mark_read: bool = True
    max_email_age_days: int = Field(default=30, gt=0)
    enable_filters: bool = True
    filter_rules: List[str] = Field(default_factory=list)


class VendorConfig(BaseModel):
    """廠商配置"""
    
    name: str
    email_patterns: List[str]
    subject_keywords: List[str]
    parser_class: str
    enabled: bool = True
    priority: int = Field(default=1, gt=0)
    timeout_seconds: int = Field(default=300, gt=0)
    retry_count: int = Field(default=3, ge=0)
    custom_settings: Dict[str, Any] = Field(default_factory=dict)


class PerformanceConfig(BaseModel):
    """效能配置"""
    
    max_concurrent_emails: int = Field(default=3, gt=0)
    email_processing_timeout: int = Field(default=300, gt=0)
    file_processing_timeout: int = Field(default=600, gt=0)
    memory_limit_mb: int = Field(default=512, gt=0)
    enable_caching: bool = True
    cache_ttl_seconds: int = Field(default=3600, gt=0)
    enable_profiling: bool = False
    profile_output_dir: Path = Field(default=Path("/tmp/outlook_profiles"))


class SecurityConfig(BaseModel):
    """安全配置"""
    
    encrypt_sensitive_data: bool = True
    log_email_content: bool = False
    allowed_file_types: List[str] = Field(default=[".csv", ".xlsx", ".zip"])
    max_attachment_size_mb: int = Field(default=50, gt=0)
    virus_scan_enabled: bool = False
    scan_command: Optional[str] = None
    quarantine_dir: Path = Field(default=Path("/tmp/outlook_quarantine"))


class AdvancedSettings(Settings):
    """擴展設定類別"""
    
    # 新增的配置區塊
    file_storage: FileStorageConfig = Field(default_factory=FileStorageConfig)
    outlook: OutlookConfig = Field(default_factory=OutlookConfig)
    performance: PerformanceConfig = Field(default_factory=PerformanceConfig)
    security: SecurityConfig = Field(default_factory=SecurityConfig)
    
    # 廠商配置
    vendors: Dict[str, VendorConfig] = Field(default_factory=dict)
    
    # 進階設定
    environment: str = "development"
    feature_flags: Dict[str, bool] = Field(default_factory=dict)
    custom_settings: Dict[str, Any] = Field(default_factory=dict)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, env: str = "development", config_dir: Optional[Path] = None):
        self.current_env = env
        self.config_dir = config_dir or Path("config")
        self.config: AdvancedSettings = self._load_environment_config()
        self._encryption_key = self._get_or_create_encryption_key()
    
    def _load_environment_config(self) -> AdvancedSettings:
        """載入環境特定配置"""
        config_file = self.config_dir / f"{self.current_env}.yaml"
        
        if config_file.exists():
            return self.load_from_file(str(config_file))
        else:
            # 使用預設配置
            return AdvancedSettings(environment=self.current_env)
    
    def load_from_file(self, file_path: str) -> AdvancedSettings:
        """從檔案載入配置"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"配置檔案不存在: {file_path}")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                config_data = yaml.safe_load(f)
            elif file_path.suffix.lower() == '.json':
                config_data = json.load(f)
            else:
                raise ValueError(f"不支援的配置檔案格式: {file_path.suffix}")
        
        return self._create_advanced_settings(config_data)
    
    def _create_advanced_settings(self, config_data: Dict[str, Any]) -> AdvancedSettings:
        """從字典建立擴展設定"""
        # 處理巢狀配置
        if "database" in config_data:
            config_data["database"] = DatabaseConfig(**config_data["database"])
        if "email" in config_data:
            config_data["email"] = EmailConfig(**config_data["email"])
        if "file_storage" in config_data:
            config_data["file_storage"] = FileStorageConfig(**config_data["file_storage"])
        if "outlook" in config_data:
            config_data["outlook"] = OutlookConfig(**config_data["outlook"])
        if "performance" in config_data:
            config_data["performance"] = PerformanceConfig(**config_data["performance"])
        if "security" in config_data:
            config_data["security"] = SecurityConfig(**config_data["security"])
        
        # 處理廠商配置
        if "vendors" in config_data:
            vendors = {}
            for vendor_code, vendor_data in config_data["vendors"].items():
                vendors[vendor_code] = VendorConfig(**vendor_data)
            config_data["vendors"] = vendors
        
        return AdvancedSettings(**config_data)
    
    def switch_environment(self, env: str) -> None:
        """切換環境"""
        valid_envs = ["development", "staging", "production", "testing"]
        if env not in valid_envs:
            raise ValueError(f"不支援的環境: {env}. 有效環境: {valid_envs}")
        
        self.current_env = env
        self.config = self._load_environment_config()
    
    def get_vendor_config(self, vendor_code: str) -> Optional[VendorConfig]:
        """取得廠商配置"""
        return self.config.vendors.get(vendor_code)
    
    def set_vendor_configs(self, vendor_configs: Dict[str, VendorConfig]) -> None:
        """設定廠商配置"""
        self.config.vendors = vendor_configs
    
    def get_enabled_vendors(self) -> List[str]:
        """取得所有啟用的廠商"""
        return [
            vendor_code 
            for vendor_code, vendor_config in self.config.vendors.items()
            if vendor_config.enabled
        ]
    
    def validate_config(self, config: AdvancedSettings) -> ValidationResult:
        """驗證配置"""
        result = ValidationResult(is_valid=True)
        
        try:
            # 使用 Pydantic 驗證
            config.model_validate(config.model_dump())
        except Exception as e:
            result.is_valid = False
            result.errors.append(f"Pydantic 驗證失敗: {str(e)}")
        
        # 自訂驗證規則
        if config.database.port <= 0 or config.database.port > 65535:
            result.is_valid = False
            result.errors.append("資料庫埠號必須在 1-65535 範圍內")
        
        if config.file_storage.max_file_size_mb <= 0:
            result.is_valid = False
            result.errors.append("檔案大小限制必須大於 0")
        
        if not config.file_storage.allowed_extensions:
            result.warnings.append("未設定允許的檔案副檔名")
        
        return result
    
    def validate_config_dict(self, config_data: Dict[str, Any]) -> ValidationResult:
        """驗證配置字典"""
        try:
            config = self._create_advanced_settings(config_data)
            return self.validate_config(config)
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"配置字典驗證失敗: {str(e)}"]
            )
    
    def reload_config(self, override_data: Optional[Dict[str, Any]] = None) -> None:
        """重新載入配置"""
        base_config = self._load_environment_config()
        
        if override_data:
            merged_data = self.merge_configs(
                base_config.model_dump(),
                override_data
            )
            self.config = self._create_advanced_settings(merged_data)
        else:
            self.config = base_config
    
    def merge_configs(self, base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
        """合併配置"""
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self.merge_configs(result[key], value)
            else:
                result[key] = value
        
        return result
    
    def _get_or_create_encryption_key(self) -> bytes:
        """取得或建立加密金鑰"""
        key_file = self.config_dir / ".encryption_key"
        
        if key_file.exists():
            with open(key_file, 'rb') as f:
                return base64.urlsafe_b64decode(f.read())
        else:
            # 建立新金鑰
            key = Fernet.generate_key()
            key_file.parent.mkdir(parents=True, exist_ok=True)
            with open(key_file, 'wb') as f:
                f.write(base64.urlsafe_b64encode(key))
            return key
    
    def encrypt_sensitive_config(self, sensitive_data: Dict[str, str]) -> Dict[str, str]:
        """加密敏感配置資料"""
        if not self.config.security.encrypt_sensitive_data:
            return sensitive_data
        
        fernet = Fernet(self._encryption_key)
        encrypted_data = {}
        
        for key, value in sensitive_data.items():
            encrypted_value = fernet.encrypt(value.encode('utf-8'))
            encrypted_data[key] = base64.urlsafe_b64encode(encrypted_value).decode('utf-8')
        
        return encrypted_data
    
    def decrypt_sensitive_config(self, encrypted_data: Dict[str, str]) -> Dict[str, str]:
        """解密敏感配置資料"""
        if not self.config.security.encrypt_sensitive_data:
            return encrypted_data
        
        fernet = Fernet(self._encryption_key)
        decrypted_data = {}
        
        for key, encrypted_value in encrypted_data.items():
            try:
                decoded_value = base64.urlsafe_b64decode(encrypted_value.encode('utf-8'))
                decrypted_value = fernet.decrypt(decoded_value)
                decrypted_data[key] = decrypted_value.decode('utf-8')
            except Exception as e:
                decrypted_data[key] = encrypted_value  # 如果解密失敗，返回原值
        
        return decrypted_data
    
    def save_config(self, file_path: Optional[str] = None) -> None:
        """儲存配置到檔案"""
        if not file_path:
            file_path = self.config_dir / f"{self.current_env}.yaml"
        
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        config_dict = self.config.model_dump()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(config_dict, f, allow_unicode=True, default_flow_style=False)
    
    def get_feature_flag(self, flag_name: str, default: bool = False) -> bool:
        """取得功能開關狀態"""
        return self.config.feature_flags.get(flag_name, default)
    
    def set_feature_flag(self, flag_name: str, enabled: bool) -> None:
        """設定功能開關"""
        self.config.feature_flags[flag_name] = enabled