#!/usr/bin/env python3
"""
EQC BIN1 完整整合處理器
結合 BIN1 統計 + FT-EQC 配對 + 超連結功能
基於 CLAUDE.md AI 設計原則，遵循功能替換原則
"""

import os
import sys
import csv
import time
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional
from pathlib import Path
import psutil

# 添加 src 路徑（動態検測）
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = None
# 向上搜尋包含 infrastructure 目錄的 src 目錄
for _ in range(10):  # 最多向上搜尋 10 層
    test_src = os.path.join(current_dir, 'infrastructure')
    if os.path.exists(test_src):
        src_path = current_dir
        break
    current_dir = os.path.dirname(current_dir)
    
if src_path:
    sys.path.append(src_path)
else:
    # 備用方案：使用環境變數
    fallback_src = os.getenv('PROJECT_SRC_PATH', '/mnt/d/project/python/outlook_summary/src')
    if os.path.exists(fallback_src):
        sys.path.append(fallback_src)

from infrastructure.adapters.excel.ft_eqc_grouping_processor import (
    CSVFileDiscovery, 
    TimeBasedMatcher, 
    OnlineEQCFailProcessor,
    FTEQCGroupingProcessor
)


class ProgressMonitor:
    """
    智能進度監控類別
    提供處理速度估算、記憶體監控和完成摘要
    """
    
    def __init__(self, start_time, timeout):
        self.start_time = start_time
        self.timeout = timeout
        self.successful_files = 0
        self.failed_files = 0
    
    def show_progress(self, current, total, filename):
        """顯示檔案處理進度與預估時間"""
        elapsed = time.time() - self.start_time
        speed = current / elapsed if elapsed > 0 else 0
        remaining = (total - current) / speed if speed > 0 else 0
        
        print(f"[REFRESH] 處理檔案 {current}/{total}: {filename}")
        if speed > 0:
            print(f"   [TIME] 預估剩餘: {remaining:.0f}秒 | 速度: {speed:.2f} 檔案/秒")
        
        # 每50個檔案檢查記憶體使用
        if current % 50 == 0 and current > 0:
            try:
                memory = psutil.virtual_memory().percent
                print(f"   [DISK] 記憶體使用: {memory:.1f}%")
            except Exception:
                pass
    
    def show_line_progress(self, lines_processed, filename, interval=1000):
        """顯示行級處理進度（每interval行顯示一次）"""
        if lines_processed % interval == 0 and lines_processed > 0:
            print(f"   [NOTES] {filename}: 已處理 {lines_processed} 行數據...")
    
    def record_success(self):
        """記錄成功處理的檔案"""
        self.successful_files += 1
    
    def record_failure(self):
        """記錄失敗處理的檔案"""
        self.failed_files += 1
    
    def show_summary(self):
        """顯示處理完成摘要"""
        elapsed = time.time() - self.start_time
        total_files = self.successful_files + self.failed_files
        speed = total_files / elapsed if elapsed > 0 else 0
        
        print(f"\n[CHART] 處理完成摘要:")
        print(f"   [OK] 成功處理: {self.successful_files}/{total_files}")
        if self.failed_files > 0:
            print(f"   [ERROR] 失敗檔案: {self.failed_files}/{total_files}")
        print(f"   [TIME] 總時間: {elapsed:.1f}秒")
        print(f"   [UP] 平均速度: {speed:.2f} 檔案/秒")


class EQCDebugLogger:
    """
    EQCTOTALDATA 生成過程的詳細日誌記錄器
    記錄 FT、ONLINE EQC、EQC RT 檔案的配對和統計資訊
    """
    
    def __init__(self, log_dir: str, enabled: bool = True):
        self.enabled = enabled and os.getenv("EQC_DETAILED_LOGS", "true").lower() == "true"
        self.log_dir = log_dir
        self.log_entries = []
        
        if self.enabled:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            self.log_filename = f"EQCTOTALDATA_DEBUG_{timestamp}.log"
            self.log_path = os.path.join(log_dir, self.log_filename)
            self._init_log()
    
    def _init_log(self):
        """初始化日誌檔案"""
        if not self.enabled:
            return
        
        header = [
            "=" * 80,
            "EQCTOTALDATA 生成過程詳細日誌",
            "=" * 80,
            f"處理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"處理目錄: {self.log_dir}",
            "=" * 80,
            ""
        ]
        self.log_entries.extend(header)
    
    def log_section(self, title: str):
        """記錄段落標題"""
        if not self.enabled:
            return
        
        self.log_entries.extend([
            "",
            f"[BOARD] {title}",
            "-" * 60
        ])
    
    def log_file_scan(self, file_type: str, files: List[str]):
        """記錄檔案掃描結果"""
        if not self.enabled:
            return
        
        self.log_entries.append(f"[FILE_FOLDER] {file_type} 檔案掃描結果: {len(files)} 個檔案")
        for file_path in files:
            filename = os.path.basename(file_path)
            self.log_entries.append(f"   - {filename}")
    
    def log_file_timestamp(self, file_path: str, timestamp: Optional[int], readable_time: str):
        """記錄檔案時間戳提取結果"""
        if not self.enabled:
            return
        
        filename = os.path.basename(file_path)
        if timestamp:
            self.log_entries.append(f"   [ALARM_CLOCK] {filename}: {readable_time} (timestamp: {timestamp})")
        else:
            self.log_entries.append(f"   [ERROR] {filename}: 時間戳提取失敗")
    
    def log_pairing_result(self, ft_file: str, eqc_file: str, match_method: str = "時間匹配"):
        """記錄配對結果"""
        if not self.enabled:
            return
        
        ft_name = os.path.basename(ft_file) if ft_file else "無"
        eqc_name = os.path.basename(eqc_file) if eqc_file else "無"
        self.log_entries.append(f"   [LINK] 配對成功 ({match_method}): {ft_name} [LEFT_RIGHT_ARROW] {eqc_name}")
    
    def log_statistics(self, file_type: str, file_path: str, pass_count: int, fail_count: int, total_count: int):
        """記錄檔案統計資訊"""
        if not self.enabled:
            return
        
        filename = os.path.basename(file_path)
        self.log_entries.append(f"   [CHART] {filename}: PASS={pass_count}, FAIL={fail_count}, TOTAL={total_count}")
    
    def log_unmatched_files(self, file_type: str, files: List[str], processor_instance=None):
        """記錄未配對檔案，包含內部時間戳資訊"""
        if not self.enabled:
            return

        if files:
            self.log_entries.append(f"[OPEN_FILE_FOLDER] 未配對的 {file_type} 檔案: {len(files)} 個")
            for file_path in files:
                filename = os.path.basename(file_path)

                # 嘗試提取內部時間戳
                timestamp_info = ""
                if processor_instance and hasattr(processor_instance, '_extract_internal_timestamp'):
                    try:
                        timestamp = processor_instance._extract_internal_timestamp(file_path)
                        if timestamp and timestamp > 0:
                            readable_time = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
                            timestamp_info = f" | 內部時間: {readable_time}"
                        else:
                            timestamp_info = " | 內部時間: 無法提取"
                    except Exception:
                        timestamp_info = " | 內部時間: 提取失敗"

                self.log_entries.append(f"   - {filename}{timestamp_info}")
    
    def log_summary(self, online_eqc_fail: int, eqc_rt_pass: int, total_pairs: int):
        """記錄處理總結"""
        if not self.enabled:
            return
        
        self.log_entries.extend([
            "",
            "[BOARD] 處理總結",
            "-" * 60,
            f"[RED_CIRCLE] Online EQC FAIL 總數: {online_eqc_fail}",
            f"[GREEN_CIRCLE] EQC RT PASS 總數: {eqc_rt_pass}",
            f"[LINK] 成功配對數量: {total_pairs}",
            "=" * 80
        ])
    
    def save_log(self):
        """保存日誌到檔案"""
        if not self.enabled or not self.log_entries:
            return
        
        try:
            with open(self.log_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(self.log_entries))
            print(f"[BOARD] 詳細日誌已保存: {os.path.basename(self.log_path)}")
        except Exception as e:
            print(f"[ERROR] 保存日誌失敗: {e}")


class HyperlinkProcessor:
    """
    超連結處理器 - 完全對應 VBA ReplacePath 功能
    遵循 CLAUDE.md 功能替換原則，替換原有簡化邏輯
    """
    
    def __init__(self, base_path: str):
        from dotenv import load_dotenv
        import os
        
        load_dotenv()
        
        # 完全動態的本地路徑，支援任意目錄
        self.temp_path = base_path
        
        # 從環境變數載入網路路徑，預設值為原有路徑
        self.net_path = os.getenv('HYPERLINK_NETWORK_PATH', r'\\192.168.1.60\temp_7days')
        
    def convert_to_network_path(self, local_path: str) -> str:
        """
        將本地路徑轉換為網路共享路徑 - 完全對應 VBA ReplacePath 函數
        
        VBA 邏輯：
        1. findStr = tempPath & "\" 
        2. replaceStr = netpath & "\"
        3. 找到 findStr 在 inputStr 中的位置
        4. 找到 findStr 後第一個 "\" 的位置
        5. 用 replaceStr 替換到該位置的路徑部分
        
        轉換範例：
        輸入: /mnt/d/project/python/outlook_summary/doc/20250523/Production Data/file.csv
        輸出: \\\\192.168.1.60\\temp_7days\\20250523\\Production Data\\file.csv
        """
        try:
            # 規範化路徑分隔符 (Unix → Windows)
            input_path = local_path.replace("/", "\\")
            temp_path_win = self.temp_path.replace("/", "\\")
            
            # VBA: findStr = tempPath & "\"
            find_str = temp_path_win + "\\"
            # VBA: replaceStr = netpath & "\"  
            replace_str = self.net_path + "\\"
            
            # VBA: startpos = InStr(1, inputStr, findStr)
            start_pos = input_path.find(find_str)
            
            if start_pos != -1:
                # VBA: endpos = InStrRev(inputStr, "\", startpos + Len(findStr))
                # 找到 findStr 之後的路徑部分
                after_temp_path = input_path[start_pos + len(find_str):]
                
                # VBA: ReplacePath = replaceStr & Mid(inputStr, endpos + 1)
                # 保留完整的子目錄結構
                network_path = replace_str + after_temp_path
                
                print(f"[LINK] 路徑轉換: {os.path.basename(local_path)}")
                print(f"   本地: {local_path}")  
                print(f"   網路: {network_path}")
                
                return network_path
            else:
                # 如果找不到本地路徑前綴，使用檔案名稱作為備用
                filename = os.path.basename(local_path)
                fallback_path = f"{self.net_path}\\{filename}"
                print(f"[WARNING]  路徑轉換備用方案: {filename}")
                return fallback_path
                
        except Exception as e:
            print(f"[ERROR] 路徑轉換失敗: {e}")
            return local_path
    
    def add_hyperlink_to_data(self, data_row: List[str], file_path: str, 
                             column_index: int = 2) -> List[str]:
        """
        在指定欄位添加超連結 - 對應 VBA AddFTToRowA 超連結處理
        遵循 CLAUDE.md 功能替換原則，完全替換指定欄位內容
        """
        result_row = data_row.copy()
        
        # 確保有足夠的欄位
        while len(result_row) <= column_index:
            result_row.append("")
        
        # 轉換為網路路徑並添加 HYPERLINK 前綴
        network_path = self.convert_to_network_path(file_path)
        result_row[column_index] = f"HYPERLINK:{network_path}"
        
        return result_row
    
    def convert_csv_to_excel_hyperlinks(self, csv_file_path: str, output_excel_path: str) -> bool:
        """
        將CSV中的超連結轉換為Excel可點擊超連結 - 對應 VBA ConvertToHyperlinks 函數
        
        VBA邏輯：
        1. 開啟Excel檔案
        2. 找到C欄（第3欄）中包含路徑的儲存格
        3. 將路徑轉換為Excel超連結格式
        4. 顯示文字為檔案名稱，連結為完整路徑
        """
        try:
            # 注意：此功能需要 openpyxl 套件，目前僅提供框架
            print(f"[BOARD] Excel超連結轉換功能:")
            print(f"   輸入CSV: {csv_file_path}")
            print(f"   輸出Excel: {output_excel_path}")
            print(f"   [WARNING]  需要安裝 openpyxl 套件來實作完整Excel功能")
            
            # 檢查是否有openpyxl
            try:
                import openpyxl
                return self._perform_excel_hyperlink_conversion(csv_file_path, output_excel_path)
            except ImportError:
                print(f"   [IDEA] 可使用: pip install openpyxl")
                return False
                
        except Exception as e:
            print(f"[ERROR] Excel超連結轉換失敗: {e}")
            return False
    
    def _perform_excel_hyperlink_conversion(self, csv_file_path: str, output_excel_path: str) -> bool:
        """執行實際的Excel超連結轉換"""
        try:
            import openpyxl
            import csv
            
            # 讀取CSV資料
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                csv_reader = csv.reader(f)
                for row_num, row_data in enumerate(csv_reader, 1):
                    for col_num, cell_value in enumerate(row_data, 1):
                        # 寫入基本資料
                        worksheet.cell(row=row_num, column=col_num, value=cell_value)
                        
                        # 處理第3欄的超連結 (對應VBA的C欄)
                        if col_num == 3 and cell_value.startswith("HYPERLINK:"):
                            hyperlink_path = cell_value.replace("HYPERLINK:", "")
                            filename = os.path.basename(hyperlink_path)
                            
                            # 建立Excel超連結：顯示檔案名稱，連結到完整路徑
                            cell = worksheet.cell(row=row_num, column=col_num)
                            cell.hyperlink = hyperlink_path
                            cell.value = filename
                            cell.style = "Hyperlink"  # Excel超連結樣式
            
            # 儲存Excel檔案
            workbook.save(output_excel_path)
            print(f"[OK] Excel超連結轉換完成: {output_excel_path}")
            return True
            
        except Exception as e:
            print(f"[ERROR] Excel轉換過程失敗: {e}")
            return False


# 舊的 EQCStatisticsCalculator 類已刪[EXCEPT_CHAR]，功能整合到 EQCBin1FinalProcessor 中


class EQCBin1FinalProcessor:
    """
    EQC BIN1 完整整合處理器
    整合所有功能：BIN1 統計 + FT-EQC 配對 + 超連結
    遵循 CLAUDE.md 功能替換原則
    """
    
    def __init__(self):
        self.data_start_row = int(os.getenv('DATA_START_ROW', '12'))  # 資料起始行（可配置）
        self.file_discovery = CSVFileDiscovery()
        self.time_matcher = TimeBasedMatcher()
        self.fail_processor = OnlineEQCFailProcessor()
        # hyperlink_processor 將在 process_complete_eqc_integration 中動態建立
        # debug_logger 將在 process_complete_eqc_integration 中動態建立
        
        # [FIRE] 核心無限迴圈防護設定
        self.MAX_SCAN_LINES = int(os.getenv('MAX_SCAN_LINES', '10000'))  # 最大掃描行數
        self.PROCESSING_TIMEOUT = int(os.getenv('PROCESSING_TIMEOUT', '120'))  # 處理超時(秒)
        self.start_time = None
    
    def check_eqc_csv_file(self, file_path: str) -> bool:
        """
        檢查 CSV 檔案是否為 EQC 檔案 - 完全對應 VBA CheckEQCCSVFile 函數
        
        VBA 邏輯：
        1. 排[EXCEPT_CHAR]包含 eqctotaldata 或 eqcfaildata 的檔案路徑
        2. 檢查前兩行是否包含 (qc) 或檔案路徑包含 .qa
        3. 檢查第三行是否包含 qa
        
        Args:
            file_path (str): 要檢查的檔案路徑
            
        Returns:
            bool: True 如果是 EQC 檔案，False 否則
        """
        if not file_path:
            return False
            
        # VBA: 排[EXCEPT_CHAR]包含 eqctotaldata 或 eqcfaildata 的檔案
        file_path_lower = file_path.lower()
        if 'eqctotaldata' in file_path_lower or 'eqcfaildata' in file_path_lower:
            return False
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # VBA: 檢查前兩行是否包含 (qc) 或檔案路徑包含 .qa
                # 嚴格按照 VBA 邏輯：只檢查 "(qc)"，不包含 "(auto_qc)" 等其他變體
                for i in range(2):
                    try:
                        line = f.readline()
                        if not line:  # 檔案行數不足
                            break
                        line_lower = line.lower()
                        if '(qc)' in line_lower or '.qa' in file_path_lower:
                            return True
                    except Exception:
                        continue
                
                # VBA: 檢查第三行是否包含 qa
                try:
                    third_line = f.readline()
                    if third_line and 'qa' in third_line.lower():
                        return True
                except Exception:
                    pass
                    
        except Exception as e:
            print(f"[WARNING] 檢查 EQC 檔案失敗 {os.path.basename(file_path)}: {e}")
            return False
            
        return False

    def find_online_eqc_bin1_datalog(self, eqc_files: List[str]) -> str:
        """
        找出 EQC BIN=1 的 golden IC 資料
        對應 VBA FindOnlieEQCBin1datalog 函數
        現在使用 VBA 一致的檔案識別邏輯 (check_eqc_csv_file)
        [FIRE] 已修復無限迴圈問題：加入掃描限制和超時檢測
        [FIRE] 已移[EXCEPT_CHAR]檔案限制，加入智能進度監控
        """
        total_files = len(eqc_files)
        print(f"[SEARCH] 開始搜尋 EQC BIN=1 資料")
        print(f"[CHART] 總檔案數: {total_files}, 最大掃描行數: {self.MAX_SCAN_LINES}")
        
        # 建立進度監控器
        progress_monitor = ProgressMonitor(self.start_time, self.PROCESSING_TIMEOUT)
        
        for file_index, file_path in enumerate(eqc_files):
            # [FIRE] 檢查處理超時
            if self.start_time and (time.time() - self.start_time) > self.PROCESSING_TIMEOUT:
                print(f"[ALARM_CLOCK] 處理超時 ({self.PROCESSING_TIMEOUT}秒)，已處理 {file_index + 1}/{total_files} 個檔案")
                break
                
            filename = os.path.basename(file_path)
            
            # 智能進度顯示
            progress_monitor.show_progress(file_index + 1, total_files, filename)
                
            # 使用 VBA 一致的檔案識別邏輯替代檔名檢查
            if self.check_eqc_csv_file(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    print(f"[PAGE_FACING_UP] 掃描檔案: {filename} ({len(lines)} 行)")
                    
                    # 建立檔案前12行標頭
                    header_content = ""
                    for i in range(12):
                        if i < len(lines):
                            header_content += lines[i]
                    
                    # [FIRE] 從第13行開始檢查，但加入最大掃描限制
                    max_scan_to = min(len(lines), 12 + self.MAX_SCAN_LINES)
                    scan_count = 0
                    
                    for i in range(12, max_scan_to):
                        scan_count += 1
                        
                        # [FIRE] 每1000行檢查一次超時
                        if scan_count % 1000 == 0:
                            if self.start_time and (time.time() - self.start_time) > self.PROCESSING_TIMEOUT:
                                print(f"[ALARM_CLOCK] 處理超時，停止掃描 {filename}")
                                break
                        
                        line = lines[i].strip()
                        if len(line) < 1:
                            break
                        
                        elements = line.split(',')
                        if len(elements) > 1:
                            try:
                                if int(elements[1]) == 1:  # 找到 BIN=1
                                    # 替換第1欄為 Golden IC 識別碼
                                    elements[0] = "9876543210"
                                    modified_line = ",".join(elements)
                                    golden_content = header_content + modified_line + "\n"
                                    print(f"[OK] 找到 EQC BIN=1 資料: {filename} (第{i+1}行，掃描了{scan_count}行)")
                                    progress_monitor.record_success()
                                    progress_monitor.show_summary()
                                    return golden_content
                            except (ValueError, IndexError):
                                continue
                    
                    print(f"[BOARD] {filename}: 已掃描 {scan_count} 行，未找到 BIN=1")
                    progress_monitor.record_failure()
                    
                except Exception as e:
                    print(f"[ERROR] 讀取檔案失敗 {filename}: {e}")
                    progress_monitor.record_failure()
                    continue
        
        print("[ERROR] 沒有找到 EQC BIN=1 資料")
        progress_monitor.show_summary()
        return ""


    def fill_eqc_bin1_statistics(self, content_lines: List[str], 
                                online_eqc_fail_count: int, eqc_rt_pass_count: int) -> List[str]:
        """
        在 EQC BIN=1 內容中填入統計資料
        對應 3.2 文檔的統計填入功能
        """
        lines = content_lines.copy()
        
        # 確保有足夠行數，但不添加額外空行
        while len(lines) < 10:
            lines.append('')
        
        # 第9行處理 (索引8) - 只替換A欄和B欄，C欄之後保持原樣
        line9_elements = lines[8].strip().split(',') if len(lines) > 8 and lines[8].strip() else []
        # 確保有足夠的欄位
        while len(line9_elements) < 2:
            line9_elements.append("")
        
        line9_elements[0] = "OnlineEQC_Fail:"  # A9
        line9_elements[1] = str(online_eqc_fail_count)                 # B9
        # C欄之後保持原樣
        lines[8] = ','.join(line9_elements)
        
        # 第10行處理 (索引9) - 只替換A欄和B欄，C欄之後保持原樣
        line10_elements = lines[9].strip().split(',') if len(lines) > 9 and lines[9].strip() else []
        # 確保有足夠的欄位
        while len(line10_elements) < 2:
            line10_elements.append("")
        
        line10_elements[0] = "EQC_RT_FINAL_PASS:"  # A10
        line10_elements[1] = str(eqc_rt_pass_count)                    # B10
        # C欄之後保持原樣
        lines[9] = ','.join(line10_elements)
        
        return lines

    def _process_eqc_rt_files_sorted(self, eqc_rt_files: List[str]) -> List[str]:
        """
        按時間排序處理 EQC RT 檔案
        遵循 CLAUDE.md 功能替換原則，替換原有的無序處理邏輯
        """
        eqc_rt_data_lines = []
        
        if not eqc_rt_files:
            return eqc_rt_data_lines
        
        # [FIRE] 已移[EXCEPT_CHAR]檔案數量限制，加入智能進度監控
        total_files = len(eqc_rt_files)
        print(f"[CHART] EQC RT 檔案總數: {total_files}")
        
        # 建立進度監控器
        progress_monitor = ProgressMonitor(self.start_time, self.PROCESSING_TIMEOUT)
        
        # 步驟1: 為每個檔案提取時間戳
        files_with_timestamps = []
        for file_index, eqc_rt_file in enumerate(eqc_rt_files):
            # [FIRE] 檢查處理超時
            if self.start_time and (time.time() - self.start_time) > self.PROCESSING_TIMEOUT:
                print(f"[ALARM_CLOCK] 處理超時，停止 EQC RT 檔案處理")
                break
                
            timestamp = self._extract_internal_timestamp(eqc_rt_file)
            if timestamp:
                files_with_timestamps.append((eqc_rt_file, timestamp))
                readable_time = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
                print(f"   [PAGE_FACING_UP] {file_index+1}/{len(eqc_rt_files)}: {os.path.basename(eqc_rt_file)}: {readable_time}")
            else:
                # 如果無法提取內部時間戳，使用檔案修改時間作為備用
                try:
                    file_mtime = int(os.path.getmtime(eqc_rt_file))
                    files_with_timestamps.append((eqc_rt_file, file_mtime))
                    readable_time = datetime.fromtimestamp(file_mtime).strftime("%Y-%m-%d %H:%M:%S")
                    print(f"   [PAGE_FACING_UP] {file_index+1}/{len(eqc_rt_files)}: {os.path.basename(eqc_rt_file)}: {readable_time} (檔案修改時間)")
                except Exception:
                    print(f"   [ERROR] {os.path.basename(eqc_rt_file)}: 無法提取時間戳")
                    continue
        
        # 步驟2: 按時間戳排序 (早→晚)
        files_with_timestamps.sort(key=lambda x: x[1])
        
        print(f"[REFRESH] EQC RT 檔案已按時間排序 (早→晚):")
        for i, (file_path, timestamp) in enumerate(files_with_timestamps, 1):
            readable_time = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")
            print(f"   {i}. {os.path.basename(file_path)} - {readable_time}")
        
        # 步驟3: 按排序後順序處理檔案
        for eqc_rt_file, _ in files_with_timestamps:
            try:
                with open(eqc_rt_file, 'r', encoding='utf-8') as f:
                    rt_lines = f.readlines()
                
                # 從第13行開始加入資料行
                for i in range(self.data_start_row, len(rt_lines)):
                    line = rt_lines[i].strip()
                    if not line:
                        break
                    
                    # 添加超連結
                    elements = line.split(',')
                    elements_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                        elements, eqc_rt_file, 2
                    )
                    eqc_rt_data_lines.append(','.join(elements_with_hyperlink))
                    
            except Exception as e:
                print(f"[ERROR] 處理 EQC RT 檔案失敗 {os.path.basename(eqc_rt_file)}: {e}")
        
        print(f"[BOARD] EQC RT 資料處理完成: 總共 {len(eqc_rt_data_lines)} 行資料")
        return eqc_rt_data_lines



    def _extract_internal_timestamp(self, file_path: str) -> Optional[int]:
        """
        提取檔案內部時間戳 - 完全參考 ft_eqc_grouping_processor.py 的實作方式
        優先使用檔案內部第6行的Date資訊，備用檔案名稱時間戳
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            # 檢查第6行 (Date行) - 與參考檔案完全一致
            if len(lines) > 5:
                date_line = lines[5]  # 第6行 (索引5)
                if "Date:" in date_line:
                    # 提取時間字串並轉換為時間戳
                    parts = date_line.split(',')
                    if len(parts) > 1:
                        date_str = parts[1].strip()
                        try:
                            # 修復2位年份解析問題 - 使用50年分界點
                            # 格式為 "05/22/25 18:44:13" 或 "07/22/24 00:00:52"
                            dt = datetime.strptime(date_str, "%m/%d/%y %H:%M:%S")
                            
                            # 修正年份邏輯：2位年份智能判斷
                            # 0-49 -> 2000-2049, 50-99 -> 1950-1999
                            if dt.year < 1970:  # Unix timestamp開始年份
                                # 如果解析出的年份小於1970，調整到正確的21世紀年份
                                corrected_year = dt.year + 100
                                dt = dt.replace(year=corrected_year)
                            
                            return int(dt.timestamp())
                        except ValueError:
                            # 如果日期格式解析失敗，嘗試其他常見格式
                            try:
                                dt = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                                return int(dt.timestamp())
                            except ValueError:
                                pass
            
            # 備用：使用檔案名稱時間戳 - 與參考檔案完全一致
            filename = os.path.splitext(os.path.basename(file_path))[0]
            if len(filename) >= 14:  # 檢查是否有時間戳
                timestamp_str = filename[-14:]  # 最後14位數字
                if timestamp_str.isdigit():
                    return int(timestamp_str)
            
            # 最後備用：檢查8位數字時間戳
            if len(filename) >= 8:
                timestamp_str = filename[-8:]  # 最後8位數字
                if timestamp_str.isdigit():
                    return int(timestamp_str)
            
            return None
            
        except Exception as e:
            # 在[EXCEPT_CHAR]錯模式下顯示詳細錯誤
            # print(f"   [EXCEPT_CHAR]錯: 時間戳提取失敗 {os.path.basename(file_path)}: {e}")
            return None

    def generate_ft_eqc_fail_data_with_hyperlinks(self, matched_pairs: List[Tuple[str, str]]) -> List[str]:
        """
        生成帶超連結的 FT-EQC 失敗配對資料
        結合 3.1 配對機制和超連結功能
        """
        fail_data_lines = []
        
        print("[REFRESH] 處理 FT-EQC 配對失敗資料 (含超連結):")
        for ft_file, eqc_file in matched_pairs:
            try:
                # 檢查 EQC 檔案是否有失敗資料
                fail_rows = []
                with open(eqc_file, 'r', encoding='utf-8') as f:
                    eqc_lines = f.readlines()
                
                # 從第13行開始檢查失敗行
                for i in range(self.data_start_row, len(eqc_lines)):
                    line = eqc_lines[i].strip()
                    if not line:
                        break
                    
                    elements = line.split(',')
                    if len(elements) >= 2:
                        try:
                            if int(elements[1]) != 1:  # BIN != 1 表示失敗
                                fail_rows.append(elements)
                        except (ValueError, IndexError):
                            continue
                
                if fail_rows:
                    print(f"   [PAGE_FACING_UP] {os.path.basename(eqc_file)}: {len(fail_rows)} 個失敗行")
                    
                    # 如果有對應的 FT 檔案，嘗試找到匹配的 FT 資料
                    if ft_file and os.path.exists(ft_file):
                        with open(ft_file, 'r', encoding='utf-8') as f:
                            ft_lines = f.readlines()
                        
                        # 為每個失敗行嘗試找到對應的 FT 資料
                        for fail_row in fail_rows:
                            # 簡化匹配邏輯：基於序號匹配
                            fail_serial = fail_row[0] if fail_row else ""
                            
                            # 找對應的 FT 行
                            for i in range(self.data_start_row, len(ft_lines)):
                                ft_line = ft_lines[i].strip()
                                if not ft_line:
                                    break
                                
                                ft_elements = ft_line.split(',')
                                if len(ft_elements) > 0 and ft_elements[0] == fail_serial:
                                    # 找到匹配的 FT 行，添加超連結
                                    ft_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                                        ft_elements, ft_file, 2
                                    )
                                    fail_data_lines.append(','.join(ft_with_hyperlink))
                                    break
                            
                            # 加入 EQC 失敗行，添加超連結
                            eqc_with_hyperlink = self.hyperlink_processor.add_hyperlink_to_data(
                                fail_row, eqc_file, 2
                            )
                            fail_data_lines.append(','.join(eqc_with_hyperlink))
                
            except Exception as e:
                print(f"   [ERROR] 處理失敗 {os.path.basename(eqc_file)}: {e}")
        
        return fail_data_lines


    def delete_files_by_extensions(self, folder_path: str) -> int:
        """
        根據環境變數DELETE_FILE_EXTENSIONS刪[EXCEPT_CHAR]指定副檔名的檔案
        功能替換原則：在檔案整合前自動清理不需要的檔案
        
        Returns:
            int: 成功刪[EXCEPT_CHAR]的檔案數量
        """
        import os
        from dotenv import load_dotenv
        
        load_dotenv()
        deleted_count = 0
        
        # 讀取環境變數
        delete_extensions = os.getenv('DELETE_FILE_EXTENSIONS', '')
        if not delete_extensions.strip():
            return 0
            
        # 解析副檔名列表，轉為小寫並移[EXCEPT_CHAR]空格
        extensions_to_delete = [ext.strip().lower() for ext in delete_extensions.split(',') if ext.strip()]
        
        if not extensions_to_delete:
            return 0
            
        print(f"[WASTEBASKET] 自動刪[EXCEPT_CHAR]副檔名: {', '.join(extensions_to_delete)}")
        
        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_name, file_ext = os.path.splitext(file)
                    
                    # 檢查副檔名是否在刪[EXCEPT_CHAR]清單中（不區分大小寫）
                    if file_ext.lower().lstrip('.') in extensions_to_delete:
                        try:
                            os.remove(file_path)
                            print(f"   [WASTEBASKET] 已刪[EXCEPT_CHAR]: {file}")
                            deleted_count += 1
                        except Exception as e:
                            print(f"   [WARNING] 刪[EXCEPT_CHAR]失敗: {file} - {e}")
                        
        except Exception as e:
            print(f"[ERROR] 檔案刪[EXCEPT_CHAR]過程中發生錯誤: {e}")
            
        if deleted_count > 0:
            print(f"[OK] 成功刪[EXCEPT_CHAR] {deleted_count} 個指定副檔名的檔案")
        else:
            print("[INFO] 未發現需要刪[EXCEPT_CHAR]的指定副檔名檔案")
            
        return deleted_count

    def convert_spd_files_to_csv(self, folder_path: str) -> int:
        """
        將目錄下所有 .spd 檔案（不區分大小寫）重命名為 .csv（小寫）
        功能替換原則：在檔案整合前自動標準化檔案副檔名
        
        Returns:
            int: 成功轉換的檔案數量
        """
        converted_count = 0
        
        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_name, file_ext = os.path.splitext(file)
                    
                    # 檢查副檔名是否為 .spd（不區分大小寫）
                    if file_ext.lower() == '.spd':
                        # 新的檔案路徑，副檔名改為 .csv（小寫）
                        new_file_path = os.path.join(root, file_name + '.csv')
                        
                        # 重命名檔案
                        os.rename(file_path, new_file_path)
                        print(f"   [NOTES] 轉換: {file} → {file_name}.csv")
                        converted_count += 1
                        
        except Exception as e:
            print(f"[ERROR] SPD檔案轉換過程中發生錯誤: {e}")
            
        if converted_count > 0:
            print(f"[OK] 成功轉換 {converted_count} 個 SPD 檔案為 CSV 格式")
        else:
            print("[INFO] 未發現需要轉換的 SPD 檔案")
            
        return converted_count

    def _calculate_statistics_from_pairs(self, grouping_result: 'GroupingResult') -> Tuple[int, int]:
        """
        直接基於配對結果計算統計數據
        Online EQC FAIL = 配對檔案中的失敗數量
        EQC RT PASS = 未配對檔案中的通過數量
        """
        online_eqc_fail_count = 0
        eqc_rt_pass_count = 0
        
        # 統計 Online EQC FAIL 數量（配對檔案中的失敗）
        print("[SEARCH] 計算 Online EQC FAIL 統計:")
        self.debug_logger.log_section("Online EQC FAIL 統計 (配對檔案)")
        
        for ft_file, eqc_file in grouping_result.matched_pairs:
            filename = os.path.basename(eqc_file)
            try:
                with open(eqc_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                file_fail_count = 0
                file_pass_count = 0
                # 從第13行開始檢查 (索引12)
                for i in range(self.data_start_row, len(lines)):
                    line = lines[i].strip()
                    if len(line) < 1:
                        break
                    
                    elements = line.split(',')
                    if len(elements) > 1:
                        try:
                            # 檢查 BIN# (第2欄) 
                            if int(elements[1]) != 1:
                                file_fail_count += 1
                            else:
                                file_pass_count += 1
                        except (ValueError, IndexError):
                            continue
                
                online_eqc_fail_count += file_fail_count
                total_count = file_pass_count + file_fail_count
                
                # 記錄到詳細日誌
                self.debug_logger.log_statistics("ONLINE EQC", eqc_file, file_pass_count, file_fail_count, total_count)
                
                if file_fail_count > 0:
                    print(f"   [PAGE_FACING_UP] {filename}: {file_fail_count} 個 FAIL")
                
            except Exception as e:
                print(f"   [ERROR] {filename}: 讀取失敗 - {e}")
        
        # 統計 EQC RT PASS 數量（未配對檔案中的通過）
        print("[SEARCH] 計算 EQC RT PASS 統計:")
        self.debug_logger.log_section("EQC RT PASS 統計 (未配對檔案)")
        
        for eqc_file in grouping_result.unmatched_eqc:
            filename = os.path.basename(eqc_file)
            try:
                with open(eqc_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                file_pass_count = 0
                file_fail_count = 0
                # 從第13行開始檢查 (索引12)
                for i in range(self.data_start_row, len(lines)):
                    line = lines[i].strip()
                    if len(line) < 1:
                        break
                    
                    elements = line.split(',')
                    if len(elements) > 1:
                        try:
                            # 檢查 BIN# (第2欄)
                            if int(elements[1]) == 1:
                                file_pass_count += 1
                            else:
                                file_fail_count += 1
                        except (ValueError, IndexError):
                            continue
                
                eqc_rt_pass_count += file_pass_count
                total_count = file_pass_count + file_fail_count
                
                # 記錄到詳細日誌
                self.debug_logger.log_statistics("EQC RT", eqc_file, file_pass_count, file_fail_count, total_count)
                
                timestamp = self._extract_internal_timestamp(eqc_file)
                readable_time = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S") if timestamp and timestamp > 0 else "無效時間"
                print(f"   [PAGE_FACING_UP] {filename}: {file_pass_count} 個 PASS ({readable_time})")
                
            except Exception as e:
                print(f"   [ERROR] {filename}: 讀取失敗 - {e}")
        
        print(f"\n[CHART] 統計結果:")
        print(f"   [RED_CIRCLE] Online EQC FAIL: {online_eqc_fail_count} 個")
        print(f"   [GREEN_CIRCLE] EQC RT PASS: {eqc_rt_pass_count} 個")
        
        return online_eqc_fail_count, eqc_rt_pass_count

    def process_complete_eqc_integration(self, folder_path: str, enable_debug_log: bool = True) -> Tuple[Optional[str], Optional[str]]:
        """
        完整的 EQC 整合處理流程 - 直接生成 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv
        遵循 CLAUDE.md 功能替換原則，整合所有功能

        Args:
            folder_path: 處理的資料夾路徑
            enable_debug_log: 是否啟用 DEBUG LOG 生成 (預設: True)
        """
        # [FIRE] 設定處理開始時間，用於超時檢測
        self.start_time = time.time()
        
        print(f"[FIRE] 開始 EQC BIN1 完整整合處理系統（超時限制: {self.PROCESSING_TIMEOUT}秒）")
        print("=" * 60)

        # 動態建立 HyperlinkProcessor，傳入當前處理的資料夾路徑
        self.hyperlink_processor = HyperlinkProcessor(folder_path)
        print(f"[LINK] 超連結處理器已初始化，本地路徑: {folder_path}")
        print(f"[GLOBE_WITH_MERIDIANS] 網路共享路徑: {self.hyperlink_processor.net_path}")

        # 動態建立 EQCDebugLogger，記錄詳細處理過程 (可控制是否啟用)
        self.debug_logger = EQCDebugLogger(folder_path, enabled=enable_debug_log)
        if self.debug_logger.enabled:
            print(f"[BOARD] 詳細日誌記錄已啟用")
        elif not enable_debug_log:
            print(f"[BOARD] 詳細日誌記錄已停用 (enable_debug_log=False)")
        print()
        
        # 步驟0A: 特定副檔名檔案自動刪[EXCEPT_CHAR]（功能替換原則：檔案清理前置步驟）
        print("[WASTEBASKET] 步驟0A: 特定副檔名檔案自動刪[EXCEPT_CHAR]")
        deleted_count = self.delete_files_by_extensions(folder_path)
        if deleted_count > 0:
            print(f"   [FOLDER_TABS] 已刪[EXCEPT_CHAR] {deleted_count} 個指定副檔名的檔案")
        print()
        
        # 步驟0B: SPD檔案自動轉換為CSV（功能替換原則：檔案標準化前置步驟）
        print("[REFRESH] 步驟0B: SPD檔案自動轉換")
        spd_converted_count = self.convert_spd_files_to_csv(folder_path)
        if spd_converted_count > 0:
            print(f"   [FILE_FOLDER] 已將 {spd_converted_count} 個 SPD 檔案轉換為 CSV 格式")
        print()
        
        # 步驟1: 執行 FT-EQC 配對處理
        ft_eqc_processor = FTEQCGroupingProcessor()
        grouping_result = ft_eqc_processor.process_folder(folder_path)
        
        print(f"[CHART] FT-EQC 配對結果:")
        print(f"   [LINK] 成功配對: {len(grouping_result.matched_pairs)} 對")
        print(f"   [PAGE_FACING_UP] 未配對 EQC: {len(grouping_result.unmatched_eqc)} 個")
        
        # 記錄配對結果到詳細日誌
        self.debug_logger.log_section("FT-EQC 檔案配對處理")
        
        # 記錄所有 FT 檔案
        ft_files = [ft_file for ft_file, _ in grouping_result.matched_pairs]
        self.debug_logger.log_file_scan("FT", ft_files)
        
        # 記錄所有 EQC 檔案（包括配對和未配對的）
        all_eqc_files = [eqc_file for _, eqc_file in grouping_result.matched_pairs] + grouping_result.unmatched_eqc
        self.debug_logger.log_file_scan("EQC", all_eqc_files)
        
        # 記錄配對結果
        self.debug_logger.log_section("配對結果詳細資訊")
        for ft_file, eqc_file in grouping_result.matched_pairs:
            # 記錄時間戳資訊
            ft_timestamp = self._extract_internal_timestamp(ft_file)
            eqc_timestamp = self._extract_internal_timestamp(eqc_file)
            
            ft_time = datetime.fromtimestamp(ft_timestamp).strftime("%Y-%m-%d %H:%M:%S") if ft_timestamp and ft_timestamp > 0 else "無效時間"
            eqc_time = datetime.fromtimestamp(eqc_timestamp).strftime("%Y-%m-%d %H:%M:%S") if eqc_timestamp and eqc_timestamp > 0 else "無效時間"
            
            self.debug_logger.log_file_timestamp(ft_file, ft_timestamp, ft_time)
            self.debug_logger.log_file_timestamp(eqc_file, eqc_timestamp, eqc_time)
            self.debug_logger.log_pairing_result(ft_file, eqc_file, "時間匹配")
        
        # 記錄未配對檔案 (包含內部時間戳)
        self.debug_logger.log_unmatched_files("EQC", grouping_result.unmatched_eqc, self)
        
        # [BUG] DEBUG: 詳細配對日誌
        print(f"\n[BUG] DEBUG: 詳細 FT-EQC 配對日誌")
        print("=" * 60)
        for i, (ft_file, eqc_file) in enumerate(grouping_result.matched_pairs, 1):
            ft_name = os.path.basename(ft_file)
            eqc_name = os.path.basename(eqc_file)
            print(f"   配對 {i:2d}: FT=[{ft_name}] [LEFT_RIGHT_ARROW] EQC=[{eqc_name}]")
        
        if grouping_result.unmatched_eqc:
            print(f"\n[BUG] DEBUG: 未配對的 EQC 檔案:")
            for i, eqc_file in enumerate(grouping_result.unmatched_eqc, 1):
                eqc_name = os.path.basename(eqc_file)
                print(f"   未配對 {i}: {eqc_name}")
        print("=" * 60)
        
        # 步驟2: 取得所有 EQC 檔案並按時間分類
        all_csv_files = self.file_discovery.find_all_csv_files(folder_path)
        eqc_files = self.file_discovery.classify_eqc_files(all_csv_files)
        
        # 排[EXCEPT_CHAR]已生成的檔案
        eqc_files = [f for f in eqc_files if 'EQC_BIN1_WITH_STATISTICS.csv' not in f 
                    and 'EQCTOTALDATA' not in f and '_EQCFAILDATA' not in f]
        
        # 發現 EQC 檔案總數
        total_eqc_files = len(eqc_files)
        print(f"[CHART] 發現 EQC 檔案總數: {total_eqc_files}")
        
        # 步驟3: 直接基於配對結果計算統計數據
        online_eqc_fail_count, eqc_rt_pass_count = self._calculate_statistics_from_pairs(grouping_result)
        
        # 步驟4: 找到 EQC BIN=1 golden IC
        print(f"[SEARCH] 搜尋 EQC BIN=1 資料...")
        golden_content = self.find_online_eqc_bin1_datalog(eqc_files)
        
        if not golden_content:
            print("[ERROR] 無法找到 EQC BIN=1 資料")
            return None, None
        
        # 步驟6: 填入統計資料
        content_lines = golden_content.strip().split('\n')
        content_lines = self.fill_eqc_bin1_statistics(
            content_lines, online_eqc_fail_count, eqc_rt_pass_count
        )
        
        
        # 步驟8: 生成帶超連結的 FT-EQC 失敗配對資料
        ft_eqc_fail_data = self.generate_ft_eqc_fail_data_with_hyperlinks(grouping_result.matched_pairs)
        
        # 步驟9: 添加 FT-EQC 失敗配對資料
        if ft_eqc_fail_data:
            print(f"[BOARD] 添加 {len(ft_eqc_fail_data)} 行 FT-EQC 配對失敗資料")
            content_lines.extend(ft_eqc_fail_data)
        
        # 步驟10: 添加帶超連結的 EQC RT 資料 (按時間排序)
        eqc_rt_data_lines = self._process_eqc_rt_files_sorted(grouping_result.unmatched_eqc)
        
        if eqc_rt_data_lines:
            print(f"[BOARD] 添加 {len(eqc_rt_data_lines)} 行 EQC RT 資料 (已按時間排序)")
            content_lines.extend(eqc_rt_data_lines)
        
        # 步驟11: 生成 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv
        eqc_total_file = os.path.join(folder_path, "EQCTOTALDATA.csv")
        eqc_raw_file = os.path.join(folder_path, "EQCTOTALDATA_RAW.csv")
        
        final_content = '\n'.join(content_lines) + '\n'
        
        # 生成 EQCTOTALDATA.csv
        with open(eqc_total_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        # 複製為 EQCTOTALDATA_RAW.csv
        with open(eqc_raw_file, 'w', encoding='utf-8') as f:
            f.write(final_content)
        
        print(f"\n[DISK] 整合檔案已生成:")
        print(f"   [PAGE_FACING_UP] EQCTOTALDATA.csv")
        print(f"   [PAGE_FACING_UP] EQCTOTALDATA_RAW.csv")
        print(f"[RULER] 檔案大小: {len(final_content)} 字元")
        print(f"[PAGE_FACING_UP] 總行數: {len(content_lines)} 行")
        
        print(f"\n[TARGET] 最終統計結果:")
        print(f"   [CHART] A9/B9 (Online EQC FAIL): {online_eqc_fail_count}")
        print(f"   [CHART] A10/B10 (EQC RT PASS): {eqc_rt_pass_count}")
        print(f"   [CHART] FT-EQC 配對失敗資料: {len(ft_eqc_fail_data)} 行")
        print(f"   [CHART] EQC RT 資料: {len(eqc_rt_data_lines)} 行")
        print(f"   [CHART] 配對成功: {len(grouping_result.matched_pairs)} 對")
        print(f"   [CHART] 未配對 EQC: {len(grouping_result.unmatched_eqc)} 個")
        
        # 步驟11: 顯示關鍵行預覽
        print(f"\n[NOTES] 關鍵內容預覽:")
        for i, line in enumerate(content_lines[:15], 1):
            if i == 9 or i == 10 or i >= 13:
                line_preview = line[:100] + "..." if len(line) > 100 else line
                print(f"   第{i:2d}行: {line_preview}")
        
        # 保存詳細日誌
        self.debug_logger.log_summary(online_eqc_fail_count, eqc_rt_pass_count, len(grouping_result.matched_pairs))
        self.debug_logger.save_log()
        
        # [FIRE] 計算總處理時間
        total_time = time.time() - self.start_time if self.start_time else 0
        print(f"[TIME] 總處理時間: {total_time:.1f} 秒")
        
        return eqc_total_file, eqc_raw_file

    def generate_eqc_total_data(self, folder_path: str) -> Tuple[str, str]:
        """
        生成完整的 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv 檔案
        對應完整 Online EQC 系統的選擇輸入自動產生功能
        """
        print(f"\n[TARGET] 開始生成 EQCTOTALDATA 檔案...")
        
        # 動態建立 HyperlinkProcessor 、EQCDebugLogger 和 ProgressMonitor
        self.hyperlink_processor = HyperlinkProcessor(folder_path)
        self.debug_logger = EQCDebugLogger(folder_path)
        progress_monitor = ProgressMonitor(self.start_time, self.PROCESSING_TIMEOUT)
        if self.debug_logger.enabled:
            print(f"[BOARD] 詳細日誌記錄已啟用")
        
        # 步驟1: 掃描所有 CSV 檔案
        discovery = CSVFileDiscovery()
        csv_files = discovery.find_all_csv_files(folder_path)
        
        # 記錄檔案掃描結果
        self.debug_logger.log_section("EQCTOTALDATA 完整資料生成")
        self.debug_logger.log_file_scan("ALL CSV", csv_files)
        
        # 步驟2: 收集所有測試資料
        all_test_data = []
        file_headers = {}
        
        print(f"\n[REFRESH] 收集所有測試資料...檔案數: {len(csv_files)} 個（無限制）")
        
        for file_index, csv_file in enumerate(csv_files):
            # [FIRE] 檢查超時
            if self.start_time and (time.time() - self.start_time) > self.PROCESSING_TIMEOUT:
                print(f"[ALARM_CLOCK] 處理超時，停止檔案處理")
                break
                
            filename = os.path.basename(csv_file)
            print(f"[PAGE_FACING_UP] 處理 {file_index+1}/{len(csv_files)}: {filename}")
            
            try:
                # [CHART] 檔案大小資訊
                file_size_mb = os.path.getsize(csv_file) / (1024 * 1024)
                if file_index % 10 == 0:  # 每10個檔案顯示一次大小資訊
                    print(f"   [PACKAGE] 檔案大小: {file_size_mb:.1f}MB")
                
                # [LINK] 檔案級別超連結轉換（只做一次）
                network_path = self.hyperlink_processor.convert_to_network_path(csv_file)
                hyperlink_cell = f"HYPERLINK:{network_path}"
                    
                with open(csv_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 找到標題行
                header_row = None
                data_start = self.data_start_row
                
                for i in range(min(15, len(lines))):
                    if ',' in lines[i] and any(term in lines[i].lower() for term in ['site', 'bin', 'index']):
                        header_row = lines[i].strip()
                        data_start = i + 1
                        break
                
                if header_row:
                    file_headers[csv_file] = header_row
                
                # [FIRE] 讀取資料行，但限制最大行數
                file_data_count = 0
                max_scan_to = min(len(lines), data_start + self.MAX_SCAN_LINES)
                
                for i in range(data_start, max_scan_to):
                    # 每1000行檢查一次超時
                    if file_data_count % 1000 == 0 and file_data_count > 0:
                        if self.start_time and (time.time() - self.start_time) > self.PROCESSING_TIMEOUT:
                            print(f"[ALARM_CLOCK] 處理超時，停止檔案 {filename} 的處理")
                            break
                    
                    line = lines[i].strip()
                    if not line or len(line) < 10:
                        continue
                    
                    # 在第3欄插入超連結（使用預先計算的值）
                    elements = line.split(',')
                    if len(elements) >= 3:
                        elements[2] = hyperlink_cell
                        enhanced_line = ','.join(elements)
                        all_test_data.append(enhanced_line)
                        file_data_count += 1
                
                print(f"   [OK] {filename}: {file_data_count} 筆測試資料")
                progress_monitor.record_success()
                
            except Exception as e:
                print(f"   [ERROR] {filename}: 讀取失敗 - {e}")
                progress_monitor.record_failure()
        
        # 步驟3: 建立統一標題行
        if file_headers:
            # 使用第一個有效標題作為基準
            unified_header = list(file_headers.values())[0]
            print(f"[NOTES] 使用統一標題: {len(unified_header.split(','))} 欄")
        else:
            # 預設標題
            unified_header = "Index_Time,Site_No,Hyperlink,Bin#,測試項目1,測試項目2,測試項目3"
            print(f"[NOTES] 使用預設標題")
        
        # 步驟4: 生成 EQCTOTALDATA.csv
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        eqc_total_file = os.path.join(folder_path, f"EQCTOTALDATA.csv")
        eqc_raw_file = os.path.join(folder_path, f"EQCTOTALDATA_RAW.csv")
        
        # 組合完整內容
        total_content = [unified_header] + all_test_data
        
        # 寫入 EQCTOTALDATA.csv
        with open(eqc_total_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(total_content) + '\n')
        
        # 寫入 EQCTOTALDATA_RAW.csv (直接複製)
        with open(eqc_raw_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(total_content) + '\n')
        
        print(f"\n[DISK] EQCTOTALDATA 檔案已生成:")
        print(f"   [PAGE_FACING_UP] EQCTOTALDATA.csv: {len(total_content)} 行")
        print(f"   [PAGE_FACING_UP] EQCTOTALDATA_RAW.csv: {len(total_content)} 行 (相同內容)")
        print(f"   [CHART] 測試資料總數: {len(all_test_data)} 筆")
        print(f"   [FILE_FOLDER] 處理檔案數: {len(csv_files)} 個")
        
        # 保存詳細日誌（完整資料生成模式，不涉及配對統計）
        self.debug_logger.log_summary(0, 0, 0)  # 該模式不涉及配對統計
        self.debug_logger.save_log()
        
        return eqc_total_file, eqc_raw_file


def main():
    """
    主函數 - Online EQC 完整處理系統
    支援選擇輸入，自動產生 EQC BIN1 統計或完整 EQCTOTALDATA
    """
    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
    else:
        # 使用環境變數或當前目錄作為預設
        folder_path = os.getenv('DEFAULT_DATA_FOLDER', '.')
    
    print(f"[TARGET] Online EQC 完整處理系統")
    print(f"[FILE_FOLDER] 處理資料夾: {folder_path}")
    print(f"\n[BOARD] 處理選項:")
    print(f"   1. EQC BIN1 整合統計處理 (含超連結、FT-EQC配對)")
    print(f"   2. 完整 EQCTOTALDATA.csv 生成 (所有測試資料)")
    print(f"   3. 同時執行兩種處理")
    
    try:
        choice = input(f"\n請選擇處理模式 (1/2/3，預設為1): ").strip()
        if not choice:
            choice = "1"
    except:
        choice = "1"
    
    processor = EQCBin1FinalProcessor()
    
    if choice in ["1", "3"]:
        print(f"\n[REFRESH] 執行 EQC BIN1 整合統計處理...")
        total_file, raw_file = processor.process_complete_eqc_integration(folder_path)
        
        if total_file and raw_file:
            print(f"[OK] EQC BIN1 處理完成！")
            print(f"   [PAGE_FACING_UP] {os.path.basename(total_file)}")
            print(f"   [PAGE_FACING_UP] {os.path.basename(raw_file)}")
        else:
            print("[ERROR] EQC BIN1 處理失敗")
    
    if choice in ["2", "3"]:
        print(f"\n[REFRESH] 執行完整 EQCTOTALDATA 生成...")
        try:
            total_file, raw_file = processor.generate_eqc_total_data(folder_path)
            print(f"[OK] EQCTOTALDATA 生成完成！")
            print(f"   [PAGE_FACING_UP] {os.path.basename(total_file)}")
            print(f"   [PAGE_FACING_UP] {os.path.basename(raw_file)}")
        except Exception as e:
            print(f"[ERROR] EQCTOTALDATA 生成失敗: {e}")
    
    if choice not in ["1", "2", "3"]:
        print(f"[WARNING]  無效選擇，執行預設 EQC BIN1 處理...")
        total_file, raw_file = processor.process_complete_eqc_integration(folder_path)
        
        if total_file and raw_file:
            print(f"[OK] 處理完成！")
            print(f"   [PAGE_FACING_UP] {os.path.basename(total_file)}")
            print(f"   [PAGE_FACING_UP] {os.path.basename(raw_file)}")
        else:
            print("[ERROR] 處理失敗")


if __name__ == "__main__":
    main()