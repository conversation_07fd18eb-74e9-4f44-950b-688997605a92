"""
SMTP 郵件發送器
提供 SMTP 郵件發送功能
"""

import smtplib
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication
from email.mime.base import MIMEBase
from email import encoders
from pathlib import Path
import os
from datetime import datetime

from src.infrastructure.logging.logger_manager import LoggerManager


@dataclass
class SMTPConfig:
    """SMTP 配置"""
    server: str
    port: int
    username: str
    password: str
    use_auth: bool = True
    use_tls: bool = False
    use_ssl: bool = False
    timeout: int = 30


@dataclass
class EmailMessage:
    """郵件訊息"""
    to: List[str]
    subject: str
    body: str
    cc: Optional[List[str]] = None
    bcc: Optional[List[str]] = None
    attachments: Optional[List[str]] = None
    html_body: Optional[str] = None
    reply_to: Optional[str] = None


class SMTPSender:
    """
    SMTP 郵件發送器
    提供郵件發送功能
    """
    
    def __init__(self, config: SMTPConfig):
        """
        初始化 SMTP 發送器
        
        Args:
            config: SMTP 配置
        """
        self.config = config
        self.logger = LoggerManager().get_logger("SMTPSender")
        
        # 統計資訊
        self.stats = {
            'total_sent': 0,
            'total_failed': 0,
            'last_send_time': None,
            'errors': []
        }
    
    def send_email(self, message: EmailMessage) -> bool:
        """
        發送郵件
        
        Args:
            message: 郵件訊息
            
        Returns:
            發送成功與否
        """
        try:
            self.logger.info(f"正在發送郵件: {message.subject}")
            
            # 建立 SMTP 連接
            with self._create_smtp_connection() as smtp:
                # 建立郵件
                mime_message = self._create_mime_message(message)
                
                # 取得所有收件人
                recipients = message.to.copy()
                if message.cc:
                    recipients.extend(message.cc)
                if message.bcc:
                    recipients.extend(message.bcc)
                
                # 發送郵件
                smtp.sendmail(
                    from_addr=self.config.username,
                    to_addrs=recipients,
                    msg=mime_message.as_string()
                )
                
                self.stats['total_sent'] += 1
                self.stats['last_send_time'] = datetime.now()
                self.logger.info(f"郵件發送成功: {message.subject}")
                return True
                
        except Exception as e:
            self.stats['total_failed'] += 1
            self.stats['errors'].append({
                'time': datetime.now(),
                'subject': message.subject,
                'error': str(e)
            })
            self.logger.error(f"郵件發送失敗: {e}")
            return False
    
    def send_test_email(self, to_address: str, subject: str = "測試郵件") -> bool:
        """
        發送測試郵件
        
        Args:
            to_address: 收件人地址
            subject: 郵件主旨
            
        Returns:
            發送成功與否
        """
        test_message = EmailMessage(
            to=[to_address],
            subject=subject,
            body=f"""這是一封測試郵件
            
發送時間: {datetime.now()}
發送者: {self.config.username}
伺服器: {self.config.server}:{self.config.port}

如果您收到這封郵件，表示 SMTP 設定正常運作。
""",
            html_body=f"""
<html>
<body>
    <h2>測試郵件</h2>
    <p>這是一封測試郵件</p>
    <ul>
        <li>發送時間: {datetime.now()}</li>
        <li>發送者: {self.config.username}</li>
        <li>伺服器: {self.config.server}:{self.config.port}</li>
    </ul>
    <p>如果您收到這封郵件，表示 SMTP 設定正常運作。</p>
</body>
</html>
"""
        )
        
        return self.send_email(test_message)
    
    def _create_smtp_connection(self):
        """
        建立 SMTP 連接
        
        Returns:
            SMTP 連接物件
        """
        try:
            # 根據配置建立連接
            if self.config.use_ssl:
                smtp = smtplib.SMTP_SSL(
                    self.config.server,
                    self.config.port,
                    timeout=self.config.timeout
                )
            else:
                smtp = smtplib.SMTP(
                    self.config.server,
                    self.config.port,
                    timeout=self.config.timeout
                )
                
                # 如果需要 TLS
                if self.config.use_tls:
                    smtp.starttls()
            
            # 認證
            if self.config.use_auth:
                smtp.login(self.config.username, self.config.password)
            
            return smtp
            
        except Exception as e:
            self.logger.error(f"建立 SMTP 連接失敗: {e}")
            raise
    
    def _create_mime_message(self, message: EmailMessage) -> MIMEMultipart:
        """
        建立 MIME 郵件訊息
        
        Args:
            message: 郵件訊息
            
        Returns:
            MIME 郵件訊息
        """
        # 建立多部分郵件
        mime_message = MIMEMultipart('alternative')
        
        # 設定標頭
        mime_message['From'] = self.config.username
        mime_message['To'] = ', '.join(message.to)
        mime_message['Subject'] = message.subject
        
        if message.cc:
            mime_message['Cc'] = ', '.join(message.cc)
        
        if message.reply_to:
            mime_message['Reply-To'] = message.reply_to
        
        # 添加文字內容
        text_part = MIMEText(message.body, 'plain', 'utf-8')
        mime_message.attach(text_part)
        
        # 添加 HTML 內容
        if message.html_body:
            html_part = MIMEText(message.html_body, 'html', 'utf-8')
            mime_message.attach(html_part)
        
        # 添加附件
        if message.attachments:
            for attachment_path in message.attachments:
                try:
                    self._add_attachment(mime_message, attachment_path)
                except Exception as e:
                    self.logger.warning(f"添加附件失敗: {attachment_path}, 錯誤: {e}")
        
        return mime_message
    
    def _add_attachment(self, mime_message: MIMEMultipart, attachment_path: str):
        """
        添加附件到郵件
        
        Args:
            mime_message: MIME 郵件訊息
            attachment_path: 附件路徑
        """
        file_path = Path(attachment_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"附件檔案不存在: {attachment_path}")
        
        with open(file_path, 'rb') as f:
            # 根據檔案類型選擇適當的 MIME 類型
            filename = file_path.name
            
            # 常見檔案類型對應
            if filename.lower().endswith(('.txt', '.log')):
                attachment = MIMEText(f.read().decode('utf-8', errors='ignore'))
            elif filename.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                attachment = MIMEApplication(f.read(), _subtype='octet-stream')
            elif filename.lower().endswith('.pdf'):
                attachment = MIMEApplication(f.read(), _subtype='pdf')
            else:
                attachment = MIMEApplication(f.read(), _subtype='octet-stream')
            
            attachment.add_header('Content-Disposition', 'attachment', filename=filename)
            mime_message.attach(attachment)
    
    def test_connection(self) -> Dict[str, Any]:
        """
        測試 SMTP 連接
        
        Returns:
            測試結果
        """
        test_result = {
            'success': False,
            'message': '',
            'server_info': {},
            'error': None
        }
        
        try:
            with self._create_smtp_connection() as smtp:
                # 測試 NOOP 指令
                status = smtp.noop()
                
                test_result['success'] = True
                test_result['message'] = 'SMTP 連接測試成功'
                test_result['server_info'] = {
                    'server': self.config.server,
                    'port': self.config.port,
                    'username': self.config.username,
                    'use_auth': self.config.use_auth,
                    'use_tls': self.config.use_tls,
                    'use_ssl': self.config.use_ssl,
                    'noop_response': status
                }
                
        except Exception as e:
            test_result['error'] = str(e)
            test_result['message'] = f'SMTP 連接測試失敗: {e}'
        
        return test_result
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        取得統計資訊
        
        Returns:
            統計資訊
        """
        return {
            **self.stats,
            'config': {
                'server': self.config.server,
                'port': self.config.port,
                'username': self.config.username,
                'use_auth': self.config.use_auth,
                'use_tls': self.config.use_tls,
                'use_ssl': self.config.use_ssl
            }
        }
    
    def clear_statistics(self):
        """清[EXCEPT_CHAR]統計資訊"""
        self.stats = {
            'total_sent': 0,
            'total_failed': 0,
            'last_send_time': None,
            'errors': []
        }