<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>郵件收件夾</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/email_inbox.css') }}?v={{ range(1000, 9999) | random }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/base.css') }}">
</head>
<body>
    <div class="inbox-container">
        <!-- 頂部導航 -->
        <header class="inbox-header">
            <div class="header-left">
                <h1>郵件收件夾</h1>
                <div class="stats-summary">
                    <span class="stat-item">總郵件: <span id="total-emails">{{ statistics.total_emails or 0 }}</span></span>
                    <span class="stat-item">未讀: <span id="unread-emails">{{ statistics.unread_emails or 0 }}</span></span>
                    <span class="stat-item">寄件者: <span id="total-senders">{{ statistics.total_senders or 0 }}</span></span>
                </div>
            </div>
            <div class="header-right">
                <button id="sync-btn" class="btn btn-primary">
                    <span class="btn-icon">↻</span>
                    <span class="btn-text">同步郵件</span>
                </button>
                <button id="auto-sync-btn" class="btn btn-secondary">
                    <span class="btn-icon">⏱</span>
                    <span class="btn-text">自動同步</span>
                </button>
                <button id="batch-parse-btn" class="btn btn-secondary" onclick="window.emailParserUI.autoParseAllPending()">
                    <span class="btn-icon">🔎</span>
                    <span class="btn-text">批次解析</span>
                </button>
                <button id="batch-process-btn" class="btn btn-primary" onclick="window.emailParserUI.batchProcessFiles()">
                    <span class="btn-icon">📁</span>
                    <span class="btn-text">處理</span>
                </button>
                <a href="/ft-eqc" class="btn btn-primary" target="_blank">
                    <span class="btn-icon">►</span>
                    <span class="btn-text">FT-EQC 處理</span>
                </a>
                <a href="/database-manager" class="btn btn-secondary">
                    <span class="btn-icon">DB</span>
                    <span class="btn-text">資料庫管理</span>
                </a>
                <a href="javascript:void(0)" id="ft-summary-link" class="btn btn-primary" target="_blank">
                    <span class="btn-icon">📈</span>
                    <span class="btn-text">FT-Summary UI</span>
                </a>
            </div>
        </header>

        <!-- 同步狀態提示 -->
        <div id="sync-status" class="sync-status hidden">
            <div class="status-content">
                <span class="status-icon">⌛</span>
                <span class="status-text">正在同步郵件...</span>
                <div class="status-progress">
                    <div class="progress-bar"></div>
                </div>
            </div>
        </div>


        <!-- 搜尋和篩選 -->
        <div class="search-filter-bar">
            <div class="search-container">
                <input type="text" id="search-input" placeholder="搜尋郵件主旨、內容或寄件者..." />
                <button id="search-btn" class="btn btn-search">🔎</button>
            </div>
            <div class="filter-container">
                <select id="sort-select">
                    <option value="received_time_desc">時間 (新到舊)</option>
                    <option value="received_time_asc">時間 (舊到新)</option>
                    <option value="sender_asc">寄件者 (A-Z)</option>
                    <option value="subject_asc">主旨 (A-Z)</option>
                </select>
                <label class="checkbox-label">
                    <input type="checkbox" id="unread-only" />
                    <span>只顯示未讀</span>
                </label>
            </div>
        </div>

        <!-- 郵件列表 -->
        <div class="email-list-container">
            <div class="email-list-header">
                <div class="header-cell checkbox-cell">
                    <input type="checkbox" id="select-all" />
                </div>
                <div class="header-cell sender-cell">寄件者</div>
                <div class="header-cell subject-cell">主旨</div>
                <div class="header-cell time-cell">時間</div>
                <div class="header-cell attachment-cell">附件</div>
                <div class="header-cell actions-cell">操作</div>
            </div>
            
            <div id="email-list" class="email-list">
                <!-- 郵件列表會通過 JavaScript 動態載入 -->
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <p>載入郵件中...</p>
                </div>
            </div>
        </div>

        <!-- 郵件詳情預覽 -->
        <div id="email-detail" class="email-detail hidden">
            <div class="detail-header">
                <h3 id="detail-subject">郵件主旨</h3>
                <button id="close-detail" class="btn btn-close">×</button>
            </div>
            <div class="detail-meta">
                <div class="meta-item">
                    <label>寄件者:</label>
                    <span id="detail-sender">寄件者</span>
                </div>
                <div class="meta-item">
                    <label>時間:</label>
                    <span id="detail-time">時間</span>
                </div>
                <div class="meta-item">
                    <label>附件:</label>
                    <span id="detail-attachments">附件列表</span>
                </div>
            </div>
            <div class="detail-content">
                <div id="detail-body" class="email-body">
                    郵件內容
                </div>
            </div>
        </div>

        <!-- 批量操作面板 -->
        <div id="batch-actions" class="batch-actions hidden">
            <div class="batch-info">
                已選取 <span id="selected-count">0</span> 封郵件
            </div>
            <div class="batch-buttons">
                <button id="batch-mark-read" class="btn btn-secondary">標記已讀</button>
                <button id="batch-delete" class="btn btn-danger">刪除</button>
                <button id="batch-process" class="btn btn-primary">處理</button>
            </div>
        </div>

        <!-- 分頁控制 -->
        <div class="pagination-container">
            <div class="pagination-info">
                顯示 <span id="page-start">1</span> - <span id="page-end">50</span> 
                共 <span id="total-count">0</span> 封郵件
            </div>
            <div class="pagination-controls">
                <button id="prev-page" class="btn btn-secondary" disabled>上一頁</button>
                <span class="page-numbers" id="page-numbers"></span>
                <button id="next-page" class="btn btn-secondary" disabled>下一頁</button>
            </div>
        </div>
    </div>

    <!-- 載入中遮罩 -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-content">
            <div class="loading-spinner large"></div>
            <p>處理中...</p>
        </div>
    </div>

    <!-- 確認對話框 -->
    <div id="confirm-dialog" class="dialog-overlay hidden">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3 id="dialog-title">確認操作</h3>
                <button id="dialog-close" class="btn btn-close">×</button>
            </div>
            <div class="dialog-body">
                <p id="dialog-message">您確定要執行此操作嗎？</p>
            </div>
            <div class="dialog-footer">
                <button id="dialog-cancel" class="btn btn-secondary">取消</button>
                <button id="dialog-confirm" class="btn btn-primary">確認</button>
            </div>
        </div>
    </div>

    <!-- 通知訊息 -->
    <div id="notification" class="notification hidden">
        <div class="notification-content">
            <span class="notification-icon">i</span>
            <span class="notification-text">訊息內容</span>
            <button class="notification-close">×</button>
        </div>
    </div>

    <!-- 批次解析對話框 -->
    <div id="batch-parse-dialog" class="dialog-overlay hidden">
        <div class="dialog-content">
            <div class="dialog-header">
                <h3>批次解析郵件</h3>
                <button class="btn btn-close" onclick="window.emailParserUI.hideBatchParseDialog()">×</button>
            </div>
            <div class="dialog-body">
                <p>選擇要批次解析的郵件類型：</p>
                <div class="batch-parse-options">
                    <label class="radio-option">
                        <input type="radio" name="parse-type" value="pending" checked>
                        <span>解析待解析郵件 <span id="pending-count" class="parse-count"></span></span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="parse-type" value="failed">
                        <span>重新解析失敗郵件 <span id="failed-count" class="parse-count"></span></span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" name="parse-type" value="all">
                        <span>解析所有未解析郵件</span>
                    </label>
                </div>
                <div class="batch-parse-info">
                    <p class="info-text">每次最多處理 50 封郵件</p>
                </div>
            </div>
            <div class="dialog-footer">
                <button class="btn btn-secondary" onclick="window.emailParserUI.hideBatchParseDialog()">取消</button>
                <button class="btn btn-primary" onclick="window.emailParserUI.executeBatchParse()">開始解析</button>
            </div>
        </div>
    </div>

    <!-- JavaScript 模組化檔案 -->
    <script src="{{ url_for('static', filename='js/email/email-ui-utils.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/email/email-attachments.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/email/email-operations.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/email/email-list-manager.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/email/email-detail.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <!-- URL 配置模組 -->
    <script src="{{ url_for('static', filename='js/utils/url-config.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <!-- API 客戶端 -->
    <script src="{{ url_for('static', filename='js/core/api-client.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <!-- 郵件功能模組 -->
    <script src="{{ url_for('static', filename='js/email/email-inbox-core.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script src="{{ url_for('static', filename='js/email/email-parser-ui.js') }}?v={{ range(1000, 9999) | random }}"></script>
    <script>
        // 初始化頁面數據
        const initialData = {
            statistics: {{ statistics|tojson|safe }},
            senders: {{ senders|tojson|safe }}
        };
        
        // 初始化郵件收件夾
        document.addEventListener('DOMContentLoaded', function() {
            // 設置動態 URL
            setupDynamicUrls();
            
            const inbox = new EmailInbox(initialData);
            inbox.initialize();
            
            // 設置全域引用
            window.emailInbox = inbox;
        });
        
        /**
         * 設置動態 URL 連結
         */
        function setupDynamicUrls() {
            try {
                // 獲取 URL 配置
                const config = UrlConfig.getConfig();
                
                // 設置 FT-Summary UI 連結
                const ftSummaryLink = document.getElementById('ft-summary-link');
                if (ftSummaryLink) {
                    ftSummaryLink.href = config.ui.ftSummary;
                    console.log('🔗 FT-Summary UI URL 設置為:', config.ui.ftSummary);
                }
                
                // 顯示配置資訊
                console.log('🖥️ 檢測作業系統:', config.os);
                console.log('🌐 使用主機名:', config.hostname);
                console.log('📡 API 基礎 URL:', config.api.base);
                
                // 可選：測試服務連接性
                testServiceConnectivity();
                
            } catch (error) {
                console.error('❌ 設置動態 URL 時發生錯誤:', error);
                
                // 回退到預設 URL
                const ftSummaryLink = document.getElementById('ft-summary-link');
                if (ftSummaryLink) {
                    ftSummaryLink.href = 'http://127.0.0.1:8010/ft-summary-ui#';
                    console.log('⚠️ 使用回退 URL:', ftSummaryLink.href);
                }
            }
        }
        
        /**
         * 測試服務連接性（可選）
         */
        async function testServiceConnectivity() {
            try {
                const results = await UrlConfig.testConnectivity();
                
                if (results.fastapi.available) {
                    console.log('✅ FastAPI 服務連接正常:', results.fastapi.url);
                } else {
                    console.warn('⚠️ FastAPI 服務連接失敗:', results.fastapi.url);
                }
                
                if (results.flask.available) {
                    console.log('✅ Flask 服務連接正常:', results.flask.url);
                } else {
                    console.warn('⚠️ Flask 服務連接失敗:', results.flask.url);
                }
                
            } catch (error) {
                console.warn('⚠️ 服務連接性測試失敗:', error);
            }
        }
    </script>
</body>
</html>