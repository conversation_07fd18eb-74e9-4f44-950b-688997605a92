# network_utils.py

網路共享瀏覽器工具函式模組，提供網路檔案操作的實用函式。

## list_smb_files

使用 smbclient 列出 SMB 共享檔案的函式。

**參數:**
- `server` (str): 伺服器名稱
- `share` (str): 共享名稱
- `credentials` (NetworkCredentials): 認證資訊
- `subpath` (str): 子路徑，預設為空字串

**返回值:**
- List[NetworkFileInfo]: 檔案資訊列表

**功能:**
- 使用 smbclient 命令列工具連接 SMB 共享
- 解析檔案列表輸出
- 識別檔案類型（目錄、文字檔案、Excel 檔案、壓縮檔案等）
- 處理錯誤狀況（路徑不存在、權限不足等）

**異常處理:**
- 捕獲所有異常並記錄錯誤日誌
- 返回空列表當發生錯誤時

## test_smb_connection

使用 smbclient 測試 SMB 連接的函式。

**參數:**
- `server` (str): 伺服器名稱
- `share` (str): 共享名稱
- `credentials` (NetworkCredentials): 認證資訊

**返回值:**
- tuple[bool, str]: (連接是否成功, 狀態訊息)

**功能:**
- 測試 SMB 連接的可用性
- 識別常見的連接錯誤類型
- 提供友善的錯誤訊息

**錯誤類型識別:**
- `NT_STATUS_LOGON_FAILURE`: 帳號或密碼錯誤
- `NT_STATUS_BAD_NETWORK_NAME`: 網路路徑不存在
- `NT_STATUS_ACCESS_DENIED`: 存取被拒絕
- 連接超時處理

## convert_unc_to_linux_path

轉換 UNC 路徑為 Linux 路徑的函式。

**參數:**
- `unc_path` (str): UNC 路徑（如 \\server\share\path）

**返回值:**
- List[str]: 可能的 Linux 路徑列表

**功能:**
- 解析 UNC 路徑格式
- 生成多個可能的 Linux 掛載點路徑
- 支援常見的掛載點命名慣例

**路徑轉換規則:**
- `/mnt/z`, `/mnt/y`, `/mnt/x`: 常見的掛載點
- `/mnt/wsl/{server}/{share}`: WSL 風格路徑
- `/mnt/{server}/{share}`: 直接映射
- `/mnt/network/{server}_{share}`: 網路專用路徑

## validate_network_path

驗證網路路徑是否有效且可存取的函式。

**參數:**
- `path` (str): 要驗證的路徑

**返回值:**
- tuple[bool, str]: (路徑是否有效, 有效的 Linux 路徑)

**功能:**
- 檢查路徑是否存在
- 嘗試多個可能的 Linux 路徑
- 返回第一個有效的路徑

**異常處理:**
- 捕獲所有檔案系統相關異常
- 返回 False 當發生錯誤時

## get_file_info

取得檔案詳細資訊的函式。

**參數:**
- `file_path` (str): 檔案路徑

**返回值:**
- Optional[NetworkFileInfo]: 檔案資訊，如果失敗則返回 None

**功能:**
- 取得檔案的詳細統計資訊
- 識別檔案類型和大小
- 格式化修改時間
- 區分檔案和目錄

**檔案類型識別:**
- 文字檔案: `.csv`, `.txt`, `.log`
- Excel 檔案: `.xlsx`, `.xls`
- 壓縮檔案: `.zip`, `.7z`, `.rar`
- 其他檔案: 未分類的檔案類型

**資訊包含:**
- 檔案名稱
- 檔案大小（位元組和 MB）
- 修改時間（ISO 格式）
- 檔案類型
- 是否為目錄

**異常處理:**
- 記錄詳細的錯誤日誌
- 返回 None 當無法取得檔案資訊時
