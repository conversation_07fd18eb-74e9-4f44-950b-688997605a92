# etd_parser.py

ETD 廠商解析器實作模組，基於 VBA ANF 邏輯，保持功能不變。

## ETDParser

ETD 廠商郵件解析器類別，繼承自 VendorParser。

### 識別條件
- 主旨包含 "anf"（不區分大小寫）

### 提取資料
- 使用 "/" 分隔主旨
- 從內文提取數量、良率、異常資訊

### 屬性
- `_vendor_code` (str): 廠商代碼 "ETD"
- `_vendor_name` (str): 廠商名稱 "ETD"
- `_identification_patterns` (List[str]): 識別模式列表
- `qty_patterns` (List[str]): 數量提取模式
- `yield_patterns` (List[str]): 良率提取模式

### __init__

初始化 ETD 解析器。

**返回值:**
- None

**功能:**
- 設定廠商代碼和名稱
- 設定識別模式為 ["anf"]
- 設定信心分數閾值為 0.8
- 初始化數量和良率提取模式

**數量提取模式:**
- `input quantity : 數字`
- `quantity : 數字 units`
- `total input : 數字`
- `input : 數字`
- `qty : 數字`

**良率提取模式:**
- `yield : 百分比`
- `良率 : 百分比`
- `pass rate : 百分比`
- `百分比%`

### vendor_code

廠商代碼屬性。

**返回值:**
- str: "ETD"

### vendor_name

廠商名稱屬性。

**返回值:**
- str: "ETD"

### supported_patterns

支援的模式列表屬性。

**返回值:**
- List[str]: ["anf"]

### identify_vendor

識別廠商方法，實作抽象方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- VendorIdentificationResult: 廠商識別結果

**識別邏輯:**
1. **主要模式檢查**: 主旨包含 "anf" (+0.5 分)
2. **寄件者檢查**: 寄件者包含 "etrendtech" (+0.4 分)
3. **格式檢查**: 主旨有斜線分隔且至少3段 (+0.2 分)
4. **關鍵字檢查**: 主旨包含 "etd" 或 "etrend" (+0.1 分)

**信心分數計算:**
- 最高 1.0 分
- 需要至少匹配主要模式且達到閾值 0.8

### parse_email

解析郵件方法，實作抽象方法。

**參數:**
- `context` (ParsingContext): 解析上下文

**返回值:**
- EmailParsingResult: 解析結果

**解析流程:**
1. **驗證廠商**: 確認是 ETD 廠商
2. **解析主旨**: 使用 "/" 分隔主旨
3. **提取資料**: 
   - Product: myArray[1]
   - MO String: myArray[6] 去掉最後一個字符
   - Lot String: myArray[4]
4. **解析內文**: 提取數量、良率、異常資訊
5. **建立結果**: 組合所有提取的資料

### parse_subject

解析主旨的私有方法。

**參數:**
- `subject` (str): 郵件主旨

**返回值:**
- Dict[str, str]: 解析結果字典

**解析邏輯:**
- 基於 VBA 邏輯：`myArray = Split(subject, "/")`
- 提取 product (index 1)
- 提取 moString (index 6，去掉最後字符)
- 提取 lotString (index 4)

**錯誤處理:**
- 檢查陣列長度
- 處理索引越界
- 記錄解析錯誤

### extract_quantity_from_body

從郵件內文提取數量。

**參數:**
- `body` (str): 郵件內文

**返回值:**
- Optional[str]: 提取的數量，如果未找到則返回 None

**功能:**
- 使用預定義的數量模式進行搜尋
- 支援多種數量表示格式
- 返回第一個匹配的數量

### extract_yield_from_body

從郵件內文提取良率。

**參數:**
- `body` (str): 郵件內文

**返回值:**
- Optional[str]: 提取的良率，如果未找到則返回 None

**功能:**
- 使用預定義的良率模式進行搜尋
- 支援中英文良率表示
- 自動處理百分比格式

### find_anomaly_info

從郵件內文尋找異常資訊。

**參數:**
- `body` (str): 郵件內文

**返回值:**
- Optional[str]: 包含異常的行，如果未找到則返回 None

**功能:**
- 基於 VBA 邏輯：`FindLineContainingString(body, "異常")`
- 搜尋包含 "異常" 的行
- 返回完整的異常資訊行

### validate_extracted_data

驗證提取的資料。

**參數:**
- `extracted_data` (Dict[str, Any]): 提取的資料

**返回值:**
- bool: 驗證是否通過

**驗證規則:**
- 檢查必要欄位是否存在
- 驗證資料格式
- 確保資料完整性

## VBA 邏輯對應

### 原始 VBA 邏輯
```vba
If InStr(1, LCase(subject), "anf", vbTextCompare) > 0 Then 'ETD
    myArray = Split(subject, "/")
    product = myArray(1)
    moString = Left(myArray(6), Len(myArray(6)) - 1)
    lotString = myArray(4)
    GetQtyFromMailBody(body)
    GetYieldFromMail(body)
    FindLineContainingString(body, "異常")
End If
```

### Python 實作對應
1. **識別條件**: `"anf" in subject.lower()`
2. **主旨分割**: `subject.split("/")`
3. **資料提取**: 對應的索引位置
4. **內文解析**: 正則表達式模式匹配

## 設計特點

### VBA 相容性
- **邏輯保持**: 完全保持原始 VBA 邏輯
- **索引對應**: Python 索引從 0 開始，VBA 從 1 開始
- **字串處理**: 對應 VBA 的字串操作

### 錯誤處理
- **索引檢查**: 防止陣列越界
- **格式驗證**: 確保主旨格式正確
- **異常捕獲**: 處理解析過程中的異常

### 擴展性
- **模式配置**: 可調整的提取模式
- **閾值設定**: 可調整的信心分數閾值
- **日誌記錄**: 詳細的解析過程記錄

### 效能考量
- **正則快取**: 編譯後的正則表達式
- **早期退出**: 不匹配時快速返回
- **記憶體效率**: 避免不必要的字串複製
