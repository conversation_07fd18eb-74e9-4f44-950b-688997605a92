"""
基礎解析器架構
TASK_005: 建立基礎解析器架構
實現支援多廠商的解析器基礎架構，使用抽象基類和工廠模式
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from datetime import datetime

from src.data_models.email_models import (
    EmailData,
    VendorIdentificationResult,
    EmailParsingResult
)


class ParsingStrategy(Enum):
    """解析策略枚舉"""
    SUBJECT_PATTERN = "subject_pattern"
    BODY_CONTENT = "body_content"
    SENDER_ANALYSIS = "sender_analysis"
    ATTACHMENT_BASED = "attachment_based"
    HYBRID = "hybrid"


class ParsingError(Exception):
    """解析錯誤類別"""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = None,
        vendor_code: str = None,
        context_data: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or "PARSE_ERROR"
        self.vendor_code = vendor_code
        self.context_data = context_data or {}
        self.timestamp = datetime.now()
    
    def __str__(self) -> str:
        return f"ParsingError[{self.error_code}]: {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        return {
            "error_code": self.error_code,
            "message": self.message,
            "vendor_code": self.vendor_code,
            "context_data": self.context_data,
            "timestamp": self.timestamp.isoformat()
        }


class ParsingContext:
    """解析上下文"""
    
    def __init__(
        self,
        email_data: EmailData,
        vendor_code: str,
        parsing_strategy: ParsingStrategy = ParsingStrategy.HYBRID,
        metadata: Optional[Dict[str, Any]] = None
    ):
        self.email_data = email_data
        self.vendor_code = vendor_code
        self.parsing_strategy = parsing_strategy
        self.metadata = metadata or {}
        self.created_time = datetime.now()
    
    def add_metadata(self, key: str, value: Any) -> None:
        """新增元數據"""
        self.metadata[key] = value
    
    def get_metadata(self, key: str, default: Any = None) -> Any:
        """取得元數據"""
        return self.metadata.get(key, default)


class BaseParser(ABC):
    """基礎解析器抽象類別"""
    
    @property
    @abstractmethod
    def vendor_code(self) -> str:
        """廠商代碼"""
        pass
    
    @property
    @abstractmethod
    def vendor_name(self) -> str:
        """廠商名稱"""
        pass
    
    @property
    @abstractmethod
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        pass
    
    @abstractmethod
    def identify_vendor(self, email_data: EmailData) -> VendorIdentificationResult:
        """識別廠商"""
        pass
    
    @abstractmethod
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件"""
        pass
    
    def can_parse(self, email_data: EmailData) -> bool:
        """檢查是否可以解析此郵件"""
        try:
            result = self.identify_vendor(email_data)
            return result.is_identified and result.confidence_score > 0.5
        except Exception:
            return False
    
    def get_confidence_score(self, email_data: EmailData) -> float:
        """取得信心分數"""
        try:
            result = self.identify_vendor(email_data)
            return result.confidence_score
        except Exception:
            return 0.0
    
    def validate_parsing_result(self, result: EmailParsingResult) -> bool:
        """驗證解析結果"""
        if not result.is_success:
            return True  # 失敗結果總是有效的
        
        # 成功結果必須有廠商代碼
        if not result.vendor_code:
            return False
        
        # 如果有 MO 編號，檢查格式
        if result.mo_number:
            import re
            mo_pattern = r'^[A-Z]\d{6}$'
            if not re.match(mo_pattern, result.mo_number):
                return False
        
        return True


class VendorParser(BaseParser):
    """廠商解析器基類 (提供通用功能)"""
    
    def __init__(self):
        self._patterns_cache: Optional[List[str]] = None
        self._confidence_threshold = 0.5
    
    def set_confidence_threshold(self, threshold: float) -> None:
        """設定信心分數閾值"""
        if not (0.0 <= threshold <= 1.0):
            raise ValueError("信心分數閾值必須在 0.0 到 1.0 之間")
        self._confidence_threshold = threshold
    
    def get_confidence_threshold(self) -> float:
        """取得信心分數閾值"""
        return self._confidence_threshold
    
    def match_patterns_in_text(self, text: str, patterns: List[str]) -> List[str]:
        """在文字中匹配模式"""
        if not text:
            return []
        
        text_lower = text.lower()
        matched_patterns = []
        
        for pattern in patterns:
            pattern_lower = pattern.lower()
            if pattern_lower in text_lower:
                matched_patterns.append(pattern)
        
        return matched_patterns
    
    def calculate_confidence_score(
        self, 
        email_data: EmailData, 
        weights: Optional[Dict[str, float]] = None
    ) -> Tuple[float, List[str]]:
        """計算信心分數"""
        if weights is None:
            weights = {
                "sender": 0.4,
                "subject": 0.4,
                "body": 0.2
            }
        
        matched_patterns = []
        total_score = 0.0
        
        # 檢查寄件者
        sender_matches = self.match_patterns_in_text(email_data.sender, self.supported_patterns)
        if sender_matches:
            total_score += weights.get("sender", 0.4)
            matched_patterns.extend(sender_matches)
        
        # 檢查主旨
        subject_matches = self.match_patterns_in_text(email_data.subject, self.supported_patterns)
        if subject_matches:
            total_score += weights.get("subject", 0.4)
            matched_patterns.extend(subject_matches)
        
        # 檢查內容
        if email_data.body:
            body_matches = self.match_patterns_in_text(email_data.body, self.supported_patterns)
            if body_matches:
                total_score += weights.get("body", 0.2)
                matched_patterns.extend(body_matches)
        
        # 確保分數不超過 1.0
        total_score = min(total_score, 1.0)
        
        return total_score, list(set(matched_patterns))  # 去重


class ParserRegistry:
    """解析器註冊表 (單例模式)"""
    
    _instance = None
    _parsers: Dict[str, BaseParser] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def register_parser(self, vendor_code: str, parser: BaseParser, allow_override: bool = False) -> None:
        """註冊解析器"""
        if not isinstance(parser, BaseParser):
            raise TypeError("解析器必須繼承自 BaseParser")
        
        if vendor_code in self._parsers:
            existing_parser = self._parsers[vendor_code]
            # 如果是相同類型的解析器，允許重複註冊（記錄警告）
            if type(existing_parser) == type(parser):
                # 只輸出調試訊息，不拋出異常
                print(f"警告：廠商 {vendor_code} 的解析器已註冊，跳過重複註冊")
                return
            elif allow_override:
                print(f"警告：覆蓋廠商 {vendor_code} 的現有解析器")
            else:
                raise ValueError(f"廠商 {vendor_code} 的解析器已註冊，且類型不同")
        
        self._parsers[vendor_code] = parser
    
    def unregister_parser(self, vendor_code: str) -> None:
        """取消註冊解析器"""
        if vendor_code in self._parsers:
            del self._parsers[vendor_code]
    
    def get_parser(self, vendor_code: str) -> BaseParser:
        """取得解析器"""
        if vendor_code not in self._parsers:
            raise ValueError(f"未找到廠商 {vendor_code} 的解析器")
        
        return self._parsers[vendor_code]
    
    def is_registered(self, vendor_code: str) -> bool:
        """檢查是否已註冊"""
        return vendor_code in self._parsers
    
    def get_registered_vendors(self) -> List[str]:
        """取得所有已註冊的廠商"""
        return list(self._parsers.keys())
    
    def get_all_parsers(self) -> Dict[str, BaseParser]:
        """取得所有解析器"""
        return self._parsers.copy()
    
    def clear(self) -> None:
        """清空註冊表"""
        self._parsers.clear()


class ParserFactory:
    """解析器工廠"""
    
    def __init__(self):
        self.registry = ParserRegistry()
    
    def identify_vendor(self, email_data: EmailData) -> Tuple[Optional[BaseParser], VendorIdentificationResult]:
        """識別廠商並返回最佳解析器"""
        best_parser = None
        best_result = VendorIdentificationResult(
            vendor_code=None,
            vendor_name=None,
            confidence_score=0.0,
            matching_patterns=[],
            is_identified=False
        )
        
        # 遍歷所有註冊的解析器
        for vendor_code, parser in self.registry.get_all_parsers().items():
            try:
                result = parser.identify_vendor(email_data)
                
                # 找到信心分數最高的解析器
                if result.confidence_score > best_result.confidence_score:
                    best_parser = parser
                    best_result = result
                    
            except Exception as e:
                # 記錄錯誤但繼續嘗試其他解析器
                print(f"廠商識別錯誤 ({vendor_code}): {e}")
                continue
        
        return best_parser, best_result
    
    def get_parser(self, vendor_code: str) -> BaseParser:
        """取得指定廠商的解析器"""
        return self.registry.get_parser(vendor_code)
    
    def parse_email(self, email_data: EmailData) -> Tuple[VendorIdentificationResult, EmailParsingResult]:
        """解析郵件 (完整流程)"""
        # 1. 識別廠商
        best_parser, vendor_result = self.identify_vendor(email_data)
        
        # 2. 如果無法識別廠商，返回失敗結果
        if not vendor_result.is_identified or best_parser is None:
            parsing_result = EmailParsingResult(
                is_success=False,
                error_message="無法識別郵件廠商",
                validation_errors=["未找到匹配的廠商模式"]
            )
            return vendor_result, parsing_result
        
        # 3. 建立解析上下文
        context = ParsingContext(
            email_data=email_data,
            vendor_code=vendor_result.vendor_code,
            parsing_strategy=ParsingStrategy.HYBRID
        )
        
        # 4. 執行解析
        try:
            parsing_result = best_parser.parse_email(context)
            
            # 5. 驗證解析結果
            if not best_parser.validate_parsing_result(parsing_result):
                parsing_result.is_success = False
                parsing_result.error_message = "解析結果驗證失敗"
                parsing_result.add_validation_error("解析結果格式不正確")
            
            return vendor_result, parsing_result
            
        except ParsingError as e:
            # 處理解析錯誤
            parsing_result = EmailParsingResult(
                is_success=False,
                error_message=e.message,
                validation_errors=[str(e)]
            )
            return vendor_result, parsing_result
            
        except Exception as e:
            # 處理未預期的錯誤
            parsing_result = EmailParsingResult(
                is_success=False,
                error_message=f"解析過程發生錯誤: {str(e)}",
                validation_errors=[f"系統錯誤: {type(e).__name__}"]
            )
            return vendor_result, parsing_result
    
    def get_supported_vendors(self) -> List[str]:
        """取得所有支援的廠商"""
        return self.registry.get_registered_vendors()
    
    def is_vendor_supported(self, vendor_code: str) -> bool:
        """檢查是否支援指定廠商"""
        return self.registry.is_registered(vendor_code)
    
    def get_parser_info(self, vendor_code: str) -> Dict[str, Any]:
        """取得解析器資訊"""
        if not self.is_vendor_supported(vendor_code):
            raise ValueError(f"不支援的廠商: {vendor_code}")
        
        parser = self.get_parser(vendor_code)
        return {
            "vendor_code": parser.vendor_code,
            "vendor_name": parser.vendor_name,
            "supported_patterns": parser.supported_patterns,
            "confidence_threshold": getattr(parser, '_confidence_threshold', 0.5)
        }
    
    def register_parser(self, vendor_code: str, parser: BaseParser) -> None:
        """註冊新的解析器"""
        self.registry.register_parser(vendor_code, parser)
    
    def batch_parse_emails(self, emails: List[EmailData]) -> List[Tuple[EmailData, VendorIdentificationResult, EmailParsingResult]]:
        """批次解析郵件"""
        results = []
        
        for email in emails:
            try:
                vendor_result, parsing_result = self.parse_email(email)
                results.append((email, vendor_result, parsing_result))
            except Exception as e:
                # 建立失敗結果
                failed_vendor_result = VendorIdentificationResult(
                    vendor_code=None,
                    vendor_name=None,
                    confidence_score=0.0,
                    matching_patterns=[],
                    is_identified=False
                )
                
                failed_parsing_result = EmailParsingResult(
                    is_success=False,
                    error_message=f"批次解析失敗: {str(e)}",
                    validation_errors=[f"系統錯誤: {type(e).__name__}"]
                )
                
                results.append((email, failed_vendor_result, failed_parsing_result))
        
        return results