# __init__.py

src 根目錄的初始化檔案，標識 src 為 Python 套件。

## 模組說明

這是 Outlook Summary System - Python 郵件處理系統的主要套件初始化檔案。

### 功能
- 將 src 目錄標識為 Python 套件
- 提供套件的基本識別資訊
- 允許從 src 套件匯入子模組

### 套件結構
src 套件包含以下子模組：
- `data_models`: 資料模型定義
- `domain`: 領域層實作
- `application`: 應用層服務
- `infrastructure`: 基礎設施層
- `presentation`: 表現層 API
- `services`: 服務層功能

### 使用方式
```python
# 匯入子模組
from src.data_models import EmailData
from src.application.use_cases import EmailProcessor
from src.infrastructure.config import ConfigManager
```

### 專案資訊
- **專案名稱**: Outlook Summary System
- **語言**: Python
- **用途**: 郵件處理系統
- **架構**: 六角架構 (Hexagonal Architecture)
