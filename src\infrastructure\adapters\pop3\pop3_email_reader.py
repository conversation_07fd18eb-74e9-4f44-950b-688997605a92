"""
POP3 郵件讀取器
實現 EmailReader 介面，提供 POP3 郵件讀取功能
"""

import asyncio
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from email.message import EmailMessage
from email.utils import parsedate_to_datetime, parseaddr
from email.header import decode_header
import tempfile
import os
from pathlib import Path
import uuid

from src.application.interfaces.email_reader import EmailReader, EmailReaderConfig
from src.data_models.email_models import EmailData, EmailAttachment, EmailParsingResult
from src.infrastructure.logging.logger_manager import LoggerManager
from .pop3_adapter import POP3Adapter, POP3ConnectionConfig


class POP3EmailReader(EmailReader):
    """
    POP3 郵件讀取器
    實現 EmailReader 介面，提供 POP3 郵件讀取功能
    """
    
    def __init__(self, connection_config: POP3ConnectionConfig, reader_config: Optional[EmailReaderConfig] = None):
        """
        初始化 POP3 郵件讀取器
        
        Args:
            connection_config: POP3 連接配置
            reader_config: 讀取器配置
        """
        self.connection_config = connection_config
        self.reader_config = reader_config or EmailReaderConfig()
        self.logger = LoggerManager().get_logger("POP3EmailReader")
        self.adapter = POP3Adapter(connection_config)
        
        # 統計資訊
        self.stats = {
            'total_read': 0,
            'total_parsed': 0,
            'total_processed': 0,
            'last_read_time': None,
            'errors': 0
        }
        
        # 快取已讀取的郵件
        self._email_cache: Dict[str, EmailData] = {}
        
    async def connect(self) -> bool:
        """
        建立連接
        
        Returns:
            連接成功與否
        """
        try:
            return self.adapter.connect()
        except Exception as e:
            self.logger.error(f"POP3 連接失敗: {e}")
            return False
    
    async def disconnect(self) -> None:
        """斷開連接"""
        try:
            self.adapter.disconnect()
        except Exception as e:
            self.logger.warning(f"斷開 POP3 連接時發生警告: {e}")
    
    def is_connected(self) -> bool:
        """
        檢查連接狀態
        
        Returns:
            是否已連接
        """
        return self.adapter.is_connected()
    
    async def read_emails(self, count: Optional[int] = None) -> List[EmailData]:
        """
        讀取郵件列表
        
        Args:
            count: 要讀取的郵件數量，None 表示讀取所有
            
        Returns:
            郵件數據列表
        """
        try:
            if not self.is_connected():
                if not await self.connect():
                    return []
            
            # 取得郵件清單
            mail_list = self.adapter.get_mail_list()
            if not mail_list:
                self.logger.info("沒有可讀取的郵件")
                return []
            
            # 排序郵件（最新的先）
            mail_list.sort(key=lambda x: x['number'], reverse=True)
            
            # 限制讀取數量
            if count:
                mail_list = mail_list[:count]
            
            emails = []
            for mail_info in mail_list:
                try:
                    # 取得郵件
                    email_message = self.adapter.retrieve_mail(mail_info['number'])
                    if email_message:
                        # 轉換為 EmailData
                        email_data = await self._convert_email_message_to_email_data(
                            email_message, mail_info['number']
                        )
                        emails.append(email_data)
                        
                        # 更新統計
                        self.stats['total_read'] += 1
                        
                except Exception as e:
                    self.logger.warning(f"讀取郵件 #{mail_info['number']} 時發生錯誤: {e}")
                    self.stats['errors'] += 1
                    continue
            
            self.stats['last_read_time'] = datetime.now()
            self.logger.info(f"成功讀取 {len(emails)} 封郵件")
            return emails
            
        except Exception as e:
            self.logger.error(f"讀取郵件失敗: {e}")
            self.stats['errors'] += 1
            return []
    
    async def parse_email(self, email_data: EmailData) -> EmailParsingResult:
        """
        解析單一郵件
        
        Args:
            email_data: 郵件數據
            
        Returns:
            解析結果
        """
        try:
            # 基本解析結果
            result = EmailParsingResult(
                is_success=True,
                vendor_code="TEST",  # 測試用的廠商代碼
                extracted_data={
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time,
                    'body_length': len(email_data.body),
                    'attachment_count': len(email_data.attachments)
                },
                validation_errors=[]
            )
            
            # 解析附件
            if email_data.attachments:
                attachment_info = []
                for attachment in email_data.attachments:
                    attachment_info.append({
                        'filename': attachment.filename,
                        'size': attachment.size_bytes,
                        'content_type': attachment.content_type
                    })
                result.extracted_data['attachments'] = attachment_info
            
            # 解析郵件內容類型
            content_analysis = self._analyze_email_content(email_data.body)
            result.extracted_data['content_analysis'] = content_analysis
            
            self.stats['total_parsed'] += 1
            self.logger.debug(f"成功解析郵件: {email_data.subject}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"解析郵件失敗: {e}")
            return EmailParsingResult(
                is_success=False,
                error_message=str(e),
                extracted_data={},
                validation_errors=[str(e)]
            )
    
    async def mark_as_processed(self, email_data: EmailData) -> bool:
        """
        標記郵件為已處理
        
        Args:
            email_data: 郵件數據
            
        Returns:
            操作成功與否
        """
        try:
            # 在 POP3 中，可以選擇刪[EXCEPT_CHAR]郵件來標記為已處理
            # 但通常我們不會真的刪[EXCEPT_CHAR]，而是記錄在本地
            
            # 從 raw_data 中取得郵件號碼
            raw_data = email_data.raw_data or {}
            mail_number = raw_data.get('mail_number')
            
            if mail_number and hasattr(self.reader_config, 'delete_after_read'):
                if getattr(self.reader_config, 'delete_after_read', False):
                    # 如果配置要求刪[EXCEPT_CHAR]，則刪[EXCEPT_CHAR]郵件
                    if self.adapter.delete_mail(mail_number):
                        self.logger.info(f"已標記郵件 #{mail_number} 為刪[EXCEPT_CHAR]")
                        self.stats['total_processed'] += 1
                        return True
            
            # 否則只是記錄為已處理
            self.stats['total_processed'] += 1
            self.logger.debug(f"已標記郵件為已處理: {email_data.subject}")
            return True
            
        except Exception as e:
            self.logger.error(f"標記郵件為已處理失敗: {e}")
            return False
    
    def get_unread_count(self) -> int:
        """
        獲取未讀郵件數量
        
        Returns:
            未讀郵件數量
        """
        try:
            if not self.is_connected():
                return 0
            
            return self.adapter.get_mail_count()
            
        except Exception as e:
            self.logger.error(f"獲取未讀郵件數量失敗: {e}")
            return 0
    
    async def search_emails(self, criteria: Dict[str, Any]) -> List[EmailData]:
        """
        搜尋符合條件的郵件
        
        Args:
            criteria: 搜尋條件 (subject, sender, date_range 等)
            
        Returns:
            符合條件的郵件列表
        """
        try:
            # POP3 不支援伺服器端搜尋，需要本地搜尋
            # 先讀取所有郵件
            all_emails = await self.read_emails()
            
            filtered_emails = []
            
            for email in all_emails:
                match = True
                
                # 檢查主旨
                if 'subject' in criteria:
                    subject_filter = criteria['subject'].lower()
                    if subject_filter not in email.subject.lower():
                        match = False
                
                # 檢查寄件者
                if 'sender' in criteria and match:
                    sender_filter = criteria['sender'].lower()
                    if sender_filter not in email.sender.lower():
                        match = False
                
                # 檢查日期範圍
                if 'date_range' in criteria and match:
                    date_range = criteria['date_range']
                    if 'start' in date_range:
                        if email.received_time < date_range['start']:
                            match = False
                    if 'end' in date_range and match:
                        if email.received_time > date_range['end']:
                            match = False
                
                if match:
                    filtered_emails.append(email)
            
            self.logger.info(f"搜尋到 {len(filtered_emails)} 封符合條件的郵件")
            return filtered_emails
            
        except Exception as e:
            self.logger.error(f"搜尋郵件失敗: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取讀取器統計資訊
        
        Returns:
            統計資訊字典
        """
        return {
            **self.stats,
            'connection_info': self.adapter.get_server_info(),
            'cache_size': len(self._email_cache),
            'config': {
                'server': self.connection_config.server,
                'port': self.connection_config.port,
                'username': self.connection_config.username,
                'use_ssl': self.connection_config.use_ssl
            }
        }
    
    def _decode_mime_header(self, header_value: str) -> str:
        """
        解碼 MIME 編碼的郵件頭
        
        Args:
            header_value: 原始頭部值
            
        Returns:
            解碼後的字串
        """
        if not header_value:
            return header_value
            
        try:
            # 解碼 MIME 編碼
            decoded_parts = decode_header(header_value)
            result = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        try:
                            result += part.decode(encoding)
                        except (UnicodeDecodeError, LookupError):
                            # 如果指定編碼失敗，嘗試常見編碼
                            for fallback_encoding in ['utf-8', 'big5', 'gbk', 'latin-1']:
                                try:
                                    result += part.decode(fallback_encoding)
                                    break
                                except (UnicodeDecodeError, LookupError):
                                    continue
                            else:
                                result += part.decode('utf-8', errors='replace')
                    else:
                        result += part.decode('utf-8', errors='replace')
                else:
                    result += str(part)
            
            return result.strip()
            
        except Exception as e:
            self.logger.warning(f"解碼失敗: {e}")
            return header_value
    
    def _decode_sender(self, sender_value: str) -> str:
        """
        解碼發件人信息
        
        Args:
            sender_value: 原始發件人值
            
        Returns:
            解碼後的發件人信息
        """
        if not sender_value:
            return sender_value
            
        try:
            # <AUTHOR> <EMAIL>
            name, email_addr = parseaddr(sender_value)
            
            # 解碼姓名部分
            if name:
                decoded_name = self._decode_mime_header(name)
                if email_addr:
                    return f"{decoded_name} <{email_addr}>"
                else:
                    return decoded_name
            else:
                return email_addr or sender_value
                
        except Exception as e:
            self.logger.warning(f"解碼發件人失敗: {e}")
            return sender_value
    
    async def _convert_email_message_to_email_data(self, email_message: EmailMessage, mail_number: int) -> EmailData:
        """
        將 EmailMessage 轉換為 EmailData
        
        Args:
            email_message: 郵件訊息
            mail_number: 郵件號碼
            
        Returns:
            郵件數據
        """
        try:
            # 取得基本資訊
            message_id = email_message.get('Message-ID', f"pop3_{mail_number}_{uuid.uuid4().hex[:8]}")
            
            # 解碼主題
            raw_subject = email_message.get('Subject', '')
            subject = self._decode_mime_header(raw_subject)
            
            # 解碼寄件人
            raw_sender = email_message.get('From', '')
            sender = self._decode_sender(raw_sender)
            
            # 處理 sender 格式，確保為完整的 email 格式
            if sender and '@' not in sender:
                # 如果 sender 不是完整的 email 格式，補完為 user@domain
                sender = f"{sender}@{self.connection_config.server}"
            
            # 解析接收時間
            received_time = datetime.now()
            date_header = email_message.get('Date')
            if date_header:
                try:
                    received_time = parsedate_to_datetime(date_header)
                except:
                    pass
            
            # 取得郵件內容
            body = self._extract_email_body(email_message)
            
            # 處理附件
            attachments = await self._extract_attachments(email_message)
            
            return EmailData(
                message_id=message_id,
                subject=subject,
                sender=sender,
                body=body,
                received_time=received_time,
                attachments=attachments,
                raw_data={'mail_number': mail_number, 'email_message': email_message}
            )
            
        except Exception as e:
            self.logger.error(f"轉換郵件訊息失敗: {e}")
            raise
    
    def _extract_email_body(self, email_message: EmailMessage) -> str:
        """
        提取郵件內容
        
        Args:
            email_message: 郵件訊息
            
        Returns:
            郵件內容
        """
        try:
            body = ""
            
            if email_message.is_multipart():
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain":
                        charset = part.get_content_charset() or 'utf-8'
                        try:
                            body = part.get_payload(decode=True).decode(charset)
                            break
                        except:
                            try:
                                body = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                                break
                            except:
                                continue
            else:
                # 單一部分郵件
                charset = email_message.get_content_charset() or 'utf-8'
                try:
                    body = email_message.get_payload(decode=True).decode(charset)
                except:
                    try:
                        body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
                    except:
                        body = str(email_message.get_payload())
            
            return body
            
        except Exception as e:
            self.logger.warning(f"提取郵件內容失敗: {e}")
            return ""
    
    async def _extract_attachments(self, email_message: EmailMessage) -> List[EmailAttachment]:
        """
        提取郵件附件
        
        Args:
            email_message: 郵件訊息
            
        Returns:
            附件列表
        """
        attachments = []
        
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    # 檢查是否為附件
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()
                        if filename:
                            # 取得附件內容
                            content = part.get_payload(decode=True)
                            if content:
                                # 建立臨時檔案
                                temp_dir = tempfile.gettempdir()
                                temp_file = os.path.join(temp_dir, f"pop3_attachment_{uuid.uuid4().hex[:8]}_{filename}")
                                
                                with open(temp_file, 'wb') as f:
                                    f.write(content)
                                
                                attachment = EmailAttachment(
                                    filename=filename,
                                    size_bytes=len(content),
                                    content_type=part.get_content_type() or 'application/octet-stream',
                                    file_path=Path(temp_file)
                                )
                                attachments.append(attachment)
            
            return attachments
            
        except Exception as e:
            self.logger.warning(f"提取附件失敗: {e}")
            return []
    
    def _analyze_email_content(self, body: str) -> Dict[str, Any]:
        """
        分析郵件內容
        
        Args:
            body: 郵件內容
            
        Returns:
            分析結果
        """
        analysis = {
            'length': len(body),
            'lines': len(body.splitlines()),
            'has_urls': 'http' in body.lower(),
            'has_attachments_mention': any(keyword in body.lower() for keyword in ['附件', '附档', 'attachment']),
            'language': 'chinese' if any(ord(char) > 127 for char in body) else 'english'
        }
        
        return analysis