"""
API 服務模組
提供模組化的 API 服務組件
"""

from .api_utils import (
    SystemConfig,
    APIUtils,
    UploadProcessorManager,
    DataParser,
    ResponseFormatter,
    FileOperations,
    LoggingUtils,
    # 向下相容函數
    convert_windows_path_to_wsl,
    process_folder_path,
    get_global_upload_processor,
    parse_summary_sheet_data,
    count_debug_matches
)

from .cleanup_service import (
    CleanupService,
    cleanup_service,
    get_cleanup_service,
    startup_cleanup_service,
    shutdown_cleanup_service
)

from .file_management_service import (
    FileManagementService,
    file_management_service,
    get_file_management_service
)

from .eqc_processing_service import (
    EQCProcessingService,
    eqc_processing_service,
    get_eqc_processing_service
)

__all__ = [
    # 配置類別
    'SystemConfig',
    # 工具類別
    'APIUtils',
    'UploadProcessorManager', 
    'DataParser',
    'ResponseFormatter',
    'FileOperations',
    'LoggingUtils',
    # 服務類別
    'CleanupService',
    'FileManagementService',
    'EQCProcessingService',
    # 服務實例
    'cleanup_service',
    'file_management_service',
    'eqc_processing_service',
    # 依賴注入函數
    'get_cleanup_service',
    'get_file_management_service',
    'get_eqc_processing_service',
    # 事件處理函數
    'startup_cleanup_service',
    'shutdown_cleanup_service',
    # 向下相容函數
    'convert_windows_path_to_wsl',
    'process_folder_path',
    'get_global_upload_processor',
    'parse_summary_sheet_data',
    'count_debug_matches'
]