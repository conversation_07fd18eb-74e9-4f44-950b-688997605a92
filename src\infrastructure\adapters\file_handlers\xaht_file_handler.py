"""
XAHT 廠商檔案處理器
對應 VBA 的 CopyFilesXAHT 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class XAHTFileHandler(BaseFileHandler):
    """
    XAHT 廠商檔案處理器
    
    VBA 邏輯：
    - 只從 \XAHT\temp\ 搜尋包含 MO 的壓縮檔
    - 不支援 LOT 搜尋或資料夾複製
    """
    
    def __init__(self, source_base_path: str):
        """初始化 XAHT 檔案處理器"""
        super().__init__(source_base_path, "XAHT")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """
        XAHT 只有一個來源路徑
        
        VBA: sourcePathXAHT = sourcePath & "\XAHT\temp\"
        """
        return [self.source_base_path / "XAHT" / "temp"]
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        XAHT 的檔案搜尋模式
        
        VBA: file = Dir(sourcePathXAHT & "*" & fileName & "*")
        """
        patterns = []
        
        if mo:
            patterns.append(f"*{mo}*")  # VBA 模式：包含 MO
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """XAHT 不支援資料夾複製"""
        return False