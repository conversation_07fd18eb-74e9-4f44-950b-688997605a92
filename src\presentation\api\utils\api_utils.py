"""
API 工具函數模組
提供通用的工具函數，包括路徑轉換、資料解析、回應格式化等功能
"""

import os
import re
import json
import pandas as pd
from datetime import datetime
from typing import Dict, Any, <PERSON><PERSON>
from pathlib import Path
from loguru import logger


def convert_windows_path_to_wsl(windows_path: str) -> str:
    """轉換 Windows 路徑為 WSL 路徑"""
    # 如果已經是 Unix 路徑，直接返回
    if windows_path.startswith('/'):
        return windows_path
    
    # 處理相對路徑
    if not ':' in windows_path:
        return windows_path
    
    # 轉換 Windows 絕對路徑到 WSL
    # D:\path -> /mnt/d/path
    # C:\Users -> /mnt/c/Users
    match = re.match(r'^([A-Za-z]):\\(.*)$', windows_path)
    if match:
        drive = match.group(1).lower()
        path = match.group(2).replace('\\', '/')
        wsl_path = f"/mnt/{drive}/{path}"
        return wsl_path
    
    return windows_path


def process_folder_path(original_path: str) -> Tuple[str, str]:
    """統一處理路徑轉換和日誌記錄（智能路徑解析）"""
    try:
        from src.utils.path_manager import PathManager
        
        # 使用智能路徑解析
        original, resolved_path, exists = PathManager.smart_path_resolution(original_path)
        
        if not exists:
            logger.warning(f"[WARNING] 路徑驗證失敗: 資料夾不存在: {resolved_path}")
        
        if original_path != resolved_path:
            logger.info(f"[REFRESH] 路徑轉換: {original_path} -> {resolved_path}")
        
        return original_path, resolved_path
        
    except ImportError:
        # 回退到原有邏輯
        folder_path = convert_windows_path_to_wsl(original_path)
        
        if original_path != folder_path:
            logger.info(f"[REFRESH] 路徑轉換: {original_path} -> {folder_path}")
        
        return original_path, folder_path


def convert_linux_to_windows_path(linux_path: str) -> str:
    """轉換 Linux 路徑為 Windows 路徑"""
    if linux_path.startswith('/mnt/d/'):
        return linux_path.replace('/mnt/d/', 'D:\\').replace('/', '\\')
    elif linux_path.startswith('/mnt/c/'):
        return linux_path.replace('/mnt/c/', 'C:\\').replace('/', '\\')
    else:
        return linux_path.replace('/', '\\')


def validate_folder_exists(folder_path: str) -> Dict[str, Any]:
    """驗證資料夾是否存在（使用智能路徑解析）"""
    try:
        # 嘗試匯入 PathManager
        from src.utils.path_manager import PathManager
        
        # 使用智能路徑解析檢查路徑
        original, resolved_path, exists = PathManager.smart_path_resolution(folder_path)
        
        if not exists:
            return {
                "valid": False,
                "error": "folder_not_found",
                "message": f"資料夾不存在: {resolved_path}"
            }
        
        if not os.path.isdir(resolved_path):
            return {
                "valid": False,
                "error": "not_directory",
                "message": f"指定路徑不是資料夾: {resolved_path}"
            }
        
        return {"valid": True}
        
    except ImportError:
        # 回退到原有邏輯
        if not os.path.exists(folder_path):
            return {
                "valid": False,
                "error": "folder_not_found",
                "message": f"資料夾不存在: {folder_path}"
            }
        
        if not os.path.isdir(folder_path):
            return {
                "valid": False,
                "error": "not_directory",
                "message": f"指定路徑不是資料夾: {folder_path}"
            }
        
        return {"valid": True}


def get_file_media_type(file_path: str) -> str:
    """根據檔案副檔名獲取 MIME 類型"""
    if file_path.endswith('.xlsx'):
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    elif file_path.endswith('.csv'):
        return 'text/csv'
    elif file_path.endswith('.txt'):
        return 'text/plain'
    elif file_path.endswith('.log'):
        return 'text/plain'
    else:
        return 'application/octet-stream'


def extract_folder_name_from_path(path: str) -> str:
    """從完整路徑中提取最後的目錄名稱"""
    return re.split(r'[/\\]', path.rstrip('/\\'))[-1]


def format_file_size(size_bytes: int) -> str:
    """格式化檔案大小顯示"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    else:
        return f"{size_bytes / (1024 * 1024):.1f} MB"


def parse_summary_sheet_data(excel_path: str) -> Dict[str, Any]:
    """解析 Summary sheet 資料獲取詳細的 FAIL 項目和 Site 統計"""
    try:
        # 讀取 Summary sheet
        summary_df = pd.read_excel(excel_path, sheet_name='Summary', header=None)
        
        # 解析基本統計資料 (第1-4行)
        total_count = int(summary_df.iloc[0, 1])  # Total
        pass_count = int(summary_df.iloc[1, 1])   # Pass
        fail_count = int(summary_df.iloc[2, 1])   # Fail
        yield_rate = float(summary_df.iloc[3, 1]) # Yield
        
        # 解析 Site 統計 (第5行)
        site1_total = int(summary_df.iloc[4, 6]) if not pd.isna(summary_df.iloc[4, 6]) else 0
        site2_total = int(summary_df.iloc[4, 8]) if not pd.isna(summary_df.iloc[4, 8]) else 0
        
        # 解析 BIN 統計資料 (從第7行開始)
        fail_details = []
        site_details = {"site1": [], "site2": []}
        
        for i in range(6, min(20, len(summary_df))):  # 檢查前20行的 BIN 資料
            if pd.isna(summary_df.iloc[i, 0]):  # 如果 BIN 欄位為空，跳過
                continue
                
            bin_number = str(summary_df.iloc[i, 0]).strip()
            count = int(summary_df.iloc[i, 1]) if not pd.isna(summary_df.iloc[i, 1]) else 0
            percentage = float(summary_df.iloc[i, 2]) if not pd.isna(summary_df.iloc[i, 2]) else 0
            definition = str(summary_df.iloc[i, 3]).strip() if not pd.isna(summary_df.iloc[i, 3]) else "未知"
            
            # Site 統計
            site1_count = int(summary_df.iloc[i, 5]) if not pd.isna(summary_df.iloc[i, 5]) else 0
            site1_percentage = float(summary_df.iloc[i, 6]) if not pd.isna(summary_df.iloc[i, 6]) else 0
            site2_count = int(summary_df.iloc[i, 7]) if not pd.isna(summary_df.iloc[i, 7]) else 0
            site2_percentage = float(summary_df.iloc[i, 8]) if not pd.isna(summary_df.iloc[i, 8]) else 0
            
            # 只記錄有意義的資料
            if count > 0:
                fail_detail = {
                    "bin": bin_number,
                    "count": count,
                    "percentage": round(percentage * 100, 2),
                    "definition": definition,
                    "status": "PASS" if bin_number == "1" else "FAIL"
                }
                fail_details.append(fail_detail)
                
                # Site 詳細統計
                if site1_count > 0:
                    site_details["site1"].append({
                        "bin": bin_number,
                        "count": site1_count,
                        "percentage": round(site1_percentage * 100, 2),
                        "definition": definition
                    })
                
                if site2_count > 0:
                    site_details["site2"].append({
                        "bin": bin_number,
                        "count": site2_count,
                        "percentage": round(site2_percentage * 100, 2),
                        "definition": definition
                    })
        
        # 按數量排序 FAIL 項目
        fail_details.sort(key=lambda x: x["count"], reverse=True)
        
        summary_data = {
            "basic_stats": {
                "total": total_count,
                "pass": pass_count,
                "fail": fail_count,
                "yield_rate": round(yield_rate * 100, 2)
            },
            "site_stats": {
                "site1_total": site1_total,
                "site2_total": site2_total
            },
            "fail_details": fail_details,
            "site_details": site_details
        }
        
        logger.info(f"   [BOARD] Summary 解析完成: {len(fail_details)} 個 BIN 項目")
        return summary_data
        
    except Exception as e:
        logger.error(f"解析 Summary sheet 失敗: {str(e)}")
        return {
            "basic_stats": {"total": 0, "pass": 0, "fail": 0, "yield_rate": 0},
            "site_stats": {"site1_total": 0, "site2_total": 0},
            "fail_details": [],
            "site_details": {"site1": [], "site2": []}
        }


def count_debug_matches(debug_file_path: str) -> int:
    """計算 Debug 日誌中的總匹配數量"""
    if not os.path.exists(debug_file_path):
        logger.warning(f"Debug 日誌檔案不存在: {debug_file_path}")
        return 0
    
    try:
        with open(debug_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # 計算所有 "[OK] 匹配" 出現次數
            match_count = content.count("[OK] 匹配")
            logger.info(f"   [BOARD] Debug 日誌匹配記錄: {match_count} 個")
            return match_count
    except Exception as e:
        logger.error(f"讀取 Debug 日誌失敗: {e}")
        return 0


def create_error_response(status_code: int, message: str, error_details: Dict = None) -> Dict[str, Any]:
    """建立標準錯誤回應格式"""
    response = {
        "status": "error",
        "message": message
    }
    
    if error_details:
        response["error_details"] = error_details
    
    return response


def create_success_response(message: str, data: Dict = None) -> Dict[str, Any]:
    """建立標準成功回應格式"""
    response = {
        "status": "success",
        "message": message
    }
    
    if data:
        response["data"] = data
    
    return response


def save_metadata_file(target_dir: Path, metadata: Dict[str, Any]) -> None:
    """保存 metadata 檔案"""
    metadata_file = target_dir / "metadata.json"
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)


def read_metadata_file(metadata_file: Path) -> Dict[str, Any]:
    """讀取 metadata 檔案"""
    try:
        with open(metadata_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        logger.warning(f"[WARNING] 讀取 metadata.json 失敗: {str(e)}")
        return {}


def format_processing_time(seconds: float) -> str:
    """格式化處理時間顯示"""
    if seconds < 60:
        return f"{seconds:.2f}秒"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        remaining_seconds = seconds % 60
        return f"{minutes}分{remaining_seconds:.1f}秒"
    else:
        hours = int(seconds // 3600)
        remaining_minutes = int((seconds % 3600) // 60)
        return f"{hours}小時{remaining_minutes}分"
