#!/usr/bin/env python3
"""
多工作表管理器
模擬 VBA 的 Sheets 集合，提供 Excel 工作表操作的 Python 實作
"""

import pandas as pd
from typing import Dict, List, Optional, Union
import logging

logger = logging.getLogger(__name__)


class WorksheetManager:
    """多工作表管理器 - 對應 VBA 的 Sheets 集合"""
    
    def __init__(self):
        self.worksheets: Dict[str, pd.DataFrame] = {}
        self.worksheet_order: List[str] = []
        
    def add_worksheet(self, name: str, data: pd.DataFrame, position: str = 'after', 
                     reference: Optional[str] = None) -> None:
        """
        添加工作表 - 對應 VBA: Sheets.Add Before/After
        
        Args:
            name: 工作表名稱
            data: 工作表資料 
            position: 'before', 'after', 'first', 'last'
            reference: 參考工作表名稱
        """
        if name in self.worksheets:
            logger.warning(f"工作表 '{name}' 已存在，將被覆蓋")
            
        self.worksheets[name] = data.copy()
        
        # 如果已存在則先移[EXCEPT_CHAR]
        if name in self.worksheet_order:
            self.worksheet_order.remove(name)
        
        if position == 'first':
            self.worksheet_order.insert(0, name)
        elif position == 'last':
            self.worksheet_order.append(name)
        elif position == 'before' and reference and reference in self.worksheet_order:
            idx = self.worksheet_order.index(reference)
            self.worksheet_order.insert(idx, name)
        elif position == 'after' and reference and reference in self.worksheet_order:
            idx = self.worksheet_order.index(reference)
            self.worksheet_order.insert(idx + 1, name)
        else:
            self.worksheet_order.append(name)
            
        logger.debug(f"工作表 '{name}' 已添加，位置: {position}")
    
    def copy_rows(self, source_sheet: str, target_sheet: str, 
                  start_row: int, end_row: int) -> None:
        """
        複製行範圍 - 對應 VBA: Sheets().Rows().Copy
        
        Args:
            source_sheet: 來源工作表名稱
            target_sheet: 目標工作表名稱  
            start_row: 開始行號 (1-based)
            end_row: 結束行號 (1-based)
        """
        if source_sheet not in self.worksheets:
            raise ValueError(f"來源工作表 '{source_sheet}' 不存在")
            
        source_data = self.worksheets[source_sheet]
        
        # 轉換為 0-based index
        start_idx = max(0, start_row - 1)
        end_idx = min(len(source_data), end_row)
        
        if start_idx >= end_idx:
            logger.warning(f"無效的行範圍: {start_row}-{end_row}")
            return
            
        copied_data = source_data.iloc[start_idx:end_idx].copy()
        
        if target_sheet not in self.worksheets:
            self.worksheets[target_sheet] = pd.DataFrame()
            if target_sheet not in self.worksheet_order:
                self.worksheet_order.append(target_sheet)
            
        # 將資料添加到目標工作表
        if self.worksheets[target_sheet].empty:
            self.worksheets[target_sheet] = copied_data
        else:
            self.worksheets[target_sheet] = pd.concat([
                self.worksheets[target_sheet], 
                copied_data
            ], ignore_index=True)
            
        logger.debug(f"已複製 {len(copied_data)} 行從 '{source_sheet}' 到 '{target_sheet}'")
    
    def delete_rows(self, sheet_name: str, start_row: int, end_row: int) -> None:
        """
        刪[EXCEPT_CHAR]行範圍 - 對應 VBA: Rows().Delete Shift:=xlUp
        
        Args:
            sheet_name: 工作表名稱
            start_row: 開始行號 (1-based)
            end_row: 結束行號 (1-based)
        """
        if sheet_name not in self.worksheets:
            logger.warning(f"工作表 '{sheet_name}' 不存在")
            return
            
        df = self.worksheets[sheet_name]
        
        # 轉換為 0-based index
        start_idx = max(0, start_row - 1)
        end_idx = min(len(df), end_row)
        
        if start_idx >= end_idx:
            logger.warning(f"無效的刪[EXCEPT_CHAR]範圍: {start_row}-{end_row}")
            return
            
        # 刪[EXCEPT_CHAR]指定行範圍
        df.drop(df.index[start_idx:end_idx], inplace=True)
        df.reset_index(drop=True, inplace=True)
        
        logger.debug(f"已刪[EXCEPT_CHAR]工作表 '{sheet_name}' 的行 {start_row}-{end_row}")
    
    def move_worksheet(self, sheet_name: str, position: str, 
                      reference: Optional[str] = None) -> None:
        """
        移動工作表 - 對應 VBA: Sheets().Move Before/After
        
        Args:
            sheet_name: 要移動的工作表名稱
            position: 移動位置 ('first', 'before', 'after')
            reference: 參考工作表名稱
        """
        if sheet_name not in self.worksheet_order:
            logger.warning(f"工作表 '{sheet_name}' 不存在於順序中")
            return
            
        # 移[EXCEPT_CHAR]原位置
        self.worksheet_order.remove(sheet_name)
        
        # 插入新位置
        if position == 'first':
            self.worksheet_order.insert(0, sheet_name)
        elif position == 'before' and reference and reference in self.worksheet_order:
            idx = self.worksheet_order.index(reference)
            self.worksheet_order.insert(idx, sheet_name)
        elif position == 'after' and reference and reference in self.worksheet_order:
            idx = self.worksheet_order.index(reference)
            self.worksheet_order.insert(idx + 1, sheet_name)
        else:
            self.worksheet_order.append(sheet_name)
            
        logger.debug(f"工作表 '{sheet_name}' 已移動到位置: {position}")
    
    def get_worksheet(self, name_or_index: Union[str, int]) -> Optional[pd.DataFrame]:
        """
        獲取工作表 - 對應 VBA: Sheets(name) 或 Sheets(index)
        
        Args:
            name_or_index: 工作表名稱或索引 (1-based)
            
        Returns:
            工作表 DataFrame 或 None
        """
        if isinstance(name_or_index, str):
            return self.worksheets.get(name_or_index)
        elif isinstance(name_or_index, int):
            # 轉換為 0-based index
            idx = name_or_index - 1
            if 0 <= idx < len(self.worksheet_order):
                sheet_name = self.worksheet_order[idx]
                return self.worksheets.get(sheet_name)
        return None
    
    def get_cell_value(self, sheet_name: str, row: int, col: int) -> str:
        """
        獲取儲存格值 - 對應 VBA: Sheets().Cells(row, col).Value
        
        Args:
            sheet_name: 工作表名稱
            row: 行號 (1-based)
            col: 列號 (1-based)
            
        Returns:
            儲存格內容字串
        """
        try:
            if sheet_name not in self.worksheets:
                return ""
                
            df = self.worksheets[sheet_name]
            
            # 轉換為 0-based index
            row_idx = row - 1
            col_idx = col - 1
            
            if row_idx >= len(df) or col_idx >= len(df.columns):
                return ""
                
            value = df.iloc[row_idx, col_idx]
            return str(value).strip() if pd.notna(value) else ""
            
        except Exception as e:
            logger.debug(f"獲取儲存格值失敗: {e}")
            return ""
    
    def set_cell_value(self, sheet_name: str, row: int, col: int, value: str) -> None:
        """
        設定儲存格值 - 對應 VBA: Sheets().Cells(row, col).Value = value
        
        Args:
            sheet_name: 工作表名稱
            row: 行號 (1-based)
            col: 列號 (1-based)
            value: 設定的值
        """
        try:
            if sheet_name not in self.worksheets:
                logger.warning(f"工作表 '{sheet_name}' 不存在")
                return
                
            df = self.worksheets[sheet_name]
            
            # 轉換為 0-based index
            row_idx = row - 1
            col_idx = col - 1
            
            # 確保 DataFrame 大小足夠
            while row_idx >= len(df):
                # 添加新行
                new_row = pd.Series([''] * len(df.columns), index=df.columns)
                df = pd.concat([df, new_row.to_frame().T], ignore_index=True)
                
            while col_idx >= len(df.columns):
                # 添加新列
                df[f'Col_{len(df.columns)}'] = ''
                
            df.iloc[row_idx, col_idx] = value
            self.worksheets[sheet_name] = df
            
        except Exception as e:
            logger.error(f"設定儲存格值失敗: {e}")
    
    def count_worksheets(self) -> int:
        """
        工作表數量 - 對應 VBA: Sheets.Count
        """
        return len(self.worksheet_order)
    
    def clear_data(self, sheet_name: str, start_row: int, end_row: int) -> None:
        """
        清[EXCEPT_CHAR]資料 - 對應 VBA: Sheets().Rows().Clear
        
        Args:
            sheet_name: 工作表名稱
            start_row: 開始行號 (1-based)
            end_row: 結束行號 (1-based)
        """
        if sheet_name not in self.worksheets:
            return
            
        df = self.worksheets[sheet_name]
        
        # 轉換為 0-based index
        start_idx = max(0, start_row - 1)
        end_idx = min(len(df), end_row)
        
        if start_idx < end_idx:
            # 清空指定行的內容
            for idx in range(start_idx, end_idx):
                if idx < len(df):
                    df.iloc[idx] = ''
                    
        logger.debug(f"已清[EXCEPT_CHAR]工作表 '{sheet_name}' 的行 {start_row}-{end_row}")
    
    def get_worksheet_names(self) -> List[str]:
        """獲取所有工作表名稱"""
        return self.worksheet_order.copy()
    
    def rename_worksheet(self, old_name: str, new_name: str) -> bool:
        """
        重命名工作表
        
        Args:
            old_name: 舊名稱
            new_name: 新名稱
            
        Returns:
            是否成功重命名
        """
        if old_name not in self.worksheets:
            return False
            
        if new_name in self.worksheets:
            logger.warning(f"工作表名稱 '{new_name}' 已存在")
            return False
            
        # 重命名
        self.worksheets[new_name] = self.worksheets.pop(old_name)
        
        # 更新順序
        idx = self.worksheet_order.index(old_name)
        self.worksheet_order[idx] = new_name
        
        logger.debug(f"工作表已重命名: '{old_name}' -> '{new_name}'")
        return True