"""
附件相關 API
處理附件的上傳、下載和管理
"""

from flask import Blueprint, jsonify, request, send_file
from pathlib import Path
from werkzeug.utils import secure_filename

from src.infrastructure.adapters.attachments.attachment_manager import AttachmentManager
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.logging.logger_manager import LoggerManager


attachment_bp = Blueprint('attachment', __name__, url_prefix='/api/attachments')
logger = LoggerManager().get_logger("AttachmentAPI")
attachment_manager = AttachmentManager()
database = EmailDatabase()


@attachment_bp.route('/<int:attachment_id>/download', methods=['GET'])
def download_attachment(attachment_id: int):
    """下載附件"""
    try:
        # 獲取附件資訊
        attachment = database.get_attachment_by_id(attachment_id)
        if not attachment:
            return jsonify({
                'success': False, 
                'error': '附件不存在'
            }), 404
            
        # 檢查檔案是否存在
        if not attachment.file_path:
            return jsonify({
                'success': False, 
                'error': '附件檔案路徑未設定'
            }), 404
            
        file_path = Path(attachment.file_path)
        if not file_path.exists():
            return jsonify({
                'success': False, 
                'error': '附件檔案不存在'
            }), 404
            
        # 安全檢查
        allowed_base_paths = [
            attachment_manager.base_path.resolve(),
            Path("attachments").resolve(),
            Path("data/attachments").resolve()
        ]
        
        file_path = file_path.resolve()
        is_safe = any(
            str(file_path).startswith(str(base_path))
            for base_path in allowed_base_paths
            if base_path.exists()
        )
        
        if not is_safe:
            logger.warning(f"嘗試下載不安全的檔案路徑: {file_path}")
            return jsonify({
                'success': False, 
                'error': '檔案路徑不安全'
            }), 403
            
        # 清理檔名
        safe_filename = secure_filename(attachment.filename)
        if not safe_filename:
            safe_filename = f"attachment_{attachment_id}"
            
        # 記錄下載日誌
        logger.info(f"下載附件: {attachment.filename} (ID: {attachment_id})")
        
        # 提供檔案下載
        return send_file(
            file_path,
            as_attachment=True,
            download_name=safe_filename,
            mimetype=attachment.content_type
        )
        
    except Exception as e:
        logger.error(f"下載附件失敗: {e}")
        return jsonify({
            'success': False, 
            'error': '下載失敗'
        }), 500


@attachment_bp.route('/email/<int:email_id>', methods=['GET'])
def get_email_attachments(email_id: int):
    """取得郵件的所有附件"""
    try:
        # 從資料庫取得附件資訊
        from src.infrastructure.adapters.database.models import AttachmentDB
        
        with database.get_session() as session:
            attachments = session.query(AttachmentDB).filter_by(
                email_id=email_id
            ).all()
            
            result = []
            for attachment in attachments:
                # 檢查檔案是否存在
                file_exists = False
                if attachment.file_path:
                    file_path = Path(attachment.file_path)
                    file_exists = file_path.exists()
                    
                result.append({
                    'id': attachment.id,
                    'filename': attachment.filename,
                    'content_type': attachment.content_type,
                    'size_bytes': attachment.size_bytes,
                    'file_path': attachment.file_path,
                    'file_exists': file_exists,
                    'is_processed': attachment.is_processed,
                    'created_at': attachment.created_at.isoformat()
                })
                
        return jsonify({
            'success': True,
            'data': result,
            'count': len(result)
        })
        
    except Exception as e:
        logger.error(f"取得附件列表失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@attachment_bp.route('/<int:attachment_id>', methods=['DELETE'])
def delete_attachment(attachment_id: int):
    """刪[EXCEPT_CHAR]附件"""
    try:
        # 取得附件資訊
        attachment = database.get_attachment_by_id(attachment_id)
        if not attachment:
            return jsonify({
                'success': False,
                'error': '附件不存在'
            }), 404
            
        # 刪[EXCEPT_CHAR]檔案
        if attachment.file_path:
            file_path = Path(attachment.file_path)
            if file_path.exists():
                try:
                    file_path.unlink()
                    logger.info(f"已刪[EXCEPT_CHAR]附件檔案: {file_path}")
                except Exception as e:
                    logger.error(f"刪[EXCEPT_CHAR]附件檔案失敗: {e}")
                    
        # 從資料庫刪[EXCEPT_CHAR]記錄
        from src.infrastructure.adapters.database.models import AttachmentDB
        
        with database.get_session() as session:
            attachment_db = session.query(AttachmentDB).filter_by(
                id=attachment_id
            ).first()
            
            if attachment_db:
                session.delete(attachment_db)
                session.commit()
                
        return jsonify({
            'success': True,
            'message': '附件已刪[EXCEPT_CHAR]'
        })
        
    except Exception as e:
        logger.error(f"刪[EXCEPT_CHAR]附件失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@attachment_bp.route('/cleanup', methods=['POST'])
def cleanup_attachments():
    """清理舊附件"""
    try:
        data = request.get_json() or {}
        days = data.get('days', 30)
        
        # 清理舊附件
        result = attachment_manager.cleanup_old_attachments(days)
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"清理附件失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@attachment_bp.route('/statistics', methods=['GET'])
def get_attachment_statistics():
    """取得附件統計資訊"""
    try:
        stats = attachment_manager.get_statistics()
        
        # 添加資料庫統計
        from src.infrastructure.adapters.database.models import AttachmentDB
        
        with database.get_session() as session:
            total_db_attachments = session.query(AttachmentDB).count()
            processed_attachments = session.query(AttachmentDB).filter_by(
                is_processed=True
            ).count()
            
            # 依類型統計
            type_stats = session.query(
                AttachmentDB.content_type,
                session.query(AttachmentDB).count()
            ).group_by(AttachmentDB.content_type).all()
            
        stats['database_stats'] = {
            'total_attachments': total_db_attachments,
            'processed_attachments': processed_attachments,
            'unprocessed_attachments': total_db_attachments - processed_attachments,
            'type_distribution': dict(type_stats) if type_stats else {}
        }
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"取得附件統計失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500