<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT Summary 批量處理系統</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- 載入模組化 CSS -->
    <link rel="stylesheet" href="../static/css/variables.css">
    <link rel="stylesheet" href="../static/css/base.css">
    <link rel="stylesheet" href="../static/css/components.css">
    <link rel="stylesheet" href="../static/css/layout.css">
    <link rel="stylesheet" href="../static/css/special-components.css">
    <link rel="stylesheet" href="../static/css/responsive.css">
    
    <style>
        /* FT Summary 專用樣式 */
        .ft-summary-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .ft-summary-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 20px;
        }
        
        .ft-summary-header {
            text-align: center;
            margin-bottom: 30px;
            color: var(--secondary-color);
        }
        
        .ft-summary-header h1 {
            font-size: 2.2em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }
        
        .ft-summary-input-group {
            margin-bottom: 20px;
        }
        
        .ft-summary-input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .ft-summary-path-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .ft-summary-path-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(116, 75, 162, 0.1);
        }
        
        .ft-summary-options {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .ft-summary-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 10px;
        }
        
        .ft-summary-btn {
            width: 100%;
            padding: 15px 20px;
            background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .ft-summary-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(116, 75, 162, 0.3);
        }
        
        .ft-summary-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .ft-summary-result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        
        .ft-summary-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .ft-summary-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .ft-summary-progress {
            margin-top: 15px;
            display: none;
        }
        
        .ft-summary-progress-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .ft-summary-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color) 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .ft-summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        
        .ft-summary-stat-item {
            text-align: center;
            padding: 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        
        .ft-summary-stat-value {
            font-size: 1.5em;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .ft-summary-stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 4px;
        }
        
        .ft-summary-download {
            margin-top: 15px;
            text-align: center;
        }
        
        .ft-summary-download-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: background-color 0.3s ease;
        }
        
        .ft-summary-download-btn:hover {
            background: #218838;
            color: white;
        }
        
        /* 檔案清單樣式 */
        .ft-summary-file-lists {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .ft-summary-file-list {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .ft-summary-file-list h5 {
            margin: 0 0 10px 0;
            color: var(--primary-color);
            font-size: 1.1em;
            font-weight: 600;
        }
        
        .ft-summary-file-items {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .ft-summary-file-item {
            padding: 8px 12px;
            background: #f8f9fa;
            margin: 4px 0;
            border-radius: 4px;
            font-size: 0.9em;
            word-break: break-all;
            border-left: 3px solid var(--primary-color);
            transition: background-color 0.2s ease;
        }
        
        .ft-summary-file-item:hover {
            background: #e9ecef;
        }
        
        .ft-summary-file-item.empty {
            color: #6c757d;
            font-style: italic;
            border-left: 3px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="ft-summary-container">
        <div class="ft-summary-card">
            <div class="ft-summary-header">
                <h1>
                    <i class="fas fa-chart-line"></i>
                    FT Summary 批量處理
                </h1>
                <p style="color: #6c757d; margin: 0;">
                    輸入資料夾路徑，自動掃描 CSV 檔案並生成 FT_SUMMARY.csv
                </p>
                <div id="modeIndicator" style="margin-top: 10px; padding: 8px 12px; border-radius: 6px; font-size: 0.9em; display: none;">
                    <i class="fas fa-info-circle"></i>
                    <span id="modeText">檢查中...</span>
                </div>
            </div>
            
            <form id="ftSummaryForm">
                <div class="ft-summary-input-group">
                    <label for="folderPath">
                        <i class="fas fa-folder-open"></i> 資料夾路徑
                    </label>
                    <input 
                        type="text" 
                        id="folderPath" 
                        class="ft-summary-path-input"
                        placeholder="D:\project\python\outlook_summary\doc\20250523"
                        value="D:\project\python\outlook_summary\doc\20250523"
                        required
                    >
                    <small style="color: #6c757d; margin-top: 8px; display: block;">
                        💡 支援 Windows 路徑自動轉換：
                        <code>D:\path</code> → <code>/mnt/d/path</code>
                    </small>
                </div>
                
                <div class="ft-summary-input-group">
                    <label>
                        <i class="fas fa-cogs"></i> 處理模式
                    </label>
                    <div class="mode-selection" style="display: flex; gap: 20px; margin-top: 8px;">
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <input type="radio" id="modeXlsSummary" name="processing_mode" value="full" checked>
                            <label for="modeXlsSummary" style="margin: 0; font-weight: normal;">
                                完整模式（Excel + Summary）
                            </label>
                        </div>
                        <div style="display: flex; align-items: center; gap: 6px;">
                            <input type="radio" id="modeSummaryOnly" name="processing_mode" value="summary_only">
                            <label for="modeSummaryOnly" style="margin: 0; font-weight: normal;">
                                快速模式（僅 Summary）
                            </label>
                        </div>
                    </div>
                    <small style="color: #6c757d; margin-top: 8px; display: block;">
                        💡 快速模式：跳過Excel轉換，僅生成Summary和最終整併檔案，處理速度更快
                    </small>
                </div>
                
                <button type="submit" class="ft-summary-btn" id="processBtn">
                    <i class="fas fa-play"></i>
                    開始批量處理
                </button>
            </form>
            
            <div class="ft-summary-progress" id="progressContainer">
                <div class="ft-summary-progress-bar">
                    <div class="ft-summary-progress-fill" id="progressFill"></div>
                </div>
                <div id="progressText" style="text-align: center; font-size: 0.9em; color: #6c757d;">
                    處理中...
                </div>
            </div>
            
            <div class="ft-summary-result" id="resultContainer">
                <div id="resultMessage"></div>
                <div class="ft-summary-stats" id="statsContainer" style="display: none;">
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="totalFiles">0</div>
                        <div class="ft-summary-stat-label">總檔案數</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="processedFiles">0</div>
                        <div class="ft-summary-stat-label">處理成功</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="skippedFiles">0</div>
                        <div class="ft-summary-stat-label">跳過檔案</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="ftSummaryFiles">0</div>
                        <div class="ft-summary-stat-label">FT Summary</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="eqcSummaryFiles">0</div>
                        <div class="ft-summary-stat-label">EQC Summary</div>
                    </div>
                    <div class="ft-summary-stat-item">
                        <div class="ft-summary-stat-value" id="processingTime">0s</div>
                        <div class="ft-summary-stat-label">處理時間</div>
                    </div>
                </div>
                
                <!-- 檔案清單顯示區域 -->
                <div class="ft-summary-file-lists" id="fileListContainer" style="display: none;">
                    <div class="ft-summary-file-list">
                        <h5><i class="fas fa-file-csv"></i> FT 檔案清單</h5>
                        <div class="ft-summary-file-items" id="ftFileList">
                            <div class="ft-summary-file-item">無 FT 檔案</div>
                        </div>
                    </div>
                    <div class="ft-summary-file-list">
                        <h5><i class="fas fa-file-csv"></i> EQC 檔案清單</h5>
                        <div class="ft-summary-file-items" id="eqcFileList">
                            <div class="ft-summary-file-item">無 EQC 檔案</div>
                        </div>
                    </div>
                </div>
                <div class="ft-summary-download" id="downloadContainer" style="display: none;">
                    <a href="#" class="ft-summary-download-btn" id="downloadBtn">
                        <i class="fas fa-download"></i>
                        下載 FT_SUMMARY.csv
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="../static/js/ft-summary-processor.js"></script>
</body>
</html>