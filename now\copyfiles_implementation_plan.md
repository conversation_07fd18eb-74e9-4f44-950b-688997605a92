# CopyFiles 功能實作計畫

## 背景說明
根據 VBA 功能對照表，系統缺少 CopyFilesGTK() 等檔案處理功能。這些功能用於在解析郵件後，從指定目錄複製相關測試資料檔案到臨時處理目錄。

## 功能需求分析

### VBA 原始功能
- **CopyFilesGTK()** - GTK 廠商檔案處理
- 可能還有其他廠商的 CopyFiles 函數（如 CopyFilesETD、CopyFilesJCET 等）
- 功能：根據解析結果，從網路磁碟或本地目錄複製測試資料到臨時目錄

### 使用場景
1. 郵件解析完成，取得 MO、LOT 等資訊
2. 根據這些資訊，到指定目錄尋找相關檔案
3. 將找到的檔案複製到臨時目錄供後續處理
4. 通常是 CSV、Excel 或其他測試資料檔案

## 建議實作架構

### 1. 建立檔案處理器基礎架構
```python
# src/infrastructure/adapters/file_handlers/__init__.py
# src/infrastructure/adapters/file_handlers/base_handler.py
# src/infrastructure/adapters/file_handlers/gtk_file_handler.py
# src/infrastructure/adapters/file_handlers/etd_file_handler.py
```

### 2. 基礎檔案處理器介面
```python
# base_handler.py
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional
import tempfile
import shutil

class BaseFileHandler(ABC):
    """檔案處理器基礎類別"""
    
    def __init__(self, source_directory: Path, temp_directory: Optional[Path] = None):
        self.source_directory = Path(source_directory)
        self.temp_directory = temp_directory or Path(tempfile.mkdtemp())
        self.logger = LoggerManager().get_logger(self.__class__.__name__)
        
    @abstractmethod
    def find_files(self, mo_number: str, lot_number: str, **kwargs) -> List[Path]:
        """根據 MO/LOT 尋找相關檔案"""
        pass
        
    @abstractmethod
    def validate_files(self, files: List[Path]) -> List[Path]:
        """驗證檔案是否符合處理條件"""
        pass
        
    def copy_files(self, mo_number: str, lot_number: str, **kwargs) -> Dict[str, Any]:
        """
        主要處理方法：尋找並複製檔案
        
        Returns:
            {
                'success': bool,
                'copied_files': List[str],  # 複製的檔案路徑
                'temp_directory': str,       # 臨時目錄路徑
                'errors': List[str]          # 錯誤訊息
            }
        """
        try:
            # 1. 尋找檔案
            files = self.find_files(mo_number, lot_number, **kwargs)
            
            if not files:
                return {
                    'success': False,
                    'copied_files': [],
                    'temp_directory': str(self.temp_directory),
                    'errors': [f'找不到相關檔案: MO={mo_number}, LOT={lot_number}']
                }
            
            # 2. 驗證檔案
            valid_files = self.validate_files(files)
            
            # 3. 複製檔案
            copied_files = []
            errors = []
            
            for file_path in valid_files:
                try:
                    dest_path = self.temp_directory / file_path.name
                    shutil.copy2(file_path, dest_path)
                    copied_files.append(str(dest_path))
                    self.logger.info(f"已複製檔案: {file_path} -> {dest_path}")
                except Exception as e:
                    error_msg = f"複製檔案失敗 {file_path}: {e}"
                    errors.append(error_msg)
                    self.logger.error(error_msg)
                    
            return {
                'success': len(copied_files) > 0,
                'copied_files': copied_files,
                'temp_directory': str(self.temp_directory),
                'errors': errors
            }
            
        except Exception as e:
            self.logger.error(f"處理檔案時發生錯誤: {e}")
            return {
                'success': False,
                'copied_files': [],
                'temp_directory': str(self.temp_directory),
                'errors': [str(e)]
            }
            
    def cleanup(self):
        """清理臨時目錄"""
        try:
            if self.temp_directory.exists():
                shutil.rmtree(self.temp_directory)
                self.logger.info(f"已清理臨時目錄: {self.temp_directory}")
        except Exception as e:
            self.logger.error(f"清理臨時目錄失敗: {e}")
```

### 3. GTK 檔案處理器實作
```python
# gtk_file_handler.py
class GTKFileHandler(BaseFileHandler):
    """GTK 廠商檔案處理器"""
    
    def __init__(self, source_directory: Path, temp_directory: Optional[Path] = None):
        super().__init__(source_directory, temp_directory)
        # GTK 特定的檔案模式
        self.file_patterns = [
            "*{mo}*{lot}*.csv",
            "*{mo}*{lot}*.xlsx",
            "*{mo}*{lot}*.txt",
            "GTK*{mo}*.csv",
            "FT*{lot}*.csv"
        ]
        
    def find_files(self, mo_number: str, lot_number: str, **kwargs) -> List[Path]:
        """根據 MO/LOT 尋找 GTK 相關檔案"""
        found_files = []
        
        for pattern in self.file_patterns:
            # 替換模式中的變數
            glob_pattern = pattern.format(mo=mo_number, lot=lot_number)
            
            # 搜尋檔案
            for file_path in self.source_directory.rglob(glob_pattern):
                if file_path.is_file():
                    found_files.append(file_path)
                    
        # 去重
        found_files = list(set(found_files))
        
        self.logger.info(
            f"GTK 檔案搜尋: MO={mo_number}, LOT={lot_number}, "
            f"找到 {len(found_files)} 個檔案"
        )
        
        return found_files
        
    def validate_files(self, files: List[Path]) -> List[Path]:
        """驗證 GTK 檔案"""
        valid_files = []
        
        for file_path in files:
            # 檢查檔案大小（不能太大）
            if file_path.stat().st_size > 100 * 1024 * 1024:  # 100MB
                self.logger.warning(f"檔案過大，跳過: {file_path}")
                continue
                
            # 檢查副檔名
            if file_path.suffix.lower() not in ['.csv', '.xlsx', '.xls', '.txt']:
                self.logger.warning(f"不支援的檔案類型: {file_path}")
                continue
                
            valid_files.append(file_path)
            
        return valid_files
```

### 4. 整合到郵件同步服務
```python
# 修改 email_sync_service.py
def _process_parsed_email(self, email_id: int, parse_result: Dict[str, Any]):
    """處理已解析的郵件，包括檔案複製"""
    
    if not parse_result['success']:
        return
        
    vendor = parse_result.get('vendor')
    mo = parse_result.get('mo')
    lot = parse_result.get('lot')
    
    if vendor and mo and lot:
        # 根據廠商選擇檔案處理器
        file_handler = self._get_file_handler(vendor)
        
        if file_handler:
            # 複製相關檔案
            copy_result = file_handler.copy_files(mo, lot)
            
            if copy_result['success']:
                self.logger.info(
                    f"已複製 {len(copy_result['copied_files'])} 個檔案"
                )
                
                # 更新資料庫，記錄檔案路徑
                self._update_file_paths(email_id, copy_result)
            else:
                self.logger.warning(
                    f"檔案複製失敗: {copy_result['errors']}"
                )
                
def _get_file_handler(self, vendor: str) -> Optional[BaseFileHandler]:
    """根據廠商取得檔案處理器"""
    
    # 從環境變數或設定取得來源目錄
    source_dir = os.getenv(f'{vendor}_SOURCE_DIR', f'/mnt/share/{vendor}')
    
    handlers = {
        'GTK': GTKFileHandler,
        'ETD': ETDFileHandler,
        'JCET': JCETFileHandler,
        # ... 其他廠商
    }
    
    handler_class = handlers.get(vendor)
    if handler_class and Path(source_dir).exists():
        return handler_class(source_dir)
        
    return None
```

## 實作步驟

### Phase 1: 基礎架構（2天）
1. 建立 file_handlers 目錄結構
2. 實作 BaseFileHandler 抽象類別
3. 建立單元測試框架

### Phase 2: GTK 實作（2天）
1. 實作 GTKFileHandler
2. 測試檔案搜尋邏輯
3. 測試檔案複製功能

### Phase 3: 整合（1天）
1. 修改 email_sync_service.py
2. 添加設定檔支援
3. 整合測試

### Phase 4: 其他廠商（3天）
1. 實作 ETD、JCET 等其他廠商處理器
2. 根據實際需求調整搜尋模式

## 配置需求

### 環境變數
```bash
# .env
GTK_SOURCE_DIR=/mnt/share/GTK/TestData
ETD_SOURCE_DIR=/mnt/share/ETD/Reports
JCET_SOURCE_DIR=/mnt/share/JCET/Data

# 臨時目錄設定
FILE_HANDLER_TEMP_DIR=/tmp/email_processor
```

### 資料庫擴充
```sql
-- 添加檔案路徑記錄
ALTER TABLE emails ADD COLUMN related_files JSON;
ALTER TABLE emails ADD COLUMN files_copied_at TIMESTAMP;
```

## 預期效益

1. **自動化檔案處理** - 減少手動複製檔案的工作
2. **追蹤性** - 記錄哪些檔案與哪封郵件相關
3. **整合性** - 與現有郵件處理流程無縫整合
4. **擴展性** - 容易添加新廠商的檔案處理邏輯

## 風險與緩解

1. **網路磁碟存取** - 需要確保有適當的權限
2. **檔案大小** - 設定合理的檔案大小限制
3. **臨時空間** - 定期清理臨時目錄
4. **效能影響** - 使用非同步處理避免阻塞