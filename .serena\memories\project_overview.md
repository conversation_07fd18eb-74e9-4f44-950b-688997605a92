# Outlook Summary System - 專案概述

## 專案目的
這是一個 Python 郵件處理系統，主要目標是將現有 VBA Excel 郵件處理系統遷移至 Python。系統自動化處理來自多個半導體測試廠商（GTK, ETD, XAHT, JCET, LINGSEN 等）的郵件，包含解析、檔案管理和報表生成功能。

## 技術棧
- **後端**: Python 3.9+, FastAPI, Pydantic, SQLAlchemy, Pandas
- **前端**: HTML/CSS/JavaScript, Playwright (E2E測試)  
- **資料庫**: PostgreSQL (生產), SQLite (開發/測試)
- **測試**: Pytest, TDD 開發方法
- **程式碼品質**: Black, Flake8, MyPy

## 架構設計
採用**六角架構 (Hexagonal Architecture)**：
- **核心領域層**: 業務邏輯與規則
- **應用層**: 使用案例與工作流程
- **基礎設施層**: 外部整合（Outlook、檔案系統、資料庫）
- **展示層**: API、CLI、Web UI

## 主要功能
1. **郵件處理流程**: 監控Outlook、廠商識別、資料解析、檔案處理、報表生成
2. **CTA處理系統**: CSV到Excel轉換，動態欄位檢測，智慧數字轉換  
3. **EQC處理系統**: 一鍵完成處理，雙重搜尋機制
4. **FT-EQC分組**: 時間戳配對、格式檢測
5. **統計分析**: 廠商效能分析、趨勢分析、異常檢測