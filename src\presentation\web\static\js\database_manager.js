/**
 * 資料庫管理前端邏輯
 */

class DatabaseManager {
    constructor() {
        this.currentTable = null;
        this.dataTable = null;
        this.selectedRows = new Set();
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDatabaseInfo();
    }

    bindEvents() {
        // 表格選擇
        $('#table-select').on('change', (e) => {
            const tableName = e.target.value;
            if (tableName) {
                this.loadTableData(tableName);
            } else {
                this.clearTableView();
            }
        });

        // 重新整理按鈕
        $('#refresh-btn').on('click', () => {
            if (this.currentTable) {
                this.loadTableData(this.currentTable);
            }
        });

        // 匯出 CSV 按鈕
        $('#export-csv-btn').on('click', () => {
            if (this.currentTable) {
                this.exportTableToCsv(this.currentTable);
            }
        });

        // 執行查詢按鈕
        $('#execute-query-btn').on('click', () => {
            this.executeQuery();
        });
        
        // 批量刪除按鈕
        $('#batch-delete-btn').on('click', () => {
            this.batchDelete();
        });
    }

    async loadDatabaseInfo() {
        try {
            const response = await fetch('/api/database/info');
            const result = await response.json();

            if (result.success) {
                const data = result.data;
                // 顯示資料庫大小
                $('#db-size').text(this.formatBytes(data.db_size));
                
                // 顯示表格記錄數
                for (const [table, count] of Object.entries(data.tables)) {
                    $(`#table-select option[value="${table}"]`).text(`${table} - ${this.getTableDisplayName(table)} (${count} 筆記錄)`);
                }
                
                // 自動選擇 emails 表
                $('#table-select').val('emails').trigger('change');
            } else {
                this.showError('載入資料庫資訊失敗');
            }
        } catch (error) {
            console.error('載入資料庫資訊失敗:', error);
            this.showError('載入資料庫資訊失敗');
        }
    }

    async loadTableData(tableName) {
        this.showLoading();
        this.currentTable = tableName;

        try {
            const response = await fetch(`/api/database/table/${tableName}`);
            const result = await response.json();

            if (result.success) {
                this.renderTable(result.data);
                $('#export-csv-btn').prop('disabled', false);
                $('#table-info').removeClass('hidden');
                $('#table-name').text(`${tableName} - ${this.getTableDisplayName(tableName)}`);
                $('#record-count').text(result.data.total);
                $('#column-count').text(result.data.columns.length);
            } else {
                this.showError('載入表格資料失敗: ' + result.error);
            }
        } catch (error) {
            console.error('載入表格資料失敗:', error);
            this.showError('載入表格資料失敗');
        } finally {
            this.hideLoading();
        }
    }

    renderTable(data) {
        // 清理舊表格
        if (this.dataTable) {
            this.dataTable.destroy();
            $('#data-table').empty();
        }
        
        // 儲存資料以供後續使用
        this.currentData = data;

        // 準備欄位配置
        const columns = [];
        
        // 添加選擇欄
        columns.push({
            data: null,
            title: '<input type="checkbox" id="select-all-rows" />',
            orderable: false,
            searchable: false,
            className: 'select-checkbox',
            render: (data, type, row) => {
                const id = row.id || row.ID || 'unknown';
                return `<input type="checkbox" class="row-select" data-id="${id}" />`;
            }
        });
        
        // 添加數據欄位
        columns.push(...data.columns.map(col => ({
            data: col.name,
            title: this.getColumnDisplayName(col.name),
            defaultContent: '',
            render: (value, type, row) => {
                if (value === null || value === undefined) {
                    return '<span class="null-value">NULL</span>';
                }
                // 處理長文本 - 根據欄位類型調整截斷長度
                if (type === 'display' && typeof value === 'string') {
                    let maxLength = 50;
                    
                    // 根據欄位名稱調整截斷長度
                    if (col.name === 'subject') {
                        maxLength = 80;  // 主旨可以長一點
                    } else if (col.name === 'body' || col.name === 'content') {
                        maxLength = 100; // 內容欄位稍微長一點
                    } else if (col.name === 'sender' || col.name === 'email_address') {
                        maxLength = 40;  // 郵件地址適中
                    }
                    
                    if (value.length > maxLength) {
                        return `<span title="${this.escapeHtml(value)}" style="max-width: ${maxLength * 8}px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;">${this.escapeHtml(value.substring(0, maxLength))}...</span>`;
                    }
                }
                return this.escapeHtml(value);
            }
        })));

        // 添加操作欄
        columns.push({
            data: null,
            title: '操作',
            orderable: false,
            render: (data, type, row) => {
                const id = row.id || row.ID || 'unknown';
                return `
                    <button class="btn-view-detail" data-id="${id}">查看</button>
                    <button class="btn-delete-row" data-id="${id}" data-table="${this.currentTable}">刪除</button>
                `;
            }
        });

        // 根據表格類型設定分頁
        let pageLength = 25;
        let lengthMenu = [[10, 25, 50, 100], [10, 25, 50, 100]];
        
        if (this.currentTable === 'email_process_status') {
            pageLength = 15;  // 處理狀態表格較少顯示
            lengthMenu = [[10, 15, 25, 50], [10, 15, 25, 50]];
        }
        
        // 初始化 DataTable
        this.dataTable = $('#data-table').DataTable({
            data: data.records,
            columns: columns,
            pageLength: pageLength,
            lengthMenu: lengthMenu,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/zh-HANT.json'
            },
            scrollX: true,
            autoWidth: false,
            drawCallback: () => {
                // 綁定查看按鈕事件
                $('.btn-view-detail').on('click', (e) => {
                    const id = $(e.target).attr('data-id');
                    
                    // 從原始資料中找到完整的記錄
                    const fullRow = this.currentData.records.find(record => 
                        (record.id || record.ID) == id
                    );
                    
                    if (fullRow) {
                        this.showDetailModal(fullRow);
                    } else {
                        this.showError('找不到記錄詳情');
                    }
                });
                
                // 綁定刪除按鈕事件
                $('.btn-delete-row').on('click', (e) => {
                    const id = $(e.target).attr('data-id');
                    const table = $(e.target).attr('data-table');
                    this.deleteRow(id, table);
                });
                
                // 綁定選擇事件
                $('#select-all-rows').on('change', (e) => {
                    const isChecked = e.target.checked;
                    $('.row-select').prop('checked', isChecked);
                    this.updateBatchActions();
                });
                
                $('.row-select').on('change', () => {
                    this.updateBatchActions();
                });
            }
        });
    }

    showDetailModal(row) {
        const modalBody = $('#modal-body');
        modalBody.empty();

        // 生成詳情內容
        const table = $('<table class="detail-table"></table>');
        for (const [key, value] of Object.entries(row)) {
            const displayKey = this.getColumnDisplayName(key);
            const displayValue = value === null ? '<span class="null-value">NULL</span>' : 
                               (typeof value === 'string' && value.length > 100 ? 
                                `<pre>${this.escapeHtml(value)}</pre>` : 
                                this.escapeHtml(value));
            
            table.append(`
                <tr>
                    <td class="detail-key">${displayKey}</td>
                    <td class="detail-value">${displayValue}</td>
                </tr>
            `);
        }
        modalBody.append(table);

        $('#modal-title').text(`${this.currentTable} 記錄詳情`);
        $('#detail-modal').removeClass('hidden');
    }

    async executeQuery() {
        const query = $('#sql-query').val().trim();
        if (!query) {
            this.showError('請輸入查詢語句');
            return;
        }

        this.showLoading();
        $('#query-error').addClass('hidden');

        try {
            const response = await fetch('/api/database/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ query })
            });

            const result = await response.json();

            if (result.success) {
                this.renderTable(result.data);
                $('#table-info').removeClass('hidden');
                $('#table-name').text('查詢結果');
                $('#record-count').text(result.data.total);
                $('#column-count').text(result.data.columns.length);
                $('#export-csv-btn').prop('disabled', true); // 查詢結果不支援匯出
            } else {
                $('#query-error').text(result.error).removeClass('hidden');
            }
        } catch (error) {
            console.error('執行查詢失敗:', error);
            $('#query-error').text('執行查詢失敗').removeClass('hidden');
        } finally {
            this.hideLoading();
        }
    }

    exportTableToCsv(tableName) {
        window.location.href = `/api/database/export/${tableName}`;
    }

    clearTableView() {
        if (this.dataTable) {
            this.dataTable.destroy();
            $('#data-table').empty();
        }
        $('#table-info').addClass('hidden');
        $('#export-csv-btn').prop('disabled', true);
        this.currentTable = null;
    }

    showLoading() {
        $('#loading').removeClass('hidden');
    }

    hideLoading() {
        $('#loading').addClass('hidden');
    }

    showError(message) {
        alert(message); // 可以改為更好看的提示
    }

    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    escapeHtml(text) {
        if (text === null || text === undefined) return '';
        return String(text)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    getTableDisplayName(tableName) {
        const names = {
            'emails': '郵件',
            'senders': '寄件者',
            'attachments': '附件',
            'email_process_status': '處理狀態'
        };
        return names[tableName] || tableName;
    }

    getColumnDisplayName(columnName) {
        const names = {
            'id': 'ID',
            'message_id': '郵件 ID',
            'sender': '寄件者',
            'sender_display_name': '寄件者名稱',
            'subject': '主旨',
            'body': '內容',
            'received_time': '接收時間',
            'created_at': '創建時間',
            'updated_at': '更新時間',
            'is_read': '已讀',
            'is_processed': '已處理',
            'has_attachments': '有附件',
            'attachment_count': '附件數',
            'email_address': '郵件地址',
            'display_name': '顯示名稱',
            'total_emails': '郵件總數',
            'last_email_time': '最後郵件時間',
            'first_email_time': '首封郵件時間',
            'email_id': '郵件 ID',
            'filename': '檔名',
            'content_type': '內容類型',
            'size_bytes': '大小 (位元組)',
            'file_path': '檔案路徑',
            'checksum': '校驗碼',
            'step_name': '步驟名稱',
            'status': '狀態',
            'started_at': '開始時間',
            'completed_at': '完成時間',
            'error_message': '錯誤訊息',
            'output_files': '輸出檔案',
            'progress_percentage': '進度百分比'
        };
        return names[columnName] || columnName;
    }
    
    /**
     * 更新批量操作按鈕狀態
     */
    updateBatchActions() {
        const selectedCount = $('.row-select:checked').length;
        
        if (selectedCount > 0) {
            // 顯示批量操作面板
            this.showBatchActionsPanel(selectedCount);
        } else {
            // 隱藏批量操作面板
            this.hideBatchActionsPanel();
        }
    }
    
    /**
     * 顯示批量操作面板
     */
    showBatchActionsPanel(selectedCount) {
        // 移除舊的批量操作面板
        $('.batch-actions-panel').remove();
        
        // 創建批量操作面板
        const panel = $(`
            <div class="batch-actions-panel">
                <div class="batch-info">
                    已選取 <strong>${selectedCount}</strong> 筆記錄
                </div>
                <div class="batch-actions">
                    <button id="batch-delete-btn" class="btn btn-danger">
                        🗑️ 刪除選取項目
                    </button>
                    <button id="clear-selection-btn" class="btn btn-secondary">
                        ❌ 清除選取
                    </button>
                </div>
            </div>
        `);
        
        // 添加樣式
        panel.css({
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            background: '#fff',
            border: '1px solid #ddd',
            borderRadius: '8px',
            padding: '15px',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            zIndex: 1000,
            display: 'flex',
            alignItems: 'center',
            gap: '15px'
        });
        
        $('body').append(panel);
        
        // 綁定事件
        $('#batch-delete-btn').on('click', () => {
            this.batchDelete();
        });
        
        $('#clear-selection-btn').on('click', () => {
            this.clearSelection();
        });
    }
    
    /**
     * 隱藏批量操作面板
     */
    hideBatchActionsPanel() {
        $('.batch-actions-panel').remove();
    }
    
    /**
     * 清除選取
     */
    clearSelection() {
        $('.row-select').prop('checked', false);
        $('#select-all-rows').prop('checked', false);
        this.hideBatchActionsPanel();
    }
    
    /**
     * 刪除單筆記錄
     */
    async deleteRow(id, tableName) {
        if (!confirm(`確定要刪除這筆記錄嗎？\n\n表格: ${this.getTableDisplayName(tableName)}\nID: ${id}`)) {
            return;
        }
        
        try {
            const response = await fetch(`/api/database/delete/${tableName}/${id}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage('記錄已刪除');
                this.loadTableData(tableName); // 重新載入表格
            } else {
                this.showError('刪除失敗: ' + result.error);
            }
        } catch (error) {
            console.error('刪除記錄失敗:', error);
            this.showError('刪除失敗');
        }
    }
    
    /**
     * 批量刪除記錄
     */
    async batchDelete() {
        const selectedIds = [];
        $('.row-select:checked').each((index, element) => {
            selectedIds.push($(element).attr('data-id'));
        });
        
        if (selectedIds.length === 0) {
            this.showError('請選取要刪除的記錄');
            return;
        }
        
        const tableName = this.currentTable;
        const confirmMessage = `確定要刪除這 ${selectedIds.length} 筆記錄嗎？\n\n表格: ${this.getTableDisplayName(tableName)}\n\n此操作無法復原！`;
        
        if (!confirm(confirmMessage)) {
            return;
        }
        
        this.showLoading();
        
        try {
            const response = await fetch(`/api/database/batch-delete/${tableName}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ ids: selectedIds })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showSuccessMessage(`成功刪除 ${selectedIds.length} 筆記錄`);
                this.hideBatchActionsPanel();
                this.loadTableData(tableName); // 重新載入表格
            } else {
                this.showError('批量刪除失敗: ' + result.error);
            }
        } catch (error) {
            console.error('批量刪除失敗:', error);
            this.showError('批量刪除失敗');
        } finally {
            this.hideLoading();
        }
    }
    
    /**
     * 顯示成功訊息
     */
    showSuccessMessage(message) {
        // 移除舊的訊息
        $('.success-message').remove();
        
        const messageDiv = $(`
            <div class="success-message" style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #4CAF50;
                color: white;
                padding: 15px 20px;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                z-index: 1001;
            ">
                ✅ ${message}
            </div>
        `);
        
        $('body').append(messageDiv);
        
        // 3秒後自動移除
        setTimeout(() => {
            messageDiv.fadeOut(() => {
                messageDiv.remove();
            });
        }, 3000);
    }
}

// 全域函數
function closeDetailModal() {
    $('#detail-modal').addClass('hidden');
}

// 初始化
$(document).ready(() => {
    new DatabaseManager();
});