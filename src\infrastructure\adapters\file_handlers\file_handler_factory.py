"""
檔案處理器工廠
用於建立和管理各廠商的檔案處理器
"""

from typing import Optional, Dict, Type, List
import os

from .base_file_handler import BaseFileHandler
from .gtk_file_handler import GTKFileHandler
from .xaht_file_handler import XAHTFileHandler
from .jcet_file_handler import J<PERSON><PERSON>ileHandler
from .nfme_file_handler import NFMEFileHandler
from .etd_file_handler import ETDFileHandler

from src.infrastructure.logging.logger_manager import LoggerManager


class FileHandlerFactory:
    """
    檔案處理器工廠
    
    負責：
    1. 註冊所有支援的廠商處理器
    2. 根據廠商代碼建立處理器實例
    3. 管理來源路徑設定
    """
    
    # 註冊的處理器類別
    _handlers: Dict[str, Type[BaseFileHandler]] = {
        'GTK': GTKFileHandler,
        'XAHT': XAHTFileHandler,
        'JCET': JCETFileHandler,
        'NFME': NFMEFileHandler,
        'ETD': ETDFileHandler,
        'ETREND': ETDFileHandler,  # ETD 的別名
    }
    
    def __init__(self, source_base_path: Optional[str] = None):
        """
        初始化工廠
        
        Args:
            source_base_path: 來源基礎路徑，如果未提供則從環境變數讀取
        """
        self.logger = LoggerManager().get_logger("FileHandlerFactory")
        
        # 設定來源基礎路徑
        if source_base_path:
            self.source_base_path = source_base_path
        else:
            # 從環境變數讀取，預設為 /mnt/share
            self.source_base_path = os.getenv('FILE_SOURCE_BASE_PATH', '/mnt/share')
            
        self.logger.info(f"檔案處理器工廠已初始化，來源路徑: {self.source_base_path}")
        
    @classmethod
    def register_handler(cls, vendor_code: str, handler_class: Type[BaseFileHandler]):
        """
        註冊新的檔案處理器
        
        Args:
            vendor_code: 廠商代碼
            handler_class: 處理器類別
        """
        cls._handlers[vendor_code.upper()] = handler_class
        
    def create_handler(self, vendor_code: str) -> Optional[BaseFileHandler]:
        """
        建立檔案處理器
        
        Args:
            vendor_code: 廠商代碼
            
        Returns:
            檔案處理器實例，如果廠商不支援則返回 None
        """
        vendor_code_upper = vendor_code.upper()
        handler_class = self._handlers.get(vendor_code_upper)
        
        if handler_class:
            try:
                handler = handler_class(self.source_base_path)
                self.logger.info(f"已建立 {vendor_code_upper} 檔案處理器")
                return handler
            except Exception as e:
                self.logger.error(f"建立 {vendor_code_upper} 處理器失敗: {e}")
                return None
        else:
            self.logger.warning(f"不支援的廠商代碼: {vendor_code}")
            return None
            
    @classmethod
    def get_supported_vendors(cls) -> List[str]:
        """取得支援的廠商列表"""
        return list(cls._handlers.keys())
        
    def process_vendor_files(self, vendor_code: str, mo: str, 
                           temp_path: str, pd: str = "default", 
                           lot: str = "default") -> Dict[str, any]:
        """
        處理廠商檔案的便利方法
        
        Args:
            vendor_code: 廠商代碼
            mo: MO 編號
            temp_path: 暫存路徑
            pd: 產品名稱
            lot: 批號
            
        Returns:
            處理結果字典
        """
        result = {
            'success': False,
            'vendor': vendor_code,
            'mo': mo,
            'temp_path': temp_path,
            'error': None
        }
        
        # 建立處理器
        handler = self.create_handler(vendor_code)
        if not handler:
            result['error'] = f"不支援的廠商: {vendor_code}"
            return result
            
        # 執行檔案複製
        try:
            success = handler.copy_files(
                file_name=mo,
                file_temp=temp_path,
                pd=pd,
                lot=lot
            )
            
            result['success'] = success
            if not success:
                result['error'] = "檔案複製失敗"
                
        except Exception as e:
            result['error'] = str(e)
            self.logger.error(f"處理 {vendor_code} 檔案時發生錯誤: {e}")
            
        return result