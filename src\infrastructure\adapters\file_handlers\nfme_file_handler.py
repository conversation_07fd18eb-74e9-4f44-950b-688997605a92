"""
NFME 廠商檔案處理器
對應 VBA 的 CopyFilesNFME 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class NFMEFileHandler(BaseFileHandler):
    """
    NFME 廠商檔案處理器
    
    VBA 邏輯特殊：
    - 不搜尋壓縮檔
    - 只複製包含 "lsr" 或 "data.csv" 的檔案
    - 使用 PD 建構路徑
    """
    
    def __init__(self, source_base_path: str):
        """初始化 NFME 檔案處理器"""
        super().__init__(source_base_path, "NFME")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """
        NFME 使用 PD 建構路徑
        
        VBA: sourcePathnfme = sourcePath & "\NFME\FT\" & pd & "\"
        """
        if pd != "default":
            return [self.source_base_path / "NFME" / "FT" / pd]
        else:
            # 如果沒有 PD，嘗試根目錄
            return [self.source_base_path / "NFME" / "FT"]
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        NFME 特殊：只複製特定類型檔案
        
        VBA: 
        If InStr(file, "lsr") > 0 Or InStr(file, "data.csv") > 0 Then
        """
        # NFME 不使用 MO 或 LOT 過濾，而是檔案類型
        return ["*lsr*", "*data.csv"]
        
    def _supports_folder_copy(self) -> bool:
        """NFME 不支援資料夾複製"""
        return False
        
    def _copy_by_mo(self, source_path: Path, dest_path: Path, mo: str) -> bool:
        """
        覆寫：NFME 不搜尋壓縮檔，而是特定檔案類型
        """
        try:
            success = False
            
            # 搜尋所有檔案
            for file_path in source_path.iterdir():
                if file_path.is_file():
                    filename_lower = file_path.name.lower()
                    
                    # 檢查是否包含 "lsr" 或 "data.csv"
                    if "lsr" in filename_lower or "data.csv" in filename_lower:
                        self.logger.info(f"找到 NFME 檔案: {file_path.name}")
                        if self._copy_file_with_check(file_path, dest_path):
                            success = True
                            
            return success
            
        except Exception as e:
            self.logger.error(f"NFME 檔案複製失敗: {e}")
            return False