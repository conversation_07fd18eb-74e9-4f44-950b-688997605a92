"""
API 工具函數模組
提供通用工具函數、路徑轉換、數據解析、錯誤處理等共用邏輯
"""

import os
import re
import json
from typing import Dict, Any, Tuple, Optional, List
from pathlib import Path
from datetime import datetime
from loguru import logger
# import pandas as pd  # 延遲導入，避免阻塞


class SystemConfig:
    """系統配置管理類別"""
    
    # 預設目錄配置 - 只清理 tmp 目錄
    DEFAULT_DIRECTORIES = [
        "/mnt/d/project/python/outlook_summary/tmp"
    ]
    
    # 臨時檔案目錄
    TEMP_BASE_DIR = Path("tmp/extracted")
    
    @classmethod
    def get_existing_directories(cls) -> List[str]:
        """獲取實際存在的目錄列表"""
        return [d for d in cls.DEFAULT_DIRECTORIES if os.path.exists(d)]


class APIUtils:
    """API 通用工具類別"""
    
    @staticmethod
    def convert_windows_path_to_wsl(windows_path: str) -> str:
        """轉換 Windows 路徑為 WSL 路徑（向下相容）"""
        # 使用新的路徑管理器
        try:
            from src.utils.path_manager import PathManager
            # 如果輸入是 Windows 路徑，轉換為 WSL
            if PathManager.is_windows_path(windows_path):
                return PathManager.windows_to_wsl(windows_path)
            return windows_path
        except ImportError:
            # 回退到原有邏輯
            if windows_path.startswith('/'):
                return windows_path
            
            if ':' not in windows_path:
                return windows_path
            
            match = re.match(r'^([A-Za-z]):\\(.*)$', windows_path)
            if match:
                drive = match.group(1).lower()
                path = match.group(2).replace('\\', '/')
                wsl_path = f"/mnt/{drive}/{path}"
                return wsl_path
            
            return windows_path
    
    @staticmethod
    def process_folder_path(original_path: str) -> Tuple[str, str]:
        """統一處理路徑轉換和日誌記錄（智能路徑解析）"""
        try:
            from src.utils.path_manager import PathManager
            
            # 使用智能路徑解析
            original, resolved_path, exists = PathManager.smart_path_resolution(original_path)
            
            if not exists:
                logger.warning(f"[WARNING] 路徑驗證失敗: 資料夾不存在: {resolved_path}")
            
            if original_path != resolved_path:
                logger.info(f"[REFRESH] 路徑轉換: {original_path} -> {resolved_path}")
            
            return original_path, resolved_path
            
        except ImportError:
            # 回退到原有邏輯
            folder_path = APIUtils.convert_windows_path_to_wsl(original_path)
            
            if original_path != folder_path:
                logger.info(f"[REFRESH] 路徑轉換: {original_path} -> {folder_path}")
            
            return original_path, folder_path
    
    @staticmethod
    def validate_folder_path(folder_path: str) -> Tuple[bool, str]:
        """驗證資料夾路徑是否有效（使用智能路徑解析）- FIXED_VERSION"""
        try:
            from src.utils.path_manager import PathManager
            
            # 使用智能路徑解析檢查路徑
            original, resolved_path, exists = PathManager.smart_path_resolution(folder_path)
            
            if not exists:
                return False, f"資料夾不存在: {resolved_path}"
            
            if not os.path.isdir(resolved_path):
                return False, f"指定路徑不是資料夾: {resolved_path}"
            
            return True, ""
            
        except ImportError:
            # 回退到原有邏輯
            if not os.path.exists(folder_path):
                return False, f"資料夾不存在: {folder_path}"
            
            if not os.path.isdir(folder_path):
                return False, f"指定路徑不是資料夾: {folder_path}"
            
            return True, ""
    
    @staticmethod
    def validate_file_path(file_path: str) -> Tuple[bool, str]:
        """驗證檔案路徑是否有效（使用智能路徑解析）"""
        try:
            from src.utils.path_manager import PathManager
            
            # 使用智能路徑解析檢查路徑
            original, resolved_path, exists = PathManager.smart_path_resolution(file_path)
            
            if not exists:
                return False, f"檔案不存在: {resolved_path}"
            
            if not os.path.isfile(resolved_path):
                return False, f"指定路徑不是檔案: {resolved_path}"
            
            return True, ""
            
        except ImportError:
            # 回退到原有邏輯
            if not os.path.exists(file_path):
                return False, f"檔案不存在: {file_path}"
            
            if not os.path.isfile(file_path):
                return False, f"指定路徑不是檔案: {file_path}"
            
            return True, ""


class UploadProcessorManager:
    """上傳處理器管理類別"""
    
    _global_processor = None
    
    @classmethod
    def get_global_upload_processor(cls):
        """獲取全局上傳處理器實例（維持重複防護狀態）"""
        if cls._global_processor is None:
            from src.infrastructure.adapters.file_upload import UploadProcessor
            cls._global_processor = UploadProcessor()
            logger.info("[TOOL] 已初始化全局上傳處理器（包含重複防護）")
        
        return cls._global_processor
    
    @classmethod
    def reset_global_processor(cls):
        """重置全局處理器（用於測試或清理）"""
        cls._global_processor = None
        logger.info("[REFRESH] 已重置全局上傳處理器")


class DataParser:
    """數據解析工具類別"""
    
    @staticmethod
    def parse_summary_sheet_data(excel_path: str) -> Dict[str, Any]:
        """解析 Summary sheet 資料獲取詳細的 FAIL 項目和 Site 統計"""
        try:
            # 延遲導入 pandas 避免阻塞
            import pandas as pd
            # 讀取 Summary sheet
            summary_df = pd.read_excel(excel_path, sheet_name='Summary', header=None)
            
            # 解析基本統計資料 (第1-4行)
            total_count = int(summary_df.iloc[0, 1])  # Total
            pass_count = int(summary_df.iloc[1, 1])   # Pass
            fail_count = int(summary_df.iloc[2, 1])   # Fail
            yield_rate = float(summary_df.iloc[3, 1]) # Yield
            
            # 解析 Site 統計 (第5行)
            site1_total = int(summary_df.iloc[4, 6]) if not pd.isna(summary_df.iloc[4, 6]) else 0
            site2_total = int(summary_df.iloc[4, 8]) if not pd.isna(summary_df.iloc[4, 8]) else 0
            
            # 解析 BIN 統計資料 (從第7行開始)
            fail_details = []
            site_details = {"site1": [], "site2": []}
            
            for i in range(6, min(20, len(summary_df))):  # 檢查前20行的 BIN 資料
                if pd.isna(summary_df.iloc[i, 0]):  # 如果 BIN 欄位為空，跳過
                    continue
                    
                bin_number = str(summary_df.iloc[i, 0]).strip()
                count = int(summary_df.iloc[i, 1]) if not pd.isna(summary_df.iloc[i, 1]) else 0
                percentage = float(summary_df.iloc[i, 2]) if not pd.isna(summary_df.iloc[i, 2]) else 0
                definition = str(summary_df.iloc[i, 3]).strip() if not pd.isna(summary_df.iloc[i, 3]) else "未知"
                
                # Site 統計
                site1_count = int(summary_df.iloc[i, 5]) if not pd.isna(summary_df.iloc[i, 5]) else 0
                site1_percentage = float(summary_df.iloc[i, 6]) if not pd.isna(summary_df.iloc[i, 6]) else 0
                site2_count = int(summary_df.iloc[i, 7]) if not pd.isna(summary_df.iloc[i, 7]) else 0
                site2_percentage = float(summary_df.iloc[i, 8]) if not pd.isna(summary_df.iloc[i, 8]) else 0
                
                # 只記錄有意義的資料
                if count > 0:
                    fail_detail = {
                        "bin": bin_number,
                        "count": count,
                        "percentage": round(percentage * 100, 2),
                        "definition": definition,
                        "status": "PASS" if bin_number == "1" else "FAIL"
                    }
                    fail_details.append(fail_detail)
                    
                    # Site 詳細統計
                    if site1_count > 0:
                        site_details["site1"].append({
                            "bin": bin_number,
                            "count": site1_count,
                            "percentage": round(site1_percentage * 100, 2),
                            "definition": definition
                        })
                    
                    if site2_count > 0:
                        site_details["site2"].append({
                            "bin": bin_number,
                            "count": site2_count,
                            "percentage": round(site2_percentage * 100, 2),
                            "definition": definition
                        })
            
            # 按數量排序 FAIL 項目
            fail_details.sort(key=lambda x: x["count"], reverse=True)
            
            summary_data = {
                "basic_stats": {
                    "total": total_count,
                    "pass": pass_count,
                    "fail": fail_count,
                    "yield_rate": round(yield_rate * 100, 2)
                },
                "site_stats": {
                    "site1_total": site1_total,
                    "site2_total": site2_total
                },
                "fail_details": fail_details,
                "site_details": site_details
            }
            
            logger.info(f"[BOARD] Summary 解析完成: {len(fail_details)} 個 BIN 項目")
            return summary_data
            
        except Exception as e:
            logger.error(f"解析 Summary sheet 失敗: {str(e)}")
            return {
                "basic_stats": {"total": 0, "pass": 0, "fail": 0, "yield_rate": 0},
                "site_stats": {"site1_total": 0, "site2_total": 0},
                "fail_details": [],
                "site_details": {"site1": [], "site2": []}
            }
    
    @staticmethod
    def count_debug_matches(debug_file_path: str) -> int:
        """計算 Debug 日誌中的總匹配數量"""
        if not os.path.exists(debug_file_path):
            logger.warning(f"Debug 日誌檔案不存在: {debug_file_path}")
            return 0
        
        try:
            with open(debug_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 計算所有 "[OK] 匹配" 出現次數
                match_count = content.count("[OK] 匹配")
                logger.info(f"[BOARD] Debug 日誌匹配記錄: {match_count} 個")
                return match_count
        except Exception as e:
            logger.error(f"讀取 Debug 日誌失敗: {e}")
            return 0


class ResponseFormatter:
    """回應格式化工具類別"""

    @staticmethod
    def get_current_timestamp() -> str:
        """取得當前時間戳"""
        return datetime.now().isoformat()

    @staticmethod
    def create_error_response(status_code: int, message: str, error_code: str = None, details: Dict = None) -> Dict[str, Any]:
        """創建標準錯誤回應格式"""
        response = {
            "status": "error",
            "message": message,
            "timestamp": logger.info.time().isoformat() if hasattr(logger.info, 'time') else None
        }
        
        if error_code:
            response["error_code"] = error_code
        
        if details:
            response["details"] = details
        
        return response
    
    @staticmethod
    def format_health_response(config) -> dict:
        """格式化健康檢查回應"""
        return {
            "status": "healthy",
            "message": "FT-EQC API 服務正常運行",
            "version": "2.0.0",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "eqc_processing": "operational",
                "file_management": "operational", 
                "cleanup_service": "operational"
            }
        }
    
    @staticmethod
    def format_eqc_bin1_response(result: dict) -> dict:
        """格式化 EQC BIN1 掃描回應"""
        return {
            "status": "success",
            "message": f"掃描完成，找到 {result.get('file_count', 0)} 個 EQC 檔案",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def format_eqc_standard_response(result: dict) -> dict:
        """格式化 EQC 標準處理回應"""
        # 提取程式碼區間資訊
        region_result = result.get('region_result', {})
        code_region = region_result.get('code_region', {})
        backup_region = region_result.get('backup_region', {})
        
        # 提取雙重搜尋結果
        dual_search_result = result.get('dual_search_result', {})
        
        # [TOOL] 使用動態檢測的備用區間數據，而不是固定公式計算
        backup_start = backup_region.get('backup_start_column', 1565) if backup_region.get('found') else 1565
        backup_end = backup_region.get('backup_end_column', 1602) if backup_region.get('found') else 1602
        
        # [SEARCH] 調試日誌：記錄備用區間數據來源
        from loguru import logger
        if backup_region.get('found'):
            backup_length = backup_end - backup_start + 1
            logger.info(f"[TARGET] API 使用動態檢測的備用區間: 第{backup_start}-{backup_end}欄 ({backup_length}個欄位)")
        else:
            logger.info(f"[WARNING] API 使用預設備用區間: 第{backup_start}-{backup_end}欄 (未檢測到動態區間)")
        
        # 構建符合 EQCStandardProcessData 的回應格式
        response_data = {
            "status": "success",
            "message": f"EQC 進階處理完成 (雙階段)",
            "data": {
                "main_region_start": code_region.get('start_column_number', 298),
                "main_region_end": code_region.get('end_column_number', 335),
                "backup_region_start": backup_start,
                "backup_region_end": backup_end,
                
                # 必需字段：雙重搜尋結果
                "search_method": dual_search_result.get('search_method', 'main_region_complete'),
                "match_rate": dual_search_result.get('match_rate', '100%'),
                
                # 必需字段：處理結果
                "total_rows_after": result.get('total_rows_after', 0),
                "processing_stage": result.get('processing_mode', 'stage2_only'),
                
                # 可選字段
                "all0_moved_count": result.get('all0_moved_count', 0),
                "online_eqc_fail_count": result.get('online_eqc_fail_count', 0),
                "step3_debug_log_file": result.get('step3_debug_log_file'),
                "execution_time": result.get('processing_time', 0.0),
                
                # 額外的調試資訊
                "processing_stages": result.get('processing_stages', []),
                "dual_search_result": dual_search_result,
                "region_result": region_result,
                "doc_directory": result.get('doc_directory', ''),
                "processing_mode": result.get('processing_mode', 'stage2_only')
            },
            "processing_time": result.get('processing_time', 0.0),
            "timestamp": datetime.now().isoformat()
        }
        
        return response_data
    
    @staticmethod
    def format_online_eqc_response(result: dict):
        """格式化線上 EQC 處理回應"""
        from ..models import OnlineEQCProcessResponse, OnlineEQCProcessData
        
        # 創建 OnlineEQCProcessData 對象
        data = OnlineEQCProcessData(**result)
        
        # 返回 OnlineEQCProcessResponse 對象
        return OnlineEQCProcessResponse(
            status="success",
            message="[OK] Online EQC 處理完成: EQCTOTALDATA 生成",
            data=data,
            processing_time=result.get('processing_time_seconds', 0.0)
        )
    
    @staticmethod
    def format_step5_flow_response(result: dict) -> dict:
        """格式化 Step5 流程回應"""
        return {
            "status": "success",
            "message": "Step5 測試流程生成完成",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def format_analysis_response(result: dict) -> dict:
        """格式化分析回應"""
        return {
            "status": "success",
            "message": "資料分析完成",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def format_upload_config_response(config) -> dict:
        """格式化上傳配置回應"""
        return config  # 假設 config 已經是正確格式
    
    @staticmethod
    def format_upload_result(result: dict) -> dict:
        """格式化上傳結果為符合 UploadResult 模型的格式"""
        # 如果 result 包含 upload_result（解壓縮結果），提取上傳資訊
        if 'upload_result' in result:
            upload_info = result['upload_result']
            return {
                "success": upload_info.get('success', False),
                "message": result.get('message', upload_info.get('message', '')),
                "original_filename": upload_info.get('original_filename'),
                "upload_path": upload_info.get('upload_path'),
                "file_size": upload_info.get('file_size'),
                "file_size_mb": upload_info.get('file_size_mb')
            }
        
        # 否則假設 result 已經是正確格式
        return result
    
    @staticmethod
    def format_upload_process_response(result: dict) -> dict:
        """格式化上傳處理回應"""
        return result  # 假設 result 已經是正確格式
    
    @staticmethod
    def format_file_list_response(files) -> dict:
        """格式化檔案列表回應"""
        return {
            "status": "success",
            "message": f"找到 {len(files) if isinstance(files, list) else '未知'} 個檔案",
            "data": files,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def format_cleanup_response(result: dict) -> dict:
        """格式化清理回應"""
        return {
            "status": "success",
            "message": "清理操作完成",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def format_status_response(status: dict) -> dict:
        """格式化狀態回應"""
        return {
            "status": "success",
            "message": "狀態查詢完成",
            "data": status,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def format_grouping_response(result: dict) -> dict:
        """格式化分組回應 (向下相容)"""
        return {
            "status": "success",
            "message": "分組處理完成",
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def format_temp_files_info(info: dict) -> dict:
        """格式化暫存檔案資訊回應"""
        return {
            "status": "success",
            "message": "暫存檔案資訊查詢完成",
            "data": info,
            "timestamp": datetime.now().isoformat()
        }

    @staticmethod
    def format_archive_info_response(info: dict) -> dict:
        """格式化壓縮檔資訊回應"""
        return {
            "status": "success",
            "message": f"壓縮檔資訊查詢完成: {info.get('file_name', '未知檔案')}",
            "data": info,
            "timestamp": datetime.now().isoformat()
        }

    @staticmethod
    def create_success_response(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """創建標準成功回應格式"""
        response = {
            "status": "success",
            "message": message,
            "timestamp": logger.info.time().isoformat() if hasattr(logger.info, 'time') else None
        }

        if data is not None:
            response["data"] = data

        return response


class FileOperations:
    """檔案操作工具類別"""
    
    @staticmethod
    def safe_file_exists(file_path: str) -> bool:
        """安全檢查檔案是否存在"""
        try:
            return os.path.exists(file_path) and os.path.isfile(file_path)
        except Exception as e:
            logger.error(f"檢查檔案存在時發生錯誤: {e}")
            return False
    
    @staticmethod
    def safe_directory_exists(dir_path: str) -> bool:
        """安全檢查目錄是否存在"""
        try:
            return os.path.exists(dir_path) and os.path.isdir(dir_path)
        except Exception as e:
            logger.error(f"檢查目錄存在時發生錯誤: {e}")
            return False
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """獲取檔案大小（位元組）"""
        try:
            if FileOperations.safe_file_exists(file_path):
                return os.path.getsize(file_path)
            return 0
        except Exception as e:
            logger.error(f"獲取檔案大小失敗: {e}")
            return 0
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """格式化檔案大小顯示"""
        if size_bytes == 0:
            return "0B"
        
        size_units = ['B', 'KB', 'MB', 'GB', 'TB']
        unit_index = 0
        size = float(size_bytes)
        
        while size >= 1024.0 and unit_index < len(size_units) - 1:
            size /= 1024.0
            unit_index += 1
        
        return f"{size:.1f}{size_units[unit_index]}"


class LoggingUtils:
    """日誌工具類別"""
    
    @staticmethod
    def log_api_start(endpoint: str, request_data: Dict = None):
        """記錄 API 開始日誌"""
        logger.info(f"[START] API 端點開始: {endpoint}")
        if request_data:
            logger.debug(f"   [BOARD] 請求數據: {request_data}")
    
    @staticmethod
    def log_api_success(endpoint: str, result_summary: str = None):
        """記錄 API 成功日誌"""
        logger.info(f"[OK] API 端點成功: {endpoint}")
        if result_summary:
            logger.info(f"   [CHART] 結果摘要: {result_summary}")
    
    @staticmethod
    def log_api_error(endpoint: str, error: Exception, context: Dict = None):
        """記錄 API 錯誤日誌"""
        logger.error(f"[ERROR] API 端點錯誤: {endpoint}")
        logger.error(f"   [FIRE] 錯誤訊息: {str(error)}")
        if context:
            logger.error(f"   [SEARCH] 錯誤上下文: {context}")
    
    @staticmethod
    def log_processing_step(step_name: str, details: str = None):
        """記錄處理步驟日誌"""
        logger.info(f"⚙ 處理步驟: {step_name}")
        if details:
            logger.info(f"   [NOTES] 步驟詳情: {details}")


# 便捷函數導出（保持向下相容性）
def convert_windows_path_to_wsl(windows_path: str) -> str:
    """轉換 Windows 路徑為 WSL 路徑（向下相容函數）"""
    return APIUtils.convert_windows_path_to_wsl(windows_path)


def process_folder_path(original_path: str) -> Tuple[str, str]:
    """統一處理路徑轉換和日誌記錄（向下相容函數）"""
    return APIUtils.process_folder_path(original_path)


def get_global_upload_processor():
    """獲取全局上傳處理器實例（向下相容函數）"""
    return UploadProcessorManager.get_global_upload_processor()


def parse_summary_sheet_data(excel_path: str) -> Dict[str, Any]:
    """解析 Summary sheet 資料（向下相容函數）"""
    return DataParser.parse_summary_sheet_data(excel_path)


class ErrorHandler:
    """錯誤處理器類別 - 統一 API 錯誤處理"""
    
    def handle_exception(self, exc: Exception, request) -> dict:
        """處理一般異常"""
        return {
            "status_code": 500,
            "content": {
                "error": str(exc), 
                "type": "internal_error",
                "timestamp": datetime.now().isoformat()
            }
        }
    
    def handle_http_exception(self, exc, request) -> dict:
        """處理 HTTP 異常"""
        return {
            "status_code": getattr(exc, 'status_code', 500),
            "content": {
                "error": getattr(exc, 'detail', str(exc)), 
                "type": "http_error",
                "timestamp": datetime.now().isoformat()
            }
        }


def count_debug_matches(debug_file_path: str) -> int:
    """計算 Debug 日誌中的總匹配數量（向下相容函數）"""
    return DataParser.count_debug_matches(debug_file_path)