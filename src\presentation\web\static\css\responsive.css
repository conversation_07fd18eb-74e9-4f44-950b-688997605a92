/* 
 * 響應式設計模組
 * 包含各種螢幕尺寸的適配樣式
 */

/* ==================== 大螢幕適配 (1200px+) ==================== */
@media (min-width: 1200px) {
    .container {
        max-width: var(--container-max-width);
    }
    
    .controls-container {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

/* ==================== 中等螢幕適配 (768px - 1199px) ==================== */
@media (max-width: 1199px) and (min-width: 768px) {
    .controls-container {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }
    
    .folder-input-card {
        grid-column: 1 / -1;
    }
    
    .container {
        margin: 10px;
    }
    
    .main-content {
        padding: var(--spacing-md);
    }
}

/* ==================== 平板適配 (768px - 1199px) ==================== */
@media (max-width: 1199px) {
    .controls-container {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .folder-input-card {
        grid-column: 1 / -1;
    }
    
    .timeline-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }
    
    .file-details {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid-new {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* ==================== 小螢幕適配 (481px - 767px) ==================== */
@media (max-width: 767px) {
    body {
        padding: var(--spacing-sm);
    }
    
    .controls-container {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .folder-input-card {
        grid-column: 1;
        padding: var(--spacing-md);
    }
    
    .header h1 {
        font-size: var(--font-size-lg);
    }
    
    .header {
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .side-card {
        padding: var(--spacing-sm);
    }
    
    .timeline-content {
        grid-template-columns: 1fr;
    }
    
    .file-details {
        grid-template-columns: 1fr;
    }
    
    .file-actions {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--spacing-xxs);
    }
    
    .action-btn {
        flex: 1;
        min-width: 80px;
    }
    
    .stats-grid-new {
        grid-template-columns: 1fr;
    }
    
    .stats-grid-extra {
        grid-template-columns: 1fr;
    }
    
    .timeline {
        padding-left: 20px;
    }
    
    .timeline::before {
        left: 10px;
    }
    
    .timeline-item::before {
        left: -27px;
    }
    
    .timeline-item {
        padding: var(--spacing-md);
    }
    
    .detail-header {
        padding: var(--spacing-md);
        flex-wrap: wrap;
    }
    
    .detail-content {
        padding: 0 var(--spacing-md) var(--spacing-md);
    }
}

/* ==================== 極小螢幕適配 (320px - 480px) ==================== */
@media (max-width: 480px) {
    body {
        padding: 10px;
    }
    
    .container {
        margin: 5px;
        border-radius: 10px;
    }
    
    .main-content {
        padding: var(--spacing-md) var(--spacing-sm);
        gap: var(--spacing-md);
    }
    
    .controls-container {
        gap: var(--spacing-sm);
    }
    
    .control-card {
        padding: var(--spacing-sm);
    }
    
    .folder-input-card {
        padding: var(--spacing-sm);
    }
    
    .folder-input-card .folder-icon {
        font-size: 2em;
    }
    
    .header {
        padding: var(--spacing-sm) var(--spacing-md);
    }
    
    .header h1 {
        font-size: 1.5em;
    }
    
    .side-card {
        padding: var(--spacing-xs);
    }
    
    .side-card h4 {
        font-size: var(--font-size-xs);
    }
    
    .timeline-item {
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-md);
    }
    
    .file-header {
        flex-wrap: wrap;
        gap: var(--spacing-xxs);
    }
    
    .file-type-badge {
        font-size: 10px;
        padding: 2px 8px;
    }
    
    .btn {
        padding: var(--spacing-xxs) var(--spacing-sm);
        font-size: var(--font-size-xs);
    }
    
    .btn-sm {
        padding: 4px var(--spacing-xxs);
        font-size: 10px;
    }
    
    .detail-header {
        padding: var(--spacing-sm);
    }
    
    .detail-content {
        padding: 0 var(--spacing-sm) var(--spacing-sm);
    }
    
    .stats-grid-new {
        gap: var(--spacing-xxs);
    }
    
    .stat-card {
        padding: var(--spacing-xxs);
    }
    
    .stat-card .value {
        font-size: 1.2em;
    }
    
    .stat-card .label {
        font-size: 10px;
    }
}

/* ==================== 橫向模式適配 ==================== */
@media (orientation: landscape) and (max-height: 600px) {
    .main-content {
        min-height: auto;
    }
    
    .timeline-item {
        margin-bottom: var(--spacing-sm);
        padding: var(--spacing-sm);
    }
    
    .detail-item {
        margin-bottom: var(--spacing-xs);
    }
}

/* ==================== 高解析度螢幕適配 ==================== */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .timeline::before {
        width: 1px;
    }
    
    .timeline-item::before {
        width: 10px;
        height: 10px;
    }
}

/* ==================== 列印樣式 ==================== */
@media print {
    body {
        background: white;
        padding: 0;
    }
    
    .container {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .btn, .action-btn {
        display: none;
    }
    
    .timeline::before,
    .timeline-item::before {
        display: none;
    }
    
    .timeline {
        padding-left: 0;
    }
    
    .timeline-item {
        break-inside: avoid;
        margin-bottom: var(--spacing-sm);
    }
}
