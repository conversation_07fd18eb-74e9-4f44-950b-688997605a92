# 程式碼文件總覽

本目錄包含 Outlook Summary System 專案中 `src` 目錄下所有 Python 檔案的詳細文件。

## 文件結構

### 資料模型層 (Data Models)
- **[email_models.md](./email_models.md)** - 郵件數據模型
  - EmailData: 郵件數據模型
  - EmailAttachment: 郵件附件模型
  - VendorIdentificationResult: 廠商識別結果模型
  - EmailParsingResult: 郵件解析結果模型
  - EmailMetadata: 郵件元數據模型
  - TaskData: 任務數據模型
  - FileProcessingInfo: 檔案處理資訊模型
  - EmailProcessingContext: 郵件處理上下文模型
  - ProcessingStatus: 處理狀態枚舉

### 應用層 (Application Layer)
- **[email_reader.md](./email_reader.md)** - 郵件讀取器介面
  - EmailReaderConfig: 郵件讀取器配置
  - EmailReader: 郵件讀取器抽象基類

- **[task_queue.md](./task_queue.md)** - 任務佇列介面
  - TaskStatus: 任務狀態枚舉
  - TaskPriority: 任務優先級枚舉
  - TaskInfo: 任務資訊資料類別
  - QueueConfig: 佇列配置資料類別
  - TaskQueue: 任務佇列抽象基類

- **[email_processor.md](./email_processor.md)** - 郵件處理引擎
  - EmailProcessingConfig: 郵件處理配置
  - ProcessingMetrics: 處理指標
  - EmailProcessor: 郵件處理引擎主類別

### 領域層 (Domain Layer)
- **[base.md](./base.md)** - 領域例外處理基礎類別
  - OutlookSummaryException: 系統基礎例外類別
  - DomainException: 領域層例外
  - ValidationException: 驗證例外
  - ConfigurationException: 配置例外
  - EmailProcessingException: 郵件處理例外
  - FileProcessingException: 檔案處理例外
  - ParsingException: 解析例外

### 基礎設施層 (Infrastructure Layer)

#### 配置與日誌
- **[config_manager.md](./config_manager.md)** - 擴展配置管理系統
  - ValidationResult: 配置驗證結果
  - FileStorageConfig: 檔案儲存配置
  - OutlookConfig: Outlook 配置
  - VendorConfig: 廠商配置
  - PerformanceConfig: 效能配置
  - SecurityConfig: 安全配置
  - AdvancedSettings: 擴展設定類別
  - ConfigManager: 配置管理器主類別

- **[settings.md](./settings.md)** - 基礎配置管理
  - DatabaseConfig: 資料庫配置
  - EmailConfig: 郵件配置
  - BIN1LogConfig: BIN1 日誌配置
  - Settings: 主要配置設定

- **[logger_manager.md](./logger_manager.md)** - 高級日誌系統管理器
  - LogLevel: 日誌級別枚舉
  - LogFormat: 日誌格式枚舉
  - ColorScheme: 顏色配置
  - PerformanceTimer: 效能計時器
  - PerformanceLogger: 效能日誌記錄器
  - StructuredLogger: 結構化日誌記錄器
  - ColoredFormatter: 帶顏色的日誌格式化器
  - JSONFormatter: JSON 格式化器
  - AsyncLogHandler: 非同步日誌處理器
  - LoggerManager: 日誌管理器主類別

#### 資料庫適配器
- **[email_database.md](./email_database.md)** - 郵件資料庫操作類
  - EmailDatabase: 郵件資料庫操作主類別

- **[models.py.md](./models.py.md)** - 資料庫模型定義
  - EmailDB: 郵件資料庫模型
  - SenderDB: 寄件者資料庫模型
  - AttachmentDB: 附件資料庫模型
  - EmailProcessStatusDB: 郵件處理狀態模型
  - DatabaseEngine: 資料庫引擎管理類別

#### 解析器系統
- **[base_parser.md](./base_parser.md)** - 基礎解析器架構
  - ParsingStrategy: 解析策略枚舉
  - ParsingError: 解析錯誤類別
  - ParsingContext: 解析上下文
  - BaseParser: 基礎解析器抽象類別
  - VendorParser: 廠商解析器基類
  - ParserFactory: 解析器工廠類別

- **[etd_parser.md](./etd_parser.md)** - ETD 廠商解析器
  - ETDParser: ETD 廠商郵件解析器類別

### 表現層 (Presentation Layer)
- **[models.md](./models.md)** - FT-EQC 分組 API 資料模型
  - FTEQCGroupingRequest: FT-EQC 分組處理請求模型
  - StatisticsData: 統計資料模型
  - FailDetail: 失敗檔案詳細資訊模型
  - EQCFailResult: Online EQC 失敗檔案結果模型

- **[network_models.md](./network_models.md)** - 網路共享瀏覽器資料模型
  - NetworkFileInfo: 網路檔案資訊模型
  - NetworkFileListResponse: 檔案列表回應模型
  - NetworkPathValidateRequest: 路徑驗證請求模型
  - NetworkPathValidateResponse: 路徑驗證回應模型
  - NetworkCredentials: 網路認證資訊模型
  - NetworkConnectRequest: 網路連接請求模型
  - NetworkConnectResponse: 網路連接回應模型

- **[network_utils.md](./network_utils.md)** - 網路共享瀏覽器工具函式
  - list_smb_files: 列出 SMB 共享檔案
  - test_smb_connection: 測試 SMB 連接
  - convert_unc_to_linux_path: 轉換 UNC 路徑為 Linux 路徑
  - validate_network_path: 驗證網路路徑
  - get_file_info: 取得檔案詳細資訊

### 服務層 (Services Layer)
- **[file_cleaner.md](./file_cleaner.md)** - 檔案清理服務
  - FileCleaner: 檔案清理服務類別

- **[scheduler.md](./scheduler.md)** - 檔案清理調度器
  - FileCleanupScheduler: 檔案清理調度器類別

### 根目錄
- **[__init__.md](./__init__.md)** - src 套件初始化檔案

## 系統架構圖

本專案採用六角架構（Hexagonal Architecture），具有清晰的層次分離和依賴倒置原則。

### 架構層次
1. **表現層**: API 服務、CLI 介面、Web 介面
2. **應用層**: 使用案例、介面定義
3. **領域層**: 實體、值物件、領域服務、例外處理
4. **資料模型層**: 郵件數據、附件、解析結果等模型
5. **基礎設施層**: 適配器、解析器、配置、日誌
6. **服務層**: 檔案清理、排程服務

### 主要資料流程
1. **郵件監控**: Outlook Adapter 持續監控收件夾
2. **廠商識別**: 解析器工廠選擇最適合的廠商解析器
3. **郵件解析**: 提取 MO、Lot、測試類型等關鍵資訊
4. **資料儲存**: 儲存到資料庫並處理附件
5. **狀態追蹤**: 記錄處理狀態和錯誤資訊

## 文件特色

### 階層式結構
所有文件都遵循統一的階層式標題結構：
- **檔案名稱** (h1): 作為最上層標題
- **類別名稱** (h2): 作為第二層標題
- **方法/函式** (h3): 作為第三層標題
- **巢狀函式** (h4): 作為第四層標題（如適用）

### 詳細資訊
每個函式和方法都包含：
- **功能描述**: 簡要說明功能和用途
- **參數說明**: 詳細的參數類型、描述和預設值
- **返回值說明**: 返回值的類型和含義
- **異常說明**: 可能拋出的異常類型和條件
- **使用範例**: 實際的使用範例（如適用）

### 設計模式
文件中記錄了各種設計模式的使用：
- **六角架構**: 清晰的層次分離
- **工廠模式**: 解析器工廠
- **策略模式**: 解析策略
- **觀察者模式**: 事件處理
- **單例模式**: 配置管理

## 使用指南

1. **查找特定功能**: 使用文件名稱快速定位相關模組
2. **理解架構**: 按照層次結構理解系統設計
3. **API 參考**: 使用詳細的參數和返回值說明
4. **錯誤處理**: 參考異常說明進行錯誤處理
5. **擴展開發**: 基於現有模式進行功能擴展

## 統計資訊

### 文件涵蓋範圍
- **總 Python 檔案數**: 100+ 個檔案
- **已建立文件數**: 20+ 個 Markdown 文件
- **涵蓋的類別數**: 80+ 個類別
- **記錄的方法數**: 300+ 個方法和函式
- **架構層次**: 6 個主要層次

### 主要模組統計
- **解析器**: 6 個廠商解析器（ETD, GTK, JCET, Lingsen, XAHT, Base）
- **適配器**: 15+ 個適配器（資料庫、郵件、Excel、檔案等）
- **API 服務**: 10+ 個 API 端點
- **資料模型**: 15+ 個資料模型類別

## 維護說明

- 當新增 Python 檔案時，請按照相同格式建立對應的 Markdown 文件
- 當修改現有程式碼時，請同步更新相關文件
- 保持文件的階層式結構和詳細程度的一致性
- 定期檢查文件與程式碼的同步性
- 使用系統架構圖了解模組間的依賴關係
- 參考資料流程圖理解系統運作流程
