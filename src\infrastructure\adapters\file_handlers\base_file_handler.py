"""
檔案處理器基礎類別
對應 VBA CopyFiles 系列函數的共同邏輯
"""

import os
import shutil
import tempfile
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import time

from src.infrastructure.logging.logger_manager import LoggerManager


class BaseFileHandler(ABC):
    """
    檔案處理器基礎類別
    
    實作 VBA CopyFiles 函數的共同邏輯：
    1. 建立目標資料夾
    2. 搜尋檔案（優先 MO，其次 LOT）
    3. 檢查檔案是否已存在
    4. 複製並驗證
    """
    
    def __init__(self, source_base_path: str, vendor_code: str):
        """
        初始化檔案處理器
        
        Args:
            source_base_path: 來源基礎路徑 (對應 VBA sourcePath)
            vendor_code: 廠商代碼
        """
        self.source_base_path = Path(source_base_path)
        self.vendor_code = vendor_code
        self.logger = LoggerManager().get_logger(f"{vendor_code}FileHandler")
        
        # 支援的壓縮檔副檔名（對應 VBA 的檢查邏輯）
        self.archive_extensions = {'.zip', '.rar', '.7z'}
        
    @abstractmethod
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """
        取得廠商特定的來源路徑列表
        每個廠商可能有不同的目錄結構
        
        Args:
            pd: 產品名稱
            lot: 批號
            mo: MO 編號
            
        Returns:
            可能的來源路徑列表
        """
        pass
        
    @abstractmethod
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        取得廠商特定的檔案搜尋模式
        
        Args:
            mo: MO 編號
            lot: 批號
            pd: 產品名稱
            
        Returns:
            檔案搜尋模式列表
        """
        pass
        
    def copy_files(self, file_name: str, file_temp: str, 
                  pd: str = "default", lot: str = "default") -> bool:
        """
        主要複製函數 - 對應 VBA CopyFilesXXX
        
        Args:
            file_name: MO 編號（對應 VBA fileName）
            file_temp: 目標暫存路徑（對應 VBA filetemp）
            pd: 產品名稱
            lot: 批號
            
        Returns:
            bool: 複製是否成功
        """
        try:
            self.logger.info(f"開始處理 {self.vendor_code} 檔案: MO={file_name}, LOT={lot}, PD={pd}")
            
            # 1. 建立目標資料夾（對應 VBA CreateFolders）
            destination_path = Path(file_temp)
            if not self._create_folders(destination_path, pd, file_name.upper()):
                return False
                
            # 2. 取得來源路徑
            source_paths = self.get_source_paths(pd, lot, file_name)
            
            # 3. 嘗試從每個來源路徑複製檔案
            for source_path in source_paths:
                if not source_path.exists():
                    self.logger.warning(f"來源路徑不存在: {source_path}")
                    continue
                    
                self.logger.debug(f"搜尋路徑: {source_path}")
                
                # 嘗試不同的搜尋策略（對應 VBA 的搜尋順序）
                # 3.1 優先使用 MO 搜尋壓縮檔
                if self._copy_by_mo(source_path, destination_path, file_name):
                    return True
                    
                # 3.2 使用 LOT 搜尋
                if self._copy_by_lot(source_path, destination_path, lot):
                    return True
                    
                # 3.3 某些廠商支援複製整個資料夾
                if self._supports_folder_copy():
                    if self._copy_entire_folder(source_path, destination_path, pd, lot):
                        return True
                    
            self.logger.error(f"無法從任何來源複製檔案: MO={file_name}, LOT={lot}")
            return False
            
        except Exception as e:
            self.logger.error(f"複製檔案時發生錯誤: {e}")
            return False
            
    def _create_folders(self, base_path: Path, pd: str, mo: str) -> bool:
        """
        建立目標資料夾結構 - 對應 VBA CreateFolders
        
        VBA: destinationPath = tempPath & "\" & pd & "\" & UCase(fileName)
        """
        try:
            # 建立 PD/MO 結構
            target_dir = base_path / pd / mo
            target_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"建立目標資料夾: {target_dir}")
            return True
        except Exception as e:
            self.logger.error(f"建立資料夾失敗: {e}")
            return False
            
    def _copy_by_mo(self, source_path: Path, dest_path: Path, mo: str) -> bool:
        """
        使用 MO 搜尋並複製檔案（優先搜尋壓縮檔）
        對應 VBA: file = Dir(sourcePathXXX & "*" & fileName & "*")
        """
        try:
            # 搜尋包含 MO 的檔案
            pattern = f"*{mo}*"
            newest_file = self._find_newest_archive(source_path, pattern)
            
            if newest_file:
                self.logger.info(f"找到符合的壓縮檔: {newest_file}")
                return self._copy_file_with_check(newest_file, dest_path / mo.upper())
                
            return False
            
        except Exception as e:
            self.logger.error(f"使用 MO 複製失敗: {e}")
            return False
            
    def _copy_by_lot(self, source_path: Path, dest_path: Path, lot: str) -> bool:
        """
        使用 LOT 搜尋並複製檔案
        對應 VBA: file = Dir(sourcePathXXX & lot & "*")
        """
        try:
            # 搜尋包含 LOT 的檔案
            pattern = f"{lot}*"
            files = list(source_path.glob(pattern))
            
            if not files:
                return False
                
            self.logger.info(f"找到 {len(files)} 個符合 LOT 的檔案")
            
            # 複製所有符合的檔案
            success = False
            for file_path in files:
                if file_path.is_file():
                    if self._copy_file_with_check(file_path, dest_path):
                        success = True
                        
            return success
            
        except Exception as e:
            self.logger.error(f"使用 LOT 複製失敗: {e}")
            return False
            
    def _copy_entire_folder(self, source_base: Path, dest_base: Path, 
                           pd: str, lot: str) -> bool:
        """
        複製整個資料夾 - 對應 VBA CopyFolder
        某些廠商（如 GTK、ETD）支援此功能
        """
        try:
            # 建構資料夾路徑
            source_folder = source_base / pd / lot
            dest_folder = dest_base / pd / lot
            
            if source_folder.exists() and source_folder.is_dir():
                shutil.copytree(source_folder, dest_folder, dirs_exist_ok=True)
                self.logger.info(f"已複製整個資料夾: {source_folder} -> {dest_folder}")
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"複製資料夾失敗: {e}")
            return False
            
    def _find_newest_archive(self, directory: Path, pattern: str) -> Optional[Path]:
        """
        尋找最新的壓縮檔案
        對應 VBA 的檔案日期比較邏輯：
        If FileDateTime(sourcePathXXX & file) > newestDate Then
        """
        newest_file = None
        newest_date = datetime.min
        
        for file_path in directory.glob(pattern):
            if file_path.suffix.lower() in self.archive_extensions:
                file_date = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_date > newest_date:
                    newest_date = file_date
                    newest_file = file_path
                    
        return newest_file
        
    def _copy_file_with_check(self, source: Path, dest_dir: Path) -> bool:
        """
        複製檔案並檢查
        對應 VBA 的檔案大小檢查邏輯：
        If FileLen(sourcePathXXX & newestFile) = FileLen(destinationPath & "\" & newestFile) Then
        """
        try:
            # 確保目標目錄存在
            dest_dir.mkdir(parents=True, exist_ok=True)
            dest_file = dest_dir / source.name
            
            # 檢查目標檔案是否已存在且大小相同
            if dest_file.exists():
                if dest_file.stat().st_size == source.stat().st_size:
                    self.logger.info(f"檔案已存在且大小相同，跳過: {dest_file}")
                    return True
                    
            # 延遲一秒（對應 VBA DelayOneSecond）
            time.sleep(1)
            
            # 複製檔案（對應 VBA FileCopy）
            shutil.copy2(source, dest_file)
            
            # 驗證複製（對應 VBA 的大小比較）
            if dest_file.stat().st_size == source.stat().st_size:
                self.logger.info(f"檔案複製成功: {source} -> {dest_file}")
                return True
            else:
                self.logger.error(f"檔案複製驗證失敗: 大小不符")
                return False
                
        except Exception as e:
            self.logger.error(f"複製檔案失敗 {source}: {e}")
            return False
            
    def _supports_folder_copy(self) -> bool:
        """
        是否支援資料夾複製
        某些廠商（如 GTK、ETD）支援複製整個資料夾
        子類別可以覆寫此方法
        """
        return False
        
    def cleanup_temp_files(self, temp_dir: Path, days_old: int = 7):
        """
        清理舊的暫存檔案
        
        Args:
            temp_dir: 暫存目錄
            days_old: 清理幾天前的檔案
        """
        try:
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            
            for file_path in temp_dir.rglob('*'):
                if file_path.is_file():
                    if file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        self.logger.debug(f"已刪[EXCEPT_CHAR]舊檔案: {file_path}")
                        
        except Exception as e:
            self.logger.error(f"清理暫存檔案失敗: {e}")