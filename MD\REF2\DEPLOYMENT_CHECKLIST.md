# 部署檢查清單

## [TOOL] 環境準備

### 1. Python 環境
- [ ] Python 3.8+ 已安裝
- [ ] pip 已更新到最新版本
- [ ] 虛擬環境已創建並激活

### 2. 依賴安裝
```bash
# 安裝所有依賴
pip install -r requirements.txt

# 檢查關鍵套件
pip show flask fastapi uvicorn sqlalchemy
```

### 3. 環境配置
- [ ] 創建 `.env` 檔案
- [ ] 設定 POP3 伺服器參數
- [ ] 設定資料庫路徑
- [ ] 配置日誌級別

## [BOARD] 系統測試

### 1. 模組測試
```bash
# 執行系統測試
python test_email_system.py

# 檢查檔案結構
python test_email_system.py --file-structure-only
```

### 2. 整合測試
```bash
# 執行整合測試
python test_integration.py

# 帶等待的整合測試
python test_integration.py --wait
```

## [ROCKET] 服務啟動

### 1. 啟動檢查
- [ ] 端口 5000 未被佔用
- [ ] 端口 8010 未被佔用
- [ ] 防火牆規則已設定
- [ ] 網路連接正常

### 2. 啟動服務
```bash
# 方式 1：整合啟動（推薦）
python start_integrated_services.py

# 方式 2：主應用程式
python email_inbox_app.py

# 方式 3：分別啟動
python email_inbox_app.py --no-fastapi  # 終端 1
python -m uvicorn src.presentation.api.ft_eqc_api:app --host 0.0.0.0 --port 8010  # 終端 2
```

## [GLOBE_WITH_MERIDIANS] 服務驗證

### 1. Web 介面檢查
- [ ] 郵件收件夾: http://localhost:5000
- [ ] FT-EQC 處理: http://localhost:8010/ui
- [ ] API 文檔: http://localhost:8010/docs
- [ ] FT Summary: http://localhost:8010/ft-summary-ui

### 2. 功能測試
- [ ] 郵件同步功能
- [ ] 搜尋和篩選功能
- [ ] 郵件詳情查看
- [ ] FT-EQC 處理功能
- [ ] 報告生成功能

## [CHART] 監控和日誌

### 1. 日誌檢查
```bash
# 查看 Flask 日誌
tail -f logs/email_inbox_app.log

# 查看 FastAPI 日誌
tail -f logs/ft_eqc_api.log

# 查看同步日誌
tail -f logs/email_sync.log
```

### 2. 系統監控
- [ ] 記憶體使用率
- [ ] CPU 使用率
- [ ] 磁碟空間
- [ ] 網路連接狀態

## [LOCKED_WITH_KEY] 安全檢查

### 1. 認證和授權
- [ ] 郵件伺服器認證
- [ ] API 端點安全
- [ ] 檔案權限設定
- [ ] 敏感資料加密

### 2. 網路安全
- [ ] HTTPS 設定（生產環境）
- [ ] 防火牆配置
- [ ] 端口訪問控制
- [ ] 跨域請求設定

## [REFRESH] 備份和恢復

### 1. 資料備份
- [ ] 郵件資料庫備份
- [ ] 配置檔案備份
- [ ] 處理報告備份
- [ ] 日誌檔案輪轉

### 2. 恢復測試
- [ ] 資料庫恢復測試
- [ ] 服務重啟測試
- [ ] 容錯能力測試
- [ ] 故障恢復程序

## [TARGET] 效能優化

### 1. 資料庫優化
- [ ] 索引優化
- [ ] 查詢效能調優
- [ ] 連接池設定
- [ ] 清理舊資料

### 2. 服務優化
- [ ] 快取設定
- [ ] 併發處理
- [ ] 資源限制
- [ ] 效能監控

## [NOTES] 生產部署

### 1. 生產環境設定
```bash
# 使用 nohup 背景執行
nohup python start_integrated_services.py > app.log 2>&1 &

# 使用 systemd 服務
sudo systemctl start email-inbox-service
sudo systemctl enable email-inbox-service
```

### 2. 反向代理設定
```nginx
# Nginx 配置範例
server {
    listen 80;
    server_name yourdomain.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /api/ft-eqc/ {
        proxy_pass http://localhost:8010/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🚨 故障排[EXCEPT_CHAR]

### 1. 常見問題
- [ ] 端口衝突
- [ ] 模組匯入錯誤
- [ ] 資料庫連接失敗
- [ ] 郵件伺服器認證失敗

### 2. [EXCEPT_CHAR]錯工具
```bash
# 檢查端口使用
netstat -tlnp | grep :5000
netstat -tlnp | grep :8010

# 檢查進程狀態
ps aux | grep python

# 檢查資料庫狀態
python -c "from src.infrastructure.adapters.database.email_database import EmailDatabase; db = EmailDatabase(); print(db.get_statistics())"
```

## [OK] 部署完成確認

- [ ] 所有服務正常運行
- [ ] Web 介面可正常訪問
- [ ] 郵件同步功能正常
- [ ] FT-EQC 處理功能正常
- [ ] 日誌記錄正常
- [ ] 監控指標正常
- [ ] 備份策略已實施
- [ ] 文檔已更新

---

**部署完成日期**: ___________  
**部署負責人**: ___________  
**版本資訊**: ___________