#!/usr/bin/env python3
"""
EQC BIN1 最終處理器 v2.0 - 完全模組化版本
整合所有功能：BIN1 統計 + FT-EQC 配對 + 超連結
遵循 CLAUDE.md 功能替換原則，使用現代化依賴注入設計
"""

import os
import sys
import time
from datetime import datetime
from typing import List, Tuple, Dict, Any, Optional

# 添加 src 路徑（動態検測）
current_dir = os.path.dirname(os.path.abspath(__file__))
src_path = None
# 向上搜尋包含 infrastructure 目錄的 src 目錄
for _ in range(10):  # 最多向上搜尋 10 層
    test_src = os.path.join(current_dir, 'infrastructure')
    if os.path.exists(test_src):
        src_path = current_dir
        break
    current_dir = os.path.dirname(current_dir)
    
if src_path:
    sys.path.append(src_path)
else:
    # 備用方案：使用環境變數
    fallback_src = os.getenv('PROJECT_SRC_PATH', '/mnt/d/project/python/outlook_summary/src')
    if os.path.exists(fallback_src):
        sys.path.append(fallback_src)

# 導入原有的配對處理器
from src.infrastructure.adapters.excel.ft_eqc_grouping_processor import (
    CSVFileDiscovery, 
    TimeBasedMatcher, 
    OnlineEQCFailProcessor,
    FTEQCGroupingProcessor
)

# 導入新的模組化組件
from .utils.timestamp_extractor import TimestampExtractor
from .hyperlinks.hyperlink_processor import HyperlinkProcessor
from .monitoring.progress_monitor import ProgressMonitor
from .monitoring.debug_logger import EQCDebugLogger
from .processors.eqc_statistics_calculator import EQCStatisticsCalculator
from .processors.eqc_file_scanner import EQCFileScanner
from .processors.eqc_bin1_processor import EQCBin1Processor

# 導入程式碼對比相關模組 (動態導入以避免循環依賴)
# from .eqc_simple_detector import EQCSimpleDetector
# from .eqc_dual_search_corrected import EQCDualSearchCorrected

# 導入 CTA All-in-One 處理器
from ..cta.cta_integrated_processor import process_directory_with_cta


class FileConverter:
    """檔案轉換工具類別 - 從原主處理器中提取"""
    
    @staticmethod
    def delete_files_by_extensions(folder_path: str) -> int:
        """
        根據環境變數DELETE_FILE_EXTENSIONS刪[EXCEPT_CHAR]指定副檔名的檔案
        
        Returns:
            int: 成功刪[EXCEPT_CHAR]的檔案數量
        """
        from dotenv import load_dotenv
        load_dotenv()
        deleted_count = 0
        
        # 讀取環境變數
        delete_extensions = os.getenv('DELETE_FILE_EXTENSIONS', '')
        if not delete_extensions.strip():
            return 0
            
        # 解析副檔名列表，轉為小寫並移[EXCEPT_CHAR]空格
        extensions_to_delete = [ext.strip().lower() for ext in delete_extensions.split(',') if ext.strip()]
        
        if not extensions_to_delete:
            return 0
            
        print(f"[DELETE] 自動刪[EXCEPT_CHAR]副檔名: {', '.join(extensions_to_delete)}")
        
        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_name, file_ext = os.path.splitext(file)
                    
                    # 檢查副檔名是否在刪[EXCEPT_CHAR]清單中（不區分大小寫）
                    if file_ext.lower().lstrip('.') in extensions_to_delete:
                        try:
                            os.remove(file_path)
                            print(f"   [DELETE] 已刪[EXCEPT_CHAR]: {file}")
                            deleted_count += 1
                        except Exception as e:
                            print(f"   [WARNING] 刪[EXCEPT_CHAR]失敗: {file} - {e}")
                        
        except Exception as e:
            print(f"[ERROR] 檔案刪[EXCEPT_CHAR]過程中發生錯誤: {e}")
            
        if deleted_count > 0:
            print(f"[OK] 成功刪[EXCEPT_CHAR] {deleted_count} 個指定副檔名的檔案")
        else:
            print("[INFO] 未發現需要刪[EXCEPT_CHAR]的指定副檔名檔案")
            
        return deleted_count

    @staticmethod
    def convert_spd_files_to_csv(folder_path: str) -> int:
        """
        將目錄下所有 .spd 檔案（不區分大小寫）重命名為 .csv（小寫）
        
        Returns:
            int: 成功轉換的檔案數量
        """
        converted_count = 0
        
        try:
            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    file_name, file_ext = os.path.splitext(file)
                    
                    # 檢查副檔名是否為 .spd（不區分大小寫）
                    if file_ext.lower() == '.spd':
                        # 新的檔案路徑，副檔名改為 .csv（小寫）
                        new_file_path = os.path.join(root, file_name + '.csv')
                        
                        # 重命名檔案
                        os.rename(file_path, new_file_path)
                        print(f"   [EDIT] 轉換: {file} → {file_name}.csv")
                        converted_count += 1
                        
        except Exception as e:
            print(f"[ERROR] SPD檔案轉換過程中發生錯誤: {e}")
            
        if converted_count > 0:
            print(f"[OK] 成功轉換 {converted_count} 個 SPD 檔案為 CSV 格式")
        else:
            print("[INFO] 未發現需要轉換的 SPD 檔案")
            
        return converted_count


class EQCBin1FinalProcessorV2:
    """
    EQC BIN1 完整整合處理器 v2.0 - 完全模組化版本
    遵循 CLAUDE.md 功能替換原則，使用依賴注入設計
    """
    
    def __init__(self):
        """初始化處理器 - 所有依賴將在需要時動態建立"""
        self.data_start_row = int(os.getenv('DATA_START_ROW', '12'))  # 資料起始行（可配置）
        self.file_discovery = CSVFileDiscovery()
        self.time_matcher = TimeBasedMatcher()
        self.fail_processor = OnlineEQCFailProcessor()
        
        # [FIRE] 核心無限迴圈防護設定
        self.MAX_SCAN_LINES = int(os.getenv('MAX_SCAN_LINES', '10000'))  # 最大掃描行數
        self.PROCESSING_TIMEOUT = int(os.getenv('PROCESSING_TIMEOUT', '1200'))  # 處理超時(秒)
        
        # 模組化組件將在處理時動態建立
        self.timestamp_extractor = None
        self.hyperlink_processor = None
        self.progress_monitor = None
        self.debug_logger = None
        self.statistics_calculator = None
        self.file_scanner = None
        self.bin1_processor = None

        # 程式碼對比組件
        self.simple_detector = None
        self.dual_search = None
    
    def _initialize_components(self, folder_path: str, enable_debug_log: bool = True) -> None:
        """
        初始化所有模組化組件 - 依賴注入模式
        
        Args:
            folder_path: 處理資料夾路徑
            enable_debug_log: 是否啟用[EXCEPT_CHAR]錯日誌
        """
        start_time = time.time()
        
        # 創建基礎組件
        self.timestamp_extractor = TimestampExtractor()
        self.hyperlink_processor = HyperlinkProcessor(folder_path)
        self.progress_monitor = ProgressMonitor(start_time, self.PROCESSING_TIMEOUT)
        self.debug_logger = EQCDebugLogger(folder_path, enabled=enable_debug_log)
        
        # 創建處理組件 - 注入依賴
        self.statistics_calculator = EQCStatisticsCalculator(
            self.timestamp_extractor, self.debug_logger, self.data_start_row
        )
        
        self.file_scanner = EQCFileScanner(
            self.timestamp_extractor, self.hyperlink_processor, 
            self.data_start_row, self.MAX_SCAN_LINES, self.PROCESSING_TIMEOUT
        )
        
        self.bin1_processor = EQCBin1Processor(
            self.timestamp_extractor, self.hyperlink_processor,
            self.progress_monitor, self.debug_logger,
            self.statistics_calculator, self.file_scanner
        )

        # 創建程式碼對比組件 (使用絕對導入)
        try:
            # 使用絕對導入以確保可靠性
            from src.infrastructure.adapters.excel.eqc.eqc_simple_detector import EQCSimpleDetector
            from src.infrastructure.adapters.excel.eqc.eqc_dual_search_corrected import EQCDualSearchCorrected

            self.simple_detector = EQCSimpleDetector()
            self.dual_search = EQCDualSearchCorrected()
            print("[OK] 程式碼對比組件載入成功")

        except ImportError as e:
            print(f"[WARNING] 程式碼對比組件導入失敗: {e}")
            self.simple_detector = None
            self.dual_search = None

        print(f"[LINK] 超連結處理器已初始化，本地路征: {folder_path}")
        print(f"[NETWORK] 網路共享路徑: {self.hyperlink_processor.get_network_path()}")

        if self.debug_logger.enabled:
            print(f"[BOARD] 詳細日誌記錄已啟用")
        elif not enable_debug_log:
            print(f"[BOARD] 詳細日誌記錄已停用 (enable_debug_log=False)")
        print()
    
    def process_complete_eqc_integration(self, folder_path: str, enable_debug_log: bool = True) -> Tuple[Optional[str], Optional[str]]:
        """
        完整的 EQC 整合處理流程 - 完全模組化版本
        保持與原版本完全相同的API接口
        
        Args:
            folder_path: 處理的資料夾路徑
            enable_debug_log: 是否啟用 DEBUG LOG 生成 (預設: True)
            
        Returns:
            Tuple[Optional[str], Optional[str]]: (EQCTOTALDATA.csv路徑, EQCTOTALDATA_RAW.csv路徑)
        """
        print("[SEARCH] [eqc_bin1_final_processor.py] process_complete_eqc_integration() - 開始執行")
        # 初始化所有模組化組件
        self._initialize_components(folder_path, enable_debug_log)
        
        print(f"[FIRE] 開始 EQC BIN1 完整整合處理系統（超時限制: {self.PROCESSING_TIMEOUT}秒）")
        print("=" * 60)

        # 步驟0: 中文路徑處理（繁中資料夾處理）
        print("[TOOL] 步驟0: 處理中文路徑與特殊符號")
        try:
            from infrastructure.adapters.filesystem.chinese_path_processor import process_chinese_paths_in_directory
            chinese_path_success = process_chinese_paths_in_directory(folder_path, verbose=False)
            if chinese_path_success:
                print("   [OK] 路徑標準化完成")
            else:
                print("   [WARNING] 路徑標準化失敗，但繼續執行")
        except ImportError:
            print("   [WARNING] 中文路徑處理器未找到，跳過此步驟")
        print()

        # 步驟0A: 特定副檔名檔案自動刪[EXCEPT_CHAR]（使用模組化工具）
        print("[DELETE] 步驟0A: 特定副檔名檔案自動刪[EXCEPT_CHAR]")
        deleted_count = FileConverter.delete_files_by_extensions(folder_path)
        if deleted_count > 0:
            print(f"   [FOLDER_TABS] 已刪[EXCEPT_CHAR] {deleted_count} 個指定副檔名的檔案")
        print()
        
        # 步驟0B: SPD檔案自動轉換為CSV（使用模組化工具）
        print("[REFRESH] 步驟0B: SPD檔案自動轉換")
        spd_converted_count = FileConverter.convert_spd_files_to_csv(folder_path)
        if spd_converted_count > 0:
            print(f"   [FOLDER] 已將 {spd_converted_count} 個 SPD 檔案轉換為 CSV 格式")
        print()
        
        # 步驟0C: CTA All-in-One 處理
        print("[TOOL] 步驟0C: CTA All-in-One 處理")
        try:
            cta_result = process_directory_with_cta(folder_path)
            if cta_result.get('success'):
                print(f"   [OK] CTA處理完成: 解壓縮{len(cta_result.get('extracted_files', []))}個檔案")
                print(f"   [FILE] 生成Data11: {len(cta_result.get('output_csv_files', []))}個檔案") 
                print(f"   [FOLDER] 歸檔CTA: {len(cta_result.get('archived_files', []))}個檔案")
            else:
                print(f"   [WARNING] CTA處理失敗: {cta_result.get('error_message', 'Unknown error')}")
        except Exception as e:
            print(f"   [WARNING] CTA處理跳過: {e}")
        print()
        
        # 步驟1: 執行 FT-EQC 配對處理（使用原有處理器）
        ft_eqc_processor = FTEQCGroupingProcessor()
        grouping_result = ft_eqc_processor.process_folder(folder_path)
        
        print(f"[CHART] FT-EQC 配對結果:")
        print(f"   [LINK] 成功配對: {len(grouping_result.matched_pairs)} 對")
        print(f"   [FILE] 未配對 EQC: {len(grouping_result.unmatched_eqc)} 個")
        
        # [BUG] DEBUG: 詳細配對日誌
        print(f"\n[BUG] DEBUG: 詳細 FT-EQC 配對日誌")
        print("=" * 60)
        for i, (ft_file, eqc_file) in enumerate(grouping_result.matched_pairs, 1):
            ft_name = os.path.basename(ft_file)
            eqc_name = os.path.basename(eqc_file)
            print(f"   配對 {i:2d}: FT=[{ft_name}] [LEFT_RIGHT_ARROW] EQC=[{eqc_name}]")
        
        if grouping_result.unmatched_eqc:
            print(f"\n[BUG] DEBUG: 未配對的 EQC 檔案:")
            for i, eqc_file in enumerate(grouping_result.unmatched_eqc, 1):
                eqc_name = os.path.basename(eqc_file)
                print(f"   未配對 {i}: {eqc_name}")
        print("=" * 60)
        
        # 步驟2: 取得所有 EQC 檔案並按時間分類
        all_csv_files = self.file_discovery.find_all_csv_files(folder_path)
        eqc_files = self.file_discovery.classify_eqc_files(all_csv_files)
        
        # 排[EXCEPT_CHAR]已生成的檔案（但保留原始的 EQCFAILDATA 檔案用於處理）
        eqc_files = [f for f in eqc_files if 'EQC_BIN1_WITH_STATISTICS.csv' not in f 
                    and 'EQCTOTALDATA' not in f]
        
        # 步驟3: 使用模組化組件處理完整流程
        return self.bin1_processor.process_complete_eqc_integration(grouping_result, eqc_files, folder_path)
    
    def generate_eqc_total_data(self, folder_path: str) -> Tuple[str, str]:
        """
        生成完整的 EQCTOTALDATA.csv 和 EQCTOTALDATA_RAW.csv 檔案
        保持與原版本完全相同的API接口
        
        Args:
            folder_path: 處理資料夾路徑
            
        Returns:
            Tuple[str, str]: (EQCTOTALDATA.csv路徑, EQCTOTALDATA_RAW.csv路徑)
        """
        print(f"\n[TARGET] 開始生成 EQCTOTALDATA 檔案...")
        
        # 初始化組件
        self._initialize_components(folder_path)
        
        # 步驟1: 掃描所有 CSV 檔案
        discovery = CSVFileDiscovery()
        csv_files = discovery.find_all_csv_files(folder_path)
        
        # 記錄檔案掃描結果
        self.debug_logger.log_section("EQCTOTALDATA 完整資料生成")
        self.debug_logger.log_file_scan("ALL CSV", csv_files)
        
        # 步驟2: 收集所有測試資料
        all_test_data = []
        file_headers = {}
        
        print(f"\n[REFRESH] 收集所有測試資料...檔案數: {len(csv_files)} 個（無限制）")
        
        for file_index, csv_file in enumerate(csv_files):
            # [FIRE] 檢查超時
            if self.progress_monitor.is_timeout():
                print(f"[TIME] 處理超時，停止檔案處理")
                break
                
            filename = os.path.basename(csv_file)
            print(f"[FILE] 處理 {file_index+1}/{len(csv_files)}: {filename}")
            
            try:
                # [CHART] 檔案大小資訊
                file_size_mb = os.path.getsize(csv_file) / (1024 * 1024)
                if file_index % 10 == 0:  # 每10個檔案顯示一次大小資訊
                    print(f"   [PACKAGE] 檔案大小: {file_size_mb:.1f}MB")
                
                # [LINK] 檔案級別超連結轉換（只做一次）
                network_path = self.hyperlink_processor.convert_to_network_path(csv_file)
                hyperlink_cell = f"HYPERLINK:{network_path}"
                    
                with open(csv_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                # 找到標題行
                header_row = None
                data_start = self.data_start_row
                
                for i in range(min(15, len(lines))):
                    if ',' in lines[i] and any(term in lines[i].lower() for term in ['site', 'bin', 'index']):
                        header_row = lines[i].strip()
                        data_start = i + 1
                        break
                
                if header_row:
                    file_headers[csv_file] = header_row
                
                # [FIRE] 讀取資料行，但限制最大行數
                file_data_count = 0
                max_scan_to = min(len(lines), data_start + self.MAX_SCAN_LINES)
                
                for i in range(data_start, max_scan_to):
                    # 每1000行檢查一次超時
                    if file_data_count % 1000 == 0 and file_data_count > 0:
                        if self.progress_monitor.is_timeout():
                            print(f"[TIME] 處理超時，停止檔案 {filename} 的處理")
                            break
                    
                    line = lines[i].strip()
                    if not line or len(line) < 10:
                        continue
                    
                    # 在第3欄插入超連結（使用預先計算的值）
                    elements = line.split(',')
                    if len(elements) >= 3:
                        elements[2] = hyperlink_cell
                        enhanced_line = ','.join(elements)
                        all_test_data.append(enhanced_line)
                        file_data_count += 1
                
                print(f"   [OK] {filename}: {file_data_count} 筆測試資料")
                self.progress_monitor.record_success()
                
            except Exception as e:
                print(f"   [ERROR] {filename}: 讀取失敗 - {e}")
                self.progress_monitor.record_failure()
        
        # 步驟3: 建立統一標題行
        if file_headers:
            # 使用第一個有效標題作為基準
            unified_header = list(file_headers.values())[0]
            print(f"[EDIT] 使用統一標題: {len(unified_header.split(','))} 欄")
        else:
            # 預設標題
            unified_header = "Index_Time,Site_No,Hyperlink,Bin#,測試項目1,測試項目2,測試項目3"
            print(f"[EDIT] 使用預設標題")
        
        # 步驟4: 生成 EQCTOTALDATA.csv
        eqc_total_file = os.path.join(folder_path, f"EQCTOTALDATA.csv")
        eqc_raw_file = os.path.join(folder_path, f"EQCTOTALDATA_RAW.csv")
        
        # 組合完整內容
        total_content = [unified_header] + all_test_data
        
        # 寫入 EQCTOTALDATA.csv
        with open(eqc_total_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(total_content) + '\n')
        
        # 寫入 EQCTOTALDATA_RAW.csv (直接複製)
        with open(eqc_raw_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(total_content) + '\n')
        
        print(f"\n[SAVE] EQCTOTALDATA 檔案已生成:")
        print(f"   [FILE] EQCTOTALDATA.csv: {len(total_content)} 行")
        print(f"   [FILE] EQCTOTALDATA_RAW.csv: {len(total_content)} 行 (相同內容)")
        print(f"   [CHART] 測試資料總數: {len(all_test_data)} 筆")
        print(f"   [FOLDER] 處理檔案數: {len(csv_files)} 個")
        
        # 保存詳細日誌（完整資料生成模式，不涉及配對統計）
        self.debug_logger.log_summary(0, 0, 0)  # 該模式不涉及配對統計
        self.debug_logger.save_log()
        
        return eqc_total_file, eqc_raw_file

    def one_click_complete_comparison(self, folder_path: str, code_regions: dict = None) -> Dict[str, Any]:
        """
        一鍵完成程式碼對比 - 4階段完整流程
        完全對應原本的多步驟流程：
        Stage 1: 自動生成 EQCTOTALDATA (process_complete_eqc_integration)
        Stage 2: 程式碼區間檢測 (find_code_region)
        Stage 3: 雙重搜尋機制 (perform_corrected_dual_search)
        Stage 4: 完整報告生成

        Args:
            folder_path: 處理資料夾路徑
            code_regions: 前端指定的程式碼區間設定

        Returns:
            Dict[str, Any]: 完整的處理結果報告
        """
        import time
        import json
        from datetime import datetime

        start_time = time.time()

        print(f"[ROCKET] 開始一鍵完成程式碼對比流程")
        print(f"[FOLDER] 處理資料夾: {folder_path}")
        print(f"[TARGET] CODE 區間設定: {code_regions}")
        print("=" * 80)

        # 步驟0: 中文路徑處理（繁中資料夾處理）
        print("[TOOL] 步驟0: 處理中文路徑與特殊符號")
        try:
            from infrastructure.adapters.filesystem.chinese_path_processor import process_chinese_paths_in_directory
            chinese_path_success = process_chinese_paths_in_directory(folder_path, verbose=False)
            if chinese_path_success:
                print("   [OK] 路徑標準化完成")
            else:
                print("   [WARNING] 路徑標準化失敗，但繼續執行")
        except ImportError:
            print("   [WARNING] 中文路徑處理器未找到，跳過此步驟")
        print()

        # 初始化所有模組化組件
        self._initialize_components(folder_path, enable_debug_log=True)

        try:
            # Stage 1: 自動生成 EQCTOTALDATA (對應原本的 Step 1)
            print(f"\n[FIRE] Stage 1: 自動生成 EQCTOTALDATA")
            print("-" * 60)
            stage1_result = self._perform_stage1_eqctotaldata_generation(folder_path)

            if not stage1_result['success']:
                return self._generate_error_response('stage1', stage1_result['error'], start_time)

            # Stage 2: 程式碼區間檢測 (對應原本的 Step 2 第一部分)
            print(f"\n[FIRE] Stage 2: 程式碼區間檢測")
            print("-" * 60)
            stage2_result = self._perform_code_region_detection(folder_path, code_regions)

            if not stage2_result['success']:
                return self._generate_error_response('stage2', stage2_result['error'], start_time)

            # Stage 3: 雙重搜尋機制 (對應原本的 Step 2 第二部分)
            print(f"\n[FIRE] Stage 3: 雙重搜尋機制")
            print("-" * 60)
            stage3_result = self._perform_dual_search_analysis(
                stage1_result['eqctotaldata_path'],
                stage2_result['result']['code_region'],
                stage2_result['result']['backup_region'],
                code_regions
            )

            if not stage3_result['success']:
                return self._generate_error_response('stage3', stage3_result['error'], start_time)

            # Stage 4: 完整報告生成 (對應原本的 Step 3-4)
            print(f"\n[FIRE] Stage 4: 完整報告生成")
            print("-" * 60)
            stage4_result = self._generate_complete_comparison_report(
                stage1_result, stage2_result, stage3_result, folder_path
            )

            # 計算總處理時間
            total_time = time.time() - start_time

            # 生成最終結果
            final_result = {
                'status': 'success',
                'message': '一鍵完成程式碼對比成功',
                'folder_path': folder_path,
                'processing_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_execution_time': round(total_time, 2),
                'stages': {
                    'stage1_eqctotaldata': stage1_result,
                    'stage2_code_region': stage2_result,
                    'stage3_dual_search': stage3_result,
                    'stage4_complete_report': stage4_result
                },
                'files': [
                    {
                        'name': 'EQCTOTALDATA.csv',
                        'path': stage1_result['eqctotaldata_path'],
                        'size_mb': self._get_file_size_mb(stage1_result['eqctotaldata_path'])
                    },
                    {
                        'name': 'EQCTOTALDATA_RAW.csv',
                        'path': stage1_result['eqctotaldata_raw_path'],
                        'size_mb': self._get_file_size_mb(stage1_result['eqctotaldata_raw_path'])
                    }
                ],
                'overall_success': True
            }

            # 如果有報告檔案，加入到檔案列表
            if stage4_result['success'] and 'report_path' in stage4_result:
                final_result['files'].append({
                    'name': os.path.basename(stage4_result['report_path']),
                    'path': stage4_result['report_path'],
                    'size_mb': self._get_file_size_mb(stage4_result['report_path'])
                })

            print(f"\n[PARTY] 一鍵完成程式碼對比流程成功完成！")
            print(f"[TIME] 總處理時間: {total_time:.2f} 秒")
            print(f"[FILE] 生成檔案數: {len(final_result['files'])} 個")

            return final_result

        except Exception as e:
            error_time = time.time() - start_time
            print(f"[ERROR] 一鍵完成程式碼對比流程失敗: {e}")
            return {
                'status': 'error',
                'message': f'一鍵完成程式碼對比失敗: {str(e)}',
                'folder_path': folder_path,
                'processing_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_execution_time': round(error_time, 2),
                'error_stage': 'unknown',
                'overall_success': False
            }


    def _perform_stage1_eqctotaldata_generation(self, folder_path: str) -> Dict[str, Any]:
        """
        執行 Stage 1: 自動生成 EQCTOTALDATA
        對應原本的 process_complete_eqc_integration() 功能
        """
        try:
            # 使用現有的 process_complete_eqc_integration 方法
            eqctotaldata_path, eqctotaldata_raw_path = self.process_complete_eqc_integration(folder_path)

            if eqctotaldata_path and eqctotaldata_raw_path:
                print(f"[OK] Stage 1 完成: EQCTOTALDATA 生成成功")
                print(f"   [FILE] EQCTOTALDATA.csv: {os.path.basename(eqctotaldata_path)}")
                print(f"   [FILE] EQCTOTALDATA_RAW.csv: {os.path.basename(eqctotaldata_raw_path)}")

                return {
                    'success': True,
                    'eqctotaldata_path': eqctotaldata_path,
                    'eqctotaldata_raw_path': eqctotaldata_raw_path,
                    'execution_time': self.progress_monitor.get_elapsed_time()
                }
            else:
                return {
                    'success': False,
                    'error': 'EQCTOTALDATA 檔案生成失敗'
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'Stage 1 執行失敗: {str(e)}'
            }

    def _perform_code_region_detection(self, folder_path: str, code_regions: dict = None) -> Dict[str, Any]:
        """
        執行 Stage 2: 程式碼區間檢測
        對應原本的 StandardEQCProcessor.process_from_stage2_only() 第一部分
        """
        try:
            print(f"[SEARCH] 執行程式碼區間檢測...")

            # 檢查組件是否可用
            if self.simple_detector is None:
                return {
                    'success': False,
                    'error': 'EQCSimpleDetector 組件未正確載入'
                }

            # 使用模組化的 EQCSimpleDetector
            detection_result = self.simple_detector.find_code_region(folder_path, code_regions)

            if detection_result['status'] == 'success':
                code_region = detection_result['code_region']
                backup_region = detection_result.get('backup_region', {})

                print(f"[OK] Stage 2 完成: 程式碼區間檢測成功")
                print(f"   [CHART] 主要區間: 第{code_region['start_column_number']}-{code_region['end_column_number']}欄")
                print(f"   [CHART] 區間長度: {code_region['column_count']} 個欄位")

                if backup_region.get('found'):
                    print(f"   [CHART] 備用區間: 第{backup_region['backup_start_column']}-{backup_region['backup_end_column']}欄")

                return {
                    'success': True,
                    'result': detection_result,
                    'execution_time': 0  # EQCSimpleDetector 內部不計時
                }
            else:
                return {
                    'success': False,
                    'error': detection_result.get('message', '程式碼區間檢測失敗')
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'Stage 2 執行失敗: {str(e)}'
            }

    def _perform_dual_search_analysis(self, eqctotaldata_path: str, main_region: dict, backup_region: dict, code_regions: dict = None) -> Dict[str, Any]:
        """
        執行 Stage 3: 雙重搜尋機制
        對應原本的 StandardEQCProcessor.process_from_stage2_only() 第二部分
        """
        try:
            print(f"[SEARCH] 執行雙重搜尋機制...")

            # 檢查組件是否可用
            if self.dual_search is None:
                return {
                    'success': False,
                    'error': 'EQCDualSearchCorrected 組件未正確載入'
                }

            # 讀取 EQCTOTALDATA.csv
            with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                eqctotaldata_rows = f.readlines()

            print(f"   [FILE] 讀取 EQCTOTALDATA.csv: {len(eqctotaldata_rows)} 行")

            # 使用模組化的 EQCDualSearchCorrected
            search_result = self.dual_search.perform_corrected_dual_search(
                eqctotaldata_rows, main_region, backup_region, code_regions
            )

            if search_result['success']:
                print(f"[OK] Stage 3 完成: 雙重搜尋成功")
                print(f"   [TARGET] 搜尋方法: {search_result['search_method']}")
                print(f"   [CHART] 匹配率: {search_result.get('match_rate', 'N/A')}")

                if 'total_matched' in search_result:
                    print(f"   [CHART] 匹配數量: {search_result['total_matched']}")

                return {
                    'success': True,
                    'result': search_result,
                    'execution_time': 0  # EQCDualSearchCorrected 內部不計時
                }
            else:
                return {
                    'success': False,
                    'error': search_result.get('main_failure_reason', '雙重搜尋失敗')
                }

        except Exception as e:
            return {
                'success': False,
                'error': f'Stage 3 執行失敗: {str(e)}'
            }

    def _generate_complete_comparison_report(self, stage1_result: dict, stage2_result: dict, stage3_result: dict, folder_path: str) -> Dict[str, Any]:
        """
        執行 Stage 4: 完整報告生成
        對應原本的 Step 3-4 (analyze_eqc_real_data + 前端整合顯示)
        """
        try:
            from datetime import datetime
            import json

            print(f"[BOARD] 生成完整程式碼對比報告...")

            # 整合所有階段結果
            complete_report = {
                'processing_summary': {
                    'folder_path': folder_path,
                    'processing_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'total_execution_time': sum([
                        stage1_result.get('execution_time', 0),
                        stage2_result.get('execution_time', 0),
                        stage3_result.get('execution_time', 0)
                    ])
                },
                'stage1_eqctotaldata_generation': {
                    'success': stage1_result['success'],
                    'eqctotaldata_path': stage1_result.get('eqctotaldata_path'),
                    'eqctotaldata_raw_path': stage1_result.get('eqctotaldata_raw_path'),
                    'execution_time': stage1_result.get('execution_time', 0)
                },
                'stage2_code_region_detection': {
                    'success': stage2_result['success'],
                    'detection_result': stage2_result.get('result'),
                    'execution_time': stage2_result.get('execution_time', 0)
                },
                'stage3_dual_search_analysis': {
                    'success': stage3_result['success'],
                    'search_result': stage3_result.get('result'),
                    'execution_time': stage3_result.get('execution_time', 0)
                },
                'overall_success': all([
                    stage1_result['success'],
                    stage2_result['success'],
                    stage3_result['success']
                ])
            }

            # 保存報告到檔案
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"EQC_一鍵完成程式碼對比報告_{timestamp}.json"
            report_path = os.path.join(folder_path, report_filename)

            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(complete_report, f, ensure_ascii=False, indent=2)

            print(f"[OK] Stage 4 完成: 完整報告已生成")
            print(f"   [FILE] 報告檔案: {report_filename}")
            print(f"   [CHART] 整體成功: {'是' if complete_report['overall_success'] else '否'}")

            return {
                'success': True,
                'report_path': report_path,
                'report_data': complete_report,
                'execution_time': 0
            }

        except Exception as e:
            return {
                'success': False,
                'error': f'Stage 4 執行失敗: {str(e)}'
            }

    def _generate_error_response(self, failed_stage: str, error_message: str, start_time: float) -> Dict[str, Any]:
        """生成錯誤響應"""
        from datetime import datetime
        import time

        error_time = time.time() - start_time
        return {
            'status': 'error',
            'message': f'{failed_stage} 階段失敗: {error_message}',
            'folder_path': getattr(self, 'folder_path', 'unknown'),
            'processing_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_execution_time': round(error_time, 2),
            'error_stage': failed_stage,
            'overall_success': False
        }

    def _get_file_size_mb(self, file_path: str) -> float:
        """獲取檔案大小(MB)"""
        try:
            if os.path.exists(file_path):
                size_bytes = os.path.getsize(file_path)
                return round(size_bytes / (1024 * 1024), 2)
            return 0.0
        except Exception:
            return 0.0


def main():
    """
    主函數 - Online EQC 完整處理系統 v2.0
    支援選擇輸入，自動產生 EQC BIN1 統計或完整 EQCTOTALDATA
    """
    if len(sys.argv) > 1:
        folder_path = sys.argv[1]
    else:
        # 使用環境變數或當前目錄作為預設
        folder_path = os.getenv('DEFAULT_DATA_FOLDER', '.')
    
    print(f"[TARGET] Online EQC 完整處理系統 v2.0 (完全模組化)")
    print(f"[FOLDER] 處理資料夾: {folder_path}")
    print(f"\n[BOARD] 處理選項:")
    print(f"   1. EQC BIN1 整合統計處理 (含超連結、FT-EQC配對)")
    print(f"   2. 完整 EQCTOTALDATA.csv 生成 (所有測試資料)")
    print(f"   3. 同時執行兩種處理")
    print(f"   4. 一鍵完成程式碼對比 (EQCTOTALDATA → 區間檢測 → 雙重搜尋 → 報告)")
    
    try:
        choice = input(f"\n請選擇處理模式 (1/2/3/4，預設為1): ").strip()
        if not choice:
            choice = "1"
    except:
        choice = "1"
    
    processor = EQCBin1FinalProcessorV2()
    
    if choice in ["1", "3"]:
        print(f"\n[REFRESH] 執行 EQC BIN1 整合統計處理...")
        total_file, raw_file = processor.process_complete_eqc_integration(folder_path)
        
        if total_file and raw_file:
            print(f"[OK] EQC BIN1 處理完成！")
            print(f"   [FILE] {os.path.basename(total_file)}")
            print(f"   [FILE] {os.path.basename(raw_file)}")
        else:
            print("[ERROR] EQC BIN1 處理失敗")
    
    if choice in ["2", "3"]:
        print(f"\n[REFRESH] 執行完整 EQCTOTALDATA 生成...")
        try:
            total_file, raw_file = processor.generate_eqc_total_data(folder_path)
            print(f"[OK] EQCTOTALDATA 生成完成！")
            print(f"   [FILE] {os.path.basename(total_file)}")
            print(f"   [FILE] {os.path.basename(raw_file)}")
        except Exception as e:
            print(f"[ERROR] EQCTOTALDATA 生成失敗: {e}")

    if choice == "4":
        print(f"\n[ROCKET] 執行一鍵完成程式碼對比...")
        try:
            result = processor.one_click_complete_comparison(folder_path)

            if result['status'] == 'success':
                print(f"[OK] 一鍵完成程式碼對比成功！")
                print(f"   [TIME] 總處理時間: {result['total_execution_time']} 秒")
                print(f"   [FILE] 生成檔案:")
                for file_info in result['files']:
                    print(f"      - {file_info['name']} ({file_info['size_mb']:.1f}MB)")

                # 顯示各階段結果摘要
                stages = result['stages']
                print(f"\n[CHART] 各階段執行結果:")
                print(f"   Stage 1 (EQCTOTALDATA): {'[OK] 成功' if stages['stage1_eqctotaldata']['success'] else '[ERROR] 失敗'}")
                print(f"   Stage 2 (程式碼區間檢測): {'[OK] 成功' if stages['stage2_code_region']['success'] else '[ERROR] 失敗'}")
                print(f"   Stage 3 (雙重搜尋機制): {'[OK] 成功' if stages['stage3_dual_search']['success'] else '[ERROR] 失敗'}")
                print(f"   Stage 4 (完整報告生成): {'[OK] 成功' if stages['stage4_complete_report']['success'] else '[ERROR] 失敗'}")

            else:
                print(f"[ERROR] 一鍵完成程式碼對比失敗: {result['message']}")

        except Exception as e:
            print(f"[ERROR] 一鍵完成程式碼對比執行失敗: {e}")

    if choice not in ["1", "2", "3", "4"]:
        print(f"[WARNING]  無效選擇，執行預設 EQC BIN1 處理...")
        total_file, raw_file = processor.process_complete_eqc_integration(folder_path)

        if total_file and raw_file:
            print(f"[OK] 處理完成！")
            print(f"   [FILE] {os.path.basename(total_file)}")
            print(f"   [FILE] {os.path.basename(raw_file)}")
        else:
            print("[ERROR] 處理失敗")


if __name__ == "__main__":
    main()