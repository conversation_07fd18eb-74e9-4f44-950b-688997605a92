# config_manager.py

擴展配置管理系統模組，實現靈活的配置管理系統，支援多環境、熱重載、加密等高級功能。

## ValidationResult

配置驗證結果資料類別，用於記錄配置驗證的結果。

### 屬性
- `is_valid` (bool): 是否驗證通過
- `errors` (List[str]): 錯誤列表，預設為空列表
- `warnings` (List[str]): 警告列表，預設為空列表

## FileStorageConfig

檔案儲存配置模型，繼承自 BaseModel，用於設定檔案儲存相關參數。

### 屬性
- `temp_dir` (Path): 暫存目錄，預設為 Path("/tmp/outlook_summary")
- `network_base_path` (str): 網路基礎路徑，預設為 "//server/outlook-data"
- `max_file_size_mb` (int): 最大檔案大小（MB），預設為100，必須大於0
- `allowed_extensions` (List[str]): 允許的副檔名，預設為 [".csv", ".xlsx", ".zip", ".txt"]
- `cleanup_after_days` (int): 清理天數，預設為7，必須大於0
- `enable_compression` (bool): 是否啟用壓縮，預設為True
- `backup_enabled` (bool): 是否啟用備份，預設為True
- `backup_retention_days` (int): 備份保留天數，預設為30，必須大於0

## OutlookConfig

Outlook 配置模型，繼承自 BaseModel，用於設定 Outlook 相關參數。

### 屬性
- `monitor_folder` (str): 監控資料夾，預設為 "Inbox"
- `backup_folder` (str): 備份資料夾，預設為 "Processed"
- `check_interval_seconds` (int): 檢查間隔（秒），預設為30，必須大於0
- `auto_mark_read` (bool): 是否自動標記為已讀，預設為True
- `max_email_age_days` (int): 最大郵件年齡（天），預設為30，必須大於0
- `enable_filters` (bool): 是否啟用過濾器，預設為True
- `filter_rules` (List[str]): 過濾規則，預設為空列表

## VendorConfig

廠商配置模型，繼承自 BaseModel，用於設定廠商相關參數。

### 屬性
- `name` (str): 廠商名稱，必填
- `email_patterns` (List[str]): 郵件模式，必填
- `subject_keywords` (List[str]): 主旨關鍵字，必填
- `parser_class` (str): 解析器類別，必填
- `enabled` (bool): 是否啟用，預設為True
- `priority` (int): 優先級，預設為1，必須大於0
- `timeout_seconds` (int): 超時時間（秒），預設為300，必須大於0
- `retry_count` (int): 重試次數，預設為3，必須大於等於0
- `custom_settings` (Dict[str, Any]): 自訂設定，預設為空字典

## PerformanceConfig

效能配置模型，繼承自 BaseModel，用於設定效能相關參數。

### 屬性
- `max_concurrent_emails` (int): 最大並發郵件數，預設為3，必須大於0
- `email_processing_timeout` (int): 郵件處理超時時間（秒），預設為300，必須大於0
- `file_processing_timeout` (int): 檔案處理超時時間（秒），預設為600，必須大於0
- `memory_limit_mb` (int): 記憶體限制（MB），預設為512，必須大於0
- `enable_caching` (bool): 是否啟用快取，預設為True
- `cache_ttl_seconds` (int): 快取存活時間（秒），預設為3600，必須大於0
- `enable_profiling` (bool): 是否啟用效能分析，預設為False
- `profile_output_dir` (Path): 效能分析輸出目錄，預設為 Path("/tmp/outlook_profiles")

## SecurityConfig

安全配置模型，繼承自 BaseModel，用於設定安全相關參數。

### 屬性
- `encrypt_sensitive_data` (bool): 是否加密敏感資料，預設為True
- `log_email_content` (bool): 是否記錄郵件內容，預設為False
- `allowed_file_types` (List[str]): 允許的檔案類型，預設為 [".csv", ".xlsx", ".zip"]
- `max_attachment_size_mb` (int): 最大附件大小（MB），預設為50，必須大於0
- `virus_scan_enabled` (bool): 是否啟用病毒掃描，預設為False
- `scan_command` (Optional[str]): 掃描命令，可選
- `quarantine_dir` (Path): 隔離目錄，預設為 Path("/tmp/outlook_quarantine")

## AdvancedSettings

擴展設定類別，繼承自 Settings，包含所有進階配置選項。

### 屬性
- `file_storage` (FileStorageConfig): 檔案儲存配置，預設為新實例
- `outlook` (OutlookConfig): Outlook 配置，預設為新實例
- `performance` (PerformanceConfig): 效能配置，預設為新實例
- `security` (SecurityConfig): 安全配置，預設為新實例
- `vendors` (Dict[str, VendorConfig]): 廠商配置字典，預設為空字典
- `environment` (str): 環境名稱，預設為 "development"
- `feature_flags` (Dict[str, bool]): 功能開關，預設為空字典
- `custom_settings` (Dict[str, Any]): 自訂設定，預設為空字典

## ConfigManager

配置管理器主類別，負責載入、驗證、管理配置。

### 屬性
- `current_env` (str): 當前環境
- `config_dir` (Path): 配置目錄
- `config` (AdvancedSettings): 當前配置
- `_encryption_key` (bytes): 加密金鑰

### __init__

初始化配置管理器。

**參數:**
- `env` (str): 環境名稱，預設為 "development"
- `config_dir` (Optional[Path]): 配置目錄，可選，預設為 Path("config")

**返回值:**
- None

### load_from_file

從檔案載入配置。

**參數:**
- `file_path` (str): 配置檔案路徑

**返回值:**
- AdvancedSettings: 載入的配置

**異常:**
- FileNotFoundError: 當配置檔案不存在時拋出
- ValueError: 當檔案格式不支援時拋出

### switch_environment

切換環境。

**參數:**
- `env` (str): 環境名稱

**返回值:**
- None

**異常:**
- ValueError: 當環境名稱不支援時拋出

### get_vendor_config

取得廠商配置。

**參數:**
- `vendor_code` (str): 廠商代碼

**返回值:**
- Optional[VendorConfig]: 廠商配置，如果不存在則返回None

### set_vendor_configs

設定廠商配置。

**參數:**
- `vendor_configs` (Dict[str, VendorConfig]): 廠商配置字典

**返回值:**
- None

### get_enabled_vendors

取得所有啟用的廠商。

**返回值:**
- List[str]: 啟用的廠商代碼列表

### validate_config

驗證配置。

**參數:**
- `config` (AdvancedSettings): 要驗證的配置

**返回值:**
- ValidationResult: 驗證結果

### validate_config_dict

驗證配置字典。

**參數:**
- `config_data` (Dict[str, Any]): 配置字典

**返回值:**
- ValidationResult: 驗證結果

### reload_config

重新載入配置。

**參數:**
- `override_data` (Optional[Dict[str, Any]]): 覆蓋資料，可選

**返回值:**
- None

### merge_configs

合併配置。

**參數:**
- `base` (Dict[str, Any]): 基礎配置
- `override` (Dict[str, Any]): 覆蓋配置

**返回值:**
- Dict[str, Any]: 合併後的配置

### encrypt_sensitive_config

加密敏感配置資料。

**參數:**
- `sensitive_data` (Dict[str, str]): 敏感資料

**返回值:**
- Dict[str, str]: 加密後的資料

### decrypt_sensitive_config

解密敏感配置資料。

**參數:**
- `encrypted_data` (Dict[str, str]): 加密的資料

**返回值:**
- Dict[str, str]: 解密後的資料

### save_config

儲存配置到檔案。

**參數:**
- `file_path` (Optional[str]): 檔案路徑，可選

**返回值:**
- None

### get_feature_flag

取得功能開關狀態。

**參數:**
- `flag_name` (str): 功能開關名稱
- `default` (bool): 預設值，預設為False

**返回值:**
- bool: 功能開關狀態

### set_feature_flag

設定功能開關。

**參數:**
- `flag_name` (str): 功能開關名稱
- `enabled` (bool): 是否啟用

**返回值:**
- None

## 私有方法

### _load_environment_config

載入環境特定配置的私有方法。

**返回值:**
- AdvancedSettings: 環境配置

### _create_advanced_settings

從字典建立擴展設定的私有方法。

**參數:**
- `config_data` (Dict[str, Any]): 配置字典

**返回值:**
- AdvancedSettings: 擴展設定實例

### _get_or_create_encryption_key

取得或建立加密金鑰的私有方法。

**返回值:**
- bytes: 加密金鑰
