#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EQC InsEqcRtData2 處理器 - 功能替換原則版本

按照 claude.md 功能替換原則重新設計：
- 舊功能：複雜的 CODE 對應與插入邏輯
- 新功能：簡化的 Step 1-2-3 處理流程

核心功能（功能替換版）：
Step 1-2: ALL0 移動功能
- 將主區間全零的 EQC RT 行移動到檔案最後
- 不改變總行數，只重新排列

Step 3: Online EQC FAIL 檢測
- 只找 Online EQC FAIL 行（BIN≠1）
- 輸出 DEBUG LOG 到檔案
- 不修改任何檔案內容

功能替換原則：直接刪[EXCEPT_CHAR]舊版本，不保留向下相容性
"""

import os
import logging
from typing import List, Dict, Any, Tuple
from datetime import datetime
# 載入環境變數（跳過dotenv）
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

class EQCInsEqcRtData2Processor:
    """
    EQC InsEqcRtData2 處理器
    實作完整的 CODE 對應與插入邏輯
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.debug_log_lines = []  # 存儲 Step 3 DEBUG LOG
        self.step3_log_file = None  # Step 3 DEBUG LOG 檔案路徑
    
    def _log_step3_debug(self, message: str):
        """記錄 Step 3 DEBUG LOG 到終端和檔案"""
        # 記錄到終端
        self.logger.info(message)
        
        # 記錄到內存
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        log_line = f"[{timestamp}] {message}"
        self.debug_log_lines.append(log_line)
        
        # 如果有檔案路徑，同時寫入檔案
        if self.step3_log_file:
            try:
                with open(self.step3_log_file, 'a', encoding='utf-8') as f:
                    f.write(log_line + '\n')
            except Exception as e:
                self.logger.warning(f"無法寫入DEBUG LOG檔案 {self.step3_log_file}: {e}")
    
    def _save_step3_debug_log(self, base_dir: str):
        """完成 Step 3 DEBUG LOG 檔案"""
        try:
            if self.step3_log_file and os.path.exists(self.step3_log_file):
                # 在檔案末尾添加結束標記
                end_lines = [
                    "",
                    "=" * 60,
                    f"處理完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "Step 3 - Online EQC FAIL 檢測 DEBUG LOG 結束",
                    "=" * 60
                ]
                
                with open(self.step3_log_file, 'a', encoding='utf-8') as f:
                    for line in end_lines:
                        f.write(line + '\n')
                
                log_filename = os.path.basename(self.step3_log_file)
                self.logger.info(f"   [OK] Step 3 DEBUG LOG 已完成: {log_filename}")
                return self.step3_log_file
            else:
                self.logger.warning("[WARNING] Step 3 DEBUG LOG 檔案不存在，無法完成")
                return None
            
        except Exception as e:
            self.logger.error(f"[ERROR] 完成 Step 3 DEBUG LOG 失敗: {e}")
            return None
    
    # ============================================================
    # 功能替換原則：新的簡化方法實作
    # ============================================================
    
    def perform_step12_all0_movement(self, rows: List[str], start1: int, end1: int) -> Dict[str, Any]:
        """
        Step 1-2: ALL0 移動功能
        將主區間全零的 EQC RT 行移動到檔案最後
        
        功能替換原則：從 eqc_standard_processor.py 整合過來
        """
        try:
            self.logger.info("[TOOL] Step 1-2: 執行 ALL0 移動功能...")
            
            # 計算 EQC RT 開始位置
            try:
                b9_content = rows[8]  # 第9行
                fail_count = int(b9_content.split(',')[1].strip())
                eqc_rt_start = 12 + 1 + (fail_count * 2)  # 第13行 + Golden + Online EQC 對
                self.logger.info(f"   EQC RT 開始位置: 第{eqc_rt_start + 1}行")
            except:
                eqc_rt_start = 34  # 預設位置
                self.logger.warning(f"   無法解析 FAIL 數量，使用預設 EQC RT 開始: 第{eqc_rt_start + 1}行")
            
            all_zero_rows = []
            normal_rows = []
            
            # 分離 ALL0 行和正常行
            for i, row in enumerate(rows):
                if i < eqc_rt_start:  # 標頭和 Online EQC 區保持不變
                    normal_rows.append(row)
                else:  # EQC RT 區域檢查 ALL0
                    if self._is_all_zero_in_code_region(row, start1, end1):
                        all_zero_rows.append(row)
                        self.logger.info(f"   找到 ALL0 行: 第{i+1}行")
                    else:
                        normal_rows.append(row)
            
            # 將 ALL0 行移動到最後
            updated_rows = normal_rows + all_zero_rows
            
            self.logger.info(f"   [OK] ALL0 移動完成: 移動 {len(all_zero_rows)} 行到檔案最後")
            
            return {
                'success': True,
                'moved_count': len(all_zero_rows),
                'updated_rows': updated_rows,
                'eqc_rt_start': eqc_rt_start
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Step 1-2 ALL0 移動失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _is_all_zero_in_code_region(self, row: str, start1: int, end1: int) -> bool:
        """檢查一行在主 CODE 區間是否全部為0"""
        try:
            elements = row.split(',')
            if len(elements) <= end1:
                return False
            
            # 檢查主區間的數值
            for i in range(start1, end1 + 1):
                value = elements[i].strip()
                if value != '0' and value != '0.0' and value != '':
                    return False
            
            return True
        except:
            return False
    
    def perform_step3_fail_detection_only(self, rows: List[str], debug_log_path: str = None) -> Dict[str, Any]:
        """
        Step 3: Online EQC FAIL 檢測（只 DEBUG LOG）
        
        功能替換原則：簡化為純 FAIL 行檢測，不修改檔案
        """
        try:
            self.step3_log_file = debug_log_path
            self.debug_log_lines = []
            
            # 初始化 DEBUG LOG 檔案（僅當路徑不為 None 時）
            if debug_log_path:
                with open(debug_log_path, 'w', encoding='utf-8') as f:
                    f.write(f"Step 3 - Online EQC FAIL 檢測 DEBUG LOG\n")
                    f.write(f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 60 + "\n\n")
            
            self._log_step3_debug("[SEARCH] Step 3: 開始 Online EQC FAIL 檢測...")
            
            # 計算 Online EQC 區間
            try:
                b9_content = rows[8]  # 第9行
                fail_count = int(b9_content.split(',')[1].strip())
                online_eqc_start = 13  # 第14行開始
                online_eqc_end = 12 + 1 + (fail_count * 2)  # 計算結束位置
                self._log_step3_debug(f"   Online EQC 區間: 第{online_eqc_start+1}-{online_eqc_end}行")
            except:
                online_eqc_start = 13
                online_eqc_end = 34
                self._log_step3_debug(f"   使用預設 Online EQC 區間: 第{online_eqc_start+1}-{online_eqc_end}行")
            
            fail_details = []
            fail_count = 0
            
            # 掃描 Online EQC 區間尋找 FAIL 行
            for i in range(online_eqc_start, min(online_eqc_end, len(rows))):
                row = rows[i]
                try:
                    elements = row.split(',')
                    if len(elements) > 1:
                        bin_value = elements[1].strip()
                        if bin_value != '1' and bin_value != '':  # BIN≠1 就是 FAIL
                            fail_count += 1
                            fail_detail = {
                                'row_number': i + 1,
                                'bin_value': bin_value,
                                'content': row[:100] + "..." if len(row) > 100 else row
                            }
                            fail_details.append(fail_detail)
                            self._log_step3_debug(f"   找到 FAIL 行: 第{i+1}行, BIN={bin_value}")
                except:
                    continue
            
            self._log_step3_debug(f"   [OK] FAIL 檢測完成: 找到 {fail_count} 行 FAIL")
            
            # 完成 DEBUG LOG
            self._save_step3_debug_log("")
            
            return {
                'success': True,
                'fail_rows_found': fail_count,
                'fail_details': fail_details,
                'debug_log_file': debug_log_path
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Step 3 FAIL 檢測失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def perform_complete_inseqcrtdata2_workflow(self, eqctotaldata_path: str, start1: int, end1: int, rows: List[str], backup_start1: int = None, backup_end1: int = None) -> Dict[str, Any]:
        """
        完整 InsEqcRtData2 工作流程
        
        功能替換原則：Step 1-2 + Step 3 的完整整合
        """
        try:
            self.logger.info("[TARGET] 開始完整 InsEqcRtData2 處理流程...")
            
            # Step 1-2: ALL0 移動
            step12_result = self.perform_step12_all0_movement(rows, start1, end1)
            if not step12_result['success']:
                return step12_result
            
            updated_rows = step12_result['updated_rows']
            moved_count = step12_result['moved_count']
            
            # 更新檔案（如果有移動）
            if moved_count > 0:
                with open(eqctotaldata_path, 'w', encoding='utf-8') as f:
                    f.writelines(updated_rows)
                self.logger.info(f"   [OK] 檔案已更新: {moved_count} 行 ALL0 移動到最後")
            
            # Step 3: FAIL 檢測（純 DEBUG LOG）- 根據環境變數控制
            eqc_detailed_logs = os.getenv("EQC_DETAILED_LOGS", "true").lower() == "true"
            debug_log_path = eqctotaldata_path.replace('.csv', '_Step3_DEBUG.log') if eqc_detailed_logs else None
            step3_result = self.perform_step3_fail_detection_only(updated_rows, debug_log_path)
            if not step3_result['success']:
                return step3_result
            
            return {
                'success': True,
                'step12_moved_count': moved_count,
                'step3_fail_count': step3_result['fail_rows_found'],
                'debug_log_file': debug_log_path,
                'total_rows_after': len(updated_rows)
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 完整 InsEqcRtData2 處理失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
        
    def perform_complete_inseqcrtdata2_with_eqc_all(self,
                                                    eqctotaldata_path: str,
                                                    start1: int,
                                                    end1: int,
                                                    rows: List[str],
                                                    max_fails_to_process: int = None) -> Dict[str, Any]:
        """
        執行完整的 InsEqcRtData2 Step 1-5 處理流程
        
        Step 1-2: 預先過濾全零 EQC RT 行（ALL0移到後面功能）
        Step 3-5: 動態邊界掃描與FAIL處理循環
        
        核心功能：
        - Step 1-2: 將全零 EQC RT 行移動到檔案末尾
        - Step 3: 找到 Online EQC FAIL 行（BIN≠1）
        - Step 4-5: 匹配並移動對應的 EQC RT 行
        - 完整的資料重組和檔案更新
        """
        try:
            # 設置完整的 DEBUG LOG 檔案
            base_dir = os.path.dirname(eqctotaldata_path)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = f"EQC_InsEqcRtData2_Complete_DEBUG_LOG_{timestamp}.txt"
            self.step3_log_file = os.path.join(base_dir, log_filename)
            
            # 初始化DEBUG LOG檔案
            header_lines = [
                "EQC InsEqcRtData2 完整處理器 - Step 1-5 DEBUG LOG",
                "=" * 60,
                f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"處理階段: Step 1-5 完整 InsEqcRtData2 重組",
                f"檔案路徑: {eqctotaldata_path}",
                f"CODE 區間: 第{start1+1}-{end1+1}欄 ({end1-start1+1}個欄位)",
                "=" * 60,
                ""
            ]
            
            with open(self.step3_log_file, 'w', encoding='utf-8') as f:
                for line in header_lines:
                    f.write(line + '\n')
            
            self.logger.info("[REFRESH] 執行完整 InsEqcRtData2 Step 1-5 重組")
            self.logger.info(f"   檔案路徑: {eqctotaldata_path}")
            self.logger.info(f"   EQC ALL 傳遞的 CODE 區間: start1={start1}, end1={end1}")
            self.logger.info(f"   CODE 匹配範圍: 第{start1+1}-{end1+1}欄 ({end1-start1+1}個欄位)")
            if max_fails_to_process:
                self.logger.info(f"   限制處理數量: 前{max_fails_to_process}顆 FAIL")
            self.logger.info(f"   [FOLDER] Complete DEBUG LOG: {log_filename}")
            
            original_row_count = len(rows)
            
            # 執行完整的 EQC ALL 整合重組邏輯（恢復 Step 1-5）
            reorganized_rows, moves_count, processed_fails = self._reorganize_data_with_eqc_all_params(
                rows, start1, end1, max_fails_to_process
            )
            
            # 寫回檔案
            self._write_updated_data(eqctotaldata_path, reorganized_rows)
            
            final_row_count = len(reorganized_rows)
            
            # 完成 DEBUG LOG
            self._save_step3_debug_log(base_dir)
            
            return {
                'status': 'success',
                'reorganization_completed': True,
                'moves_performed': moves_count,
                'processed_fails': processed_fails,
                'total_rows_before': original_row_count,
                'total_rows_after': final_row_count,
                'row_count_unchanged': original_row_count == final_row_count,
                'eqc_all_integration': {
                    'start1': start1,
                    'end1': end1,
                    'code_match_columns': f"第{start1+1}-{end1+1}欄",
                    'total_columns': end1 - start1 + 1
                },
                'processing_timestamp': datetime.now().isoformat(),
                'final_structure': 'FT PASS → Online EQC FAIL → EQC RT(同IC) → 下一對...',
                'debug_log_file': self.step3_log_file
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 完整 InsEqcRtData2 重組失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }

    def perform_step3_analysis_only(self,
                                  eqctotaldata_path: str,
                                  rows: List[str]) -> Dict[str, Any]:
        """
        Step 3: 只找 Online EQC FAIL 行（功能替換原則 - 簡化版）
        
        核心功能：
        - 找到所有 Online EQC FAIL 行（BIN≠1）
        - 輸出 DEBUG LOG 到檔案
        - 不修改任何檔案內容
        """
        try:
            # 設置 DEBUG LOG 檔案
            base_dir = os.path.dirname(eqctotaldata_path)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = f"EQC_Step3_FAIL_Detection_DEBUG_LOG_{timestamp}.txt"
            self.step3_log_file = os.path.join(base_dir, log_filename)
            
            # 初始化DEBUG LOG檔案
            header_lines = [
                "EQC Step 3 - Online EQC FAIL 行檢測",
                "=" * 50,
                f"處理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"檔案路徑: {eqctotaldata_path}",
                "=" * 50,
                ""
            ]
            
            with open(self.step3_log_file, 'w', encoding='utf-8') as f:
                for line in header_lines:
                    f.write(line + '\n')
            
            self.logger.info("[SEARCH] 執行 Step 3: 找 Online EQC FAIL 行...")
            self.logger.info(f"   [FOLDER] DEBUG LOG: {log_filename}")
            
            # 簡化版 FAIL 行檢測
            fail_rows = self._find_simple_fail_rows(rows)
            
            # 完成 DEBUG LOG
            self._complete_step3_log()
            
            return {
                'status': 'success',
                'fail_rows_found': len(fail_rows),
                'fail_rows': fail_rows,
                'debug_log_file': self.step3_log_file
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 簡化版 Step 3 FAIL行識別失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def _reorganize_data_with_eqc_all_params(self, 
                                            rows: List[str], 
                                            start1: int, 
                                            end1: int,
                                            max_fails_to_process: int = None) -> Tuple[List[str], int, int]:
        """
        使用 EQC ALL 傳遞的參數進行資料重組
        基於固定資料結構和 B9 欄位的動態循環處理
        
        恢復完整的 Step 1-5 功能：
        - Step 1-2: 預先過濾全零 EQC RT 行到檔案末尾（ALL0移到後面功能）
        - Step 3-5: 動態循環處理 FAIL 匹配與移動
        
        Args:
            rows: 所有行資料
            start1: EQC ALL 檢測的 CODE 區間起始位置
            end1: EQC ALL 檢測的 CODE 區間結束位置
            max_fails_to_process: 最大處理的 FAIL 數量
            
        Returns:
            tuple: (重組後的行資料, 移動次數, 處理的FAIL數量)
        """
        rows_copy = [row for row in rows]  # 深拷貝
        moves_count = 0
        processed_fails = 0
        processed_fail_serials = set()  # 追蹤已處理的 FAIL Serial
        
        # 1. 從 B9 欄位獲取 FAIL 數量（消[EXCEPT_CHAR]循環依賴）
        total_onlineqc_fail_cnt = self._get_fail_count_from_b9(rows_copy)
        
        self.logger.info(f"[TARGET] 完整 InsEqcRtData2 Step 1-5 處理開始:")
        self.logger.info(f"   使用 CODE 區間: 第{start1+1}-{end1+1}欄 ({end1-start1+1}個欄位)")
        self.logger.info(f"   B9 欄位 FAIL 數量: {total_onlineqc_fail_cnt}")
        if max_fails_to_process:
            self.logger.info(f"   限制處理數量: 前{max_fails_to_process}顆 FAIL")
        
        # 2. 【Step 1-2: ALL0移到後面功能】預先過濾全零 EQC RT 行到檔案末尾
        boundary_info = self._analyze_boundary_with_b9(rows_copy, total_onlineqc_fail_cnt)
        eqc_rt_start_index = boundary_info['eqc_rt_start_index']
        
        self.logger.info(f"[TOOL] 執行 Step 1-2: 預先過濾全零 EQC RT 行（ALL0移到後面功能）...")
        rows_copy, zero_moves = self._prefilter_zero_eqc_rt_rows(rows_copy, eqc_rt_start_index, start1, end1)
        moves_count += zero_moves
        self.logger.info(f"   [OK] Step 1-2 完成: 預先移動 {zero_moves} 個全零 EQC RT 行到檔案末尾")
        
        # 3. 【Step 3-5】動態循環處理每個 FAIL（現在只處理非零的EQC RT行）
        target_fails = min(max_fails_to_process or total_onlineqc_fail_cnt, total_onlineqc_fail_cnt)
        
        self.logger.info(f"[REFRESH] 執行 Step 3-5: 動態FAIL處理循環（目標: {target_fails}個FAIL）")
        
        while processed_fails < target_fails:
            # 重新計算分界線（每次移動後結構改變）
            self._log_step3_debug(f"\n[REFRESH] 第 {processed_fails + 1} 輪循環:")
            self._log_step3_debug(f"   重新計算邊界...")
            
            boundary_info = self._analyze_boundary_with_b9(rows_copy, total_onlineqc_fail_cnt)
            online_eqc_range = range(13, boundary_info['online_eqc_end_index'])
            eqc_rt_range = range(boundary_info['eqc_rt_start_index'], len(rows_copy))
            
            self._log_step3_debug(f"   [CHART] 邊界計算結果:")
            self._log_step3_debug(f"      Online EQC 結束位置: 第{boundary_info['online_eqc_end_index']}行")
            self._log_step3_debug(f"      EQC RT 開始位置: 第{boundary_info['eqc_rt_start_index']}行")
            
            # 在當前 Online EQC 範圍找下一個 FAIL 行
            self._log_step3_debug(f"   [SEARCH] 在 Online EQC 範圍掃描未處理的 FAIL...")
            fail_rows = self._find_fail_rows_in_range(rows_copy, online_eqc_range, processed_fail_serials)
            
            if not fail_rows:
                self.logger.warning(f"[WARNING] 未找到更多 FAIL 行，已處理 {processed_fails} 個")
                break
                
            current_fail = fail_rows[0]  # 處理第一個找到的 FAIL
            fail_row_index = current_fail['row_index']
            fail_serial = current_fail['serial']
            
            self.logger.info(f"   [TARGET] 找到 FAIL: 第{fail_row_index + 1}行 (Serial={fail_serial}, BIN={current_fail['bin_value']})")
            
            # 在 EQC RT 範圍找匹配的同一顆 IC
            backup_start1 = 1564
            backup_end1 = 1599
            main_mapping_start1 = start1
            main_mapping_end1 = start1 + 35
            
            matching_rt = self._find_matching_rt_with_eqc_all_range(
                rows_copy, current_fail, eqc_rt_range, start1, end1, backup_start1, backup_end1, 
                main_mapping_start1, main_mapping_end1
            )
            
            if matching_rt and matching_rt.get('found'):
                # 批量處理所有匹配的 RT 行
                matched_rt_rows = matching_rt['matched_rt_rows']
                self.logger.info(f"   [OK] 找到 {len(matched_rt_rows)} 個同一顆 IC 的 RT 行")
                
                # 批量移動：從最後一個開始移動（避免索引變化）
                batch_moves = 0
                for i, match_info in enumerate(reversed(matched_rt_rows)):
                    rt_row_index = match_info['rt_row_index']
                    match_type = match_info['match_type']
                    rt_serial = match_info['serial']
                    rt_bin = match_info['bin']
                    
                    # 計算插入位置：FAIL行下方 + 已移動的行數
                    insert_position = fail_row_index + 1 + batch_moves
                    
                    self.logger.info(f"     [BOARD] 移動 RT: 第{rt_row_index+1}行 → 第{insert_position+1}行 (Serial={rt_serial}, 類型={match_type})")
                    
                    # 執行移動
                    rows_copy = self._move_row_to_position(rows_copy, rt_row_index, insert_position)
                    moves_count += 1
                    batch_moves += 1
                
                self.logger.info(f"   [PARTY] 批量移動完成: 共移動 {batch_moves} 個 RT 行到 FAIL 第{fail_row_index+1}行下方")
            else:
                self.logger.info(f"   [WARNING] 第 {processed_fails + 1} 個 FAIL 未找到匹配的 EQC RT")
            
            # 記錄已處理的 FAIL Serial
            processed_fail_serials.add(fail_serial)
            processed_fails += 1
            
            self.logger.info(f"   [OK] 第 {processed_fails} 個 FAIL 處理完成，Serial={fail_serial}")
        
        self.logger.info(f"\n[PARTY] 完整 InsEqcRtData2 Step 1-5 重組完成:")
        self.logger.info(f"   處理的 FAIL 數: {processed_fails}")
        self.logger.info(f"   執行的移動次數: {moves_count}")
        self.logger.info(f"   總行數: {len(rows)} → {len(rows_copy)} (不變)")
        self.logger.info(f"   已處理的所有 FAIL Serials: {processed_fail_serials}")
        
        return rows_copy, moves_count, processed_fails

    def _find_simple_fail_rows(self, rows: List[str]) -> List[Dict]:
        """簡化版：只找 Online EQC FAIL 行（BIN≠1）- 正確範圍版"""
        fail_rows = []
        
        # 計算 Online EQC 結束位置：第9行取得 OnlineEQC_Fail 數量
        try:
            b9_content = rows[8]  # 第9行
            fail_count = int(b9_content.split(',')[1].strip())
            online_eqc_start = 13  # 第14行（索引 13）
            online_eqc_end = 13 + (fail_count * 2)  # Golden(1) + FAIL*2
            
            self._write_step3_log(f"[CHART] 從 B9 欄位解析 OnlineEQC_Fail 數量: {fail_count}")
            self._write_step3_log(f"[TARGET] Online EQC 範圍: 第{online_eqc_start + 1}-{online_eqc_end}行")
            
        except Exception as e:
            # 預設範圍
            online_eqc_start = 13  # 第14行
            online_eqc_end = 33    # 第34行
            self._write_step3_log(f"[WARNING] B9 解析失敗，使用預設範圍: 第{online_eqc_start + 1}-{online_eqc_end + 1}行")
        
        # 只在 Online EQC 範圍內搜尋 FAIL 行
        for i in range(online_eqc_start, min(online_eqc_end + 1, len(rows))):
            row = rows[i]
            if not row.strip():
                continue
                
            line_elements = row.split(',')
            if len(line_elements) < 2:
                continue
                
            serial = line_elements[0].strip()
            bin_value = line_elements[1].strip()
            
            # 檢查是否為 FAIL 行（BIN≠1）
            if bin_value != "1" and bin_value not in ["BIN", "Bin#", "", "1"]:
                fail_info = {
                    'row_index': i,
                    'row_number': i + 1,
                    'serial': serial,
                    'bin_value': bin_value
                }
                fail_rows.append(fail_info)
                
                # 記錄到 DEBUG LOG
                self._write_step3_log(f"🟠 Online EQC FAIL: 第{i+1}行, Serial={serial}, BIN={bin_value}")
        
        self._write_step3_log(f"[TARGET] 在 Online EQC 範圍內找到 {len(fail_rows)} 個 FAIL 行")
        
        return fail_rows
    
    def _write_step3_log(self, message: str):
        """寫入 Step 3 DEBUG LOG"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_line = f"[{timestamp}] {message}"
        
        # 終端輸出
        self.logger.info(f"   {message}")
        
        # 寫入檔案
        if self.step3_log_file:
            try:
                with open(self.step3_log_file, 'a', encoding='utf-8') as f:
                    f.write(log_line + '\n')
            except Exception:
                pass
    
    def _complete_step3_log(self):
        """完成 Step 3 DEBUG LOG"""
        end_lines = [
            "",
            "=" * 50,
            f"處理完成: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "Step 3 - Online EQC FAIL 行檢測結束",
            "=" * 50
        ]
        
        if self.step3_log_file:
            try:
                with open(self.step3_log_file, 'a', encoding='utf-8') as f:
                    for line in end_lines:
                        f.write(line + '\n')
                        
                log_filename = os.path.basename(self.step3_log_file)
                self.logger.info(f"   [OK] Step 3 DEBUG LOG 完成: {log_filename}")
            except Exception as e:
                self.logger.warning(f"[WARNING] 無法完成 DEBUG LOG: {e}")

    # 功能替換原則：[DELETE_CHAR][EXCEPT_CHAR]複雜分析邏輯，已被簡化的 _find_simple_fail_rows 取代
    def _find_online_eqc_fail_rows(self, rows: List[str]) -> List[Dict]:
        """已被 _find_simple_fail_rows 取代（功能替換原則）"""
        # 直接呼叫簡化版本
        return self._find_simple_fail_rows(rows)
    def perform_inseqcrtdata2_reorganization_with_eqc_all_DEPRECATED(self, 
                                                        eqctotaldata_path: str,
                                                        start1: int, 
                                                        end1: int,
                                                        rows: List[str],
                                                        max_fails_to_process: int = None) -> Dict[str, Any]:
        """
        執行 InsEqcRtData2 資料重組邏輯 - EQC ALL 整合版
        基於 EQC ALL 傳遞的參數進行精確的同一顆 IC 匹配和移動
        
        Args:
            eqctotaldata_path: EQCTOTALDATA.csv 檔案路徑
            start1: EQC ALL 檢測的程式碼區間起始位置（如 297）
            end1: EQC ALL 檢測的程式碼區間結束位置（如 334）
            rows: CSV 檔案的所有行資料
            max_fails_to_process: 最大處理的 FAIL 數量（如 2 表示只處理前2顆）
            
        Returns:
            Dict: 完整的處理結果
        """
        try:
            # 設置 Step 3 DEBUG LOG 檔案
            base_dir = os.path.dirname(eqctotaldata_path)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = f"EQC_Step3_DEBUG_LOG_{timestamp}.txt"
            self.step3_log_file = os.path.join(base_dir, log_filename)
            
            # 初始化DEBUG LOG檔案
            header_lines = [
                "EQC InsEqcRtData2 處理器 - Step 3 DEBUG LOG",
                "=" * 60,
                f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"處理階段: Step 3 - 動態邊界掃描與FAIL處理循環",
                f"檔案路徑: {eqctotaldata_path}",
                f"CODE 區間: 第{start1+1}-{end1+1}欄 ({end1-start1+1}個欄位)",
                "=" * 60,
                ""
            ]
            
            with open(self.step3_log_file, 'w', encoding='utf-8') as f:
                for line in header_lines:
                    f.write(line + '\n')
            
            self.logger.info("[REFRESH] 執行 InsEqcRtData2 資料重組 - EQC ALL 整合版...")
            self.logger.info(f"   檔案路徑: {eqctotaldata_path}")
            self.logger.info(f"   EQC ALL 傳遞的 CODE 區間: start1={start1}, end1={end1}")
            self.logger.info(f"   CODE 匹配範圍: 第{start1+1}-{end1+1}欄 ({end1-start1+1}個欄位)")
            if max_fails_to_process:
                self.logger.info(f"   限制處理數量: 前{max_fails_to_process}顆 FAIL")
            self.logger.info(f"   [FOLDER] Step 3 DEBUG LOG: {log_filename}")
            
            original_row_count = len(rows)
            
            # 執行 EQC ALL 整合的資料重組邏輯
            reorganized_rows, moves_count, processed_fails = self._reorganize_data_with_eqc_all_params(
                rows, start1, end1, max_fails_to_process
            )
            
            # 寫回檔案
            self._write_updated_data(eqctotaldata_path, reorganized_rows)
            
            final_row_count = len(reorganized_rows)
            
            # 保存 Step 3 DEBUG LOG 到檔案
            base_dir = os.path.dirname(eqctotaldata_path)
            log_file_saved = self._save_step3_debug_log(base_dir)
            
            return {
                'status': 'success',
                'reorganization_completed': True,
                'moves_performed': moves_count,
                'processed_fails': processed_fails,
                'total_rows_before': original_row_count,
                'total_rows_after': final_row_count,
                'row_count_unchanged': original_row_count == final_row_count,
                'eqc_all_integration': {
                    'start1': start1,
                    'end1': end1,
                    'code_match_columns': f"第{start1+1}-{end1+1}欄",
                    'total_columns': end1 - start1 + 1
                },
                'processing_timestamp': datetime.now().isoformat(),
                'final_structure': 'FT PASS → Online EQC FAIL → EQC RT(同IC) → 下一對...'
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] InsEqcRtData2 重組失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }

    # 功能替換原則：刪[EXCEPT_CHAR]複雜分析邏輯，已被簡化的 _find_online_eqc_fail_rows 取代
    def _perform_step3_analysis_only_logic_DEPRECATED(self, 
                                         rows: List[str], 
                                         start1: int, 
                                         end1: int,
                                         max_fails_to_process: int = None) -> Dict[str, Any]:
        """
        執行 Step 3 純分析邏輯 - 只讀取分析，絕不修改資料
        
        分析功能：
        1. 識別所有 Online EQC FAIL 行
        2. 動態邊界掃描分析
        3. 匹配 EQC RT 行分析
        4. 詳細統計報告
        
        Args:
            rows: CSV 檔案的所有行資料（只讀）
            start1: CODE 區間起始位置
            end1: CODE 區間結束位置
            max_fails_to_process: 分析的 FAIL 數量限制
            
        Returns:
            Dict: 完整的分析結果統計
        """
        分析結果 = {
            '已識別的FAIL行': [],
            '邊界掃描結果': {},
            'EQC_RT匹配分析': [],
            '統計摘要': {}
        }
        
        processed_fail_serials = set()
        processed_fails = 0
        total_potential_matches = 0
        
        # 1. 從 B9 欄位獲取 FAIL 數量
        total_onlineqc_fail_cnt = self._get_fail_count_from_b9(rows)
        
        self._log_step3_debug(f"[TARGET] Step 3 純分析開始:")
        self._log_step3_debug(f"   使用 CODE 區間: 第{start1+1}-{end1+1}欄 ({end1-start1+1}個欄位)")
        self._log_step3_debug(f"   B9 欄位 FAIL 數量: {total_onlineqc_fail_cnt}")
        if max_fails_to_process:
            self._log_step3_debug(f"   分析數量限制: 前{max_fails_to_process}顆 FAIL")
        self._log_step3_debug(f"   重要: 本次只進行分析，絕不修改檔案內容")
        
        # 2. 預先分析全零 EQC RT 行分佈
        邊界分析 = self._analyze_boundary_with_b9(rows, total_onlineqc_fail_cnt)
        eqc_rt_start_index = 邊界分析['eqc_rt_start_index']
        
        self._log_step3_debug(f"[TOOL] 分析全零 EQC RT 行分佈...")
        零行分析 = self._analyze_zero_eqc_rt_distribution(rows, eqc_rt_start_index, start1, end1)
        分析結果['邊界掃描結果'] = {**邊界分析, **零行分析}
        
        # 3. 動態循環分析每個 FAIL
        target_fails = min(max_fails_to_process or total_onlineqc_fail_cnt, total_onlineqc_fail_cnt)
        
        while processed_fails < target_fails:
            self._log_step3_debug(f"\n[REFRESH] 第 {processed_fails + 1} 輪分析:")
            self._log_step3_debug(f"   重新分析邊界...")
            
            # 動態邊界分析
            boundary_info = self._analyze_boundary_with_b9(rows, total_onlineqc_fail_cnt)
            online_eqc_range = range(13, boundary_info['online_eqc_end_index'])
            eqc_rt_range = range(boundary_info['eqc_rt_start_index'], len(rows))
            
            self._log_step3_debug(f"   [CHART] 邊界分析結果:")
            self._log_step3_debug(f"      Online EQC 結束位置: 第{boundary_info['online_eqc_end_index']}行")
            self._log_step3_debug(f"      EQC RT 開始位置: 第{boundary_info['eqc_rt_start_index']}行")
            
            if len(online_eqc_range) > 0:
                self._log_step3_debug(f"      Online EQC 範圍: 第{min(online_eqc_range)+1}-{max(online_eqc_range)+1}行 (共{len(online_eqc_range)}行)")
            else:
                self._log_step3_debug(f"      Online EQC 範圍: 空範圍")
            
            if len(eqc_rt_range) > 0:
                self._log_step3_debug(f"      EQC RT 範圍: 第{min(eqc_rt_range)+1}行往後 (共{len(eqc_rt_range)}行)")
            else:
                self._log_step3_debug(f"      EQC RT 範圍: 空範圍")
            
            # 4. 在 Online EQC 範圍分析 FAIL 行
            self._log_step3_debug(f"   [SEARCH] 分析 Online EQC 範圍內的 FAIL...")
            fail_rows = self._find_fail_rows_in_range(rows, online_eqc_range, processed_fail_serials)
            
            if not fail_rows:
                self._log_step3_debug(f"[WARNING] 未發現更多 FAIL 行，已分析 {processed_fails} 個")
                break
                
            current_fail = fail_rows[0]
            fail_row_index = current_fail['row_index']
            fail_serial = current_fail['serial']
            
            fail_info = {
                'fail_序號': processed_fails + 1,
                'row_index': fail_row_index,
                'serial': fail_serial,
                'bin_value': current_fail['bin_value'],
                '匹配的RT行': []
            }
            
            self._log_step3_debug(f"   [TARGET] 發現 FAIL: 第{fail_row_index + 1}行 (Serial={fail_serial}, BIN={current_fail['bin_value']})")
            self._log_step3_debug(f"     [SEARCH] 分析此 FAIL IC 的所有可能匹配 EQC RT:")
            
            # 5. 分析 EQC RT 範圍的匹配情況
            backup_start1 = 1564
            backup_end1 = 1599
            main_mapping_start1 = start1
            main_mapping_end1 = start1 + 35
            
            matching_analysis = self._analyze_matching_rt_with_eqc_all_range(
                rows, current_fail, eqc_rt_range, start1, end1, 
                backup_start1, backup_end1, main_mapping_start1, main_mapping_end1
            )
            
            fail_info['匹配的RT行'] = matching_analysis.get('matched_rt_rows', [])
            total_potential_matches += len(fail_info['匹配的RT行'])
            
            分析結果['已識別的FAIL行'].append(fail_info)
            分析結果['EQC_RT匹配分析'].append(matching_analysis)
            
            # 記錄已分析的 FAIL Serial
            processed_fail_serials.add(fail_serial)
            processed_fails += 1
            
            self._log_step3_debug(f"   [OK] 第 {processed_fails} 個 FAIL 分析完成，Serial={fail_serial}")
        
        # 6. 生成統計摘要
        分析結果['統計摘要'] = {
            '分析的FAIL數': processed_fails,
            '發現的潛在匹配數': total_potential_matches,
            '總行數': len(rows),
            '已分析的FAIL_Serials': list(processed_fail_serials),
            'B9欄位FAIL數量': total_onlineqc_fail_cnt,
            '全零EQC_RT行數': 零行分析.get('zero_rows_count', 0),
            '非零EQC_RT行數': 零行分析.get('non_zero_rows_count', 0)
        }
        
        self._log_step3_debug(f"\n[PARTY] Step 3 純分析完成:")
        self._log_step3_debug(f"   分析的 FAIL 數: {processed_fails}")
        self._log_step3_debug(f"   發現的潛在匹配數: {total_potential_matches}")
        self._log_step3_debug(f"   總行數: {len(rows)} (未修改)")
        self._log_step3_debug(f"   已分析的所有 FAIL Serials: {processed_fail_serials}")
        self._log_step3_debug(f"   [CHART] 檔案內容保持完全不變")
        
        return 分析結果

    def _analyze_boundary_with_b9(self, rows: List[str], total_onlineqc_fail_cnt: int) -> Dict[str, int]:
        """
        分析邊界，不修改任何資料
        """
        data_start_index = 12
        golden_count = 1
        
        online_eqc_end_index = self._analyze_actual_online_eqc_boundary(rows, data_start_index + golden_count)
        eqc_rt_start_index = online_eqc_end_index
        
        self._log_step3_debug(f"[TOOL] 動態邊界分析結果:")
        self._log_step3_debug(f"   Online EQC 結束位置: 第{online_eqc_end_index+1}行")
        self._log_step3_debug(f"   EQC RT 開始位置: 第{eqc_rt_start_index+1}行")
        
        return {
            'online_eqc_end_index': online_eqc_end_index,
            'eqc_rt_start_index': eqc_rt_start_index,
            'total_fail_count': total_onlineqc_fail_cnt
        }

    def _analyze_actual_online_eqc_boundary(self, rows: List[str], start_scan_index: int) -> int:
        """
        分析實際的 Online EQC 結束邊界，不修改資料
        """
        current_index = start_scan_index
        ft_online_eqc_pairs = 0
        standalone_fails = 0
        inserted_rt_count = 0
        
        self._log_step3_debug(f"[SEARCH] 動態分析 Online EQC 邊界，起始位置: 第{start_scan_index + 1}行")
        
        while current_index < len(rows):
            if current_index >= len(rows) or not rows[current_index].strip():
                current_index += 1
                continue
                
            line_elements = rows[current_index].split(',')
            if len(line_elements) < 2:
                current_index += 1
                continue
                
            bin_value = line_elements[1].strip()
            serial = line_elements[0].strip() if len(line_elements) > 0 else "未知"
            
            self._log_step3_debug(f"   分析第{current_index + 1}行: Serial={serial}, BIN={bin_value}")
            
            if bin_value == "1":
                if self._is_inserted_eqc_rt_row(rows, current_index):
                    self._log_step3_debug(f"     → 已插入的 EQC RT 行，跳過")
                    inserted_rt_count += 1
                    current_index += 1
                    continue
                elif self._is_ft_row_in_pair(rows, current_index):
                    self._log_step3_debug(f"     → FT 配對行，與下一行形成配對")
                    ft_online_eqc_pairs += 1
                    current_index += 2
                    continue
                else:
                    self._log_step3_debug(f"     → 真正的 EQC RT 區域開始")
                    break
            else:
                if current_index > 0:
                    prev_line = rows[current_index - 1].split(',')
                    prev_bin = prev_line[1].strip() if len(prev_line) > 1 else ""
                    if prev_bin != "1":
                        standalone_fails += 1
                        self._log_step3_debug(f"     → 獨立 FAIL 行")
                
                current_index += 1
                continue
        
        self._log_step3_debug(f"[TARGET] 動態分析完成:")
        self._log_step3_debug(f"   找到 FT/Online EQC 配對: {ft_online_eqc_pairs} 對")
        self._log_step3_debug(f"   獨立 FAIL 行: {standalone_fails} 個")
        self._log_step3_debug(f"   已插入 EQC RT: {inserted_rt_count} 個")
        self._log_step3_debug(f"   實際邊界位置: 第{current_index + 1}行")
        
        return current_index

    def _analyze_zero_eqc_rt_distribution(self, rows: List[str], eqc_rt_start_index: int, start1: int, end1: int) -> Dict[str, Any]:
        """
        分析全零 EQC RT 行的分佈，不修改資料
        """
        self._log_step3_debug(f"[TOOL] 分析全零 EQC RT 行分佈...")
        
        eqc_rt_rows = rows[eqc_rt_start_index:]
        
        zero_rows_info = []
        non_zero_rows_info = []
        
        for i, row in enumerate(eqc_rt_rows):
            if not row.strip():
                non_zero_rows_info.append({'index': i, 'type': 'empty_line'})
                continue
                
            line_elements = row.split(',')
            serial = line_elements[0].strip() if len(line_elements) > 0 else ""
            
            # 檢查 CODE 區間是否全零
            is_all_zero = True
            for k in range(start1, min(end1 + 1, len(line_elements))):
                if line_elements[k].strip() not in ["0", ""]:
                    is_all_zero = False
                    break
            
            if is_all_zero:
                self._log_step3_debug(f"   [SEARCH] 發現全零 EQC RT 行: EQC RT 第{i+1}行 (Serial={serial})")
                zero_rows_info.append({
                    'eqc_rt_index': i,
                    'absolute_index': eqc_rt_start_index + i,
                    'serial': serial,
                    'type': 'zero_row'
                })
            else:
                non_zero_rows_info.append({
                    'eqc_rt_index': i,
                    'absolute_index': eqc_rt_start_index + i,
                    'serial': serial,
                    'type': 'non_zero_row'
                })
        
        zero_count = len(zero_rows_info)
        non_zero_count = len(non_zero_rows_info)
        
        self._log_step3_debug(f"[PARTY] 全零行分佈分析完成:")
        self._log_step3_debug(f"   全零行數: {zero_count}")
        self._log_step3_debug(f"   非零行數: {non_zero_count}")
        self._log_step3_debug(f"   全零序號: {[info['serial'] for info in zero_rows_info]}")
        
        return {
            'zero_rows_count': zero_count,
            'non_zero_rows_count': non_zero_count,
            'zero_rows_info': zero_rows_info,
            'non_zero_rows_info': non_zero_rows_info
        }

    def _analyze_matching_rt_with_eqc_all_range(self, 
                                              rows: List[str], 
                                              fail_row: Dict[str, Any], 
                                              eqc_rt_range, 
                                              main_start1: int, 
                                              main_end1: int,
                                              backup_start1: int = None,
                                              backup_end1: int = None,
                                              main_mapping_start1: int = None,
                                              main_mapping_end1: int = None) -> Dict[str, Any]:
        """
        分析匹配的 EQC RT 行，不修改資料
        """
        fail_elements = fail_row['line_elements']
        fail_serial = fail_elements[0].strip() if len(fail_elements) > 0 else "未知"
        fail_bin = fail_elements[1].strip() if len(fail_elements) > 1 else "未知"
        
        matched_rt_analysis = []
        checked_count = 0
        skipped_count = 0
        
        self._log_step3_debug(f"     [SEARCH] 分析 FAIL IC 的所有可能匹配 EQC RT:")
        self._log_step3_debug(f"       FAIL Serial: {fail_serial}, BIN: {fail_bin}")
        if len(eqc_rt_range) > 0:
            self._log_step3_debug(f"       EQC RT 分析範圍: 第{min(eqc_rt_range)+1}-{max(eqc_rt_range)+1}行 (共{len(eqc_rt_range)}行)")
        else:
            self._log_step3_debug(f"       EQC RT 分析範圍: 空範圍 (共0行)")
        
        for rt_row_index in eqc_rt_range:
            if rt_row_index >= len(rows) or not rows[rt_row_index].strip():
                skipped_count += 1
                continue
                
            rt_elements = rows[rt_row_index].split(',')
            rt_serial = rt_elements[0].strip() if len(rt_elements) > 0 else "未知"
            rt_bin = rt_elements[1].strip() if len(rt_elements) > 1 else "未知"
            
            checked_count += 1
            self._log_step3_debug(f"     [MAGNIFYING_GLASS_TILTED_RIGHT] 分析 RT 第{rt_row_index+1}行: Serial={rt_serial}, BIN={rt_bin}")
            
            # 主要區間匹配分析
            is_main_match, is_main_all_zero = self._check_code_match_with_eqc_all_range(
                fail_elements, rt_elements, main_start1, main_end1, "主要區間"
            )
            
            rt_analysis = {
                'rt_row_index': rt_row_index,
                'rt_serial': rt_serial,
                'rt_bin': rt_bin,
                'main_region_match': is_main_match,
                'main_region_all_zero': is_main_all_zero,
                'backup_region_match': False,
                'backup_mapping_match': False,
                'final_match_type': None
            }
            
            if is_main_match and not is_main_all_zero:
                self._log_step3_debug(f"     [OK] 主要區間匹配成功!")
                rt_analysis['final_match_type'] = 'main_region'
                matched_rt_analysis.append(rt_analysis)
                continue
            
            # 備用區間分析
            if is_main_all_zero and backup_start1 is not None and backup_end1 is not None:
                self._log_step3_debug(f"     [REFRESH] 主要區間全零，分析備用區間映射")
                is_backup_rt_match, is_backup_rt_all_zero = self._check_backup_region_mapping(
                    fail_elements, rt_elements, backup_start1, backup_end1, main_mapping_start1, main_mapping_end1
                )
                
                rt_analysis['backup_mapping_match'] = is_backup_rt_match
                
                if is_backup_rt_match and not is_backup_rt_all_zero:
                    self._log_step3_debug(f"     [OK] 備用區間映射匹配成功!")
                    rt_analysis['final_match_type'] = 'backup_mapping'
                    matched_rt_analysis.append(rt_analysis)
                    continue
            
            elif not is_main_match and not is_main_all_zero and backup_start1 is not None:
                is_backup_match, is_backup_all_zero = self._check_code_match_with_eqc_all_range(
                    fail_elements, rt_elements, backup_start1, backup_end1, "備用區間"
                )
                
                rt_analysis['backup_region_match'] = is_backup_match
                
                if is_backup_match and not is_backup_all_zero:
                    self._log_step3_debug(f"     [OK] 備用區間匹配成功!")
                    rt_analysis['final_match_type'] = 'backup_region'
                    matched_rt_analysis.append(rt_analysis)
                    continue
        
        matching_results = [rt for rt in matched_rt_analysis if rt['final_match_type']]
        
        if matching_results:
            self._log_step3_debug(f"     [OK] 分析發現 {len(matching_results)} 個可能匹配的同一顆 IC:")
            for i, match in enumerate(matching_results, 1):
                self._log_step3_debug(f"       {i}. RT 第{match['rt_row_index']+1}行: Serial={match['rt_serial']}, BIN={match['rt_bin']} ({match['final_match_type']})")
        else:
            self._log_step3_debug(f"     [ERROR] 分析完成，未發現可能匹配的 EQC RT")
            self._log_step3_debug(f"       總共分析: {checked_count} 個 RT 行，跳過: {skipped_count} 個空行")
        
        return {
            'analysis_completed': True,
            'matched_rt_rows': matching_results,
            'total_matches': len(matching_results),
            'total_checked': checked_count,
            'total_skipped': skipped_count,
            'fail_serial': fail_serial,
            'fail_bin': fail_bin
        }
    
    # 功能替換原則：刪[EXCEPT_CHAR]複雜重組邏輯，Step 3 只需要找 FAIL 行
    def _reorganize_data_with_eqc_all_params_DEPRECATED(self, 
                                            rows: List[str], 
                                            start1: int, 
                                            end1: int,
                                            max_fails_to_process: int = None) -> Tuple[List[str], int, int]:
        """
        使用 EQC ALL 傳遞的參數進行資料重組
        基於固定資料結構和 B9 欄位的動態循環處理
        
        優化版本：預先過濾全零 EQC RT 行，提升處理效率
        
        Args:
            rows: 所有行資料
            start1: EQC ALL 檢測的 CODE 區間起始位置
            end1: EQC ALL 檢測的 CODE 區間結束位置
            max_fails_to_process: 最大處理的 FAIL 數量
            
        Returns:
            tuple: (重組後的行資料, 移動次數, 處理的FAIL數量)
        """
        rows_copy = [row for row in rows]  # 深拷貝
        moves_count = 0
        processed_fails = 0
        processed_fail_serials = set()  # 追蹤已處理的 FAIL Serial
        
        # 1. 從 B9 欄位獲取 FAIL 數量（消[EXCEPT_CHAR]循環依賴）
        total_onlineqc_fail_cnt = self._get_fail_count_from_b9(rows_copy)
        
        self.logger.info(f"[TARGET] EQC ALL 整合處理開始:")
        self.logger.info(f"   使用 CODE 區間: 第{start1+1}-{end1+1}欄 ({end1-start1+1}個欄位)")
        self.logger.info(f"   B9 欄位 FAIL 數量: {total_onlineqc_fail_cnt}")
        if max_fails_to_process:
            self.logger.info(f"   限制處理數量: 前{max_fails_to_process}顆 FAIL")
        
        # 2. 【優化】預先過濾全零 EQC RT 行到檔案末尾
        boundary_info = self._analyze_boundary_with_b9(rows_copy, total_onlineqc_fail_cnt)
        eqc_rt_start_index = boundary_info['eqc_rt_start_index']
        
        self.logger.info(f"[TOOL] 執行預先過濾全零 EQC RT 行...")
        rows_copy, zero_moves = self._prefilter_zero_eqc_rt_rows(rows_copy, eqc_rt_start_index, start1, end1)
        moves_count += zero_moves
        self.logger.info(f"   [OK] 預先移動 {zero_moves} 個全零 EQC RT 行到檔案末尾")
        
        # 3. 動態循環處理每個 FAIL（現在只處理非零的EQC RT行）
        target_fails = min(max_fails_to_process or total_onlineqc_fail_cnt, total_onlineqc_fail_cnt)
        
        while processed_fails < target_fails:
            # 3. 重新計算分界線（每次移動後結構改變）
            self._log_step3_debug(f"\n[REFRESH] 第 {processed_fails + 1} 輪循環:")
            self._log_step3_debug(f"   重新計算邊界...")
            
            boundary_info = self._analyze_boundary_with_b9(rows_copy, total_onlineqc_fail_cnt)
            online_eqc_range = range(13, boundary_info['online_eqc_end_index'])
            eqc_rt_range = range(boundary_info['eqc_rt_start_index'], len(rows_copy))
            
            self._log_step3_debug(f"   [CHART] 邊界計算結果:")
            self._log_step3_debug(f"      Online EQC 結束位置: 第{boundary_info['online_eqc_end_index']}行")
            self._log_step3_debug(f"      EQC RT 開始位置: 第{boundary_info['eqc_rt_start_index']}行")
            self._log_step3_debug(f"      動態掃描保護配對: {boundary_info.get('protected_pairs', 0)} 對")
            self._log_step3_debug(f"      已插入 EQC RT: {boundary_info.get('inserted_rt_count', 0)} 個")
            
            self._log_step3_debug(f"   [LOCATION] 掃描範圍確定:")
            if len(online_eqc_range) > 0:
                self._log_step3_debug(f"      Online EQC 範圍: 第{min(online_eqc_range)+1}-{max(online_eqc_range)+1}行 (共{len(online_eqc_range)}行)")
            else:
                self._log_step3_debug(f"      Online EQC 範圍: 空範圍 (共0行)")
            
            if len(eqc_rt_range) > 0:
                self._log_step3_debug(f"      EQC RT 範圍: 第{min(eqc_rt_range)+1}行往後 (共{len(eqc_rt_range)}行)")
            else:
                self._log_step3_debug(f"      EQC RT 範圍: 空範圍 (共0行)")
            self._log_step3_debug(f"   [FAST_FORWARD_DOUBLE] 接下來將在 Online EQC 範圍尋找未處理的 FAIL")
            self._log_step3_debug(f"      已處理的 FAIL Serials: {processed_fail_serials}")
            self._log_step3_debug(f"      目標: 尋找第 {processed_fails + 1} 個 FAIL")
            
            # 4. 在當前 Online EQC 範圍找下一個 FAIL 行（跳過已處理的）
            self._log_step3_debug(f"   [SEARCH] 在 Online EQC 範圍掃描未處理的 FAIL...")
            fail_rows = self._find_fail_rows_in_range(rows_copy, online_eqc_range, processed_fail_serials)
            
            if not fail_rows:
                self.logger.warning(f"[WARNING] 未找到更多 FAIL 行，已處理 {processed_fails} 個")
                if len(online_eqc_range) > 0:
                    self.logger.info(f"   掃描完成: Online EQC 範圍第{min(online_eqc_range)+1}-{max(online_eqc_range)+1}行內無更多未處理 FAIL")
                else:
                    self.logger.info(f"   掃描完成: Online EQC 範圍為空，無FAIL可處理")
                break
                
            current_fail = fail_rows[0]  # 處理第一個找到的 FAIL
            fail_row_index = current_fail['row_index']
            fail_serial = current_fail['serial']
            
            self.logger.info(f"   [TARGET] 找到 FAIL: 第{fail_row_index + 1}行 (Serial={fail_serial}, BIN={current_fail['bin_value']})")
            self.logger.info(f"     [SEARCH] 搜尋 FAIL IC 的所有匹配 EQC RT:")
            self.logger.info(f"       FAIL Serial: {fail_serial}, BIN: {current_fail['bin_value']}")
            if len(eqc_rt_range) > 0:
                self.logger.info(f"       EQC RT 搜尋範圍: 第{min(eqc_rt_range)+1}-{max(eqc_rt_range)+1}行 (共{len(eqc_rt_range)}行)")
            else:
                self.logger.info(f"       EQC RT 搜尋範圍: 空範圍 (共0行)")
            self.logger.info(f"       主要 CODE 區間: 第{start1+1}-{end1+1}欄 (共{end1-start1+1}個欄位)")
            # 5. 在 EQC RT 範圍找匹配的同一顆 IC (支援備用區間和批量處理)
            # 備用區間邏輯：第1565-1600欄(36個欄位) 對應 主要區間前36個欄位(第298-333欄)
            backup_start1 = 1564     # 第1565欄，索引1564
            backup_end1 = 1599       # 第1600欄，索引1599
            main_mapping_start1 = start1     # 主要區間起始：第298欄，索引297
            main_mapping_end1 = start1 + 35  # 主要區間前36個欄位：第298-333欄，索引297-332
            
            self.logger.info(f"       備用 CODE 區間: 第{backup_start1+1}-{backup_end1+1}欄 (共{backup_end1-backup_start1+1}個欄位)")
            
            matching_rt = self._find_matching_rt_with_eqc_all_range(
                rows_copy, current_fail, eqc_rt_range, start1, end1, backup_start1, backup_end1, 
                main_mapping_start1, main_mapping_end1
            )
            
            if matching_rt and matching_rt.get('found'):
                # 批量處理所有匹配的 RT 行
                matched_rt_rows = matching_rt['matched_rt_rows']
                self.logger.info(f"   [OK] 找到 {len(matched_rt_rows)} 個同一顆 IC 的 RT 行")
                
                # 6. 批量移動：從最後一個開始移動（避免索引變化）
                batch_moves = 0
                for i, match_info in enumerate(reversed(matched_rt_rows)):
                    rt_row_index = match_info['rt_row_index']
                    match_type = match_info['match_type']
                    rt_serial = match_info['serial']
                    rt_bin = match_info['bin']
                    
                    # [FIRE] DETAILED DEBUG: 記錄移動原因和詳細資訊
                    self.logger.info(f"   [BOARD] 【移動追蹤】 批量移動 {len(matched_rt_rows)-i}/{len(matched_rt_rows)}")
                    self.logger.info(f"      來源位置: RT 第{rt_row_index+1}行 (Serial={rt_serial}, BIN={rt_bin})")
                    self.logger.info(f"      目標位置: FAIL 第{fail_row_index+1}行 (Serial={fail_serial}, BIN={current_fail['bin_value']}) 下方")
                    self.logger.info(f"      匹配類型: {match_type}")
                    
                    # 詳細移動原因
                    if match_type == 'main_region':
                        self.logger.info(f"      移動原因: 主要 CODE 區間 (第{start1+1}-{end1+1}欄) 100% 匹配")
                        self.logger.info(f"               → RT Serial={rt_serial} 與 FAIL Serial={fail_serial} 在 CODE 區間完全相同")
                    elif match_type == 'backup_mapping':
                        self.logger.info(f"      移動原因: 備用區間映射匹配 (第{backup_start1+1}-{backup_end1+1}欄 → 第{main_mapping_start1+1}-{main_mapping_end1+1}欄)")
                        self.logger.info(f"               → RT Serial={rt_serial} 的備用區間與 FAIL Serial={fail_serial} 的主要區間映射相符")
                    elif match_type == 'backup_region':
                        self.logger.info(f"      移動原因: 備用區間 (第{backup_start1+1}-{backup_end1+1}欄) 100% 匹配")
                        self.logger.info(f"               → RT Serial={rt_serial} 與 FAIL Serial={fail_serial} 在備用區間完全相同")
                    
                    # 記錄CODE區間匹配詳情
                    if match_info.get('match_details'):
                        details = match_info['match_details']
                        self.logger.info(f"      匹配詳情: {details.get('matched_fields', 0)}/{details.get('total_fields', 0)} 個欄位匹配")
                        if details.get('first_mismatch_column'):
                            self.logger.info(f"               → 首個不匹配欄位: 第{details['first_mismatch_column']}欄")
                    
                    # 記錄移動前後的狀態
                    self.logger.info(f"      移動前狀態: 第{rt_row_index+1}行 → 第{fail_row_index + 1 + batch_moves + 1}行")
                    
                    # 計算插入位置：FAIL行下方 + 已移動的行數
                    insert_position = fail_row_index + 1 + batch_moves
                    
                    # 記錄移動前的狀態
                    self.logger.info(f"     [BOARD] 開始移動 RT 資料:")
                    self.logger.info(f"       來源: 第{rt_row_index+1}行 (Serial={rt_serial}, BIN={rt_bin})")
                    self.logger.info(f"       目標: 第{insert_position+1}行")
                    
                    # 執行移動
                    rows_copy = self._move_row_to_position(rows_copy, rt_row_index, insert_position)
                    moves_count += 1
                    batch_moves += 1
                    
                    # 驗證移動結果
                    moved_row = rows_copy[insert_position]
                    moved_elements = moved_row.split(',')
                    moved_serial = moved_elements[0].strip() if len(moved_elements) > 0 else ''
                    moved_bin = moved_elements[1].strip() if len(moved_elements) > 1 else ''
                    
                    self.logger.info(f"       [OK] 已從第{rt_row_index+1}行刪[EXCEPT_CHAR]")
                    self.logger.info(f"       [OK] 已插入到第{insert_position+1}行")
                    self.logger.info(f"     [OK] 移動完成驗證:")
                    self.logger.info(f"       新位置第{insert_position+1}行: Serial={moved_serial}, BIN={moved_bin}")
                    
                    self.logger.info(f"   [OK] 【移動完成】 RT Serial={rt_serial} 移到第{insert_position+1}行 (原因: {match_type})")
                
                self.logger.info(f"   [PARTY] 批量移動完成: 共移動 {batch_moves} 個 RT 行到 FAIL 第{fail_row_index+1}行下方")
            else:
                self.logger.info(f"   [WARNING] 第 {processed_fails + 1} 個 FAIL 未找到匹配的 EQC RT (已嘗試主要和備用區間)")
            
            # 7. 記錄已處理的 FAIL Serial（無論是否找到匹配的 RT）
            processed_fail_serials.add(fail_serial)
            processed_fails += 1
            
            self.logger.info(f"   [OK] 第 {processed_fails} 個 FAIL 處理完成，Serial={fail_serial}")
            
            # 結構已改變，下一輪循環會重新計算範圍
        
        self.logger.info(f"\n[PARTY] EQC ALL 整合重組完成:")
        self.logger.info(f"   處理的 FAIL 數: {processed_fails}")
        self.logger.info(f"   執行的移動次數: {moves_count}")
        self.logger.info(f"   總行數: {len(rows)} → {len(rows_copy)} (不變)")
        self.logger.info(f"   已處理的所有 FAIL Serials: {processed_fail_serials}")
        
        return rows_copy, moves_count, processed_fails
    
    # 功能替換原則：刪[EXCEPT_CHAR]複雜過濾邏輯
    def _prefilter_zero_eqc_rt_rows_DEPRECATED(self, rows: List[str], eqc_rt_start_index: int, start1: int, end1: int) -> Tuple[List[str], int]:
        """
        預先過濾全零 EQC RT 行，保持相對順序分離
        
        重要修正：全零行保持相對順序，不移到檔案最末端
        而是在非零行後面，維持原本的相對位置關係
        
        Args:
            rows: 所有行資料
            eqc_rt_start_index: EQC RT 區域起始索引
            start1: CODE 區間起始位置
            end1: CODE 區間結束位置
            
        Returns:
            tuple: (重組後的行資料, 移動次數)
        """
        self.logger.info(f"[TOOL] 執行全零 EQC RT 行分離（保持相對順序）...")
        
        # 分離 EQC RT 區域的零行和非零行
        eqc_rt_rows = rows[eqc_rt_start_index:]
        non_eqc_rt_rows = rows[:eqc_rt_start_index]
        
        zero_rows = []
        non_zero_rows = []
        zero_rows_info = []
        
        # 掃描 EQC RT 區域，分離零行和非零行
        for i, row in enumerate(eqc_rt_rows):
            if not row.strip():
                non_zero_rows.append(row)
                continue
                
            line_elements = row.split(',')
            serial = line_elements[0].strip() if len(line_elements) > 0 else ""
            
            # 檢查 CODE 區間是否全零
            is_all_zero = True
            for k in range(start1, min(end1 + 1, len(line_elements))):
                if line_elements[k].strip() not in ["0", ""]:
                    is_all_zero = False
                    break
            
            if is_all_zero:
                self.logger.info(f"   [SEARCH] 發現全零 EQC RT 行: EQC RT 第{i+1}行 (Serial={serial})")
                zero_rows.append(row)
                zero_rows_info.append({
                    'original_eqc_rt_index': i,
                    'serial': serial,
                    'data': row
                })
            else:
                non_zero_rows.append(row)
        
        # 重組：非零行在前，零行在後（保持各自的相對順序）
        reorganized_eqc_rt_rows = non_zero_rows + zero_rows
        
        # 重組完整檔案
        reorganized_rows = non_eqc_rt_rows + reorganized_eqc_rt_rows
        
        moves_count = len(zero_rows)
        
        if moves_count > 0:
            self.logger.info(f"[PARTY] 預先分離完成: 共分離 {moves_count} 個全零 EQC RT 行")
            self.logger.info(f"   分離的序號: {[info['serial'] for info in zero_rows_info]}")
            self.logger.info(f"   非零行數: {len(non_zero_rows)}, 全零行數: {len(zero_rows)}")
            self.logger.info(f"   [OK] 全零行已放置在非零行後面，保持相對順序")
        else:
            self.logger.info(f"[INFO] 未發現需要分離的全零 EQC RT 行")
        
        return reorganized_rows, moves_count
    
    # 功能替換原則：刪[EXCEPT_CHAR]複雜B9解析邏輯
    def _get_fail_count_from_b9_DEPRECATED(self, rows: List[str]) -> int:
        """
        從 B9 欄位直接讀取 FAIL 數量
        格式: "OnlineEQC_Fail:10,10" → 取後面的 10
        消[EXCEPT_CHAR]循環依賴問題
        """
        try:
            # 從第9行 B9 欄位讀取 FAIL 數量
            # 格式: "OnlineEQC_Fail:10,10" → 取後面的 10
            b9_content = rows[8]  # 第9行索引8
            b9_elements = b9_content.split(',')
            
            if len(b9_elements) >= 2:
                fail_count_str = b9_elements[1].strip()
                fail_count = int(fail_count_str)
            else:
                # 備用解析
                fail_info = b9_elements[0].strip()
                if ':' in fail_info:
                    fail_count = int(fail_info.split(':')[1])
                else:
                    raise ValueError(f"無法解析 B9 欄位: {b9_content}")
            
            self.logger.info(f"   B9 欄位解析成功: FAIL 數量 = {fail_count}")
            return fail_count
            
        except Exception as e:
            self.logger.error(f"[ERROR] B9 欄位解析失敗: {e}")
            raise
    
    # 功能替換原則：刪[EXCEPT_CHAR]複雜邊界計算邏輯
    def _analyze_boundary_with_b9_DEPRECATED(self, rows: List[str], total_onlineqc_fail_cnt: int) -> Dict[str, int]:
        """
        動態計算分界線，考慮已插入的 EQC RT 行
        修正版：解決 FT 和 Online EQC 配對被破壞的問題
        """
        # 硬編碼參數
        data_start_index = 12      # 前12行是固定檔案標頭
        golden_count = 1           # 1個 Golden IC
        
        # 動態計算：掃描實際的 FT/Online EQC 配對和已插入的 EQC RT
        online_eqc_end_index = self._find_actual_online_eqc_boundary(rows, data_start_index + golden_count)
        eqc_rt_start_index = online_eqc_end_index
        
        self.logger.info(f"[TOOL] 動態邊界計算結果:")
        self.logger.info(f"   Online EQC 結束位置: 第{online_eqc_end_index+1}行")
        self.logger.info(f"   EQC RT 開始位置: 第{eqc_rt_start_index+1}行")
        
        return {
            'online_eqc_end_index': online_eqc_end_index,
            'eqc_rt_start_index': eqc_rt_start_index,
            'total_fail_count': total_onlineqc_fail_cnt
        }
    
    # 功能替換原則：刪[EXCEPT_CHAR]複雜邊界掃描邏輯
    def _find_actual_online_eqc_boundary_DEPRECATED(self, rows: List[str], start_scan_index: int) -> int:
        """
        動態掃描找到實際的 Online EQC 結束邊界
        保護 FT 和 Online EQC 配對不被破壞
        
        修正版：完全取代硬編碼邊界計算，防止第34行後的配對被拆分
        """
        current_index = start_scan_index
        ft_online_eqc_pairs = 0
        standalone_fails = 0
        inserted_rt_count = 0
        
        self._log_step3_debug(f"[SEARCH] 動態掃描 Online EQC 邊界，起始位置: 第{start_scan_index + 1}行")
        
        while current_index < len(rows):
            if current_index >= len(rows) or not rows[current_index].strip():
                current_index += 1
                continue
                
            line_elements = rows[current_index].split(',')
            if len(line_elements) < 2:
                current_index += 1
                continue
                
            bin_value = line_elements[1].strip()
            serial = line_elements[0].strip() if len(line_elements) > 0 else "未知"
            
            self._log_step3_debug(f"   掃描第{current_index + 1}行: Serial={serial}, BIN={bin_value}")
            
            # 檢查是否為 BIN=1 的行
            if bin_value == "1":
                # 檢查是否是已插入的 EQC RT 行（前一行是 FAIL）
                if self._is_inserted_eqc_rt_row(rows, current_index):
                    self.logger.info(f"     → 已插入的 EQC RT 行，跳過")
                    inserted_rt_count += 1
                    current_index += 1
                    continue
                # 檢查是否是 FT 行（配對中的第一行）
                elif self._is_ft_row_in_pair(rows, current_index):
                    self._log_step3_debug(f"     → FT 配對行，與下一行形成配對")
                    ft_online_eqc_pairs += 1
                    current_index += 2  # 跳過 FT + Online EQC FAIL 配對
                    continue
                else:
                    # 到達真正的 EQC RT 區域（獨立的 BIN=1 行）
                    self.logger.info(f"     → 真正的 EQC RT 區域開始")
                    break
            else:
                # Online EQC FAIL 行（BIN≠1）
                # 檢查是否是獨立的 FAIL 行（不與前一行的 FT 配對）
                if current_index > 0:
                    prev_line = rows[current_index - 1].split(',')
                    prev_bin = prev_line[1].strip() if len(prev_line) > 1 else ""
                    if prev_bin != "1":
                        # 獨立的 FAIL 行
                        standalone_fails += 1
                        self._log_step3_debug(f"     → 獨立 FAIL 行")
                
                current_index += 1
                continue
        
        self._log_step3_debug(f"[TARGET] 動態掃描完成:")
        self._log_step3_debug(f"   找到 FT/Online EQC 配對: {ft_online_eqc_pairs} 對")
        self._log_step3_debug(f"   獨立 FAIL 行: {standalone_fails} 個")
        self._log_step3_debug(f"   已插入 EQC RT: {inserted_rt_count} 個")
        self._log_step3_debug(f"   實際邊界位置: 第{current_index + 1}行")
        
        return current_index
    
    def _is_inserted_eqc_rt_row(self, rows: List[str], row_index: int) -> bool:
        """檢查是否為已插入的 EQC RT 行"""
        # 簡單邏輯：檢查前一行是否為 FAIL 行
        if row_index > 0:
            prev_line = rows[row_index - 1].split(',')
            if len(prev_line) > 1 and prev_line[1].strip() != "1":
                return True
        return False
    
    def _is_ft_row_in_pair(self, rows: List[str], row_index: int) -> bool:
        """檢查是否為配對中的 FT 行"""
        # 檢查下一行是否為 Online EQC FAIL
        if row_index + 1 < len(rows):
            next_line = rows[row_index + 1].split(',')
            if len(next_line) > 1 and next_line[1].strip() != "1":
                return True
        return False
    
    def _find_fail_rows_in_range(self, rows: List[str], online_eqc_range, processed_fail_serials: set = None) -> List[Dict[str, Any]]:
        """
        在 Online EQC 範圍內找所有 FAIL 行（BIN≠1），並跳過已處理的 FAIL
        
        Args:
            rows: 所有行資料
            online_eqc_range: Online EQC 範圍
            processed_fail_serials: 已處理的 FAIL Serial 集合
        """
        fail_rows = []
        processed_fail_serials = processed_fail_serials or set()
        
        for row_index in online_eqc_range:
            if row_index >= len(rows) or not rows[row_index].strip():
                continue
                
            line_elements = rows[row_index].split(',')
            
            # 檢查 BIN 值（第2欄，索引1）
            if len(line_elements) > 1 and line_elements[1].strip() != "1":
                serial = line_elements[0].strip() if len(line_elements) > 0 else ""
                
                # 跳過已處理的 FAIL
                if serial not in processed_fail_serials:
                    fail_rows.append({
                        'row_index': row_index,
                        'line_elements': line_elements,
                        'bin_value': line_elements[1].strip(),
                        'serial': serial
                    })
        
        return fail_rows
    
    # 功能替換原則：刪[EXCEPT_CHAR]複雜匹配邏輯
    def _find_matching_rt_with_eqc_all_range_DEPRECATED(self, 
                                            rows: List[str], 
                                            fail_row: Dict[str, Any], 
                                            eqc_rt_range, 
                                            main_start1: int, 
                                            main_end1: int,
                                            backup_start1: int = None,
                                            backup_end1: int = None,
                                            main_mapping_start1: int = None,
                                            main_mapping_end1: int = None) -> Dict[str, Any]:
        """
        使用主要和備用區間在 EQC RT 範圍尋找所有匹配的同一顆 IC (批量匹配版本)
        """
        try:
            # 設置簡化版 DEBUG LOG 檔案
            base_dir = os.path.dirname(eqctotaldata_path)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_filename = f"EQC_Step3_Analysis_Only_DEBUG_LOG_{timestamp}.txt"
            self.step3_log_file = os.path.join(base_dir, log_filename)
            
            # 初始化DEBUG LOG檔案
            header_lines = [
                "EQC Step 3 - Online EQC FAIL 行識別（僅分析版）",
                "=" * 60,
                f"生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"檔案路徑: {eqctotaldata_path}",
                "=" * 60,
                ""
            ]
            
            with open(self.step3_log_file, 'w', encoding='utf-8') as f:
                for line in header_lines:
                    f.write(line + '\n')
            
            self.logger.info("[SEARCH] 執行簡化版 Step 3: 找 Online EQC FAIL 行")
            self.logger.info(f"   [FOLDER] Step 3 Analysis DEBUG LOG: {log_filename}")
            
            # 找 Online EQC FAIL 行
            fail_rows = self._find_online_eqc_fail_rows(rows)
            
            # 完成 Step 3 DEBUG LOG
            self._save_step3_debug_log(base_dir)
            
            return {
                'status': 'success',
                'fail_rows_found': len(fail_rows),
                'fail_rows': fail_rows,
                'debug_log_file': self.step3_log_file
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 簡化版 Step 3 FAIL行識別失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def _check_code_match_with_eqc_all_range(self, 
                                           line_elements_a: List[str], 
                                           line_elements_b: List[str], 
                                           start1: int, 
                                           end1: int,
                                           region_name: str = "CODE區間") -> Tuple[bool, bool]:
        """
        使用 EQC ALL 傳遞的區間進行 CODE 完全匹配檢查 (詳細 debug 版本)
        
        Args:
            start1, end1: EQC ALL 檢測的程式碼區間
            region_name: 區間名稱（主要區間/備用區間）
            
        Returns:
            tuple: (是否完全匹配, 是否全零)
        """
        found_match = True
        all_zero = True
        mismatch_count = 0
        match_count = 0
        first_mismatch_col = None
        
        self.logger.info(f"       [SEARCH] {region_name}匹配檢查: 第{start1+1}-{end1+1}欄 (共{end1-start1+1}個欄位)")
        
        # 在 EQC ALL 指定的 CODE 區間內進行完全匹配檢查
        for k in range(start1, end1 + 1):
            if k < len(line_elements_a) and k < len(line_elements_b):
                fail_value = line_elements_a[k].strip()
                rt_value = line_elements_b[k].strip()
                
                if rt_value != fail_value:
                    found_match = False
                    mismatch_count += 1
                    if first_mismatch_col is None:
                        first_mismatch_col = k + 1
                        if mismatch_count == 1:  # 只顯示第一個不匹配的詳情
                            self.logger.info(f"         [ERROR] 第{k+1}欄不匹配: FAIL='{fail_value}' vs RT='{rt_value}'")
                else:
                    match_count += 1
                    
                # 檢查是否為零值 (只檢查FAIL行，不檢查RT行)
                if fail_value not in ["0", ""]:
                    all_zero = False
            else:
                found_match = False
                mismatch_count += 1
                if first_mismatch_col is None:
                    first_mismatch_col = k + 1
                self.logger.info(f"         [ERROR] 第{k+1}欄超出範圍: FAIL長度={len(line_elements_a)}, RT長度={len(line_elements_b)}")
        
        # 顯示匹配結果統計
        total_fields = end1 - start1 + 1
        match_rate = (match_count / total_fields * 100) if total_fields > 0 else 0
        
        if found_match:
            self.logger.info(f"       [OK] {region_name}完全匹配: {match_count}/{total_fields} 欄位 (100%)")
        else:
            self.logger.info(f"       [ERROR] {region_name}不匹配: 匹配{match_count}/{total_fields} 欄位 ({match_rate:.1f}%)")
            if first_mismatch_col:
                self.logger.info(f"         不匹配數量: {mismatch_count}, 首個不匹配: 第{first_mismatch_col}欄")
        
        if all_zero:
            self.logger.info(f"       [WARNING] {region_name}FAIL資料全零")
        
        return found_match, all_zero
    
    def _check_backup_region_mapping(self, 
                                   fail_elements: List[str], 
                                   rt_elements: List[str], 
                                   backup_start1: int, 
                                   backup_end1: int,
                                   main_mapping_start1: int,
                                   main_mapping_end1: int) -> Tuple[bool, bool]:
        """
        檢查備用區間映射匹配邏輯
        備用區間(FAIL) vs 主要區間前36欄位(RT)
        
        Args:
            backup_start1, backup_end1: 備用區間範圍(第1565-1600欄)
            main_mapping_start1, main_mapping_end1: 主要區間前36欄位範圍(第298-333欄)
        """
        self.logger.info(f"       [SEARCH] 備用區間映射檢查:")
        self.logger.info(f"         FAIL備用區間: 第{backup_start1+1}-{backup_end1+1}欄 (共{backup_end1-backup_start1+1}個欄位)")
        self.logger.info(f"         RT主要區間前36欄: 第{main_mapping_start1+1}-{main_mapping_end1+1}欄 (共{main_mapping_end1-main_mapping_start1+1}個欄位)")
        
        found_match = True
        all_zero = True
        mismatch_count = 0
        match_count = 0
        first_mismatch_col = None
        
        # 檢查映射關係：FAIL備用區間 vs RT主要區間前36欄位
        backup_range = backup_end1 - backup_start1 + 1
        main_mapping_range = main_mapping_end1 - main_mapping_start1 + 1
        mapping_range = min(backup_range, main_mapping_range)
        
        for i in range(mapping_range):
            fail_idx = backup_start1 + i      # FAIL行的備用區間索引
            rt_idx = main_mapping_start1 + i  # RT行的主要區間前36欄位索引
            
            if fail_idx < len(fail_elements) and rt_idx < len(rt_elements):
                fail_value = fail_elements[fail_idx].strip()
                rt_value = rt_elements[rt_idx].strip()
                
                if rt_value != fail_value:
                    found_match = False
                    mismatch_count += 1
                    if first_mismatch_col is None:
                        first_mismatch_col = fail_idx + 1
                        if mismatch_count == 1:
                            self.logger.info(f"         [ERROR] 映射不匹配: FAIL第{fail_idx+1}欄='{fail_value}' vs RT第{rt_idx+1}欄='{rt_value}'")
                else:
                    match_count += 1
                
                # 檢查FAIL備用區間是否為零值
                if fail_value not in ["0", ""]:
                    all_zero = False
            else:
                found_match = False
                mismatch_count += 1
                if first_mismatch_col is None:
                    first_mismatch_col = fail_idx + 1
                self.logger.info(f"         [ERROR] 映射超出範圍: FAIL長度={len(fail_elements)}, RT長度={len(rt_elements)}")
        
        # 顯示映射結果統計
        match_rate = (match_count / mapping_range * 100) if mapping_range > 0 else 0
        
        if found_match:
            self.logger.info(f"       [OK] 備用區間映射完全匹配: {match_count}/{mapping_range} 欄位 (100%)")
        else:
            self.logger.info(f"       [ERROR] 備用區間映射不匹配: 匹配{match_count}/{mapping_range} 欄位 ({match_rate:.1f}%)")
            if first_mismatch_col:
                self.logger.info(f"         不匹配數量: {mismatch_count}, 首個不匹配: FAIL第{first_mismatch_col}欄")
        
        if all_zero:
            self.logger.info(f"       [WARNING] FAIL備用區間資料全零")
        
        return found_match, all_zero
    
    def _move_row_to_position(self, 
                            rows: List[str], 
                            source_index: int, 
                            target_index: int) -> List[str]:
        """
        將行從 source_index 移動到 target_index (詳細 debug 版本)
        對應 VBA 的行移動邏輯
        """
        if source_index >= len(rows) or target_index >= len(rows):
            self.logger.error(f"[ERROR] 移動失敗: 索引超出範圍 source={source_index}, target={target_index}, 總行數={len(rows)}")
            return rows
            
        # 記錄移動前的狀態
        source_data = rows[source_index].split(',')
        source_serial = source_data[0].strip() if len(source_data) > 0 else "未知"
        source_bin = source_data[1].strip() if len(source_data) > 1 else "未知"
        
        self.logger.info(f"     [BOARD] 開始移動 RT 資料:")
        self.logger.info(f"       來源: 第{source_index+1}行 (Serial={source_serial}, BIN={source_bin})")
        self.logger.info(f"       目標: 第{target_index+1}行")
        
        # 保存要移動的行
        row_to_move = rows[source_index]
        
        # 從原位置刪[EXCEPT_CHAR]
        rows.pop(source_index)
        self.logger.info(f"       [OK] 已從第{source_index+1}行刪[EXCEPT_CHAR]")
        
        # 計算實際插入位置
        actual_target_index = target_index
        if target_index > source_index:
            actual_target_index -= 1  # 因為已經刪[EXCEPT_CHAR]了一行
            
        # 插入到新位置
        rows.insert(actual_target_index, row_to_move)
        self.logger.info(f"       [OK] 已插入到第{actual_target_index+1}行")
        
        # 驗證移動結果
        moved_data = rows[actual_target_index].split(',')
        moved_serial = moved_data[0].strip() if len(moved_data) > 0 else "未知"
        moved_bin = moved_data[1].strip() if len(moved_data) > 1 else "未知"
        
        self.logger.info(f"     [OK] 移動完成驗證:")
        self.logger.info(f"       新位置第{actual_target_index+1}行: Serial={moved_serial}, BIN={moved_bin}")
        
        return rows
    
    def _write_updated_data(self, file_path: str, updated_rows: List[str]):
        """寫回檔案（不備份）"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(updated_rows)
            self.logger.info(f"[OK] 成功更新檔案: {file_path}")
        except Exception as e:
            self.logger.error(f"[ERROR] 檔案寫入失敗: {e}")
            raise
    
    # ============================================================
    # Step 4 專用 DEBUG LOG 功能實作
    # ============================================================
    
    def _log_step4_debug(self, message: str):
        """記錄 Step 4 DEBUG LOG 到終端和檔案"""
        # 記錄到終端
        self.logger.info(message)
        
        # 記錄到內存
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        log_line = f"[{timestamp}] {message}"
        self.debug_log_lines.append(log_line)
        
        # 如果有檔案路徑，同時寫入檔案
        if hasattr(self, 'step4_log_file') and self.step4_log_file:
            try:
                with open(self.step4_log_file, 'a', encoding='utf-8') as f:
                    f.write(log_line + '\n')
            except Exception as e:
                self.logger.warning(f"無法寫入 Step4 DEBUG LOG檔案 {self.step4_log_file}: {e}")
    
    def _save_step4_debug_log(self, base_dir: str):
        """完成 Step 4 DEBUG LOG 檔案"""
        try:
            if hasattr(self, 'step4_log_file') and self.step4_log_file and os.path.exists(self.step4_log_file):
                # 在檔案末尾添加結束標記
                end_lines = [
                    "",
                    "=" * 60,
                    f"處理完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                    "Step 4 - CODE 區間匹配搜尋 DEBUG LOG 結束",
                    "=" * 60
                ]
                
                with open(self.step4_log_file, 'a', encoding='utf-8') as f:
                    for line in end_lines:
                        f.write(line + '\n')
                
                log_filename = os.path.basename(self.step4_log_file)
                self.logger.info(f"   [OK] Step 4 DEBUG LOG 已完成: {log_filename}")
                return self.step4_log_file
            else:
                self.logger.warning("[WARNING] Step 4 DEBUG LOG 檔案不存在，無法完成")
                return None
            
        except Exception as e:
            self.logger.error(f"[ERROR] 完成 Step 4 DEBUG LOG 失敗: {e}")
            return None
    
    def perform_step4_code_matching_debug(self, rows: List[str], fail_details: List[Dict], start1: int, end1: int, backup_start1: int = None, backup_end1: int = None) -> Dict[str, Any]:
        """
        Step 4: CODE 區間匹配搜尋 DEBUG LOG
        為每個 Online EQC FAIL 行搜尋相同 CODE 區間值的 EQC RT 行
        """
        try:
            # 建立 Step 4 DEBUG LOG 檔案
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            debug_log_path = f"doc/20250523/EQCTOTALDATA_Step4_DEBUG.log"
            
            # 初始化 DEBUG LOG 檔案
            debug_header = [
                "Step 4 - CODE 區間匹配搜尋 DEBUG LOG",
                f"開始時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                "=" * 60,
                ""
            ]
            
            with open(debug_log_path, 'w', encoding='utf-8') as f:
                for line in debug_header:
                    f.write(line + '\n')
            
            # 設定 Step 4 DEBUG LOG 檔案路徑
            self.step4_log_file = debug_log_path
            
            self._log_step4_debug("[SEARCH] Step 4: 開始 CODE 區間匹配搜尋...")
            self._log_step4_debug(f"   主要 CODE 區間: 第{start1+1}-{end1+1}欄 ({end1-start1+1}個欄位)")
            if backup_start1 is not None and backup_end1 is not None:
                self._log_step4_debug(f"   備用 CODE 區間: 第{backup_start1+1}-{backup_end1+1}欄 ({backup_end1-backup_start1+1}個欄位) - 強制比對")
            else:
                self._log_step4_debug(f"   備用 CODE 區間: 未設置 (backup_start1={backup_start1}, backup_end1={backup_end1})")
            
            # 確定 EQC RT 開始位置
            try:
                b9_content = rows[8]  # 第9行
                fail_count = int(b9_content.split(',')[1].strip())
                eqc_rt_start = 12 + 1 + (fail_count * 2)  # 第13行 + Golden + Online EQC 對
                self._log_step4_debug(f"   EQC RT 搜尋範圍: 第{eqc_rt_start + 1}行 到 第{len(rows)}行")
            except:
                eqc_rt_start = 34  # 預設位置
                self._log_step4_debug(f"   EQC RT 搜尋範圍: 第{eqc_rt_start + 1}行 到 第{len(rows)}行 (預設)")
            
            total_matches = 0
            match_results = []
            
            # 為每個 FAIL 行搜尋匹配的 EQC RT 行
            for fail_idx, fail_detail in enumerate(fail_details, 1):
                self._log_step4_debug(f"\n[TARGET] 【FAIL #{fail_idx}】匹配搜尋:")
                self._log_step4_debug(f"   目標: 第{fail_detail['row_number']}行, BIN={fail_detail['bin_value']}")
                
                fail_row = rows[fail_detail['row_number'] - 1]
                fail_elements = fail_row.split(',')
                
                # 提取 FAIL 行的 CODE 區間值
                fail_code_values = []
                if len(fail_elements) > end1:
                    for i in range(start1, end1 + 1):
                        fail_code_values.append(fail_elements[i].strip())
                
                # 取得前一行 (FT 行) 的備用區間值
                ft_backup_values = []
                if fail_detail['row_number'] > 1 and backup_start1 is not None and backup_end1 is not None:
                    ft_row_idx = fail_detail['row_number'] - 2  # 前一行的索引
                    if ft_row_idx >= 0:
                        ft_row = rows[ft_row_idx]
                        ft_elements = ft_row.split(',')
                        if len(ft_elements) > backup_end1:
                            for i in range(backup_start1, backup_end1 + 1):
                                ft_backup_values.append(ft_elements[i].strip())
                            self._log_step4_debug(f"   FT行(第{ft_row_idx+1}行)備用區間樣本: {ft_backup_values[:5]}...{ft_backup_values[-3:]} (顯示前5+後3欄)")
                
                self._log_step4_debug(f"   CODE 區間樣本: {fail_code_values[:5]}...{fail_code_values[-3:]} (顯示前5+後3欄)")
                
                # 搜尋匹配的 EQC RT 行
                matches_found = 0
                for rt_idx in range(eqc_rt_start, len(rows)):
                    rt_row = rows[rt_idx]
                    rt_elements = rt_row.split(',')
                    
                    if len(rt_elements) <= end1:
                        continue
                    
                    # 檢查主要區間匹配
                    main_match = self._check_code_region_match(fail_elements, rt_elements, start1, end1)
                    
                    # 檢查備用區間匹配（使用FT行的備用區間值）
                    backup_match = None
                    if len(ft_backup_values) > 0:
                        # 使用新的比較邏輯：將FT行的備用區間值與RT行的主要區間比較
                        backup_match = self._check_ft_backup_against_main_region(ft_backup_values, rt_elements, start1, end1)
                    else:
                        # 沒有備用區間資料
                        backup_match = {
                            'is_match': False,
                            'matched_fields': 0,
                            'total_fields': 0,
                            'match_rate': 0.0,
                            'status': '無備用區間資料'
                        }
                    
                    # 判斷是否有任何匹配（主要區間或備用區間）
                    has_main_match = main_match['is_match']
                    has_backup_match = backup_match and backup_match['is_match']
                    
                    if has_main_match or has_backup_match:
                        matches_found += 1
                        rt_serial = rt_elements[0].strip() if len(rt_elements) > 0 else 'Unknown'
                        rt_bin = rt_elements[1].strip() if len(rt_elements) > 1 else 'Unknown'
                        
                        self._log_step4_debug(f"   [OK] 匹配 #{matches_found}: 第{rt_idx+1}行, Serial={rt_serial}, BIN={rt_bin}")
                        self._log_step4_debug(f"       主要區間: {main_match['matched_fields']}/{main_match['total_fields']} 欄位匹配 ({main_match['match_rate']:.0f}%)")
                        
                        # Debug: 顯示 RT 行前5個 CODE 欄位
                        rt_code_values = []
                        if len(rt_elements) > start1:
                            for i in range(start1, min(start1 + 5, len(rt_elements))):
                                rt_code_values.append(rt_elements[i].strip())
                        self._log_step4_debug(f"       RT行前5個CODE: {rt_code_values}")
                        
                        # Debug: 重新顯示 FAIL 行前5個 CODE 欄位以便對比
                        fail_code_debug = fail_code_values[:5] if len(fail_code_values) >= 5 else fail_code_values
                        self._log_step4_debug(f"       FAIL行前5個CODE: {fail_code_debug}")
                        
                        # 強制顯示備用區間結果
                        if backup_match:
                            backup_rate = backup_match['match_rate']
                            if 'status' in backup_match:
                                self._log_step4_debug(f"       備用區間(FT行): {backup_match['status']}")
                            else:
                                self._log_step4_debug(f"       備用區間(FT行→主區): {backup_match['matched_fields']}/{backup_match['total_fields']} 欄位匹配 ({backup_rate:.0f}%)")
                        else:
                            self._log_step4_debug(f"       備用區間(FT行): 檢查失敗或無法執行")
                        
                        # 決定匹配類型
                        match_type = 'main_region' if has_main_match else 'backup_mapping'
                        
                        match_results.append({
                            'fail_row': fail_detail['row_number'],
                            'rt_row': rt_idx + 1,
                            'rt_serial': rt_serial,
                            'rt_bin': rt_bin,
                            'match_type': match_type,
                            'main_match_details': main_match,
                            'backup_match_details': backup_match
                        })
                
                self._log_step4_debug(f"   [TARGET] FAIL #{fail_idx} 搜尋結果: 找到 {matches_found} 個匹配的同一顆 IC")
                total_matches += matches_found
            
            # 完成 Step 4 搜尋統計
            self._log_step4_debug(f"\n[CHART] Step 4 完整搜尋統計:")
            self._log_step4_debug(f"   總 FAIL 行數: {len(fail_details)}")
            self._log_step4_debug(f"   總匹配數量: {total_matches}")
            if len(fail_details) > 0:
                self._log_step4_debug(f"   平均每 FAIL: {total_matches/len(fail_details):.1f} 個匹配") 
            
            # 顯示指定行數的前5個 CODE 值作為對比
            self._log_step4_debug(f"\n[SEARCH] 【DEBUG】指定行數的前5個 CODE 對比:")
            specified_rows = [38, 43, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 339, 340]
            
            for row_num in specified_rows:
                if row_num <= len(rows):
                    row_data = rows[row_num - 1]  # 轉換為索引
                    row_elements = row_data.split(',')
                    
                    # 提取前5個 CODE 值
                    code_values = []
                    if len(row_elements) > start1:
                        for i in range(start1, min(start1 + 5, len(row_elements))):
                            code_values.append(row_elements[i].strip())
                    
                    # 獲取 Serial 和 BIN 資訊
                    serial = row_elements[0].strip() if len(row_elements) > 0 else 'Unknown'
                    bin_val = row_elements[1].strip() if len(row_elements) > 1 else 'Unknown'
                    
                    self._log_step4_debug(f"   第{row_num}行 [Serial={serial}, BIN={bin_val}] 前5個CODE: {code_values}")
                else:
                    self._log_step4_debug(f"   第{row_num}行: 超出檔案範圍 (總行數: {len(rows)})")
            
            # 完成 DEBUG LOG
            self._save_step4_debug_log("")
            
            return {
                'success': True,
                'total_matches': total_matches,
                'match_results': match_results,
                'debug_log_file': debug_log_path
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Step 4 CODE 匹配搜尋失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _check_code_region_match(self, fail_elements: List[str], rt_elements: List[str], start1: int, end1: int) -> Dict[str, Any]:
        """檢查主要 CODE 區間匹配"""
        matched_fields = 0
        total_fields = end1 - start1 + 1
        
        for i in range(start1, end1 + 1):
            if i < len(fail_elements) and i < len(rt_elements):
                if fail_elements[i].strip() == rt_elements[i].strip():
                    matched_fields += 1
        
        is_match = (matched_fields == total_fields)
        match_rate = (matched_fields / total_fields) * 100 if total_fields > 0 else 0
        
        return {
            'is_match': is_match,
            'matched_fields': matched_fields,
            'total_fields': total_fields,
            'match_rate': match_rate
        }
    
    
    def _check_ft_backup_against_main_region(self, ft_backup_values: List[str], rt_elements: List[str], start1: int, end1: int) -> Dict[str, Any]:
        """
        檢查FT行的備用區間值與RT行的主要區間匹配
        將備用區間的值對齊到主要區間位置進行比較
        """
        matched_fields = 0
        total_fields = min(len(ft_backup_values), end1 - start1 + 1)
        
        # 將FT行的備用區間值與RT行的主要區間對應位置比較
        for i in range(total_fields):
            rt_idx = start1 + i
            if rt_idx < len(rt_elements) and i < len(ft_backup_values):
                if ft_backup_values[i].strip() == rt_elements[rt_idx].strip():
                    matched_fields += 1
        
        is_match = (matched_fields == total_fields) if total_fields > 0 else False
        match_rate = (matched_fields / total_fields) * 100 if total_fields > 0 else 0
        
        return {
            'is_match': is_match,
            'matched_fields': matched_fields,
            'total_fields': total_fields,
            'match_rate': match_rate
        }