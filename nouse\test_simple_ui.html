<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <title>簡單測試</title>
</head>
<body>
    <h1>網路共享瀏覽器</h1>
    <div>
        <input type="text" id="usernameInput" placeholder="使用者名稱">
        <input type="password" id="passwordInput" placeholder="密碼">
        <button onclick="connectToShare()">連接</button>
    </div>
    <div id="status"></div>
    <script>
        const API = '/network/api';
        let currentPath = '\\\\************\\temp_7days';
        let isConnected = false;
        
        function showStatus(msg) {
            document.getElementById('status').innerHTML = msg;
        }
        
        async function connectToShare() {
            const username = document.getElementById('usernameInput').value.trim();
            const password = document.getElementById('passwordInput').value;
            
            if (!username || !password) {
                showStatus('請輸入使用者名稱和密碼');
                return;
            }
            
            showStatus('連接中...');
            
            try {
                const r = await fetch(`${API}/connect`, {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        path: currentPath,
                        username: username,
                        password: password,
                        domain: 'gmt'
                    })
                });
                const result = await r.json();
                
                if (result.status === 'success' && result.connected) {
                    showStatus('連接成功！');
                    isConnected = true;
                } else {
                    showStatus('連接失敗: ' + result.message);
                }
            } catch (e) {
                showStatus('連接失敗');
            }
        }
    </script>
</body>
</html>