# 程式碼標準與規範

## 強制要求
- ✅ **虛擬環境**: 每次開發前必須啟動
- ✅ **TDD開發**: 後端程式碼必須先寫測試
- ✅ **API測試**: 所有端點必須實際測試
- ✅ **程式測試**: 實際執行驗證功能

## 品質標準
- **測試覆蓋率**: > 90%
- **型別檢查**: MyPy 100% 通過
- **程式碼格式**: Black + Flake8
- **檔案大小**: ≤500行限制

## Anti-Fake Testing原則（零容忍）
- ❌ 使用假資料測試
- ❌ 假設API成功=功能成功
- ❌ 不等待真實處理時間就下結論
- ✅ 檔案時間戳必須實際改變
- ✅ 等待時間必須≥30秒（真實處理）

## 功能替換原則
- 實現新功能後立即刪除舊版本
- 不保留任何功能重複的程式碼
- 搜索確認無殘留：`grep -r "old_function_name"`

## 程式碼風格
- Line length: 100字元
- Type hints: 強制使用
- Docstrings: 必須包含
- 變數命名: snake_case
- 類別命名: PascalCase