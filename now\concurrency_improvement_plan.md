# 並發處理改進計畫

## 1. 實現任務隊列

### 使用 Celery 或自定義隊列
```python
# 安裝 Celery
# pip install celery redis

# tasks.py
from celery import Celery

app = Celery('outlook_summary', broker='redis://localhost:6379')

@app.task
def process_email_parsing(email_id):
    """異步處理郵件解析"""
    # 解析邏輯
    pass

@app.task
def process_eqc_batch(email_ids):
    """異步處理 EQC"""
    # EQC 處理邏輯
    pass
```

## 2. 改進同步鎖定機制

```python
import threading
import asyncio

class EmailSyncService:
    def __init__(self):
        self._sync_lock = threading.Lock()
        self._processing_emails = set()  # 正在處理的郵件 ID
        
    async def sync_emails_once(self, max_emails: int = 100):
        """加強的同步機制"""
        if not self._sync_lock.acquire(blocking=False):
            return {
                'success': False, 
                'message': '另一個同步正在進行中',
                'retry_after': 60
            }
        
        try:
            # 執行同步
            result = await self._perform_sync(max_emails)
            return result
        finally:
            self._sync_lock.release()
    
    def is_email_processing(self, email_id: int) -> bool:
        """檢查郵件是否正在處理"""
        return email_id in self._processing_emails
```

## 3. 資料庫級並發控制

```python
from sqlalchemy.orm import Session
from sqlalchemy import select

class EmailProcessor:
    def process_email_with_lock(self, email_id: int):
        """使用資料庫鎖定處理郵件"""
        with self.get_session() as session:
            # 使用 FOR UPDATE 鎖定行
            email = session.query(EmailDB)\
                .with_for_update(skip_locked=True)\
                .filter_by(id=email_id)\
                .first()
            
            if not email:
                return {'success': False, 'message': '郵件正在被處理或不存在'}
            
            # 處理邏輯
            email.is_processing = True
            session.commit()
            
            try:
                # 執行處理
                result = self._process_email(email)
                email.is_processed = True
            finally:
                email.is_processing = False
                session.commit()
```

## 4. 實現處理隊列和優先級

```python
import queue
import threading
from enum import IntEnum

class Priority(IntEnum):
    HIGH = 1
    NORMAL = 2
    LOW = 3

class TaskProcessor:
    def __init__(self, num_workers=4):
        self.task_queue = queue.PriorityQueue()
        self.workers = []
        self.running = False
        
        # 啟動工作線程
        for i in range(num_workers):
            worker = threading.Thread(
                target=self._worker, 
                daemon=True,
                name=f'Worker-{i}'
            )
            self.workers.append(worker)
    
    def add_task(self, priority: Priority, task_func, *args, **kwargs):
        """添加任務到隊列"""
        self.task_queue.put((priority, task_func, args, kwargs))
    
    def _worker(self):
        """工作線程"""
        while self.running:
            try:
                priority, task_func, args, kwargs = self.task_queue.get(timeout=1)
                task_func(*args, **kwargs)
                self.task_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"任務執行失敗: {e}")
```

## 5. 監控和限流

```python
from collections import defaultdict
from datetime import datetime, timedelta
import time

class RateLimiter:
    def __init__(self):
        self.requests = defaultdict(list)
        self.limits = {
            'sync': (10, 60),      # 每 60 秒最多 10 次同步
            'parse': (100, 60),    # 每 60 秒最多 100 次解析
            'process': (50, 60)    # 每 60 秒最多 50 次處理
        }
    
    def check_limit(self, operation: str, identifier: str = 'global') -> bool:
        """檢查是否超過限制"""
        if operation not in self.limits:
            return True
        
        max_requests, time_window = self.limits[operation]
        now = datetime.now()
        cutoff = now - timedelta(seconds=time_window)
        
        # 清理過期記錄
        key = f"{operation}:{identifier}"
        self.requests[key] = [
            req_time for req_time in self.requests[key] 
            if req_time > cutoff
        ]
        
        # 檢查限制
        if len(self.requests[key]) >= max_requests:
            return False
        
        # 記錄新請求
        self.requests[key].append(now)
        return True
```

## 6. 系統狀態監控

```python
class SystemMonitor:
    def __init__(self):
        self.stats = {
            'emails_synced': 0,
            'emails_parsed': 0,
            'emails_processed': 0,
            'active_tasks': 0,
            'queue_size': 0,
            'errors': 0
        }
    
    def get_system_status(self):
        """獲取系統狀態"""
        return {
            'timestamp': datetime.now().isoformat(),
            'stats': self.stats,
            'health': self._check_health(),
            'performance': self._get_performance_metrics()
        }
    
    def _check_health(self):
        """健康檢查"""
        if self.stats['errors'] > 100:
            return 'unhealthy'
        elif self.stats['queue_size'] > 1000:
            return 'degraded'
        return 'healthy'
```

## 實施優先級

1. **Phase 1**: 實現基本的線程鎖定（已部分完成）
2. **Phase 2**: 加入資料庫級鎖定
3. **Phase 3**: 實現任務隊列系統
4. **Phase 4**: 加入監控和限流機制

這樣可以確保系統在高並發情況下穩定運行。