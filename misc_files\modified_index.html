<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT-EQC 分組配對分析系統</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* 原有的CSS樣式保持不變 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        /* 其他樣式省略以節省空間 */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-project-diagram"></i> FT-EQC 分組配對分析系統</h1>
            <p>智能檔案分類、時間戳配對、CTA 格式檢測 - 一站式解決方案</p>
        </div>

        <div class="main-content">
            <!-- 左側控制面板 -->
            <div class="control-panel">
                <div class="folder-selector">
                    <h3><i class="fas fa-folder-open"></i> 資料夾選擇</h3>
                    <div class="folder-input-group">
                        <input type="text" class="folder-input" id="folderPath" placeholder="請輸入資料夾路徑" value="D:\project\python\outlook_summary\doc\20250523">
                        <button class="btn btn-secondary" onclick="selectFolder()">
                            <i class="fas fa-folder"></i> 瀏覽
                        </button>
                    </div>
                    <button class="btn btn-primary" onclick="processFolder()">
                        <i class="fas fa-play"></i> 開始分析
                    </button>
                    
                    <!-- EQC 一鍵完成處理按鈕 -->
                    <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #ddd;">
                        <h4 style="color: #2c3e50; margin-bottom: 10px; font-size: 14px;">
                            <i class="fas fa-rocket"></i> EQC 一鍵完成處理
                        </h4>
                        <button class="btn btn-primary" onclick="processCompleteEQCWorkflow()">
                            <i class="fas fa-magic"></i> 一鍵完成到程式碼對比
                        </button>
                        <div style="margin-top: 8px; font-size: 11px; color: #666; text-align: center;">
                            自動生成 EQCTOTALDATA → 程式碼區間檢測 → 雙重搜尋 → 完整報告
                        </div>
                    </div>
                    
                    <!-- 
                    ============================================================
                    【已移除】InsEqcRtData2 CODE對應處理區塊
                    ============================================================
                    原本這裡有：
                    - InsEqcRtData2 CODE對應處理標題
                    - 執行 CODE 對應插入按鈕
                    - 雙重搜尋 → CODE位置提取 → FAIL資料識別 → RT資料插入描述
                    - InsEqcRtData2 處理結果顯示區塊
                    ============================================================
                    -->

                </div>

                <!-- 統計面板等其他功能保持不變 -->
                <div class="stats-panel">
                    <h3><i class="fas fa-chart-bar"></i> 分析統計</h3>
                    <!-- 統計內容保持原樣 -->
                    <div id="totalFiles">-</div>
                    <div id="eqcFailCount">-</div>
                </div>

                <!-- 處理指示器 -->
                <div class="processing-indicator" id="processingIndicator" style="display: none;">
                    <div>正在分析檔案...</div>
                </div>
            </div>

            <!-- 右側結果面板保持不變 -->
            <div class="results-panel">
                <div class="timeline-header">
                    <h3><i class="fas fa-clock"></i> 時間軸分析結果</h3>
                </div>
                <div class="timeline" id="timelineContainer">
                    <div class="empty-state">
                        <i class="fas fa-search"></i>
                        <h4>尚未開始分析</h4>
                        <p>請選擇資料夾並點擊「開始分析」按鈕</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentResults = null;
        let currentFilter = 'all';

        function selectFolder() {
            const currentPath = document.getElementById('folderPath').value;
            const path = prompt('請輸入資料夾路徑:', currentPath);
            if (path) {
                document.getElementById('folderPath').value = path;
            }
        }

        async function processFolder() {
            const folderPath = document.getElementById('folderPath').value.trim();
            
            if (!folderPath) {
                alert('請輸入資料夾路徑');
                return;
            }

            showProcessing(true);
            
            try {
                const response = await fetch('http://localhost:8010/api/process_eqc_standard', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        folder_path: folderPath,
                        include_step123: true
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                currentResults = result.data;
                updateStatistics(result.data.statistics, result.data.eqc_fail_result);
                renderTimeline(result.data);
                
            } catch (error) {
                console.error('處理失敗:', error);
                alert(`處理失敗: ${error.message}`);
            }
            
            showProcessing(false);
        }

        function showProcessing(show) {
            const indicator = document.getElementById('processingIndicator');
            indicator.style.display = show ? 'block' : 'none';
        }

        async function processCompleteEQCWorkflow() {
            const folderPath = document.getElementById('folderPath').value.trim();
            
            if (!folderPath) {
                alert('請輸入資料夾路徑');
                return;
            }

            showProcessing(true);
            
            try {
                // EQC 一鍵完成處理邏輯
                console.log('開始 EQC 一鍵完成處理流程');
                alert('EQC 一鍵完成處理功能執行中...');
            } catch (error) {
                console.error('EQC 一鍵完成處理失敗:', error);
                alert(`處理失敗: ${error.message}`);
            }
            
            showProcessing(false);
        }

        function updateStatistics(stats, eqcFailResult = null) {
            document.getElementById('totalFiles').textContent = stats.total_csv_files || 0;
            const failCount = eqcFailResult ? eqcFailResult.fail_count || 0 : 0;
            document.getElementById('eqcFailCount').textContent = failCount;
        }

        function renderTimeline(result) {
            const container = document.getElementById('timelineContainer');
            container.innerHTML = '<div class="empty-state"><h4>分析完成</h4><p>檢查統計面板查看結果</p></div>';
        }

        // 
        // ============================================================
        // 【已移除】以下 InsEqcRtData2 相關函數：
        // ============================================================
        // - processInsEqcRtData2()
        // - displayInsEqcRtData2Result()
        // - hideInsEqcRtData2Result()
        // - addInsEqcRtData2ToTimeline()
        // ============================================================
        //

    </script>

</body>
</html>