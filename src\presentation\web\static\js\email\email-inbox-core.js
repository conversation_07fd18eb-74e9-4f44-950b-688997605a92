/**
 * 郵件收件夾核心類
 * 主要類定義和初始化邏輯
 */

/**
 * 郵件收件夾主類
 */
class EmailInbox {
    constructor(initialData = {}) {
        this.statistics = initialData.statistics || {};
        this.senders = initialData.senders || [];
        this.currentSender = 'all';
        this.currentPage = 1;
        this.pageSize = 50;
        this.totalCount = 0;
        this.selectedEmails = new Set();
        this.emails = [];
        this.isLoading = false;
        this.autoSyncEnabled = false;
        this.autoSyncInterval = null;
        
        // DOM 元素
        this.elements = {};
        
        // 事件監聽器
        this.eventListeners = new Map();
        
        // 模組管理器
        this.listManager = null;
        this.operations = null;
    }
    
    /**
     * 初始化郵件收件夾
     */
    initialize() {
        this.initializeElements();
        this.initializeManagers();
        this.setupEventListeners();
        this.loadEmails();
        this.updateStatistics();
        this.setupAutoSync();
    }
    
    /**
     * 初始化模組管理器
     */
    initializeManagers() {
        this.listManager = new EmailListManager(this);
        this.operations = new EmailOperations(this);
    }
    
    /**
     * 初始化 DOM 元素
     */
    initializeElements() {
        this.elements = {
            // 統計元素
            totalEmails: document.getElementById('total-emails'),
            unreadEmails: document.getElementById('unread-emails'),
            totalSenders: document.getElementById('total-senders'),
            allCount: document.getElementById('all-count'),
            
            // 按鈕元素
            syncBtn: document.getElementById('sync-btn'),
            autoSyncBtn: document.getElementById('auto-sync-btn'),
            settingsBtn: document.getElementById('settings-btn'),
            searchBtn: document.getElementById('search-btn'),
            
            // 輸入元素
            searchInput: document.getElementById('search-input'),
            sortSelect: document.getElementById('sort-select'),
            unreadOnlyCheckbox: document.getElementById('unread-only'),
            selectAllCheckbox: document.getElementById('select-all'),
            
            // 容器元素
            syncStatus: document.getElementById('sync-status'),
            emailList: document.getElementById('email-list'),
            emailDetail: document.getElementById('email-detail'),
            batchActions: document.getElementById('batch-actions'),
            
            // 分頁元素
            pageStart: document.getElementById('page-start'),
            pageEnd: document.getElementById('page-end'),
            totalCountSpan: document.getElementById('total-count'),
            prevPageBtn: document.getElementById('prev-page'),
            nextPageBtn: document.getElementById('next-page'),
            pageNumbers: document.getElementById('page-numbers'),
            
            // 對話框元素
            loadingOverlay: document.getElementById('loading-overlay'),
            confirmDialog: document.getElementById('confirm-dialog'),
            notification: document.getElementById('notification'),
            
            // 其他元素
            selectedCount: document.getElementById('selected-count'),
            detailSubject: document.getElementById('detail-subject'),
            detailSender: document.getElementById('detail-sender'),
            detailTime: document.getElementById('detail-time'),
            detailAttachments: document.getElementById('detail-attachments'),
            detailBody: document.getElementById('detail-body'),
            closeDetailBtn: document.getElementById('close-detail')
        };
    }
    
    /**
     * 設置事件監聽器
     */
    setupEventListeners() {
        // 同步按鈕
        this.addEventListenerSafe(this.elements.syncBtn, 'click', () => this.operations.syncEmails());
        
        // 自動同步按鈕
        this.addEventListenerSafe(this.elements.autoSyncBtn, 'click', () => this.operations.toggleAutoSync());
        
        // 搜尋功能
        this.addEventListenerSafe(this.elements.searchBtn, 'click', () => this.listManager.searchEmails());
        this.addEventListenerSafe(this.elements.searchInput, 'keypress', (e) => {
            if (e.key === 'Enter') this.listManager.searchEmails();
        });
        
        // 篩選和排序
        this.addEventListenerSafe(this.elements.sortSelect, 'change', () => this.listManager.setSortAndReload(this.elements.sortSelect.value));
        this.addEventListenerSafe(this.elements.unreadOnlyCheckbox, 'change', () => this.loadEmails());
        
        // 全選功能
        this.addEventListenerSafe(this.elements.selectAllCheckbox, 'change', (e) => this.operations.toggleSelectAll(e.target.checked));
        
        // 分頁按鈕
        this.addEventListenerSafe(this.elements.prevPageBtn, 'click', () => this.listManager.previousPage());
        this.addEventListenerSafe(this.elements.nextPageBtn, 'click', () => this.listManager.nextPage());
        
        // 批量操作
        this.addEventListenerSafe(document.getElementById('batch-mark-read'), 'click', () => this.operations.batchMarkRead());
        this.addEventListenerSafe(document.getElementById('batch-delete'), 'click', () => this.operations.batchDelete());
        this.addEventListenerSafe(document.getElementById('batch-process'), 'click', () => this.operations.batchProcess());
        
        // 郵件詳情
        this.addEventListenerSafe(this.elements.closeDetailBtn, 'click', () => this.listManager.closeEmailDetail());
        
        // 對話框
        this.addEventListenerSafe(document.getElementById('dialog-close'), 'click', () => EmailUIUtils.closeDialog(this.elements));
        this.addEventListenerSafe(document.getElementById('dialog-cancel'), 'click', () => EmailUIUtils.closeDialog(this.elements));
        
        // 通知
        this.addEventListenerSafe(document.querySelector('.notification-close'), 'click', () => EmailUIUtils.closeNotification(this.elements));
    }
    
    /**
     * 安全地添加事件監聽器
     */
    addEventListenerSafe(element, event, handler) {
        if (element) {
            element.addEventListener(event, handler);
            this.eventListeners.set(element, { event, handler });
        }
    }
    
    /**
     * 載入郵件列表（委派給列表管理器）
     */
    async loadEmails() {
        return await this.listManager.loadEmails();
    }
    
    /**
     * 更新統計資訊（委派給操作管理器）
     */
    async updateStatistics() {
        return await this.operations.updateStatistics();
    }
    
    /**
     * 設置自動同步
     */
    async setupAutoSync() {
        // 檢查是否有持續的同步狀態
        this.operations.checkSyncStatus();
        
        // 檢查自動同步狀態
        try {
            const response = await fetch('/api/sync/status');
            const result = await response.json();
            
            if (result.auto_sync_enabled) {
                this.autoSyncEnabled = true;
                if (this.elements.autoSyncBtn) {
                    this.elements.autoSyncBtn.innerHTML = '<span class="btn-icon">⏸️</span><span class="btn-text">停止自動同步</span>';
                    this.elements.autoSyncBtn.classList.add('active');
                }
            }
        } catch (error) {
            console.error('檢查自動同步狀態失敗:', error);
        }
    }
    
    /**
     * 下載附件（委派給附件管理器）
     */
    async downloadAttachment(attachmentId, filename) {
        return await window.emailAttachments.downloadAttachment(attachmentId, filename);
    }
    
    /**
     * 清理資源
     */
    cleanup() {
        // 清理事件監聽器
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.eventListeners.clear();
        
        // 清理自動同步
        this.operations.stopAutoSync();
    }
    
    /**
     * 獲取當前選中的郵件數量
     */
    getSelectedEmailCount() {
        return this.selectedEmails.size;
    }
    
    /**
     * 獲取當前頁面的郵件數量
     */
    getCurrentPageEmailCount() {
        return this.emails.length;
    }
    
    /**
     * 檢查是否有選中的郵件
     */
    hasSelectedEmails() {
        return this.selectedEmails.size > 0;
    }
    
    /**
     * 重置分頁到第一頁
     */
    resetToFirstPage() {
        this.currentPage = 1;
    }
    
    /**
     * 設置頁面大小
     */
    setPageSize(size) {
        this.pageSize = size;
        this.resetToFirstPage();
        this.loadEmails();
    }
    
    /**
     * 獲取當前過濾器狀態
     */
    getCurrentFilters() {
        return {
            sender: this.currentSender,
            unreadOnly: this.elements.unreadOnlyCheckbox?.checked || false,
            sortBy: this.elements.sortSelect?.value || 'received_time_desc',
            searchTerm: this.elements.searchInput?.value?.trim() || ''
        };
    }
    
    /**
     * 應用過濾器
     */
    applyFilters(filters) {
        if (filters.sender !== undefined) {
            this.currentSender = filters.sender;
        }
        if (filters.unreadOnly !== undefined && this.elements.unreadOnlyCheckbox) {
            this.elements.unreadOnlyCheckbox.checked = filters.unreadOnly;
        }
        if (filters.sortBy !== undefined && this.elements.sortSelect) {
            this.elements.sortSelect.value = filters.sortBy;
        }
        if (filters.searchTerm !== undefined && this.elements.searchInput) {
            this.elements.searchInput.value = filters.searchTerm;
        }
        
        this.resetToFirstPage();
        this.loadEmails();
    }
    
    /**
     * 清除所有過濾器
     */
    clearAllFilters() {
        this.applyFilters({
            sender: 'all',
            unreadOnly: false,
            sortBy: 'received_time_desc',
            searchTerm: ''
        });
    }
}

// 全域實例
let emailInbox = null;

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    // 如果有初始化數據，在 HTML 中已經處理
    // 這裡是備用的初始化邏輯
    if (typeof window.emailInbox === 'undefined' && window.location.pathname === '/') {
        emailInbox = new EmailInbox();
        emailInbox.initialize();
        
        // 設置全域引用
        window.emailInbox = emailInbox;
    }
});

// 全域可用
window.EmailInbox = EmailInbox;