# 郵件解析與附件處理整合計畫

## 一、專案架構設計（每個檔案 ≤500行）

### 1. 資料庫層修改
```
src/infrastructure/adapters/database/
├── models.py (現有 ~164行)
│   └── 添加解析欄位 (+6行)
├── email_database.py (現有 ~675行) 
│   └── 需要拆分為兩個檔案
├── email_database_core.py (新建 ~400行)
│   └── 核心 CRUD 操作
└── email_database_parser.py (新建 ~275行)
    └── 解析相關操作
```

### 2. 附件管理模組（新建）
```
src/infrastructure/adapters/attachments/
├── __init__.py
├── attachment_manager.py (~200行)
│   ├── AttachmentManager 類別
│   ├── save_attachment()
│   ├── get_attachment_path()
│   └── cleanup_old_attachments()
└── attachment_validator.py (~100行)
    ├── validate_file_type()
    ├── check_file_size()
    └── scan_for_threats()
```

### 3. 郵件解析服務（新建）
```
src/infrastructure/adapters/email_parser/
├── __init__.py
├── parser_service.py (~300行)
│   ├── EmailParserService 類別
│   ├── register_all_parsers()
│   ├── parse_email()
│   └── batch_parse_emails()
├── parser_registry.py (~150行)
│   ├── 管理所有解析器註冊
│   └── 動態載入解析器
└── parser_result_processor.py (~150行)
    ├── 處理解析結果
    └── 資料格式轉換
```

### 4. 修改後的同步服務
```
src/infrastructure/adapters/email_inbox/
├── email_sync_service.py (現有 ~400行)
│   └── 添加附件下載邏輯 (+50行)
└── sync_attachment_handler.py (新建 ~200行)
    ├── 處理附件下載
    └── 更新附件路徑
```

### 5. Web API 更新
```
src/presentation/web/api/
├── email_api.py (新建 ~300行)
│   ├── 郵件相關 API
│   └── 從 email_inbox_app.py 分離
├── parser_api.py (新建 ~200行)
│   ├── 解析相關 API
│   ├── /api/emails/<id>/reparse
│   └── /api/emails/batch-parse
└── attachment_api.py (新建 ~200行)
    ├── 附件相關 API
    └── /api/attachments/<id>/download
```

## 二、實作步驟與檔案內容

### Step 1: 資料庫模型修改

**models.py** (添加到 EmailDB 類別):
```python
# 解析相關欄位
pd = Column(String(100), nullable=True, index=True)
lot = Column(String(100), nullable=True, index=True) 
yield_value = Column(String(50), nullable=True)
vendor_code = Column(String(50), nullable=True, index=True)
parsed_at = Column(DateTime, nullable=True)
parse_status = Column(String(20), default='pending')  # pending, parsed, failed
parse_error = Column(Text, nullable=True)
```

### Step 2: 附件管理器

**attachment_manager.py**:
```python
"""
附件管理器
處理郵件附件的儲存、驗證和管理
"""

from pathlib import Path
from typing import Optional, List
import hashlib
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename

from src.data_models.email_models import EmailAttachment
from src.infrastructure.logging.logger_manager import LoggerManager


class AttachmentManager:
    """附件管理器"""
    
    ALLOWED_EXTENSIONS = {'.csv', '.xlsx', '.xls', '.txt', '.pdf', 
                         '.zip', '.7z', '.rar', '.gz', '.tar'}
    MAX_FILE_SIZE_MB = 50
    
    def __init__(self, base_path: str = "attachments"):
        self.base_path = Path(base_path)
        self.base_path.mkdir(exist_ok=True)
        self.logger = LoggerManager().get_logger("AttachmentManager")
        
    def save_attachment(self, email_id: int, attachment: EmailAttachment, 
                       content: bytes) -> Optional[Path]:
        """
        儲存附件到檔案系統
        
        Args:
            email_id: 郵件 ID
            attachment: 附件資訊
            content: 附件內容
            
        Returns:
            儲存的檔案路徑，失敗返回 None
        """
        try:
            # 驗證檔案
            if not self._validate_attachment(attachment, content):
                return None
                
            # 創建郵件專屬目錄
            email_dir = self.base_path / f"email_{email_id}"
            email_dir.mkdir(exist_ok=True)
            
            # 生成安全的檔名
            safe_filename = secure_filename(attachment.filename)
            if not safe_filename:
                safe_filename = f"attachment_{datetime.now().timestamp()}"
                
            # 處理重複檔名
            file_path = email_dir / safe_filename
            counter = 1
            while file_path.exists():
                name_parts = safe_filename.rsplit('.', 1)
                if len(name_parts) == 2:
                    file_path = email_dir / f"{name_parts[0]}_{counter}.{name_parts[1]}"
                else:
                    file_path = email_dir / f"{safe_filename}_{counter}"
                counter += 1
                
            # 儲存檔案
            file_path.write_bytes(content)
            
            # 計算檔案雜湊
            file_hash = hashlib.sha256(content).hexdigest()
            
            self.logger.info(f"附件已儲存: {file_path} (hash: {file_hash[:8]}...)")
            return file_path
            
        except Exception as e:
            self.logger.error(f"儲存附件失敗: {e}")
            return None
            
    def _validate_attachment(self, attachment: EmailAttachment, 
                           content: bytes) -> bool:
        """驗證附件"""
        # 檢查檔案大小
        size_mb = len(content) / (1024 * 1024)
        if size_mb > self.MAX_FILE_SIZE_MB:
            self.logger.warning(f"附件過大: {size_mb:.1f}MB > {self.MAX_FILE_SIZE_MB}MB")
            return False
            
        # 檢查副檔名
        file_ext = Path(attachment.filename).suffix.lower()
        if file_ext not in self.ALLOWED_EXTENSIONS:
            self.logger.warning(f"不允許的檔案類型: {file_ext}")
            return False
            
        return True
        
    def get_attachment_path(self, email_id: int, filename: str) -> Optional[Path]:
        """取得附件路徑"""
        email_dir = self.base_path / f"email_{email_id}"
        file_path = email_dir / secure_filename(filename)
        
        if file_path.exists():
            return file_path
        return None
        
    def cleanup_old_attachments(self, days: int = 30):
        """清理舊附件"""
        cutoff_time = datetime.now() - timedelta(days=days)
        cleaned_count = 0
        
        for email_dir in self.base_path.iterdir():
            if email_dir.is_dir():
                # 檢查目錄修改時間
                mtime = datetime.fromtimestamp(email_dir.stat().st_mtime)
                if mtime < cutoff_time:
                    # 刪[EXCEPT_CHAR]整個目錄
                    import shutil
                    shutil.rmtree(email_dir)
                    cleaned_count += 1
                    
        self.logger.info(f"已清理 {cleaned_count} 個舊附件目錄")
```

### Step 3: 郵件解析服務

**parser_service.py**:
```python
"""
郵件解析服務
整合所有廠商解析器，提供統一的解析介面
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from src.data_models.email_models import EmailData, EmailParsingResult
from src.infrastructure.parsers.base_parser import ParserFactory, ParserRegistry
from src.infrastructure.logging.logger_manager import LoggerManager

# 導入所有解析器
from src.infrastructure.parsers.gtk_parser import GTKParser
from src.infrastructure.parsers.etd_parser import ETDParser
from src.infrastructure.parsers.jcet_parser import JCETParser
from src.infrastructure.parsers.sigurd_parser import SIGURDParser
from src.infrastructure.parsers.utac_parser import UTACParser


class EmailParserService:
    """郵件解析服務"""
    
    def __init__(self):
        self.logger = LoggerManager().get_logger("EmailParserService")
        self.factory = ParserFactory()
        self._register_all_parsers()
        
        # 解析統計
        self.stats = {
            'total_parsed': 0,
            'success_count': 0,
            'failed_count': 0,
            'vendor_counts': {}
        }
        
    def _register_all_parsers(self):
        """註冊所有解析器"""
        registry = ParserRegistry()
        
        parsers = [
            ("GTK", GTKParser()),
            ("ETD", ETDParser()),
            ("JCET", JCETParser()),
            ("SIGURD", SIGURDParser()),
            ("UTAC", UTACParser())
        ]
        
        for vendor_code, parser in parsers:
            try:
                registry.register_parser(vendor_code, parser)
                self.logger.info(f"已註冊解析器: {vendor_code}")
            except Exception as e:
                self.logger.error(f"註冊解析器失敗 {vendor_code}: {e}")
                
    def parse_email(self, email_data: EmailData) -> Dict[str, Any]:
        """
        解析郵件
        
        Args:
            email_data: 郵件資料
            
        Returns:
            解析結果字典
        """
        self.stats['total_parsed'] += 1
        
        try:
            # 使用工廠進行解析
            vendor_result, parsing_result = self.factory.parse_email(email_data)
            
            if parsing_result.is_success:
                self.stats['success_count'] += 1
                
                # 更新廠商統計
                vendor = vendor_result.vendor_code or 'UNKNOWN'
                self.stats['vendor_counts'][vendor] = \
                    self.stats['vendor_counts'].get(vendor, 0) + 1
                
                return {
                    'success': True,
                    'vendor': vendor_result.vendor_code,
                    'vendor_name': vendor_result.vendor_name,
                    'pd': parsing_result.extracted_data.get('product_name'),
                    'lot': parsing_result.lot_number,
                    'yield': parsing_result.extracted_data.get('yield_value'),
                    'confidence': vendor_result.confidence_score,
                    'mo_number': parsing_result.mo_number,
                    'extracted_data': parsing_result.extracted_data,
                    'error': None
                }
            else:
                self.stats['failed_count'] += 1
                return {
                    'success': False,
                    'vendor': None,
                    'pd': None,
                    'lot': None,
                    'yield': None,
                    'error': parsing_result.error_message or "解析失敗"
                }
                
        except Exception as e:
            self.stats['failed_count'] += 1
            self.logger.error(f"解析郵件時發生錯誤: {e}")
            return {
                'success': False,
                'vendor': None,
                'pd': None,
                'lot': None,
                'yield': None,
                'error': str(e)
            }
            
    def batch_parse_emails(self, emails: List[EmailData], 
                          progress_callback=None) -> List[Dict[str, Any]]:
        """
        批次解析郵件
        
        Args:
            emails: 郵件列表
            progress_callback: 進度回調函數
            
        Returns:
            解析結果列表
        """
        results = []
        total = len(emails)
        
        for i, email in enumerate(emails):
            result = self.parse_email(email)
            result['email_id'] = getattr(email, 'id', None)
            result['subject'] = email.subject
            results.append(result)
            
            # 回報進度
            if progress_callback:
                progress_callback(i + 1, total)
                
        return results
        
    def get_statistics(self) -> Dict[str, Any]:
        """取得解析統計"""
        success_rate = 0
        if self.stats['total_parsed'] > 0:
            success_rate = (self.stats['success_count'] / 
                           self.stats['total_parsed']) * 100
                           
        return {
            'total_parsed': self.stats['total_parsed'],
            'success_count': self.stats['success_count'],
            'failed_count': self.stats['failed_count'],
            'success_rate': round(success_rate, 2),
            'vendor_distribution': self.stats['vendor_counts'],
            'supported_vendors': self.factory.get_supported_vendors()
        }
        
    def identify_vendor_only(self, email_data: EmailData) -> Dict[str, Any]:
        """
        僅識別廠商，不進行完整解析
        
        Args:
            email_data: 郵件資料
            
        Returns:
            廠商識別結果
        """
        best_parser, vendor_result = self.factory.identify_vendor(email_data)
        
        return {
            'is_identified': vendor_result.is_identified,
            'vendor_code': vendor_result.vendor_code,
            'vendor_name': vendor_result.vendor_name,
            'confidence_score': vendor_result.confidence_score,
            'matching_patterns': vendor_result.matching_patterns
        }
```

### Step 4: 更新同步服務

**sync_attachment_handler.py**:
```python
"""
同步附件處理器
處理郵件同步時的附件下載和儲存
"""

from typing import List, Dict, Any, Optional
from pathlib import Path

from src.data_models.email_models import EmailData, EmailAttachment
from src.infrastructure.adapters.attachments.attachment_manager import AttachmentManager
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.logging.logger_manager import LoggerManager


class SyncAttachmentHandler:
    """同步附件處理器"""
    
    def __init__(self, database: EmailDatabase, 
                 attachment_manager: AttachmentManager = None):
        self.database = database
        self.attachment_manager = attachment_manager or AttachmentManager()
        self.logger = LoggerManager().get_logger("SyncAttachmentHandler")
        
    async def process_email_attachments(self, email_id: int, 
                                      email_data: EmailData) -> Dict[str, Any]:
        """
        處理郵件的所有附件
        
        Args:
            email_id: 郵件 ID
            email_data: 郵件資料
            
        Returns:
            處理結果
        """
        if not email_data.attachments:
            return {
                'success': True,
                'processed_count': 0,
                'failed_count': 0,
                'errors': []
            }
            
        processed_count = 0
        failed_count = 0
        errors = []
        
        for attachment in email_data.attachments:
            try:
                # 下載附件內容
                content = await self._download_attachment_content(
                    email_data, attachment
                )
                
                if content:
                    # 儲存附件
                    file_path = self.attachment_manager.save_attachment(
                        email_id, attachment, content
                    )
                    
                    if file_path:
                        # 更新資料庫
                        self.database.update_attachment_path(
                            email_id, 
                            attachment.filename, 
                            str(file_path)
                        )
                        processed_count += 1
                        self.logger.info(
                            f"附件已處理: {attachment.filename} -> {file_path}"
                        )
                    else:
                        failed_count += 1
                        errors.append(f"無法儲存附件: {attachment.filename}")
                else:
                    failed_count += 1
                    errors.append(f"無法下載附件: {attachment.filename}")
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"處理附件失敗 {attachment.filename}: {e}"
                errors.append(error_msg)
                self.logger.error(error_msg)
                
        return {
            'success': failed_count == 0,
            'processed_count': processed_count,
            'failed_count': failed_count,
            'errors': errors
        }
        
    async def _download_attachment_content(self, email_data: EmailData, 
                                         attachment: EmailAttachment) -> Optional[bytes]:
        """
        下載附件內容
        
        注意：這裡需要根據實際的郵件來源實作
        POP3 的附件應該已經在 email_data 中
        """
        # 如果附件已經包含內容
        if hasattr(attachment, 'content') and attachment.content:
            return attachment.content
            
        # 否則需要從郵件服務器下載
        # 這裡需要根據實際情況實作
        self.logger.warning(f"附件 {attachment.filename} 沒有內容資料")
        return None
```

## 三、Web API 模組化

### Step 5: API 模組拆分

**parser_api.py**:
```python
"""
解析相關 API
提供郵件解析的 RESTful API
"""

from flask import Blueprint, jsonify, request
from datetime import datetime

from src.infrastructure.adapters.email_parser.parser_service import EmailParserService
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.data_models.email_models import EmailData
from src.infrastructure.logging.logger_manager import LoggerManager


parser_bp = Blueprint('parser', __name__, url_prefix='/api/parser')
logger = LoggerManager().get_logger("ParserAPI")
parser_service = EmailParserService()
database = EmailDatabase()


@parser_bp.route('/emails/<int:email_id>/reparse', methods=['POST'])
def reparse_email(email_id: int):
    """重新解析郵件"""
    try:
        # 取得郵件資料
        email = database.get_email_by_id(email_id)
        if not email:
            return jsonify({
                'success': False, 
                'error': '郵件不存在'
            }), 404
            
        # 轉換為 EmailData
        email_data = EmailData(
            message_id=email['message_id'],
            subject=email['subject'],
            sender=email['sender'],
            body=email.get('body', ''),
            received_time=datetime.fromisoformat(email['received_time'])
        )
        
        # 重新解析
        result = parser_service.parse_email(email_data)
        
        # 更新資料庫
        if result['success']:
            database.update_email_parse_result(email_id, result)
            
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        logger.error(f"重新解析郵件失敗: {e}")
        return jsonify({
            'success': False, 
            'error': str(e)
        }), 500


@parser_bp.route('/emails/batch-parse', methods=['POST'])
def batch_parse_emails():
    """批次解析郵件"""
    try:
        # 取得請求參數
        data = request.get_json() or {}
        limit = data.get('limit', 100)
        parse_failed_only = data.get('failed_only', False)
        
        # 查詢待解析郵件
        if parse_failed_only:
            emails = database.get_emails_by_parse_status('failed', limit=limit)
        else:
            emails = database.get_emails_by_parse_status('pending', limit=limit)
            
        if not emails:
            return jsonify({
                'success': True,
                'message': '沒有待解析的郵件',
                'parsed_count': 0
            })
            
        # 批次解析
        results = []
        for email in emails:
            email_data = EmailData(
                message_id=email['message_id'],
                subject=email['subject'],
                sender=email['sender'],
                body=email.get('body', ''),
                received_time=datetime.fromisoformat(email['received_time'])
            )
            
            result = parser_service.parse_email(email_data)
            result['email_id'] = email['id']
            
            # 更新資料庫
            if result['success']:
                database.update_email_parse_result(email['id'], result)
                
            results.append(result)
            
        success_count = sum(1 for r in results if r['success'])
        
        return jsonify({
            'success': True,
            'parsed_count': len(results),
            'success_count': success_count,
            'failed_count': len(results) - success_count,
            'results': results
        })
        
    except Exception as e:
        logger.error(f"批次解析失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@parser_bp.route('/statistics', methods=['GET'])
def get_parser_statistics():
    """取得解析統計"""
    try:
        stats = parser_service.get_statistics()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"取得統計失敗: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
```

## 四、資料庫更新方法

**email_database_parser.py** (新增方法):
```python
def update_email_parse_result(self, email_id: int, 
                             parse_result: Dict[str, Any]) -> bool:
    """更新郵件解析結果"""
    try:
        with self.get_session() as session:
            email = session.query(EmailDB).filter_by(id=email_id).first()
            
            if not email:
                return False
                
            # 更新解析結果
            if parse_result['success']:
                email.vendor_code = parse_result.get('vendor')
                email.pd = parse_result.get('pd')
                email.lot = parse_result.get('lot')
                email.yield_value = parse_result.get('yield')
                email.parsed_at = datetime.utcnow()
                email.parse_status = 'parsed'
                email.parse_error = None
            else:
                email.parse_status = 'failed'
                email.parse_error = parse_result.get('error')
                
            session.commit()
            return True
            
    except Exception as e:
        self.logger.error(f"更新解析結果失敗: {e}")
        return False

def get_emails_by_parse_status(self, status: str, 
                               limit: int = 100) -> List[Dict[str, Any]]:
    """根據解析狀態取得郵件"""
    try:
        with self.get_session() as session:
            emails = session.query(EmailDB).filter_by(
                parse_status=status
            ).limit(limit).all()
            
            return [self._email_to_dict(email) for email in emails]
            
    except Exception as e:
        self.logger.error(f"查詢郵件失敗: {e}")
        return []

def update_attachment_path(self, email_id: int, filename: str, 
                          file_path: str) -> bool:
    """更新附件路徑"""
    try:
        with self.get_session() as session:
            attachment = session.query(AttachmentDB).filter_by(
                email_id=email_id,
                filename=filename
            ).first()
            
            if attachment:
                attachment.file_path = file_path
                attachment.is_processed = True
                session.commit()
                return True
                
            return False
            
    except Exception as e:
        self.logger.error(f"更新附件路徑失敗: {e}")
        return False
```

## 五、需要移[EXCEPT_CHAR]的檔案

### 移[EXCEPT_CHAR]清單：
1. `src/infrastructure/config/` - 整個目錄（過度設計）
2. `src/domain/` - 整個目錄（空白層）
3. `src/application/use_cases/` - 大部分檔案（保留有用的邏輯）
4. `src/infrastructure/logging/logger_manager.py` - 簡化為基本日誌

### 保留並整合：
1. 所有 parser 檔案（GTK、ETD、JCET 等）
2. `EmailData` 和相關資料模型
3. 基本的錯誤處理類別

## 六、預期成果

完成整合後：
- [OK] 每個 Python 檔案都在 500 行以內
- [OK] 結構清晰，職責分明
- [OK] 自動下載和儲存附件
- [OK] 自動解析郵件提取 PD、LOT、YIELD
- [OK] Web UI 顯示完整資訊
- [OK] 支援批次處理和重新解析
- [OK] 簡化的專案結構，易於維護

## 七、實施時間表

1. **第一週**：資料庫遷移和附件管理器
2. **第二週**：整合解析服務和更新同步邏輯
3. **第三週**：API 模組化和 Web UI 更新
4. **第四週**：測試、優化和清理專案