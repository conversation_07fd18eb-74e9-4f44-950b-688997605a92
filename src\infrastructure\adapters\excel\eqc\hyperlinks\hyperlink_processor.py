#!/usr/bin/env python3
"""
超連結處理模組
完全對應 VBA ReplacePath 功能，處理本地路徑到網路路徑轉換
遵循 CLAUDE.md 功能替換原則，從主處理器中獨立出來
"""

import os
from typing import List, Optional
from dotenv import load_dotenv


class HyperlinkProcessor:
    """
    超連結處理器 - 完全對應 VBA ReplacePath 功能
    遵循 CLAUDE.md 功能替換原則，替換原有簡化邏輯
    """
    
    def __init__(self, base_path: str):
        """
        初始化超連結處理器
        
        Args:
            base_path: 本地基礎路徑
        """
        load_dotenv()
        
        # 完全動態的本地路徑，支援任意目錄
        self.temp_path = base_path
        
        # 從環境變數載入網路路徑，預設值為原有路徑
        self.net_path = os.getenv('HYPERLINK_NETWORK_PATH', r'\\192.168.1.60\temp_7days')
        
    def convert_to_network_path(self, local_path: str) -> str:
        """
        將本地路徑轉換為網路共享路徑 - 完全對應 VBA ReplacePath 函數
        
        VBA 邏輯：
        1. findStr = tempPath & "\" 
        2. replaceStr = netpath & "\"
        3. 找到 findStr 在 inputStr 中的位置
        4. 找到 findStr 後第一個 "\" 的位置
        5. 用 replaceStr 替換到該位置的路徑部分
        
        轉換範例：
        輸入: /mnt/d/project/python/outlook_summary/doc/20250523/Production Data/file.csv
        輸出: \\\\192.168.1.60\\temp_7days\\20250523\\Production Data\\file.csv
        
        Args:
            local_path: 本地檔案路徑
            
        Returns:
            str: 網路共享路徑
        """
        try:
            # 規範化路徑分隔符 (Unix → Windows)
            input_path = local_path.replace("/", "\\")
            temp_path_win = self.temp_path.replace("/", "\\")
            
            # VBA: findStr = tempPath & "\"
            find_str = temp_path_win + "\\"
            # VBA: replaceStr = netpath & "\"  
            replace_str = self.net_path + "\\"
            
            # VBA: startpos = InStr(1, inputStr, findStr)
            start_pos = input_path.find(find_str)
            
            if start_pos != -1:
                # VBA: endpos = InStrRev(inputStr, "\", startpos + Len(findStr))
                # 找到 findStr 之後的路徑部分
                after_temp_path = input_path[start_pos + len(find_str):]
                
                # VBA: ReplacePath = replaceStr & Mid(inputStr, endpos + 1)
                # 保留完整的子目錄結構
                network_path = replace_str + after_temp_path
                
                print(f"[LINK] 路徑轉換: {os.path.basename(local_path)}")
                print(f"   本地: {local_path}")  
                print(f"   網路: {network_path}")
                
                return network_path
            else:
                # 如果找不到本地路徑前綴，使用檔案名稱作為備用
                filename = os.path.basename(local_path)
                fallback_path = f"{self.net_path}\\{filename}"
                print(f"[WARNING]  路徑轉換備用方案: {filename}")
                return fallback_path
                
        except Exception as e:
            print(f"[ERROR] 路徑轉換失敗: {e}")
            return local_path
    
    def add_hyperlink_to_data(self, data_row: List[str], file_path: str, 
                             column_index: int = 2) -> List[str]:
        """
        在指定欄位添加超連結 - 對應 VBA AddFTToRowA 超連結處理
        遵循 CLAUDE.md 功能替換原則，完全替換指定欄位內容
        
        Args:
            data_row: 資料行列表
            file_path: 檔案路徑
            column_index: 超連結欄位索引
            
        Returns:
            List[str]: 含超連結的資料行
        """
        result_row = data_row.copy()
        
        # 確保有足夠的欄位
        while len(result_row) <= column_index:
            result_row.append("")
        
        # 轉換為網路路徑並添加 HYPERLINK 前綴
        network_path = self.convert_to_network_path(file_path)
        result_row[column_index] = f"HYPERLINK:{network_path}"
        
        return result_row
    
    def convert_csv_to_excel_hyperlinks(self, csv_file_path: str, output_excel_path: str) -> bool:
        """
        將CSV中的超連結轉換為Excel可點擊超連結 - 對應 VBA ConvertToHyperlinks 函數
        
        VBA邏輯：
        1. 開啟Excel檔案
        2. 找到C欄（第3欄）中包含路徑的儲存格
        3. 將路徑轉換為Excel超連結格式
        4. 顯示文字為檔案名稱，連結為完整路徑
        
        Args:
            csv_file_path: 輸入CSV檔案路徑
            output_excel_path: 輸出Excel檔案路徑
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            # 注意：此功能需要 openpyxl 套件，目前僅提供框架
            print(f"[BOARD] Excel超連結轉換功能:")
            print(f"   輸入CSV: {csv_file_path}")
            print(f"   輸出Excel: {output_excel_path}")
            print(f"   [WARNING]  需要安裝 openpyxl 套件來實作完整Excel功能")
            
            # 檢查是否有openpyxl
            try:
                import openpyxl
                return self._perform_excel_hyperlink_conversion(csv_file_path, output_excel_path)
            except ImportError:
                print(f"   [IDEA] 可使用: pip install openpyxl")
                return False
                
        except Exception as e:
            print(f"[ERROR] Excel超連結轉換失敗: {e}")
            return False
    
    def _perform_excel_hyperlink_conversion(self, csv_file_path: str, output_excel_path: str) -> bool:
        """
        執行實際的Excel超連結轉換
        
        Args:
            csv_file_path: 輸入CSV檔案路徑
            output_excel_path: 輸出Excel檔案路徑
            
        Returns:
            bool: 轉換是否成功
        """
        try:
            import openpyxl
            import csv
            
            # 讀取CSV資料
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            
            with open(csv_file_path, 'r', encoding='utf-8') as f:
                csv_reader = csv.reader(f)
                for row_num, row_data in enumerate(csv_reader, 1):
                    for col_num, cell_value in enumerate(row_data, 1):
                        # 寫入基本資料
                        worksheet.cell(row=row_num, column=col_num, value=cell_value)
                        
                        # 處理第3欄的超連結 (對應VBA的C欄)
                        if col_num == 3 and cell_value.startswith("HYPERLINK:"):
                            hyperlink_path = cell_value.replace("HYPERLINK:", "")
                            filename = os.path.basename(hyperlink_path)
                            
                            # 建立Excel超連結：顯示檔案名稱，連結到完整路徑
                            cell = worksheet.cell(row=row_num, column=col_num)
                            cell.hyperlink = hyperlink_path
                            cell.value = filename
                            cell.style = "Hyperlink"  # Excel超連結樣式
            
            # 儲存Excel檔案
            workbook.save(output_excel_path)
            print(f"[OK] Excel超連結轉換完成: {output_excel_path}")
            return True
            
        except Exception as e:
            print(f"[ERROR] Excel轉換過程失敗: {e}")
            return False
    
    def get_network_path(self) -> str:
        """
        獲取當前配置的網路路徑
        
        Returns:
            str: 網路共享路徑
        """
        return self.net_path
    
    def set_network_path(self, new_path: str) -> None:
        """
        設定新的網路路徑
        
        Args:
            new_path: 新的網路共享路徑
        """
        self.net_path = new_path
        
    def get_temp_path(self) -> str:
        """
        獲取當前本地路徑
        
        Returns:
            str: 本地基礎路徑
        """
        return self.temp_path