# email_database.py

郵件資料庫操作類模組，提供郵件資料的增刪查改功能，支援 MIME 編碼解析和完整的資料庫操作。

## EmailDatabase

郵件資料庫操作類，提供郵件相關的資料庫操作功能。

### 屬性
- `logger` (logging.Logger): 日誌記錄器

### __init__

初始化郵件資料庫。

**參數:**
- `database_url` (str): 資料庫連接 URL，None 表示使用預設值

**返回值:**
- None

**功能:**
- 初始化日誌記錄器
- 設定資料庫連接 URL
- 初始化資料庫引擎

### decode_mime_header

解碼 MIME 編碼的郵件頭。

**參數:**
- `header_value` (str): 要解碼的郵件頭值

**返回值:**
- str: 解碼後的字串

**功能:**
- 解析 MIME 編碼的郵件頭
- 支援多種編碼格式（UTF-8, Big5, GBK, CP950, Latin-1）
- 處理編碼失敗的情況
- 使用備用編碼策略
- 記錄解碼過程中的問題

**異常處理:**
- 捕獲 UnicodeDecodeError 和 LookupError
- 使用 replace 模式作為最後手段
- 記錄詳細的錯誤資訊

### decode_sender

解碼發件人信息。

**參數:**
- `sender_value` (str): 發件人原始值

**返回值:**
- str: 解碼後的發件人資訊

**功能:**
- 解析發件人格式：Name <<EMAIL>>
- 解碼姓名部分的 MIME 編碼
- 處理只有郵件地址的情況
- 保持原始格式結構

### get_session

取得資料庫會話的上下文管理器。

**返回值:**
- Session: SQLAlchemy 會話物件

**功能:**
- 提供資料庫會話管理
- 自動提交成功的操作
- 自動回滾失敗的操作
- 確保會話正確關閉

### save_email

儲存郵件到資料庫。

**參數:**
- `email_data` (EmailData): 郵件數據對象

**返回值:**
- Optional[int]: 儲存的郵件 ID，如果失敗則返回 None

**功能:**
- 檢查郵件是否已存在（避免重複）
- 驗證必填欄位
- 處理欄位長度限制
- 儲存郵件基本資訊
- 儲存附件資訊
- 更新寄件者統計
- 詳細的錯誤處理和日誌記錄

**欄位限制:**
- Message ID: 255 字符
- 寄件者: 255 字符
- 主題: 10KB 限制
- 內容: 1MB 限制

### get_emails

查詢郵件列表。

**參數:**
- `sender` (Optional[str]): 寄件者篩選，可選
- `limit` (int): 限制數量，預設為50
- `offset` (int): 偏移量，預設為0
- `order_by` (str): 排序欄位，預設為'received_time'
- `order_desc` (bool): 是否降序，預設為True

**返回值:**
- List[Dict[str, Any]]: 郵件列表

**功能:**
- 支援寄件者篩選
- 支援自訂排序
- 支援分頁查詢
- 自動解碼 MIME 編碼
- 包含附件資訊

### get_email_by_id

根據 ID 取得單一郵件。

**參數:**
- `email_id` (int): 郵件 ID

**返回值:**
- Optional[Dict[str, Any]]: 郵件詳細資訊

**功能:**
- 取得完整的郵件資訊
- 包含附件列表
- 包含處理狀態
- 自動解碼編碼內容

### get_senders

取得所有寄件者列表。

**返回值:**
- List[Dict[str, Any]]: 寄件者列表

**功能:**
- 按郵件數量排序
- 包含統計資訊
- 包含時間範圍資訊

### get_email_count

取得郵件總數。

**參數:**
- `sender` (Optional[str]): 寄件者篩選，可選

**返回值:**
- int: 郵件數量

### mark_email_as_read

標記郵件為已讀。

**參數:**
- `email_id` (int): 郵件 ID

**返回值:**
- bool: 操作成功與否

### delete_email

刪[EXCEPT_CHAR]郵件。

**參數:**
- `email_id` (int): 郵件 ID

**返回值:**
- bool: 操作成功與否

**功能:**
- 更新寄件者統計
- 自動刪[EXCEPT_CHAR]關聯的附件和處理狀態
- 記錄刪[EXCEPT_CHAR]操作

### search_emails

搜尋郵件。

**參數:**
- `search_term` (str): 搜尋關鍵字
- `search_fields` (List[str]): 搜尋欄位列表，可選
- `sender` (Optional[str]): 寄件者篩選，可選
- `limit` (int): 限制數量，預設為50

**返回值:**
- List[Dict[str, Any]]: 搜尋結果

**功能:**
- 支援多欄位搜尋（主題、內容、寄件者）
- 支援寄件者篩選
- 按時間排序結果
- 截斷長內容以提高效能

### get_statistics

取得資料庫統計資訊。

**返回值:**
- Dict[str, Any]: 統計資訊

**統計項目:**
- 總郵件數
- 總寄件者數
- 未讀郵件數
- 已處理郵件數
- 有附件的郵件數
- 最新郵件時間

## 私有方法

### _get_email_attachments

取得郵件附件列表的私有方法。

**參數:**
- `session` (Session): 資料庫會話
- `email_id` (int): 郵件 ID

**返回值:**
- List[Dict[str, Any]]: 附件資訊列表

### _get_email_process_status

取得郵件處理狀態的私有方法。

**參數:**
- `session` (Session): 資料庫會話
- `email_id` (int): 郵件 ID

**返回值:**
- List[Dict[str, Any]]: 處理狀態列表

### _update_sender_stats

更新寄件者統計的私有方法。

**參數:**
- `session` (Session): 資料庫會話
- `sender_email` (str): 寄件者郵件地址
- `email_time` (datetime): 郵件時間

**功能:**
- 更新或建立寄件者記錄
- 維護郵件數量統計
- 追蹤首次和最後郵件時間

### _decrease_sender_stats

減少寄件者統計的私有方法。

**參數:**
- `session` (Session): 資料庫會話
- `sender_email` (str): 寄件者郵件地址

**功能:**
- 減少郵件數量統計
- 清理無郵件的寄件者記錄

## 設計特點

### 編碼處理
- **多編碼支援**: 支援 UTF-8, Big5, GBK, CP950, Latin-1
- **備用策略**: 編碼失敗時使用備用編碼
- **錯誤恢復**: 使用 replace 模式處理無法解碼的內容

### 資料完整性
- **重複檢查**: 避免儲存重複郵件
- **欄位驗證**: 檢查必填欄位
- **長度限制**: 防止資料庫欄位溢出
- **關聯維護**: 自動維護寄件者統計

### 效能優化
- **索引使用**: 充分利用資料庫索引
- **分頁查詢**: 支援大量資料的分頁處理
- **批次操作**: 減少資料庫往返次數
- **連接管理**: 使用上下文管理器確保連接正確關閉
