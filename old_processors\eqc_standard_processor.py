#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EQC 一鍵完成處理器 - 完整整合版
執行：自動生成 EQCTOTALDATA → 程式碼區間檢測 → 雙重搜尋 → InsEqcRtData2 資料重組 → 完整報告
"""

import os
import sys
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Tuple

# 載入環境變數（如果有dotenv）
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    pass

# 添加路徑
sys.path.append('src')

from src.infrastructure.adapters.excel.eqc.eqc_simple_detector import EQCSimpleDetector
from src.infrastructure.adapters.excel.eqc.eqc_dual_search_corrected import EQCDualSearchCorrected
from src.infrastructure.adapters.excel.eqc.eqc_inseqcrtdata2_processor import EQCInsEqcRtData2Processor
from src.infrastructure.adapters.excel.eqc.eqc_step5_testflow_processor import EQCStep5TestFlowProcessor
from src.infrastructure.adapters.excel.eqc.eqc_step6_excel_processor import EQCStep6ExcelProcessor
from src.infrastructure.adapters.excel.csv_to_excel_converter import CsvToExcelConverter
from src.infrastructure.adapters.filesystem.chinese_path_processor import process_chinese_paths_in_directory
from src.infrastructure.adapters.excel.eqc.utils.timestamp_extractor import TimestampExtractor

class StandardEQCProcessor:
    """EQC 一鍵完成處理器 - 完整整合 InsEqcRtData2"""
    
    def __init__(self):
        self.simple_detector = EQCSimpleDetector()
        self.dual_search = EQCDualSearchCorrected()
        self.inseqcrtdata2_processor = EQCInsEqcRtData2Processor()
        self.step5_processor = EQCStep5TestFlowProcessor()
        self.step6_processor = EQCStep6ExcelProcessor()
        self.csv_to_excel_converter = CsvToExcelConverter()
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """設置日誌記錄器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def process_standard_eqc_pipeline(self, doc_directory: str, include_inseqcrtdata2: bool = True, include_step5_testflow: bool = True, include_step6_excel: bool = True, include_final_excel_conversion: bool = True, code_regions: dict = None):
        """
        執行完整 EQC 一鍵處理流程
        0. 中文路徑處理（資料夾名稱標準化）
        1. 自動生成 EQCTOTALDATA
        2. 程式碼區間檢測  
        3. 雙重搜尋
        4. InsEqcRtData2 資料重組（可選）
        5. Step 5 測試流程生成（可選）
        6. Step 6 Excel 生成與黃色標記（可選）
        7. 最終 CSV 到 Excel 轉換（包含 Summary）（可選）
        8. 完整報告
        """
        try:
            mode_text = "完整 EQC 一鍵處理流程" if include_inseqcrtdata2 else "標準 EQC 處理流程（不包含 InsEqcRtData2）"
            self.logger.info(f"[ROCKET] 執行{mode_text}")
            self.logger.info(f"   資料目錄: {doc_directory}")
            self.logger.info(f"   InsEqcRtData2: {'[OK] 啟用' if include_inseqcrtdata2 else '[ERROR] 停用'}")
            self.logger.info(f"   Step 5 測試流程: {'[OK] 啟用' if include_step5_testflow else '[ERROR] 停用'}")
            self.logger.info(f"   Step 6 Excel 生成: {'[OK] 啟用' if include_step6_excel else '[ERROR] 停用'}")
            self.logger.info(f"   最終 Excel 轉換: {'[OK] 啟用' if include_final_excel_conversion else '[ERROR] 停用'}")
            self.logger.info("")
            
            # 步驟0: 中文路徑處理（資料夾名稱標準化）
            self.logger.info("[TOOL] 步驟0: 處理中文路徑與特殊符號")
            chinese_path_success = process_chinese_paths_in_directory(doc_directory, verbose=False)
            if chinese_path_success:
                self.logger.info("   [OK] 路徑標準化完成")
            else:
                self.logger.warning("   [WARNING] 路徑標準化失敗，但繼續執行")
            
            # 步驟1: 自動生成 EQCTOTALDATA.csv
            self.logger.info("[CHART] 步驟1: 自動生成 EQCTOTALDATA.csv")
            eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            self._generate_eqctotaldata(doc_directory, eqctotaldata_path)
            
            # 步驟2: 程式碼區間檢測
            self.logger.info("[SEARCH] 步驟2: 執行程式碼區間檢測")
            region_result = self.simple_detector.find_code_region(doc_directory, code_regions)
            
            if region_result['status'] != 'success':
                return {
                    'status': 'error',
                    'error_message': f"區間檢測失敗: {region_result.get('message')}",
                    'stage': 'region_detection'
                }
            
            self.logger.info(f"   [OK] 主要區間: 第{region_result['code_region']['start_column_number']}-{region_result['code_region']['end_column_number']}欄")
            if region_result['backup_region']['found']:
                self.logger.info(f"   [OK] 備用區間: 第{region_result['backup_region']['backup_start_column']}-{region_result['backup_region']['backup_end_column']}欄")
            
            # 步驟3: 雙重搜尋
            self.logger.info("[REFRESH] 步驟3: 執行雙重搜尋機制")
            dual_search_result = self._execute_dual_search_only(doc_directory, region_result, code_regions)
            
            if dual_search_result['status'] != 'success':
                return {
                    'status': 'error', 
                    'error_message': f"雙重搜尋失敗: {dual_search_result.get('error_message')}",
                    'stage': 'dual_search'
                }
            
            # 步驟4: InsEqcRtData2 資料重組（可選）
            inseqcrtdata2_result = None
            if include_inseqcrtdata2:
                self.logger.info("[REFRESH] 步驟4: 執行 InsEqcRtData2 Step 1-2-3-4 處理")
                inseqcrtdata2_result = self._execute_inseqcrtdata2_step1234_processing(doc_directory, region_result)
                
                if inseqcrtdata2_result.get('status') != 'success':
                    self.logger.warning(f"   [WARNING] InsEqcRtData2 處理失敗: {inseqcrtdata2_result.get('error_message')}")
                    # 不中斷流程，繼續生成報告
                else:
                    self.logger.info(f"   [OK] InsEqcRtData2 Step 1-2-3-4 處理成功")
                    if inseqcrtdata2_result.get('step12_moved_count', 0) > 0:
                        self.logger.info(f"   ALL0 移動: {inseqcrtdata2_result['step12_moved_count']} 行")
                    if inseqcrtdata2_result.get('step3_fail_count', 0) > 0:
                        self.logger.info(f"   FAIL 檢測: {inseqcrtdata2_result['step3_fail_count']} 行")
                    if inseqcrtdata2_result.get('step4_matches', 0) > 0:
                        self.logger.info(f"   CODE 匹配: {inseqcrtdata2_result['step4_matches']} 個")
                    if inseqcrtdata2_result.get('step4_debug_log'):
                        debug_log_name = os.path.basename(inseqcrtdata2_result['step4_debug_log'])
                        self.logger.info(f"   Step4 DEBUG LOG: {debug_log_name}")
            else:
                self.logger.info("[FAST] 步驟4: 跳過 InsEqcRtData2 資料重組")
            
            # 步驟5: Step 5 測試流程生成（可選）
            step5_result = None
            if include_step5_testflow and inseqcrtdata2_result and inseqcrtdata2_result.get('step4_debug_log'):
                self.logger.info("[TEST] 步驟5: 執行 Step 5 測試流程生成")
                step5_result = self._execute_step5_testflow_generation(doc_directory, inseqcrtdata2_result)
                
                if step5_result.get('status') != 'success':
                    self.logger.warning(f"   [WARNING] Step 5 測試流程生成失敗: {step5_result.get('error_message')}")
                else:
                    self.logger.info(f"   [OK] Step 5 測試流程生成成功")
                    self.logger.info(f"   輸出檔案: {os.path.basename(step5_result['output_file'])}")
                    self.logger.info(f"   重新排列行數: {step5_result['reordered_rows']}")
                    if step5_result.get('final_online_eqc_rt_rows'):
                        self.logger.info(f"   final_online_eqc_rt_rows: {step5_result['final_online_eqc_rt_rows']}")
            else:
                reason = "無 Step 4 DEBUG LOG" if not (inseqcrtdata2_result and inseqcrtdata2_result.get('step4_debug_log')) else "功能已停用"
                self.logger.info(f"[FAST] 步驟5: 跳過 Step 5 測試流程生成（{reason}）")
            
            # 步驟6: Step 6 Excel 生成與黃色標記（可選）
            step6_result = None
            if include_step6_excel and step5_result and step5_result.get('status') == 'success' and step5_result.get('final_online_eqc_rt_rows'):
                self.logger.info("[CHART] 步驟6: 執行 Step 6 Excel 生成與黃色標記")
                step6_result = self._execute_step6_excel_generation(doc_directory, step5_result, region_result)
                
                if step6_result.get('status') != 'success':
                    self.logger.warning(f"   [WARNING] Step 6 Excel 生成失敗: {step6_result.get('error_message')}")
                else:
                    self.logger.info(f"   [OK] Step 6 Excel 生成成功")
                    self.logger.info(f"   Excel 檔案: {os.path.basename(step6_result['excel_filename'])}")
                    self.logger.info(f"   標記行數: {step6_result['highlighted_rows_count']} 行")
                    self.logger.info(f"   標記範圍: {step6_result['highlight_column_range']}")
            else:
                reason_parts = []
                if not include_step6_excel:
                    reason_parts.append("功能已停用")
                if not (step5_result and step5_result.get('status') == 'success'):
                    reason_parts.append("Step 5 未成功")
                if not (step5_result and step5_result.get('final_online_eqc_rt_rows')):
                    reason_parts.append("無 final_online_eqc_rt_rows")
                
                reason = " | ".join(reason_parts) if reason_parts else "未知原因"
                self.logger.info(f"[FAST] 步驟6: 跳過 Step 6 Excel 生成（{reason}）")
            
            # 步驟7: 自動重新命名 Step 6 Excel 檔案為 EQCTOTALDATA.xlsx（如果存在）
            rename_result = None
            if step6_result and step6_result.get('status') == 'success' and step6_result.get('excel_file_path'):
                self.logger.info("[REFRESH] 步驟7: 自動重新命名 Step 6 Excel 檔案")
                rename_result = self._rename_step6_to_final(doc_directory, step6_result['excel_file_path'])
                
                if rename_result.get('status') == 'success':
                    self.logger.info(f"   [OK] 檔案重新命名成功: {rename_result['original_filename']} → EQCTOTALDATA.xlsx")
                    self.logger.info(f"   檔案大小: {rename_result['file_size']:,} bytes")
                else:
                    self.logger.warning(f"   [WARNING] 檔案重新命名失敗: {rename_result.get('error_message')}")
            else:
                self.logger.info("[FAST] 步驟7: 跳過檔案重新命名（Step 6 未生成 Excel 檔案）")

            # 步驟8: 最終 CSV 到 Excel 轉換（只在 Step6 失敗時執行保底方案）
            final_excel_result = None
            if include_final_excel_conversion:
                # 檢查 Step6 是否成功且已生成 EQCTOTALDATA.xlsx
                final_excel_path = os.path.join(doc_directory, "EQCTOTALDATA.xlsx")
                step6_success = (step6_result and step6_result.get('status') == 'success' and 
                                rename_result and rename_result.get('status') == 'success' and 
                                os.path.exists(final_excel_path))
                
                if not step6_success:
                    self.logger.info("[CHART] 步驟8: 執行最終 CSV 到 Excel 轉換（Step6 未完成，使用保底方案）")
                    final_excel_result = self._execute_final_excel_conversion(doc_directory)
                    
                    if final_excel_result.get('status') != 'success':
                        self.logger.warning(f"   [WARNING] 最終 Excel 轉換失敗: {final_excel_result.get('error_message')}")
                    else:
                        self.logger.info(f"   [OK] 最終 Excel 轉換成功（保底方案）")
                        self.logger.info(f"   Excel 檔案: {os.path.basename(final_excel_result['output_file'])}") 
                        self.logger.info(f"   處理時間: {final_excel_result['processing_time']:.3f} 秒")
                        self.logger.info(f"   總行數: {final_excel_result['total_rows']} 行")
                        self.logger.info(f"   總欄數: {final_excel_result['total_columns']} 欄")
                else:
                    self.logger.info("[FAST] 步驟8: 跳過最終轉換（Step6 已成功生成帶標記的 EQCTOTALDATA.xlsx）")
                    final_excel_result = {'status': 'skipped', 'message': 'Step6 已完成'}
            else:
                self.logger.info("[FAST] 步驟8: 跳過最終 CSV 到 Excel 轉換（功能已停用）")
            
            # 步驟9: 生成完整報告
            # 動態計算步驟編號
            if include_inseqcrtdata2 and include_step5_testflow and include_step6_excel and include_final_excel_conversion:
                step_num = "9"
            elif include_inseqcrtdata2 and include_step5_testflow and include_step6_excel:
                step_num = "8"
            elif include_inseqcrtdata2 and include_step5_testflow:
                step_num = "7"
            elif include_inseqcrtdata2:
                step_num = "6"
            else:
                step_num = "5"
                
            self.logger.info(f"[NOTES] 步驟{step_num}: 生成完整處理報告")
            
            # 根據環境變數控制是否生成詳細報告
            eqc_detailed_logs = os.getenv("EQC_DETAILED_LOGS", "true").lower() == "true"
            if eqc_detailed_logs:
                report_result = self._generate_standard_report({
                    'doc_directory': doc_directory,
                    'region_result': region_result,
                    'dual_search_result': dual_search_result,
                    'inseqcrtdata2_result': inseqcrtdata2_result,
                    'step5_result': step5_result,
                    'step6_result': step6_result,
                    'final_excel_result': final_excel_result,
                'eqctotaldata_path': eqctotaldata_path,
                'include_inseqcrtdata2': include_inseqcrtdata2,
                'include_step5_testflow': include_step5_testflow,
                'include_step6_excel': include_step6_excel,
                'include_final_excel_conversion': include_final_excel_conversion
                })
            else:
                self.logger.info("   [INFO] 詳細報告生成已停用 (EQC_DETAILED_LOGS=false)")
                report_result = {'status': 'skipped', 'message': '詳細報告生成已停用'}
            
            self.logger.info("")
            self.logger.info("[PARTY] 標準 EQC 處理流程完成")
            self.logger.info(f"   生成檔案: {eqctotaldata_path}")
            if eqc_detailed_logs and report_result.get('report_path'):
                self.logger.info(f"   處理報告: {report_result['report_path']}")
            
            return {
                'status': 'success',
                'eqctotaldata_path': eqctotaldata_path,
                'region_result': region_result,
                'dual_search_result': dual_search_result,
                'report_result': report_result,
                'processing_stages': [
                    {'name': '中文路徑處理', 'status': 'success' if chinese_path_success else 'warning'},
                    {'name': 'EQCTOTALDATA生成', 'status': 'success'},
                    {'name': '程式碼區間檢測', 'status': 'success'}, 
                    {'name': '雙重搜尋', 'status': 'success'},
                    {'name': 'InsEqcRtData2處理', 'status': 'success' if inseqcrtdata2_result and inseqcrtdata2_result.get('status') == 'success' else ('failed' if inseqcrtdata2_result else 'skipped')},
                    {'name': 'Step5測試流程生成', 'status': 'success' if step5_result and step5_result.get('status') == 'success' else ('failed' if step5_result else 'skipped')},
                    {'name': 'Step6Excel生成標記', 'status': 'success' if step6_result and step6_result.get('status') == 'success' else ('failed' if step6_result else 'skipped')},
                    {'name': '最終Excel轉換Summary', 'status': 'success' if final_excel_result and final_excel_result.get('status') == 'success' else ('failed' if final_excel_result else 'skipped')},
                    {'name': 'Step6檔案重新命名', 'status': 'success' if rename_result and rename_result.get('status') == 'success' else ('failed' if rename_result else 'skipped')},
                    {'name': '報告生成', 'status': 'success'}
                ]
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 標準 EQC 處理失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def process_from_stage2_only(self, doc_directory: str, include_inseqcrtdata2: bool = True, include_step5_testflow: bool = True, include_step6_excel: bool = True, include_final_excel_conversion: bool = True, code_regions: dict = None):
        """
        執行第二階段EQC處理流程（跳過步驟1的EQCTOTALDATA.csv生成）
        用於兩階段處理流程中的第二階段
        
        Args:
            doc_directory: 處理目錄
            include_inseqcrtdata2: 是否包含InsEqcRtData2處理
            include_step5_testflow: 是否包含Step5測試流程
            include_step6_excel: 是否包含Step6 Excel生成
            include_final_excel_conversion: 是否包含最終Excel轉換
            code_regions: 程式碼區間設定
            
        Returns:
            Dict: 處理結果
        """
        print("[SEARCH] [eqc_standard_processor.py] process_from_stage2_only() - 開始執行")
        try:
            self.logger.info("[ROCKET] 執行第二階段EQC處理流程（跳過EQCTOTALDATA.csv生成）")
            self.logger.info(f"   資料目錄: {doc_directory}")
            self.logger.info(f"   InsEqcRtData2: {'[OK] 啟用' if include_inseqcrtdata2 else '[ERROR] 停用'}")
            self.logger.info(f"   Step 5 測試流程: {'[OK] 啟用' if include_step5_testflow else '[ERROR] 停用'}")
            self.logger.info(f"   Step 6 Excel 生成: {'[OK] 啟用' if include_step6_excel else '[ERROR] 停用'}")
            self.logger.info(f"   最終 Excel 轉換: {'[OK] 啟用' if include_final_excel_conversion else '[ERROR] 停用'}")
            self.logger.info("")
            
            # 步驟0: 中文路徑處理（第二階段也執行，確保一致性）
            self.logger.info("[TOOL] 步驟0: 處理中文路徑與特殊符號")
            chinese_path_success = process_chinese_paths_in_directory(doc_directory, verbose=False)
            if chinese_path_success:
                self.logger.info("   [OK] 路徑標準化完成")
            else:
                self.logger.warning("   [WARNING] 路徑標準化失敗，但繼續執行")
            
            # 檢查EQCTOTALDATA.csv是否存在（第一階段應該已經生成）
            eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            if not os.path.exists(eqctotaldata_path):
                return {
                    'status': 'error',
                    'error_message': 'EQCTOTALDATA.csv不存在，請先執行第一階段處理',
                    'stage': 'stage2_prerequisite_check'
                }
            
            self.logger.info("[OK] EQCTOTALDATA.csv存在，繼續第二階段處理")
            
            # 步驟2: 程式碼區間檢測
            self.logger.info("[SEARCH] 步驟2: 執行程式碼區間檢測")
            
            # 🆕 新增：詳細連續整數區間掃描和日誌記錄
            try:
                print("[SEARCH] [eqc_standard_processor.py] 第二次調用 CodeRegionDetailedDetector")
                from src.infrastructure.adapters.excel.eqc.code_region_detailed_detector import CodeRegionDetailedDetector
                detailed_detector = CodeRegionDetailedDetector(doc_directory)
                detailed_detector.detect_and_log_all_regions()
            except Exception as e:
                self.logger.warning(f"   [WARNING] 詳細區間檢測器載入失敗: {e}")
            
            region_result = self.simple_detector.find_code_region(doc_directory, code_regions)
            
            if region_result['status'] != 'success':
                return {
                    'status': 'error',
                    'error_message': f"區間檢測失敗: {region_result.get('message')}",
                    'stage': 'region_detection'
                }
            
            self.logger.info(f"   [OK] 主要區間: 第{region_result['code_region']['start_column_number']}-{region_result['code_region']['end_column_number']}欄")
            if region_result['backup_region']['found']:
                self.logger.info(f"   [OK] 備用區間: 第{region_result['backup_region']['backup_start_column']}-{region_result['backup_region']['backup_end_column']}欄")
            
            # 步驟3: 雙重搜尋
            self.logger.info("[REFRESH] 步驟3: 執行雙重搜尋機制")
            dual_search_result = self._execute_dual_search_only(doc_directory, region_result, code_regions)
            
            if dual_search_result['status'] != 'success':
                return {
                    'status': 'error', 
                    'error_message': f"雙重搜尋失敗: {dual_search_result.get('error_message')}",
                    'stage': 'dual_search'
                }
            
            # 步驟4: InsEqcRtData2 資料重組（可選）
            inseqcrtdata2_result = None
            if include_inseqcrtdata2:
                self.logger.info("[REFRESH] 步驟4: 執行 InsEqcRtData2 Step 1-2-3-4 處理")
                inseqcrtdata2_result = self._execute_inseqcrtdata2_step1234_processing(doc_directory, region_result)
                
                if inseqcrtdata2_result.get('status') != 'success':
                    self.logger.warning(f"   [WARNING] InsEqcRtData2 處理失敗: {inseqcrtdata2_result.get('error_message')}")
                else:
                    self.logger.info(f"   [OK] InsEqcRtData2 Step 1-2-3-4 處理成功")
            else:
                self.logger.info("[FAST] 步驟4: 跳過 InsEqcRtData2 資料重組")
            
            # 步驟5: Step 5 測試流程生成（可選）
            step5_result = None
            if include_step5_testflow and inseqcrtdata2_result and inseqcrtdata2_result.get('step4_debug_log'):
                self.logger.info("[TEST] 步驟5: 執行 Step 5 測試流程生成")
                step5_result = self._execute_step5_testflow_generation(doc_directory, inseqcrtdata2_result)
                
                if step5_result.get('status') != 'success':
                    self.logger.warning(f"   [WARNING] Step 5 測試流程生成失敗: {step5_result.get('error_message')}")
                else:
                    self.logger.info(f"   [OK] Step 5 測試流程生成成功")
            else:
                self.logger.info("[FAST] 步驟5: 跳過 Step 5 測試流程生成")
            
            # 步驟6: Step 6 Excel 生成與黃色標記（可選）
            step6_result = None
            if include_step6_excel and step5_result and step5_result.get('status') == 'success':
                self.logger.info("[CHART] 步驟6: 執行 Step 6 Excel 生成與黃色標記")
                step6_result = self._execute_step6_excel_generation(doc_directory, step5_result, region_result)
                
                if step6_result.get('status') != 'success':
                    self.logger.warning(f"   [WARNING] Step 6 Excel 生成失敗: {step6_result.get('error_message')}")
                else:
                    self.logger.info(f"   [OK] Step 6 Excel 生成成功")
            else:
                self.logger.info("[FAST] 步驟6: 跳過 Step 6 Excel 生成")
            
            # 步驟7: 最終 CSV 到 Excel 轉換（可選）
            final_excel_result = None
            if include_final_excel_conversion:
                self.logger.info("[UP] 步驟7: 執行最終 CSV 到 Excel 轉換")
                final_excel_result = self._execute_final_excel_conversion(doc_directory)
                
                if final_excel_result.get('status') != 'success':
                    self.logger.warning(f"   [WARNING] 最終 Excel 轉換失敗: {final_excel_result.get('error_message')}")
                else:
                    self.logger.info(f"   [OK] 最終 Excel 轉換成功")
            else:
                self.logger.info("[FAST] 步驟7: 跳過最終 Excel 轉換")
            
            # 生成最終報告
            self.logger.info("[EDIT] 生成最終處理報告...")
            report = self._generate_final_report(
                doc_directory, region_result, dual_search_result, 
                inseqcrtdata2_result, step5_result, step6_result, final_excel_result
            )
            
            self.logger.info("[OK] 第二階段EQC處理流程完成")
            
            return {
                'status': 'success',
                'message': '第二階段EQC處理完成',
                'doc_directory': doc_directory,
                'region_result': region_result,
                'dual_search_result': dual_search_result,
                'inseqcrtdata2_result': inseqcrtdata2_result,
                'step5_result': step5_result,
                'step6_result': step6_result,
                'final_excel_result': final_excel_result,
                'report': report,
                'processing_mode': 'stage2_only',
                'processing_stages': [
                    {'name': '中文路徑處理', 'status': 'success' if chinese_path_success else 'warning'},
                    {'name': 'EQCTOTALDATA生成', 'status': 'skipped_stage1'},
                    {'name': '程式碼區間檢測', 'status': 'success'}, 
                    {'name': '雙重搜尋', 'status': 'success'},
                    {'name': 'InsEqcRtData2處理', 'status': 'success' if inseqcrtdata2_result and inseqcrtdata2_result.get('status') == 'success' else ('failed' if inseqcrtdata2_result else 'skipped')},
                    {'name': 'Step5測試流程生成', 'status': 'success' if step5_result and step5_result.get('status') == 'success' else ('failed' if step5_result else 'skipped')},
                    {'name': 'Step6Excel生成標記', 'status': 'success' if step6_result and step6_result.get('status') == 'success' else ('failed' if step6_result else 'skipped')},
                    {'name': '最終Excel轉換Summary', 'status': 'success' if final_excel_result and final_excel_result.get('status') == 'success' else ('failed' if final_excel_result else 'skipped')},
                    {'name': '報告生成', 'status': 'success'}
                ]
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 第二階段EQC處理失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e),
                'stage': 'stage2_processing'
            }

    def process_code_comparison_pipeline(self, doc_directory: str, code_regions: dict = None):
        """
        執行一鍵完成程式碼對比流程（簡化版4步驟）
        0. 中文路徑處理（資料夾名稱標準化）
        1. 自動生成 EQCTOTALDATA
        2. 程式碼區間檢測  
        3. 雙重搜尋
        4. 完整報告
        """
        try:
            self.logger.info(f"[ROCKET] 執行一鍵完成程式碼對比流程")
            self.logger.info(f"   資料目錄: {doc_directory}")
            self.logger.info("")
            
            # 步驟0: 中文路徑處理（資料夾名稱標準化）
            self.logger.info("[TOOL] 步驟0: 處理中文路徑與特殊符號")
            chinese_path_success = process_chinese_paths_in_directory(doc_directory, verbose=False)
            if chinese_path_success:
                self.logger.info("   [OK] 路徑標準化完成")
            else:
                self.logger.warning("   [WARNING] 路徑標準化失敗，但繼續執行")
            
            # 步驟1: 自動生成 EQCTOTALDATA.csv
            self.logger.info("[CHART] 步驟1: 自動生成 EQCTOTALDATA.csv")
            eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            self._generate_eqctotaldata(doc_directory, eqctotaldata_path)
            
            # 步驟2: 程式碼區間檢測
            self.logger.info("[SEARCH] 步驟2: 執行程式碼區間檢測")
            region_result = self.simple_detector.find_code_region(doc_directory, code_regions)
            
            if region_result['status'] != 'success':
                return {
                    'status': 'error',
                    'error_message': f"區間檢測失敗: {region_result.get('message')}",
                    'stage': 'region_detection'
                }
            
            self.logger.info(f"   [OK] 主要區間: 第{region_result['code_region']['start_column_number']}-{region_result['code_region']['end_column_number']}欄")
            if region_result['backup_region']['found']:
                self.logger.info(f"   [OK] 備用區間: 第{region_result['backup_region']['backup_start_column']}-{region_result['backup_region']['backup_end_column']}欄")
            
            # 步驟3: 雙重搜尋
            self.logger.info("[REFRESH] 步驟3: 執行雙重搜尋機制")
            dual_search_result = self._execute_dual_search_only(doc_directory, region_result, code_regions)
            
            if dual_search_result['status'] != 'success':
                return {
                    'status': 'error', 
                    'error_message': f"雙重搜尋失敗: {dual_search_result.get('error_message')}",
                    'stage': 'dual_search'
                }
            
            # 步驟4: 生成完整報告
            self.logger.info("[NOTES] 步驟4: 生成程式碼對比完整報告")
            
            # 根據環境變數控制是否生成詳細報告
            eqc_detailed_logs = os.getenv("EQC_DETAILED_LOGS", "true").lower() == "true"
            if eqc_detailed_logs:
                report_result = self._generate_standard_report({
                    'doc_directory': doc_directory,
                    'region_result': region_result,
                    'dual_search_result': dual_search_result,
                    'eqctotaldata_path': eqctotaldata_path,
                    'include_inseqcrtdata2': False,
                    'include_step5_testflow': False,
                    'include_step6_excel': False,
                    'include_final_excel_conversion': False
                })
            else:
                self.logger.info("   [INFO] 詳細報告生成已停用 (EQC_DETAILED_LOGS=false)")
                report_result = {'status': 'skipped', 'message': '詳細報告生成已停用'}
            
            self.logger.info("")
            self.logger.info("[PARTY] 程式碼對比流程完成")
            self.logger.info(f"   生成檔案: {eqctotaldata_path}")
            if eqc_detailed_logs and report_result.get('report_path'):
                self.logger.info(f"   對比報告: {report_result['report_path']}")
            
            return {
                'status': 'success',
                'eqctotaldata_path': eqctotaldata_path,
                'region_result': region_result,
                'dual_search_result': dual_search_result,
                'report_result': report_result,
                'processing_stages': [
                    {'name': '中文路徑處理', 'status': 'success' if chinese_path_success else 'warning'},
                    {'name': 'EQCTOTALDATA生成', 'status': 'success'},
                    {'name': '程式碼區間檢測', 'status': 'success'}, 
                    {'name': '雙重搜尋', 'status': 'success'},
                    {'name': '程式碼對比報告', 'status': 'success'}
                ]
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 程式碼對比流程失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }


    def view_eqctotaldata_structure(self, doc_directory: str):
        """查看 EQCTOTALDATA.csv 的結構"""
        try:
            eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            
            if not os.path.exists(eqctotaldata_path):
                self.logger.warning("[WARNING] EQCTOTALDATA.csv 不存在")
                return
            
            with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                rows = f.readlines()
            
            self.logger.info(f"[FOLDER] EQCTOTALDATA.csv 結構分析 (總行數: {len(rows)})行)")
            self.logger.info("=" * 60)
            
            # 顯示前15行的結構
            for i in range(min(15, len(rows))):
                row = rows[i].strip()
                if row:
                    # 只顯示前兩個欄位
                    elements = row.split(',')
                    if len(elements) >= 2:
                        first_col = elements[0][:20] + "..." if len(elements[0]) > 20 else elements[0]
                        second_col = elements[1][:10] + "..." if len(elements[1]) > 10 else elements[1]
                        self.logger.info(f"第{i+1:2d}行: {first_col:25} | {second_col:15}")
                    else:
                        self.logger.info(f"第{i+1:2d}行: {row[:50]}...")
            
            self.logger.info("=" * 60)
            self.logger.info("[INFO] 第13行往後是數據區，第14行開始是 Online EQC 區間")
            
        except Exception as e:
            self.logger.error(f"[ERROR] 查看 EQCTOTALDATA.csv 結構失敗: {e}")
    
    def _generate_eqctotaldata(self, doc_directory: str, output_path: str):
        """生成標準的 EQCTOTALDATA.csv - 使用智能檔案選擇邏輯"""
        # 搜尋可用的 CSV 檔案
        csv_files = []
        for file in os.listdir(doc_directory):
            if file.endswith('.csv') and 'EQCTOTALDATA' not in file:
                csv_files.append(os.path.join(doc_directory, file))
        
        if csv_files:
            # 使用智能檔案選擇機制
            source_file = self._select_optimal_csv_file(csv_files)
            self.logger.info(f"   [NOTES] 智能選擇來源檔案: {os.path.basename(source_file)}")
            
            # 直接複製檔案作為 EQCTOTALDATA.csv
            import shutil
            shutil.copy2(source_file, output_path)
            
            # 計算行數
            with open(output_path, 'r', encoding='utf-8') as f:
                total_rows = len(f.readlines())
            
            self.logger.info(f"   [OK] EQCTOTALDATA.csv 已生成，總行數: {total_rows}")
        else:
            self.logger.warning("   [WARNING] 未找到可用的 CSV 檔案，將使用現有的 EQCTOTALDATA.csv")
    
    def _select_optimal_csv_file(self, csv_files: List[str]) -> str:
        """
        智能選擇最佳的 CSV 檔案作為 EQCTOTALDATA 來源
        
        選擇邏輯優先級：
        1. EQC 檔案優先於 FT 檔案
        2. 相同類型中選擇最新的檔案（按時間戳）
        3. 備用邏輯：檔案名稱字母順序
        
        Args:
            csv_files: CSV 檔案路徑列表
            
        Returns:
            str: 選中的檔案路徑
        """
        if len(csv_files) == 1:
            return csv_files[0]
        
        # 分類檔案
        eqc_files = []
        ft_files = []
        other_files = []
        
        for file_path in csv_files:
            filename = os.path.basename(file_path).lower()
            if '_eqc_' in filename or 'eqc' in filename:
                eqc_files.append(file_path)
            elif '_ft_' in filename or 'ft' in filename:
                ft_files.append(file_path)
            else:
                other_files.append(file_path)
        
        self.logger.info(f"   [CHART] 檔案分類: EQC={len(eqc_files)}, FT={len(ft_files)}, 其他={len(other_files)}")
        
        # 選擇邏輯：優先選擇 EQC 檔案
        if eqc_files:
            selected_file = self._select_latest_file_by_timestamp(eqc_files)
            self.logger.info(f"   [TARGET] 選擇策略: EQC 檔案優先")
            return selected_file
        elif ft_files:
            selected_file = self._select_latest_file_by_timestamp(ft_files)
            self.logger.info(f"   [TARGET] 選擇策略: FT 檔案（無 EQC 可用）")
            return selected_file
        else:
            selected_file = self._select_latest_file_by_timestamp(other_files)
            self.logger.info(f"   [TARGET] 選擇策略: 其他檔案（按時間戳）")
            return selected_file
    
    def _select_latest_file_by_timestamp(self, files: List[str]) -> str:
        """
        根據時間戳選擇最新的檔案
        
        Args:
            files: 檔案路徑列表
            
        Returns:
            str: 最新的檔案路徑
        """
        if len(files) == 1:
            return files[0]
        
        # 提取時間戳並排序
        file_timestamps = []
        for file_path in files:
            timestamp = TimestampExtractor.extract_internal_timestamp(file_path)
            if timestamp is None:
                # 備用：使用檔案修改時間
                timestamp = int(os.path.getmtime(file_path))
            file_timestamps.append((timestamp, file_path))
            
            # 記錄時間戳信息
            filename = os.path.basename(file_path)
            time_str = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
            self.logger.info(f"   [CLOCK] {filename}: {time_str} (timestamp: {timestamp})")
        
        # 按時間戳降序排序，選擇最新的
        file_timestamps.sort(key=lambda x: x[0], reverse=True)
        latest_file = file_timestamps[0][1]
        
        latest_filename = os.path.basename(latest_file)
        latest_time_str = datetime.fromtimestamp(file_timestamps[0][0]).strftime('%Y-%m-%d %H:%M:%S')
        self.logger.info(f"   🕒 最新檔案: {latest_filename} ({latest_time_str})")
        
        return latest_file
    
    def _execute_dual_search_only(self, doc_directory: str, region_result: dict, code_regions: dict = None):
        """只執行雙重搜尋，不包含 InsEqcRtData2"""
        try:
            # 讀取 EQCTOTALDATA.csv
            eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                rows = f.readlines()
            
            # 執行雙重搜尋
            dual_search_result = self.dual_search.perform_corrected_dual_search(
                rows,
                region_result['code_region'],
                region_result['backup_region'],
                code_regions
            )
            
            # 輸出搜尋結果摘要
            if dual_search_result.get('success'):
                self.logger.info("   [OK] 雙重搜尋成功完成")
                if 'search_method' in dual_search_result:
                    self.logger.info(f"   搜尋方法: {dual_search_result['search_method']}")
                if 'match_rate' in dual_search_result:
                    self.logger.info(f"   匹配率: {dual_search_result['match_rate']}")
            else:
                self.logger.warning("   [WARNING] 雙重搜尋未找到匹配結果")
            
            return {
                'status': 'success',
                'dual_search_result': dual_search_result,
                'region_result': region_result,
                'total_rows': len(rows)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def _generate_standard_report(self, processing_data: dict):
        """生成標準處理報告"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_filename = f"EQC_標準處理報告_{timestamp}.txt"
            
            doc_directory = processing_data['doc_directory']
            report_path = os.path.join(doc_directory, report_filename)
            
            # 生成報告內容
            content = []
            content.append("EQC 標準處理系統 - 處理報告")
            content.append("=" * 60)
            content.append("")
            content.append(f"處理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content.append(f"資料目錄: {doc_directory}")
            
            # 動態模式顯示
            modes = []
            if processing_data.get('include_inseqcrtdata2'):
                modes.append("InsEqcRtData2 Step 1-2-3-4")
            if processing_data.get('include_step5_testflow'):
                modes.append("Step 5 測試流程生成")
            
            if modes:
                mode_description = f"完整模式（包含 {', '.join(modes)}）"
            else:
                mode_description = "標準模式（僅基礎處理）"
            content.append(f"處理模式: {mode_description}")
            content.append("")
            
            # 區間檢測結果
            if 'region_result' in processing_data:
                region = processing_data['region_result']
                content.append("📍 程式碼區間檢測結果:")
                content.append(f"  主要區間: 第{region['code_region']['start_column_number']}-{region['code_region']['end_column_number']}欄")
                content.append(f"  區間範圍: {region['code_region']['end1'] - region['code_region']['start1'] + 1} 個欄位")
                if region['backup_region']['found']:
                    content.append(f"  備用區間: 第{region['backup_region']['backup_start_column']}-{region['backup_region']['backup_end_column']}欄")
                content.append("")
            
            # 雙重搜尋結果
            if 'dual_search_result' in processing_data:
                dual_result = processing_data['dual_search_result']['dual_search_result']
                content.append("[REFRESH] 雙重搜尋結果:")
                content.append(f"  搜尋方法: {dual_result.get('search_method', '未知')}")
                content.append(f"  匹配成功: {'是' if dual_result.get('success') else '否'}")
                if 'match_rate' in dual_result:
                    content.append(f"  匹配率: {dual_result['match_rate']}")
                content.append("")
            
            # 檔案資訊
            eqctotaldata_path = processing_data.get('eqctotaldata_path')
            if eqctotaldata_path and os.path.exists(eqctotaldata_path):
                with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                    total_rows = len(f.readlines())
                content.append("[CHART] 生成檔案資訊:")
                content.append(f"  EQCTOTALDATA.csv: {total_rows} 行")
                content.append("")
            
            # InsEqcRtData2 結果
            if 'inseqcrtdata2_result' in processing_data and processing_data['inseqcrtdata2_result']:
                inseqcrt_result = processing_data['inseqcrtdata2_result']
                if inseqcrt_result.get('status') == 'success':
                    content.append("[REFRESH] InsEqcRtData2 處理結果:")
                    content.append(f"  處理階段: {inseqcrt_result.get('processing_stage', '未知')}")
                    content.append(f"  Step 1-2 ALL0移動: {inseqcrt_result.get('step12_moved_count', 0)} 行")
                    content.append(f"  Step 3 FAIL檢測: {inseqcrt_result.get('step3_fail_count', 0)} 行")
                    content.append(f"  Step 4 CODE匹配: {inseqcrt_result.get('step4_matches', 0)} 個")
                    if inseqcrt_result.get('step3_debug_log'):
                        content.append(f"  Step 3 DEBUG LOG: {os.path.basename(inseqcrt_result['step3_debug_log'])}")
                    if inseqcrt_result.get('step4_debug_log'):
                        content.append(f"  Step 4 DEBUG LOG: {os.path.basename(inseqcrt_result['step4_debug_log'])}")
                    content.append("")
                else:
                    content.append("[WARNING] InsEqcRtData2 處理失敗")
                    content.append(f"  失敗原因: {inseqcrt_result.get('error_message', '未知')}")
                    content.append("")
            
            # Step 5 結果
            if 'step5_result' in processing_data and processing_data['step5_result']:
                step5_result = processing_data['step5_result']
                if step5_result.get('status') == 'success':
                    content.append("[TEST] Step 5 測試流程生成結果:")
                    content.append(f"  輸出檔案: {os.path.basename(step5_result['output_file'])}")
                    content.append(f"  總行數: {step5_result['total_rows']}")
                    content.append(f"  重新排列行數: {step5_result['reordered_rows']}")
                    content.append(f"  FAIL 對應關係: {step5_result['fail_mappings_count']} 個")
                    content.append("")
                else:
                    content.append("[WARNING] Step 5 測試流程生成失敗")
                    content.append(f"  失敗原因: {step5_result.get('error_message', '未知')}")
                    content.append("")
            
            # Step 6 結果
            if 'step6_result' in processing_data and processing_data['step6_result']:
                step6_result = processing_data['step6_result']
                if step6_result.get('status') == 'success':
                    content.append("[CHART] Step 6 Excel 生成與標記結果:")
                    content.append(f"  Excel 檔案: {step6_result['excel_filename']}")
                    content.append(f"  總行數: {step6_result['total_rows']}")
                    content.append(f"  總欄數: {step6_result['total_columns']}")
                    content.append(f"  標記行數: {step6_result['highlighted_rows_count']} 行")
                    content.append(f"  標記範圍: {step6_result['highlight_column_range']}")
                    if step6_result.get('step6_report'):
                        content.append(f"  Step 6 報告: {step6_result['step6_report']['report_filename']}")
                    content.append("")
                else:
                    content.append("[WARNING] Step 6 Excel 生成失敗")
                    content.append(f"  失敗原因: {step6_result.get('error_message', '未知')}")
                    content.append("")
            
            # 最終 Excel 轉換結果
            if 'final_excel_result' in processing_data and processing_data['final_excel_result']:
                final_excel_result = processing_data['final_excel_result']
                if final_excel_result.get('status') == 'success':
                    content.append("[CHART] 最終 CSV 到 Excel 轉換結果:")
                    content.append(f"  Excel 檔案: {os.path.basename(final_excel_result['output_file'])}")
                    content.append(f"  總行數: {final_excel_result['total_rows']}")
                    content.append(f"  總欄數: {final_excel_result['total_columns']}")
                    content.append(f"  處理時間: {final_excel_result['processing_time']:.3f} 秒")
                    content.append(f"  BIN1保護位置: {final_excel_result.get('protection_positions_count', 0)} 個")
                    content.append("")
                else:
                    content.append("[WARNING] 最終 Excel 轉換失敗")
                    content.append(f"  失敗原因: {final_excel_result.get('error_message', '未知')}")
                    content.append("")
            
            content.append("處理流程:")
            content.append("  1. [OK] 自動生成 EQCTOTALDATA.csv")
            content.append("  2. [OK] 程式碼區間檢測")
            content.append("  3. [OK] 雙重搜尋機制")
            
            step_count = 4
            if processing_data.get('include_inseqcrtdata2'):
                content.append(f"  {step_count}. [OK] InsEqcRtData2 資料重組 (Step 1-2-3-4)")
                step_count += 1
            
            if processing_data.get('include_step5_testflow'):
                content.append(f"  {step_count}. [OK] Step 5 測試流程生成")
                step_count += 1
            
            if processing_data.get('include_step6_excel'):
                content.append(f"  {step_count}. [OK] Step 6 Excel 生成與黃色標記")
                step_count += 1
            
            if processing_data.get('include_final_excel_conversion'):
                content.append(f"  {step_count}. [OK] 最終 CSV 到 Excel 轉換（包含 Summary）")
                step_count += 1
            
            content.append(f"  {step_count}. [OK] 完整報告生成")
            
            if not processing_data.get('include_inseqcrtdata2') and not processing_data.get('include_step5_testflow'):
                content.append("")
                content.append("注意: 此為基礎處理模式")
            
            content.append("")
            content.append("處理完成時間: " + datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            # 寫入報告檔案
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write("\n".join(content))
            
            self.logger.info(f"   [OK] 處理報告已生成: {report_filename}")
            
            return {
                'status': 'success',
                'report_path': report_path,
                'report_filename': report_filename
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    
    def _execute_inseqcrtdata2_step1234_processing(self, doc_directory: str, region_result: dict):
        """
        執行 InsEqcRtData2 Step 1-2-3-4 處理
        Step 1-2: ALL0 移動功能（主區間全零 EQC RT 行移動到檔案最後）
        Step 3: Online EQC FAIL 行檢測（只 show debug log）
        Step 4: CODE 區間匹配搜尋 DEBUG LOG（新增）
        """
        try:
            # 讀取 EQCTOTALDATA.csv
            eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                rows = f.readlines()
            
            self.logger.info(f"   原始檔案行數: {len(rows)}")
            
            # 獲取備用區間參數
            backup_region = region_result.get('backup_region', {})
            if backup_region.get('found'):
                backup_start_col = backup_region.get('backup_start_column')
                backup_end_col = backup_region.get('backup_end_column')
                if backup_start_col and backup_end_col:
                    backup_start1 = backup_start_col - 1  # 轉換為索引
                    backup_end1 = backup_end_col - 1      # 轉換為索引
                else:
                    backup_start1 = backup_region.get('backup_start1')
                    backup_end1 = backup_region.get('backup_end1')
            else:
                backup_start1 = None
                backup_end1 = None
            
            # 功能替換原則：使用新的完整 InsEqcRtData2 工作流程（整合 ALL0 移動 + FAIL 檢測 + Step4 CODE匹配）
            self.logger.info("[TOOL] InsEqcRtData2 Step 1-2-3-4: 完整處理...")
            inseqcrtdata2_result = self.inseqcrtdata2_processor.perform_complete_inseqcrtdata2_workflow(
                eqctotaldata_path=eqctotaldata_path,
                start1=region_result['code_region']['start1'],
                end1=region_result['code_region']['end1'],
                rows=rows,
                backup_start1=backup_start1,
                backup_end1=backup_end1
            )
            
            # 處理結果
            if inseqcrtdata2_result.get('success'):
                moved_count = inseqcrtdata2_result.get('step12_moved_count', 0)
                fail_count = inseqcrtdata2_result.get('step3_fail_count', 0)
                debug_log = inseqcrtdata2_result.get('debug_log_file')
                total_rows_after = inseqcrtdata2_result.get('total_rows_after', len(rows))
                
                step3_result = {
                    'fail_rows_found': fail_count,
                    'debug_log_file': debug_log
                }
                
                self.logger.info(f"   [OK] InsEqcRtData2 Step 1-2-3 處理完成: ALL0移動 {moved_count} 行, FAIL檢測 {fail_count} 行")
                
                # Step 4: 執行 CODE 區間匹配搜尋 DEBUG LOG
                self.logger.info("[REFRESH] Step 4: 執行 CODE 區間匹配搜尋...")
                
                # 從現有的 rows 重新檢測 FAIL 詳情進行 Step 4 處理
                try:
                    # 計算動態邊界（基於 B9 欄位）
                    fail_count = int(rows[8].split(',')[1].strip())  # B9 欄位 FAIL 數量
                    online_eqc_start = 13  # 第14行（索引13）
                    online_eqc_end = 13 + (fail_count * 2) - 1  # 動態計算結束位置
                    eqc_rt_start = online_eqc_end + 1  # RT 開始位置
                    
                    self.logger.info(f"   [TARGET] 動態邊界計算: B9 FAIL數={fail_count}, Online EQC範圍=第{online_eqc_start+1}-{online_eqc_end+1}行, RT開始=第{eqc_rt_start+1}行")
                    
                    # 生成 FAIL 詳情用於 Step 4
                    fail_details = []
                    
                    for i in range(online_eqc_start, min(online_eqc_end + 1, len(rows))):
                        try:
                            row = rows[i].strip()
                            if row:
                                elements = row.split(',')
                                if len(elements) >= 2:
                                    bin_value = elements[1].strip()
                                    if bin_value != '1':
                                        fail_detail = {
                                            'row_number': i + 1,
                                            'bin_value': bin_value,
                                            'content': row[:100] + "..." if len(row) > 100 else row
                                        }
                                        fail_details.append(fail_detail)
                        except:
                            continue
                    
                    if fail_details:
                        step4_result = self._execute_step4_code_matching_debug(
                            rows, 
                            fail_details,
                            region_result,
                            doc_directory
                        )
                        
                        if step4_result.get('success'):
                            self.logger.info(f"   [OK] Step 4 CODE匹配: {step4_result.get('total_matches', 0)} 個匹配")
                            self.logger.info(f"   [NOTES] Step 4 DEBUG LOG: {step4_result.get('debug_log_file', 'N/A')}")
                        else:
                            self.logger.warning(f"   [WARNING] Step 4 執行失敗: {step4_result.get('error', '未知錯誤')}")
                    else:
                        step4_result = {'success': True, 'total_matches': 0, 'message': 'No FAIL data for Step 4'}
                        self.logger.info("   [INFO] Step 4: 未找到 FAIL 行，跳過 CODE 匹配")
                        
                except Exception as e:
                    step4_result = {'success': False, 'error': str(e)}
                    self.logger.error(f"   [ERROR] Step 4 執行異常: {e}")
            else:
                moved_count = 0
                total_rows_after = len(rows)
                step3_result = {
                    'fail_rows_found': 0,
                    'debug_log_file': None
                }
                step4_result = {'success': False, 'total_matches': 0}
                self.logger.warning(f"   [WARNING] InsEqcRtData2 處理失敗: {inseqcrtdata2_result.get('error')}")
            
            # 確保 step4_result 存在
            if 'step4_result' not in locals():
                step4_result = {'success': True, 'total_matches': 0, 'message': 'Step 4 skipped'}
            
            return {
                'status': 'success',
                'step12_moved_count': moved_count,
                'step3_fail_count': step3_result.get('fail_rows_found', 0),
                'step3_debug_log': step3_result.get('debug_log_file'),
                'step4_matches': step4_result.get('total_matches', 0),
                'step4_debug_log': step4_result.get('debug_log_file'),
                'total_rows_after': total_rows_after,
                'dynamic_boundary': {
                    'online_eqc_end': locals().get('online_eqc_end', 32),  # 動態計算的邊界
                    'eqc_rt_start': locals().get('eqc_rt_start', 33),
                    'fail_count': locals().get('fail_count', 0)
                },
                'processing_stage': 'step_1_2_3_4_complete'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    
    def _execute_step4_code_matching_debug(self, rows: List[str], fail_details: List, region_result: dict, doc_directory: str = None):
        """
        執行 Step 4: CODE 區間匹配搜尋 DEBUG LOG
        使用 Step 3 的 FAIL 檢測結果作為輸入，生成 Step 4 DEBUG LOG
        """
        try:
            # 獲取區間參數
            start1 = region_result['code_region']['start1']
            end1 = region_result['code_region']['end1']
            
            # 修正備用區間參數提取邏輯
            backup_region = region_result.get('backup_region', {})
            if backup_region.get('found'):
                # 使用檢測到的備用區間起始和結束欄位
                backup_start_col = backup_region.get('backup_start_column')
                backup_end_col = backup_region.get('backup_end_column')
                if backup_start_col and backup_end_col:
                    backup_start1 = backup_start_col - 1  # 轉換為索引 (欄位編號 - 1)
                    backup_end1 = backup_end_col - 1      # 轉換為索引 (欄位編號 - 1)
                else:
                    backup_start1 = backup_region.get('backup_start1')
                    backup_end1 = backup_region.get('backup_end1')
            else:
                backup_start1 = None
                backup_end1 = None
            
            self.logger.info("[SEARCH] Step 4: 開始 CODE 區間匹配搜尋 DEBUG LOG...")
            
            # 如果沒有 FAIL 詳情，從 inseqcrtdata2_processor 獲取
            if not fail_details:
                self.logger.info("   [WARNING] 無 FAIL 詳情，跳過 Step 4 CODE 匹配")
                return {
                    'success': True,
                    'total_matches': 0,
                    'message': 'No FAIL details available'
                }
            
            # 重新讀取更新後的 CSV 檔案以獲得正確的行數
            self.logger.info("   [NOTES] 重新讀取 ALL0 移動後的檔案以確保行數正確...")
            
            # 使用正確的文檔目錄路徑
            if doc_directory:
                eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            else:
                # 檢查可能的檔案路徑（後備選項）
                possible_paths = [
                    "doc/20250523/EQCTOTALDATA.csv",
                    "EQCTOTALDATA.csv",
                    os.path.join(os.getcwd(), "doc/20250523/EQCTOTALDATA.csv")
                ]
                
                eqctotaldata_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        eqctotaldata_path = path
                        break
                
                if not eqctotaldata_path:
                    eqctotaldata_path = "doc/20250523/EQCTOTALDATA.csv"  # 預設路徑
            
            # 重新讀取檔案
            try:
                with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                    updated_rows = f.readlines()
                self.logger.info(f"   [OK] 重新讀取完成: {len(updated_rows)} 行 (原始: {len(rows)} 行)")
            except Exception as e:
                self.logger.warning(f"   [WARNING] 無法重新讀取檔案，使用原始行數: {e}")
                updated_rows = rows
            
            # 調用 inseqcrtdata2_processor 的 Step 4 功能 (使用更新後的行數)
            step4_result = self.inseqcrtdata2_processor.perform_step4_code_matching_debug(
                rows=updated_rows,
                fail_details=fail_details,
                start1=start1,
                end1=end1,
                backup_start1=backup_start1,
                backup_end1=backup_end1
            )
            
            if step4_result.get('success'):
                total_matches = step4_result.get('total_matches', 0)
                debug_log_file = step4_result.get('debug_log_file')
                
                self.logger.info(f"   [OK] Step 4 完成: 找到 {total_matches} 個 CODE 匹配")
                if debug_log_file:
                    self.logger.info(f"   [NOTES] DEBUG LOG: {os.path.basename(debug_log_file)}")
                
                return {
                    'success': True,
                    'total_matches': total_matches,
                    'debug_log_file': debug_log_file,
                    'match_results': step4_result.get('match_results', [])
                }
            else:
                self.logger.warning(f"   [WARNING] Step 4 失敗: {step4_result.get('error', '未知錯誤')}")
                return {
                    'success': False,
                    'error': step4_result.get('error', '未知錯誤')
                }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Step 4 CODE 匹配搜尋失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_step5_testflow_generation(self, doc_directory: str, inseqcrtdata2_result: dict):
        """
        執行 Step 5 測試流程生成
        使用 Step 4 DEBUG LOG 重新排列 CSV 資料行生成線性測試流程
        """
        try:
            self.logger.info("[TEST] Step 5: 測試流程生成...")
            
            # 檢查必要的輸入檔案
            eqctotaldata_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            step4_debug_log_path = inseqcrtdata2_result.get('step4_debug_log')
            
            if not step4_debug_log_path or not os.path.exists(step4_debug_log_path):
                return {
                    'status': 'error',
                    'error_message': 'Step 4 DEBUG LOG 檔案不存在或路徑無效'
                }
            
            # 執行 Step 5 測試流程生成
            step5_result = self.step5_processor.generate_test_flow_csv(
                eqctotaldata_path=eqctotaldata_path,
                step4_debug_log_path=step4_debug_log_path
            )
            
            if step5_result['status'] == 'success':
                self.logger.info(f"   [OK] Step 5 完成: 生成 {os.path.basename(step5_result['output_file'])}")
                self.logger.info(f"   總行數: {step5_result['total_rows']}")
                self.logger.info(f"   重新排列行數: {step5_result['reordered_rows']}")
                self.logger.info(f"   FAIL 對應關係: {step5_result['fail_mappings_count']} 個")
            
            return step5_result
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': f'Step 5 測試流程生成失敗: {str(e)}'
            }
    
    def _execute_step6_excel_generation(self, doc_directory: str, step5_result: dict, region_result: dict):
        """
        執行 Step 6 Excel 生成與黃色標記
        使用增強版 CsvToExcelConverter 替代原本的 openpyxl 實作
        """
        try:
            self.logger.info("[CHART] Step 6: Excel 生成與黃色標記...")
            
            # 檢查必要的輸入資料
            step5_csv_path = step5_result.get('output_file')
            final_online_eqc_rt_rows = step5_result.get('final_online_eqc_rt_rows', [])
            
            if not step5_csv_path or not os.path.exists(step5_csv_path):
                return {
                    'status': 'error',
                    'error_message': 'Step 5 輸出的 CSV 檔案不存在或路徑無效'
                }
            
            if not final_online_eqc_rt_rows:
                return {
                    'status': 'error',
                    'error_message': '無 final_online_eqc_rt_rows 資料可用於標記'
                }
            
            # 獲取主 CODE 區間參數
            code_region_start_column = region_result['code_region']['start_column_number']
            code_region_end_column = region_result['code_region']['end_column_number']
            
            # 生成輸出檔案名稱
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            excel_filename = f"EQCTOTALDATA_Step6_HighlightedEQCRT_{timestamp}.xlsx"
            excel_file_path = os.path.join(doc_directory, excel_filename)
            
            # 準備 EQC 自訂格式參數
            self.logger.info(f"   準備 EQC 格式參數...")
            self.logger.info(f"   黃色標記行數: {len(final_online_eqc_rt_rows)} 行")
            self.logger.info(f"   黃色標記範圍: 第1欄 到 第{code_region_end_column}欄")
            self.logger.info(f"   粗體格式: 第8行，第{code_region_start_column}-{code_region_end_column}欄")
            
            # 使用增強版 CsvToExcelConverter
            conversion_result = self.csv_to_excel_converter.convert_csv_to_excel(
                csv_file_path=step5_csv_path,
                output_file_path=excel_file_path,
                # EQC 格式參數
                highlight_rows=final_online_eqc_rt_rows,
                highlight_end_column=code_region_end_column,
                bold_row=8,
                bold_start_column=code_region_start_column,
                bold_end_column=code_region_end_column,
                hyperlink_start_row=14,  # C14 開始
                hyperlink_column=3,      # C 欄 (第3欄)
                hyperlink_template=r"\\192.168.1.60\temp_7days\20250523\Production Data\{filename}"
            )
            
            if conversion_result.success:
                self.logger.info(f"   [OK] Step 6 完成: 生成 {excel_filename}")
                self.logger.info(f"   標記行數: {len(final_online_eqc_rt_rows)} 行")
                self.logger.info(f"   標記範圍: 第1欄 到 第{code_region_end_column}欄")
                self.logger.info(f"   處理時間: {conversion_result.processing_time:.3f} 秒")
                
                # 執行重命名：將 Step6 檔案重命名為 EQCTOTALDATA.xlsx（功能替換原則：恢復原始重命名功能）
                final_excel_path = self._rename_step6_to_final(doc_directory, excel_file_path)
                
                return {
                    'status': 'success',
                    'excel_file_path': final_excel_path,
                    'original_step6_file': excel_file_path,
                    'excel_filename': "EQCTOTALDATA.xlsx",
                    'total_rows': conversion_result.total_rows,
                    'total_columns': conversion_result.total_columns,
                    'highlighted_rows_count': len(final_online_eqc_rt_rows),
                    'highlight_column_range': f"A-{chr(64 + code_region_end_column)}",
                    'processing_time': conversion_result.processing_time,
                    'processing_stage': 'step6_excel_generation_complete'
                }
            else:
                return {
                    'status': 'error',
                    'error_message': conversion_result.error_message or 'Step 6 Excel 轉換失敗'
                }
            
        except Exception as e:
            return {
                'status': 'error',
                'error_message': f'Step 6 Excel 生成失敗: {str(e)}'
            }
    
    def _execute_final_excel_conversion(self, doc_directory: str):
        """
        執行最終 CSV 到 Excel 轉換（包含 Summary 工作表），並確保輸出在來源目錄。
        """
        # 暫時註解新邏輯，回到原本功能進行測試
        self.logger.info("[FAST] 暫時跳過最終 Excel 轉換（功能已註解，測試中）")
        return {
            'status': 'skipped_for_testing',
            'message': '暫時停用此功能進行測試'
        }
        
        """
        # === 以下程式碼暫時註解，用於測試原本功能 ===
        try:
            self.logger.info("[CHART] 最終 Excel 轉換: 處理 EQCTOTALDATA.csv...")

            # 定義來源和目標路徑，確保在同一個目錄
            source_csv_path = os.path.join(doc_directory, "EQCTOTALDATA.csv")
            final_excel_path = os.path.join(doc_directory, "EQCTOTALDATA.xlsx")

            # 檢查來源 CSV 檔案是否存在
            if not os.path.exists(source_csv_path):
                self.logger.warning(f"   [WARNING] 來源檔案 {os.path.basename(source_csv_path)} 不存在，跳過 Excel 轉換。")
                return {
                    'status': 'skipped',
                    'error_message': 'EQCTOTALDATA.csv 檔案不存在'
                }

            # 呼叫核心轉換器，明確指定輸入和輸出路徑
            conversion_result = self.csv_to_excel_converter.convert_csv_to_excel(
                csv_file_path=source_csv_path,
                output_file_path=final_excel_path
            )

            if conversion_result.success:
                self.logger.info(f"   [OK] 最終 Excel 轉換成功")
                self.logger.info(f"   Excel 檔案: {os.path.basename(conversion_result.output_file)}")
                return {
                    'status': 'success',
                    'output_file': conversion_result.output_file,
                    'processing_time': conversion_result.processing_time,
                    'total_rows': conversion_result.total_rows,
                    'total_columns': conversion_result.total_columns
                }
            else:
                self.logger.error(f"   [ERROR] 最終 Excel 轉換失敗: {conversion_result.error_message}")
                return {
                    'status': 'error',
                    'error_message': conversion_result.error_message or '最終 Excel 轉換失敗'
                }

        except Exception as e:
            self.logger.error(f"   [ERROR] 最終 Excel 轉換過程中發生未預期錯誤: {e}")
            return {
                'status': 'error',
                'error_message': f'最終 Excel 轉換失敗: {str(e)}'
            }
        # === 註解結束 ===
        """

    def _rename_step6_to_final(self, doc_directory: str, step6_excel_filename: str):
        """
        將 Step 6 Excel 檔案重新命名為 EQCTOTALDATA.xlsx
        
        Args:
            doc_directory: 文檔目錄
            step6_excel_filename: Step 6 Excel 檔案的完整路徑
            
        Returns:
            str: 最終的 EQCTOTALDATA.xlsx 路徑
        """
        try:
            # 檢查來源檔案是否存在
            if not os.path.exists(step6_excel_filename):
                self.logger.error(f"[ERROR] Step 6 Excel 檔案不存在: {step6_excel_filename}")
                return step6_excel_filename  # 失敗時返回原始路徑
            
            # 目標檔案路徑
            target_file = os.path.join(doc_directory, "EQCTOTALDATA.xlsx")
            
            # 如果目標檔案已存在，先刪除
            if os.path.exists(target_file):
                os.remove(target_file)
                self.logger.info(f"   [TRASH] 已刪除舊的 EQCTOTALDATA.xlsx")
            
            # 移動檔案到目標位置（使用移動而非複製，刪除原檔案）
            import shutil
            shutil.move(step6_excel_filename, target_file)
            self.logger.info(f"   [EDIT] 已重命名: {os.path.basename(step6_excel_filename)} → EQCTOTALDATA.xlsx")
            
            # 驗證移動是否成功
            if os.path.exists(target_file):
                file_size = os.path.getsize(target_file)
                self.logger.info(f"   [OK] 重命名成功，檔案大小: {file_size} bytes")
                return target_file
            else:
                self.logger.error("[ERROR] 檔案移動失敗，目標檔案不存在")
                return step6_excel_filename  # 失敗時返回原始路徑
            
        except Exception as e:
            self.logger.error(f"[ERROR] Excel 檔案重命名失敗: {e}")
            return step6_excel_filename  # 失敗時返回原始路徑

    def _generate_final_report(self, doc_directory, region_result, dual_search_result, 
                              inseqcrtdata2_result, step5_result, step6_result, final_excel_result):
        """
        生成最終處理報告
        
        Args:
            doc_directory: 處理目錄
            region_result: 區間檢測結果
            dual_search_result: 雙重搜尋結果
            inseqcrtdata2_result: InsEqcRtData2處理結果
            step5_result: Step5測試流程結果
            step6_result: Step6 Excel生成結果
            final_excel_result: 最終Excel轉換結果
            
        Returns:
            Dict: 報告生成結果
        """
        try:
            self.logger.info("[EDIT] 生成最終處理報告...")
            
            # 生成報告內容
            report_content = []
            report_content.append("[TARGET] EQC 第二階段處理流程報告")
            report_content.append("=" * 60)
            report_content.append(f"處理時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_content.append(f"處理目錄: {doc_directory}")
            report_content.append("")
            
            # 程式碼區間檢測結果
            if region_result and region_result.get('status') == 'success':
                code_region = region_result['code_region']
                report_content.append("[OK] 程式碼區間檢測:")
                report_content.append(f"   主要區間: 第{code_region['start_column_number']}-{code_region['end_column_number']}欄")
                if region_result['backup_region']['found']:
                    backup_region = region_result['backup_region']
                    report_content.append(f"   備用區間: 第{backup_region['backup_start_column']}-{backup_region['backup_end_column']}欄")
                report_content.append("")
            
            # 雙重搜尋結果
            if dual_search_result and dual_search_result.get('success'):
                report_content.append("[OK] 雙重搜尋機制:")
                report_content.append(f"   搜尋方法: {dual_search_result.get('search_method', '未知')}")
                report_content.append(f"   匹配率: {dual_search_result.get('match_rate', '未知')}")
                report_content.append("")
            
            # InsEqcRtData2處理結果
            if inseqcrtdata2_result and inseqcrtdata2_result.get('status') == 'success':
                report_content.append("[OK] InsEqcRtData2處理:")
                report_content.append(f"   ALL0移動: {inseqcrtdata2_result.get('step12_moved_count', 0)} 行")
                report_content.append(f"   FAIL檢測: {inseqcrtdata2_result.get('step3_fail_count', 0)} 行")
                report_content.append(f"   CODE匹配: {inseqcrtdata2_result.get('step4_matches', 0)} 個")
                report_content.append("")
            
            # Step5結果
            if step5_result and step5_result.get('status') == 'success':
                report_content.append("[OK] Step5測試流程生成:")
                report_content.append(f"   重新排列行數: {step5_result.get('reordered_rows', 0)}")
                report_content.append(f"   輸出檔案: {step5_result.get('output_file', '未知')}")
                report_content.append("")
            
            # Step6結果
            if step6_result and step6_result.get('status') == 'success':
                report_content.append("[OK] Step6 Excel生成:")
                report_content.append(f"   輸出檔案: {step6_result.get('output_file', '未知')}")
                report_content.append("")
            
            # 最終Excel轉換結果
            if final_excel_result and final_excel_result.get('status') == 'success':
                report_content.append("[OK] 最終Excel轉換:")
                report_content.append(f"   Excel檔案: {final_excel_result.get('excel_file', '未知')}")
                report_content.append(f"   處理時間: {final_excel_result.get('processing_time', '未知')} 秒")
                report_content.append("")
            
            report_content.append("[TARGET] 第二階段處理流程完成")
            
            # 檢查環境變數決定是否生成報告檔案
            eqc_detailed_logs = os.getenv('EQC_DETAILED_LOGS', 'false').lower() == 'true'
            
            if eqc_detailed_logs:
                # 生成報告檔案
                report_filename = f"EQC_Stage2_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                report_path = os.path.join(doc_directory, report_filename)
                
                with open(report_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(report_content))
                
                self.logger.info(f"[NOTES] 報告已生成: {report_filename}")
            else:
                report_filename = None
                report_path = None
                self.logger.info("[NOTES] EQC_DETAILED_LOGS=false，跳過報告檔案生成")
            
            return {
                'status': 'success',
                'report_path': report_path,
                'report_filename': report_filename,
                'report_content': report_content
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] 報告生成失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }






def main():
    """主函數"""
    print("[TARGET] EQC 一鍵完成處理系統")
    print("=" * 60)
    print("處理流程: 自動生成 EQCTOTALDATA → 程式碼區間檢測 → 雙重搜尋 → Step 1-2-3-4 (ALL0移動 + FAIL檢測 + CODE匹配) → Step 5 (測試流程生成) → Step 6 (Excel生成與標記) → 最終 CSV 到 Excel 轉換（包含 Summary）")
    print()
    
    # 設置日誌
    logging.basicConfig(level=logging.INFO, format='%(message)s')
    
    processor = StandardEQCProcessor()
    
    # 查看 EQCTOTALDATA.csv 結構
    processor.view_eqctotaldata_structure('doc/GHKR03.13')
    print()
    
    # 執行完整流程：區間檢測 + 雙重搜尋 + InsEqcRtData2 Step 1-2-3-4 + Step 5 + Step 6 + 最終 Excel 轉換
    result = processor.process_standard_eqc_pipeline(
        'doc/GHKR03.13', 
        include_inseqcrtdata2=True, 
        include_step5_testflow=True, 
        include_step6_excel=True,
        include_final_excel_conversion=True
    )
    
    print()
    print("[NOTES] 處理結果總結:")
    print("-" * 60)
    
    if result['status'] == 'success':
        print("[OK] 狀態: 成功")
        print(f"[FOLDER] EQCTOTALDATA.csv: {result.get('eqctotaldata_path', '未知')}")
        if result.get('report_result', {}).get('report_path'):
            print(f"[NOTES] 處理報告: {result['report_result']['report_filename']}")
        
        print("\n處理階段:")
        for stage in result.get('processing_stages', []):
            status_icon = "[OK]" if stage['status'] == 'success' else "[ERROR]"
            print(f"  {status_icon} {stage['name']}")
    else:
        print("[ERROR] 狀態: 失敗")
        print(f"錯誤訊息: {result.get('error_message', '未知錯誤')}")
        if 'stage' in result:
            print(f"失敗階段: {result['stage']}")

if __name__ == "__main__":
    main()