"""
Site 欄位動態查找器
對照 VBA Device2BinControl 中的 mySiteColumnN 查找邏輯
"""

import pandas as pd
import os
from typing import Optional, Tuple
from datetime import datetime


class SiteColumnFinder:
    """
    Site 欄位動態查找器
    
    對照 VBA 邏輯：
    For i = 3 To 3 + myTotalItemNumber - 1
        If InStr(LCase(CStr(ActiveSheet.Cells(8, i).value)), "site") And _
           InStr(1, ActiveSheet.Cells(8, i).value, "Site_Check", vbTextCompare) = 0 Then
            mySiteColumnN = i
            Exit For
        End If
    Next i
    """
    
    def __init__(self, enable_logging: bool = True):
        self.enable_logging = enable_logging
        
    def find_site_column(self, df: pd.DataFrame, header_row: int = 7, start_col: int = 2, total_items: Optional[int] = None) -> Optional[int]:
        """
        動態查找 Site 欄位位置
        
        Args:
            df: DataFrame
            header_row: 標頭行號 (第8行 = index 7)
            start_col: 開始搜尋欄位 (C欄 = index 2)
            total_items: 測試項目總數 (None = 搜尋到最後一欄)
            
        Returns:
            Site 欄位的欄位索引，找不到回傳 None
        """
        if df.empty or header_row >= len(df):
            return None
            
        # 決定搜尋範圍
        end_col = len(df.columns) - 1
        if total_items is not None:
            end_col = min(start_col + total_items - 1, len(df.columns) - 1)
        
        # 對照 VBA: For i = 3 To 3 + myTotalItemNumber - 1
        for col_idx in range(start_col, end_col + 1):
            if col_idx < len(df.columns):
                cell_value = df.iloc[header_row, col_idx]
                
                if pd.notna(cell_value) and str(cell_value).strip():
                    cell_str = str(cell_value).lower()
                    
                    # 對照 VBA: InStr(LCase(CStr(...)), "site") 
                    # 且排[EXCEPT_CHAR] Site_Check: InStr(..., "Site_Check", vbTextCompare) = 0
                    if "site" in cell_str and "site_check" not in cell_str:
                        return col_idx
        
        return None
    
    def find_site_column_with_log(self, df: pd.DataFrame, csv_file_path: str = "", header_row: int = 7, start_col: int = 2) -> Tuple[Optional[int], str]:
        """
        查找 Site 欄位並記錄詳細 LOG
        
        Returns:
            (site_column_index, log_content)
        """
        print("[SEARCH] 執行 Site 欄位動態查找")
        
        # 查找 Site 欄位
        site_column = self.find_site_column(df, header_row, start_col)
        
        # 生成 LOG 內容
        log_content = self._generate_site_log(df, site_column, csv_file_path, header_row, start_col)
        
        # 寫入 LOG 檔案
        if self.enable_logging:
            self._write_site_log(log_content)
        
        # 顯示結果
        if site_column is not None:
            col_letter = self._get_column_letter(site_column)
            site_name = df.iloc[header_row, site_column] if header_row < len(df) else "N/A"
            print(f"[OK] 找到 Site 欄位: {col_letter}欄 (index {site_column}) = '{site_name}'")
        else:
            print("[ERROR] 未找到 Site 欄位")
            
        return site_column, log_content
    
    def _generate_site_log(self, df: pd.DataFrame, site_column: Optional[int], csv_file_path: str, header_row: int, start_col: int) -> str:
        """生成詳細的 Site 查找 LOG"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        log_lines = []
        log_lines.append("[SEARCH] Site 欄位動態查找記錄")
        log_lines.append("=" * 80)
        log_lines.append(f"檔案: {os.path.basename(csv_file_path)}")
        log_lines.append(f"搜尋時間: {timestamp}")
        log_lines.append(f"搜尋範圍: 第{header_row + 1}行，從第{start_col + 1}欄開始")
        log_lines.append("")
        
        # 顯示搜尋結果
        if site_column is not None:
            col_letter = self._get_column_letter(site_column)
            site_name = df.iloc[header_row, site_column] if header_row < len(df) else "N/A"
            log_lines.append(f"[OK] 查找結果: 成功")
            log_lines.append(f"   Site 欄位位置: {col_letter}欄 (index {site_column})")
            log_lines.append(f"   Site 欄位名稱: '{site_name}'")
        else:
            log_lines.append(f"[ERROR] 查找結果: 失敗 (未找到符合條件的 Site 欄位)")
        
        log_lines.append("")
        log_lines.append("[SEARCH] 搜尋過程詳細記錄:")
        log_lines.append("| 欄位 | Excel欄 | 欄位名稱              | 包含site | 排[EXCEPT_CHAR]site_check | 結果 |")
        log_lines.append("|------|----------|---------------------|----------|----------------|------|")
        
        # 詳細搜尋過程
        search_end = min(start_col + 20, len(df.columns))  # 最多顯示20欄
        for col_idx in range(start_col, search_end):
            if col_idx < len(df.columns) and header_row < len(df):
                cell_value = df.iloc[header_row, col_idx]
                col_letter = self._get_column_letter(col_idx)
                
                if pd.notna(cell_value) and str(cell_value).strip():
                    cell_str = str(cell_value).lower()
                    has_site = "site" in cell_str
                    no_site_check = "site_check" not in cell_str
                    is_match = has_site and no_site_check
                    
                    result = "[OK] 找到" if (col_idx == site_column) else ("[SEARCH] 符合" if is_match else "[ERROR] 跳過")
                    
                    log_lines.append(f"| {col_idx:4d} | {col_letter:7s} | {str(cell_value)[:19]:<19s} | {str(has_site):8s} | {str(no_site_check):14s} | {result:4s} |")
                    
                    # 如果找到目標，就停止記錄
                    if col_idx == site_column:
                        break
                else:
                    col_letter = self._get_column_letter(col_idx)
                    log_lines.append(f"| {col_idx:4d} | {col_letter:7s} | {'(空白)':19s} | {'False':8s} | {'True':14s} | {'[ERROR] 跳過':4s} |")
        
        log_lines.append("")
        log_lines.append("對照 VBA 邏輯:")
        log_lines.append("  For i = 3 To 3 + myTotalItemNumber - 1")
        log_lines.append("    If InStr(LCase(CStr(ActiveSheet.Cells(8, i).value)), \"site\") And _")
        log_lines.append("       InStr(1, ActiveSheet.Cells(8, i).value, \"Site_Check\", vbTextCompare) = 0 Then")
        log_lines.append("      mySiteColumnN = i")
        log_lines.append("      Exit For")
        log_lines.append("    End If")
        log_lines.append("  Next i")
        log_lines.append("")
        log_lines.append(f"Site 欄位查找完成時間: {timestamp}")
        
        return "\n".join(log_lines)
    
    def _get_column_letter(self, col_idx: int) -> str:
        """將欄位索引轉換為 Excel 欄位字母"""
        if col_idx < 26:
            return chr(65 + col_idx)
        else:
            return f"Col{col_idx + 1}"
    
    def _write_site_log(self, log_content: str):
        """寫入 Site 查找 LOG 到 datalog.txt"""
        try:
            # 統一輸出到 logs 資料夾的 datalog.txt
            project_root = "/mnt/d/project/python/outlook_summary"
            logs_dir = os.path.join(project_root, "logs")
            os.makedirs(logs_dir, exist_ok=True)
            log_filename = os.path.join(logs_dir, "datalog.txt")
            
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 附加模式寫入，保留之前的記錄
            with open(log_filename, 'a', encoding='utf-8') as f:
                f.write(f"\n{'='*80}\n")
                f.write(f"SITE 欄位查找記錄 - {timestamp}\n")
                f.write(f"{'='*80}\n")
                f.write(log_content)
                f.write(f"\n{'='*80}\n\n")
            
            print(f"[EDIT] Site 查找記錄已附加到: datalog.txt")
            
        except Exception as e:
            print(f"[WARNING] 無法保存 Site 查找記錄: {str(e)}")


def test_with_real_csv():
    """使用實際 CSV 檔案測試 Site 欄位查找功能"""
    csv_file_path = "/mnt/d/project/python/outlook_summary/doc/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv"
    
    print(f"[START] 測試 Site 欄位查找功能")
    print(f"[FOLDER] 使用檔案: {os.path.basename(csv_file_path)}")
    
    # 安全讀取 CSV
    try:
        lines = []
        with open(csv_file_path, 'r', encoding='utf-8', errors='ignore') as f:
            for i, line in enumerate(f):
                if i < 20:  # 只讀前20行就夠了
                    parts = line.strip().split(',')
                    lines.append(parts)
        
        # 統一欄數
        max_cols = max(len(parts) for parts in lines)
        for parts in lines:
            while len(parts) < max_cols:
                parts.append('')
        
        df = pd.DataFrame(lines)
        print(f"[OK] CSV 讀取成功: {len(df)} 行 x {len(df.columns)} 欄")
        
        # 執行 Site 欄位查找
        finder = SiteColumnFinder(enable_logging=True)
        site_column, log_content = finder.find_site_column_with_log(df, csv_file_path, header_row=7, start_col=2)
        
        return site_column, log_content
        
    except Exception as e:
        print(f"[ERROR] 測試失敗: {str(e)}")
        return None, ""


if __name__ == "__main__":
    test_with_real_csv()