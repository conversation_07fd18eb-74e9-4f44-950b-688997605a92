# logger_manager.py

高級日誌系統管理器模組，建立結構化日誌系統，支援 info, debug, error, warning, 效能等級別，不同顏色，包含檔案和函式名稱。

## LogLevel

日誌級別枚舉類別，定義所有支援的日誌級別。

### 枚舉值
- `DEBUG`: [EXCEPT_CHAR]錯級別
- `INFO`: 資訊級別
- `WARNING`: 警告級別
- `ERROR`: 錯誤級別
- `CRITICAL`: 嚴重錯誤級別
- `PERFORMANCE`: 效能級別

## LogFormat

日誌格式枚舉類別，定義支援的日誌格式。

### 枚舉值
- `SIMPLE`: 簡單格式
- `DETAILED`: 詳細格式
- `JSON`: JSON 格式

## ColorScheme

顏色配置資料類別，定義各級別日誌的顏色。

### 屬性
- `debug` (str): [EXCEPT_CHAR]錯級別顏色，預設為藍色
- `info` (str): 資訊級別顏色，預設為綠色
- `warning` (str): 警告級別顏色，預設為黃色
- `error` (str): 錯誤級別顏色，預設為紅色
- `critical` (str): 嚴重錯誤級別顏色，預設為紅底白字
- `performance` (str): 效能級別顏色，預設為洋紅色
- `reset` (str): 重置顏色

## PerformanceTimer

效能計時器類別，用於測量操作執行時間。

### 屬性
- `logger_manager` (LoggerManager): 日誌管理器實例
- `operation_name` (str): 操作名稱
- `start_time` (Optional[float]): 開始時間

### __init__

初始化效能計時器。

**參數:**
- `logger_manager` (LoggerManager): 日誌管理器實例
- `operation_name` (str): 操作名稱

### __enter__

進入上下文管理器，開始計時。

**返回值:**
- PerformanceTimer: 自身實例

### __exit__

退出上下文管理器，結束計時並記錄效能指標。

**參數:**
- `exc_type`: 例外類型
- `exc_val`: 例外值
- `exc_tb`: 例外追蹤

## PerformanceLogger

效能日誌記錄器類別，提供效能相關的日誌功能。

### 屬性
- `logger_manager` (LoggerManager): 日誌管理器實例

### __init__

初始化效能日誌記錄器。

**參數:**
- `logger_manager` (LoggerManager): 日誌管理器實例

### timer

建立效能計時器。

**參數:**
- `operation_name` (str): 操作名稱

**返回值:**
- PerformanceTimer: 效能計時器實例

### log_metrics

記錄效能指標。

**參數:**
- `metrics` (Dict[str, Union[float, int, str]]): 效能指標字典

**返回值:**
- None

## StructuredLogger

結構化日誌記錄器類別，提供結構化的日誌記錄功能。

### 屬性
- `logger_manager` (LoggerManager): 日誌管理器實例
- `name` (str): 日誌器名稱
- `logger` (logging.Logger): 原始日誌器

### __init__

初始化結構化日誌記錄器。

**參數:**
- `logger_manager` (LoggerManager): 日誌管理器實例
- `name` (str): 日誌器名稱

### log_structured

記錄結構化日誌。

**參數:**
- `level` (LogLevel): 日誌級別
- `message` (str): 日誌訊息
- `**kwargs`: 額外的上下文資訊

**返回值:**
- None

### debug

記錄 DEBUG 級別日誌。

**參數:**
- `message` (str): 日誌訊息
- `**kwargs`: 額外的上下文資訊

**返回值:**
- None

### info

記錄 INFO 級別日誌。

**參數:**
- `message` (str): 日誌訊息
- `**kwargs`: 額外的上下文資訊

**返回值:**
- None

### warning

記錄 WARNING 級別日誌。

**參數:**
- `message` (str): 日誌訊息
- `**kwargs`: 額外的上下文資訊

**返回值:**
- None

### error

記錄 ERROR 級別日誌。

**參數:**
- `message` (str): 日誌訊息
- `**kwargs`: 額外的上下文資訊

**返回值:**
- None

### critical

記錄 CRITICAL 級別日誌。

**參數:**
- `message` (str): 日誌訊息
- `**kwargs`: 額外的上下文資訊

**返回值:**
- None

### performance

記錄 PERFORMANCE 級別日誌。

**參數:**
- `message` (str): 日誌訊息
- `**kwargs`: 額外的上下文資訊

**返回值:**
- None

## ColoredFormatter

帶顏色的日誌格式化器類別，繼承自 logging.Formatter。

### 屬性
- `color_scheme` (ColorScheme): 顏色配置
- `include_caller_info` (bool): 是否包含呼叫者資訊

### __init__

初始化帶顏色的格式化器。

**參數:**
- `color_scheme` (ColorScheme): 顏色配置
- `include_caller_info` (bool): 是否包含呼叫者資訊，預設為True

### format

格式化日誌記錄。

**參數:**
- `record` (logging.LogRecord): 日誌記錄

**返回值:**
- str: 格式化後的日誌字串，包含適當的顏色

## JSONFormatter

JSON 格式化器類別，繼承自 logging.Formatter。

### 屬性
- `include_caller_info` (bool): 是否包含呼叫者資訊

### __init__

初始化 JSON 格式化器。

**參數:**
- `include_caller_info` (bool): 是否包含呼叫者資訊，預設為True

### format

格式化日誌記錄為 JSON 格式。

**參數:**
- `record` (logging.LogRecord): 日誌記錄

**返回值:**
- str: JSON 格式的日誌字串，包含時間戳、級別、訊息、上下文等資訊

## AsyncLogHandler

非同步日誌處理器類別，提供非同步的日誌處理功能。

### 屬性
- `logger_manager` (LoggerManager): 日誌管理器實例
- `log_queue` (queue.Queue): 日誌佇列
- `worker_thread` (threading.Thread): 工作執行緒
- `running` (bool): 是否運行中

### __init__

初始化非同步日誌處理器。

**參數:**
- `logger_manager` (LoggerManager): 日誌管理器實例

### add_log

加入日誌到佇列。

**參數:**
- `logger` (logging.Logger): 日誌器
- `level` (LogLevel): 日誌級別
- `message` (str): 日誌訊息
- `context` (Optional[Dict]): 上下文資訊，可選

**返回值:**
- None

### flush

清空佇列，等待所有日誌處理完成。

**返回值:**
- None

### stop

停止非同步處理。

**返回值:**
- None

## LoggerManager

日誌管理器主類別，負責管理所有日誌相關功能。

### 屬性
- `log_dir` (Path): 日誌目錄
- `default_level` (LogLevel): 預設日誌級別
- `enable_colors` (bool): 是否啟用顏色
- `log_format` (LogFormat): 日誌格式
- `include_caller_info` (bool): 是否包含呼叫者資訊
- `max_log_size_mb` (float): 最大日誌檔案大小（MB）
- `backup_count` (int): 備份檔案數量
- `async_logging` (bool): 是否啟用非同步日誌
- `color_scheme` (ColorScheme): 顏色配置
- `loggers` (Dict[str, logging.Logger]): 日誌器字典
- `formatters` (Dict[str, logging.Formatter]): 格式化器字典
- `async_handler` (Optional[AsyncLogHandler]): 非同步處理器

### __init__

初始化日誌管理器。

**參數:**
- `log_dir` (Path): 日誌目錄，預設為 Path("logs")
- `default_level` (LogLevel): 預設日誌級別，預設為 LogLevel.INFO
- `enable_colors` (bool): 是否啟用顏色，預設為True
- `log_format` (LogFormat): 日誌格式，預設為 LogFormat.DETAILED
- `include_caller_info` (bool): 是否包含呼叫者資訊，預設為True
- `max_log_size_mb` (float): 最大日誌檔案大小（MB），預設為10.0
- `backup_count` (int): 備份檔案數量，預設為5
- `async_logging` (bool): 是否啟用非同步日誌，預設為False

### get_logger

取得結構化日誌器。

**參數:**
- `name` (str): 日誌器名稱

**返回值:**
- StructuredLogger: 結構化日誌器實例

### get_performance_logger

取得效能日誌器。

**返回值:**
- PerformanceLogger: 效能日誌器實例

### log

記錄日誌。

**參數:**
- `logger` (logging.Logger): 日誌器
- `level` (LogLevel): 日誌級別
- `message` (str): 日誌訊息
- `context` (Optional[Dict]): 上下文資訊，可選

**返回值:**
- None

### log_exception

記錄例外。

**參數:**
- `logger` (logging.Logger): 日誌器
- `exception` (Exception): 例外物件
- `context` (Optional[Dict]): 上下文資訊，可選

**返回值:**
- None

### log_performance_metrics

記錄效能指標。

**參數:**
- `metrics` (Dict[str, Union[float, int, str]]): 效能指標字典

**返回值:**
- None

### log_email_processing

記錄郵件處理日誌。

**參數:**
- `email_data` (Dict[str, Any]): 郵件資料
- `stage` (str): 處理階段
- `additional_info` (Optional[Dict]): 額外資訊，可選

**返回值:**
- None

### flush_async_logs

清空非同步日誌佇列。

**返回值:**
- None

### from_config

從配置建立日誌管理器的類別方法。

**參數:**
- `config` (Dict[str, Any]): 配置字典
- `log_dir` (Optional[Path]): 日誌目錄，可選

**返回值:**
- LoggerManager: 日誌管理器實例
