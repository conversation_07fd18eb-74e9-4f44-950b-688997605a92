"""
多核優化實作指南
展示如何充分利用多核處理器支援多人同時使用
"""

import multiprocessing
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
import asyncio
from flask import Flask
from fastapi import FastAPI
import uvicorn

# === 方案 1: 使用 Gunicorn 多進程部署 Flask ===
"""
# 安裝 gunicorn
pip install gunicorn

# 啟動命令（使用所有 CPU 核心）
gunicorn -w 12 -b 0.0.0.0:5000 --threads 4 email_inbox_app:app

# 解釋：
# -w 12: 啟動 12 個工作進程（等於 CPU 核心數）
# --threads 4: 每個進程 4 個線程
# 總共：12 進程 × 4 線程 = 48 個並發處理單元
"""

# === 方案 2: 使用多進程處理郵件解析 ===
class MultiCoreEmailProcessor:
    def __init__(self, num_workers=None):
        self.num_workers = num_workers or multiprocessing.cpu_count()
        
    def process_emails_parallel(self, email_ids):
        """使用多進程並行處理郵件"""
        with ProcessPoolExecutor(max_workers=self.num_workers) as executor:
            # 將郵件分配給不同的進程處理
            futures = []
            for email_id in email_ids:
                future = executor.submit(self.process_single_email, email_id)
                futures.append(future)
            
            # 收集結果
            results = []
            for future in futures:
                try:
                    result = future.result(timeout=30)
                    results.append(result)
                except Exception as e:
                    print(f"處理失敗: {e}")
                    
        return results
    
    def process_single_email(self, email_id):
        """單個郵件處理邏輯"""
        # 這裡會在獨立進程中執行
        print(f"進程 {multiprocessing.current_process().name} 處理郵件 {email_id}")
        # 實際處理邏輯...
        return {"email_id": email_id, "status": "processed"}

# === 方案 3: 異步並發處理 ===
class AsyncEmailHandler:
    def __init__(self):
        self.semaphore = asyncio.Semaphore(10)  # 限制同時處理數
        
    async def handle_multiple_users(self, user_requests):
        """同時處理多個用戶請求"""
        tasks = []
        for user_id, request in user_requests.items():
            task = self.handle_user_request(user_id, request)
            tasks.append(task)
        
        # 並行執行所有任務
        results = await asyncio.gather(*tasks)
        return results
    
    async def handle_user_request(self, user_id, request):
        """處理單個用戶請求"""
        async with self.semaphore:  # 限流
            print(f"處理用戶 {user_id} 的請求")
            # 模擬處理時間
            await asyncio.sleep(1)
            return {"user_id": user_id, "result": "success"}

# === 方案 4: 負載均衡配置 ===
"""
使用 Nginx 進行負載均衡，將請求分配到多個進程

# nginx.conf
upstream outlook_app {
    server 127.0.0.1:5001;
    server 127.0.0.1:5002;
    server 127.0.0.1:5003;
    server 127.0.0.1:5004;
}

server {
    listen 80;
    location / {
        proxy_pass http://outlook_app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
"""

# === 方案 5: 實際部署腳本 ===
def create_multicore_deployment_script():
    """創建多核部署腳本"""
    script = '''#!/bin/bash
# 多核部署腳本

# 檢測 CPU 核心數
CPU_CORES=$(nproc)
echo "檢測到 $CPU_CORES 個 CPU 核心"

# 啟動多個 Flask 實例
for i in $(seq 1 $CPU_CORES); do
    PORT=$((5000 + $i))
    echo "啟動 Flask 實例 $i 在端口 $PORT"
    python email_inbox_app.py --port $PORT &
done

# 啟動 FastAPI 服務（使用 uvicorn 的多工作進程）
echo "啟動 FastAPI 服務"
uvicorn src.presentation.api.ft_eqc_api:app --host 0.0.0.0 --port 8010 --workers $CPU_CORES &

# 等待所有進程
wait
'''
    
    with open('start_multicore.sh', 'w') as f:
        f.write(script)
    
    print("已創建多核部署腳本: start_multicore.sh")

# === 性能測試 ===
def performance_test():
    """測試多核性能"""
    import time
    
    # 單進程測試
    start = time.time()
    processor = MultiCoreEmailProcessor(num_workers=1)
    processor.process_emails_parallel(range(100))
    single_time = time.time() - start
    
    # 多進程測試
    start = time.time()
    processor = MultiCoreEmailProcessor(num_workers=12)
    processor.process_emails_parallel(range(100))
    multi_time = time.time() - start
    
    print(f"單進程處理時間: {single_time:.2f} 秒")
    print(f"多進程處理時間: {multi_time:.2f} 秒")
    print(f"性能提升: {single_time/multi_time:.2f} 倍")

# === 建議的系統架構 ===
"""
1. Web 服務器層（Nginx）
   - 負載均衡
   - 靜態文件服務
   - SSL 終止

2. 應用服務器層
   - Flask 實例 × 12（使用 Gunicorn）
   - FastAPI 實例 × 12（使用 Uvicorn）
   
3. 任務處理層
   - Celery Worker × 12
   - 每個 Worker 處理不同類型的任務
   
4. 資料庫層
   - 連接池大小: CPU 核心數 × 2
   - 讀寫分離（如果需要）

這樣的架構可以支援：
- 同時 48+ 個用戶操作（12核 × 4線程）
- 每秒處理 1000+ 個請求
- 郵件解析吞吐量提升 10 倍以上
"""

if __name__ == "__main__":
    # 展示多核處理能力
    print("=== 多核處理展示 ===")
    
    # 創建部署腳本
    create_multicore_deployment_script()
    
    # 測試多核性能
    # performance_test()