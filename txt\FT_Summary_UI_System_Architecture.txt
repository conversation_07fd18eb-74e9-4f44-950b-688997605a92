===============================================================================
                   FT Summary UI 系統架構圖
                   http://127.0.0.1:8010/ft-summary-ui#
===============================================================================

                    ┌─────────────────────────────────────────────────────────────┐
                    │                      前端層 (Frontend)                      │
                    │                     Port: 8010                             │
                    └─────────────────────────────────────────────────────────────┘
                                                  │
                    ┌─────────────────────────────────────────────────────────────┐
                    │            HTML 模板: ft_summary_ui.html                    │
                    │  ┌─────────────────────────────────────────────────────────┐ │
                    │  │  • 資料夾路徑輸入框                                     │ │
                    │  │  • 處理模式選擇: 完整模式/快速模式                       │ │
                    │  │  • 服務狀態顯示                                         │ │
                    │  │  • 進度條顯示                                           │ │
                    │  │  • 結果統計顯示                                         │ │
                    │  │  • 下載連結                                             │ │
                    │  └─────────────────────────────────────────────────────────┘ │
                    └─────────────────────────────────────────────────────────────┘
                                                  │
                    ┌─────────────────────────────────────────────────────────────┐
                    │         JavaScript: ft-summary-processor.js                 │
                    │  ┌─────────────────────────────────────────────────────────┐ │
                    │  │  class FTSummaryProcessor {                             │ │
                    │  │    • checkServiceStatus()    - 檢查服務狀態            │ │
                    │  │    • validateInput()         - 驗證輸入                │ │
                    │  │    • startProcessing()       - 開始處理                │ │
                    │  │    • showProcessing()        - 顯示進度                │ │
                    │  │    • showResults()           - 顯示結果                │ │
                    │  │  }                                                      │ │
                    │  └─────────────────────────────────────────────────────────┘ │
                    └─────────────────────────────────────────────────────────────┘
                                                  │
                                              HTTP API
                                                  │
                    ┌─────────────────────────────────────────────────────────────┐
                    │                    API 層 (Backend)                        │
                    │                   FastAPI Service                          │
                    └─────────────────────────────────────────────────────────────┘
                                                  │
                    ┌─────────────────────────────────────────────────────────────┐
                    │                ft_eqc_api.py - 核心 API 服務               │
                    │  ┌─────────────────────────────────────────────────────────┐ │
                    │  │  主要端點:                                              │ │
                    │  │  • GET  /ft-summary-ui        - 提供 Web 界面          │ │
                    │  │  • POST /api/process_ft_summary - 處理 FT Summary      │ │
                    │  │  • GET  /api/ft-summary-status - 檢查服務狀態          │ │
                    │  │                                                         │ │
                    │  │  關鍵功能:                                              │ │
                    │  │  • 服務狀態檢查                                         │ │
                    │  │  • 模板渲染                                             │ │
                    │  │  • 異步處理調用                                         │ │
                    │  │  • 錯誤處理                                             │ │
                    │  └─────────────────────────────────────────────────────────┘ │
                    └─────────────────────────────────────────────────────────────┘
                                                  │
                                              調用
                                                  │
                    ┌─────────────────────────────────────────────────────────────┐
                    │              核心處理器 - 批量處理引擎                      │
                    │            batch_csv_to_excel_processor.py                  │
                    └─────────────────────────────────────────────────────────────┘
                                                  │
                    ┌─────────────────────────────────────────────────────────────┐
                    │  class BatchCsvToExcelProcessor - 主要處理類                │
                    │  ┌─────────────────────────────────────────────────────────┐ │
                    │  │  核心方法:                                              │ │
                    │  │  • __init__()                 - 初始化處理器           │ │
                    │  │  • scan_csv_files()           - 掃描 CSV 檔案          │ │
                    │  │  • process_folder()           - 批量處理資料夾          │ │
                    │  │  • process_single_csv()       - 單一 CSV 處理          │ │
                    │  │  • process_with_ft_summary()  - FT Summary 處理        │ │
                    │  │  • consolidate_summaries()    - 整合 Summary 結果      │ │
                    │  │  • generate_eqc_all_pass_file() - 生成 EQC 全通過檔案 │ │
                    │  │                                                         │ │
                    │  │  處理流程:                                              │ │
                    │  │  1. CTA 前置處理                                        │ │
                    │  │  2. CSV 檔案掃描和分類                                  │ │
                    │  │  3. MD5 去重複                                          │ │
                    │  │  4. 並行處理 (FT/EQC)                                   │ │
                    │  │  5. Summary 整合                                        │ │
                    │  │  6. 結果返回                                            │ │
                    │  └─────────────────────────────────────────────────────────┘ │
                    └─────────────────────────────────────────────────────────────┘
                                                  │
                                              依賴
                                                  │
                    ┌─────────────────────────────────────────────────────────────┐
                    │                    底層處理器群組                           │
                    │                   (Infrastructure)                          │
                    └─────────────────────────────────────────────────────────────┘
                                                  │
                ┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
                │                 │                 │                 │                 │
        ┌───────────────┐ ┌───────────────┐ ┌───────────────┐ ┌───────────────┐ ┌───────────────┐
        │  CTA 處理器   │ │ CSV 轉換器    │ │ FT Summary    │ │ EQC 處理器    │ │ 檔案發現器    │
        │               │ │               │ │   生成器      │ │               │ │               │
        └───────────────┘ └───────────────┘ └───────────────┘ └───────────────┘ └───────────────┘
                │                 │                 │                 │                 │
        ┌───────────────┐ ┌───────────────┐ ┌───────────────┐ ┌───────────────┐ ┌───────────────┐
        │CTA Integrated │ │CsvToExcel     │ │FTSummary      │ │OnlineEQCFail  │ │CSVFile        │
        │Processor      │ │Converter      │ │Generator      │ │Processor      │ │Discovery      │
        │               │ │               │ │               │ │               │ │               │
        │• 前置處理     │ │• Excel 轉換   │ │• 統計生成     │ │• 失敗分析     │ │• 檔案分類     │
        │• CTA 解析     │ │• 格式化       │ │• 橫向整合     │ │• 全通過檔案   │ │• 類型檢測     │
        │• Data11生成   │ │• 樣式設定     │ │• 表格生成     │ │• 狀態檢查     │ │• 排序去重     │
        └───────────────┘ └───────────────┘ └───────────────┘ └───────────────┘ └───────────────┘

===============================================================================
                            系統運作流程
===============================================================================

1. 【用戶操作】
   用戶訪問 http://127.0.0.1:8010/ft-summary-ui#
   ↓
   
2. 【前端載入】
   • ft_eqc_api.py 提供 HTML 模板
   • 載入 ft-summary-processor.js
   • 檢查服務狀態 (/api/ft-summary-status)
   ↓
   
3. 【用戶輸入】
   • 輸入資料夾路徑
   • 選擇處理模式 (完整模式/快速模式)
   • 點擊「開始批量處理」
   ↓
   
4. 【API 調用】
   JavaScript 發送 POST 請求到 /api/process_ft_summary
   ↓
   
5. 【後端處理】
   ft_eqc_api.py 接收請求並調用 BatchCsvToExcelProcessor
   ↓
   
6. 【批量處理】
   BatchCsvToExcelProcessor 執行以下步驟：
   
   a) CTA 前置處理
      • 搜尋並處理 CTA 檔案
      • 生成 Data11 格式 CSV
   
   b) CSV 檔案掃描
      • 掃描資料夾中所有 CSV 檔案
      • 排[EXCEPT_CHAR]關鍵字過濾
      • MD5 去重複
   
   c) 檔案分類處理
      • FT 檔案: 執行 Excel 轉換 + FT Summary 生成
      • EQC 檔案: 執行 Excel 轉換 + EQC 狀態檢查
   
   d) Summary 整合
      • 合併所有 FT Summary 檔案
      • 生成 EQC 全通過檔案
   
   e) 結果返回
      • 統計資訊: 總檔案數、成功數、失敗數
      • 輸出檔案路徑
      • 處理時間
   ↓
   
7. 【結果展示】
   • 前端接收處理結果
   • 更新進度條和統計資訊
   • 提供下載連結
   • 顯示處理日誌

===============================================================================
                            關鍵技術特點
===============================================================================

【架構特色】
• 前後端分離: HTML/JS + FastAPI
• 微服務架構: 模組化處理器
• 異步處理: 支援大批量檔案處理
• 錯誤恢復: 完整的錯誤處理機制

【效能優化】
• MD5 去重複: 避免重複處理相同檔案
• 批量處理: 提高檔案處理效率
• 智能分類: 根據檔案類型選擇處理策略
• 記憶體管理: 適當的資源釋放

【處理模式】
• 完整模式: Excel 轉換 + Summary 生成
• 快速模式: 僅 Summary 生成（節省時間）
• CTA 整合: 自動處理 CTA 前置格式

【檔案支援】
• FT 檔案: 執行完整的 FT Summary 流程
• EQC 檔案: 失敗分析和全通過檔案生成
• CTA 檔案: 前置處理和格式轉換
• 批量去重: MD5 檢查避免重複處理

===============================================================================
                            核心程式說明
===============================================================================

【程式 1: ft_eqc_api.py】
角色: API 服務端點和 Web 界面提供者
功能:
• 提供 RESTful API 端點
• 渲染 HTML 模板
• 處理 HTTP 請求和回應
• 調用批量處理器
• 錯誤處理和狀態管理

【程式 2: batch_csv_to_excel_processor.py】
角色: 核心批量處理引擎
功能:
• 檔案掃描和分類
• CSV 到 Excel 轉換
• FT Summary 生成
• EQC 狀態分析
• 檔案整合和輸出

【協作關係】
ft_eqc_api.py 作為前端接口，接收用戶請求後調用 
batch_csv_to_excel_processor.py 執行實際的批量處理工作，
然後將結果返回給前端進行展示。

===============================================================================
                                 結論
===============================================================================

此系統是一個完整的 CSV 到 Excel 批量處理系統，採用現代化的 Web 架構，
支援多種檔案類型和處理模式，具有良好的錯誤處理和用戶體驗。
兩個核心程式通過 API 調用協作，實現了高效的批量檔案處理功能。

===============================================================================