# 開發命令與工作流程

## 虛擬環境（強制第一步）
```bash
source venv/bin/activate     # Linux/Mac
venv\Scripts\activate        # Windows
which python                 # 確認虛擬環境
```

## 核心開發命令
```bash
# 安裝依賴
pip install -r requirements.txt

# 測試執行
pytest tests/ -v
python tools/run_pytest_rag.py --offline

# 程式碼品質檢查  
black src/ tests/
flake8 src/ tests/
mypy src/

# 主要入口點
python main.py                           # 主程式
python cta_processor.py                  # CTA處理CLI
python eqc_standard_processor.py         # EQC標準處理
python api_integration.py               # API整合服務
python start_ft_summary_service.py      # FT摘要服務
```

## 測試策略 (TDD)
1. **Red**: 先寫失敗的測試
2. **Green**: 寫最少程式碼讓測試通過  
3. **Refactor**: 重構程式碼
4. **Program Test**: 實際執行驗證

## Make命令
```bash
make help              # 顯示所有可用命令
make install           # 安裝開發依賴
make test             # 執行所有測試
make quality-check    # 程式碼品質檢查
make clean            # 清理暫存檔案
```