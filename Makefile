# Outlook Summary System - 開發輔助命令

.PHONY: help install test lint format type-check clean dev-setup quality-check all-tests

# 變數定義
VENV = venv
PYTHON = $(VENV)/bin/python
PIP = $(VENV)/bin/pip
PYTEST = $(VENV)/bin/pytest
BLACK = $(VENV)/bin/black
MYPY = $(VENV)/bin/mypy
FLAKE8 = $(VENV)/bin/flake8

help: ## 顯示幫助資訊
	@echo "可用命令："
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install: ## 安裝開發依賴
	@echo "🔧 安裝開發依賴套件..."
	$(PIP) install -r requirements-dev.txt

test: ## 執行所有測試
	@echo "🧪 執行測試..."
	$(PYTEST)

test-unit: ## 執行單元測試
	@echo "🧪 執行單元測試..."
	$(PYTEST) tests/unit/

test-integration: ## 執行整合測試
	@echo "🧪 執行整合測試..."
	$(PYTEST) tests/integration/

test-e2e: ## 執行端對端測試
	@echo "🧪 執行端對端測試..."
	$(PYTEST) tests/e2e/

test-watch: ## 監控模式執行測試
	@echo "👀 監控模式執行測試..."
	$(PYTEST) --looponfail

format: ## 格式化程式碼
	@echo "🎨 格式化程式碼..."
	$(BLACK) src/ tests/

format-check: ## 檢查程式碼格式
	@echo "🎨 檢查程式碼格式..."
	$(BLACK) --check src/ tests/

lint: ## 執行 linting
	@echo "🔍 執行 linting..."
	$(FLAKE8) src/ tests/

type-check: ## 執行型別檢查
	@echo "🔍 執行型別檢查..."
	$(MYPY) src/

quality-check: format-check lint type-check ## 執行所有程式碼品質檢查
	@echo "✅ 程式碼品質檢查完成"

all-tests: test quality-check ## 執行所有測試和品質檢查
	@echo "✅ 所有檢查完成"

clean: ## 清理暫存檔案
	@echo "🧹 清理暫存檔案..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .pytest_cache/
	rm -rf htmlcov/
	rm -rf .coverage
	rm -rf .mypy_cache/
	rm -rf dist/
	rm -rf build/

dev-setup: ## 設定開發環境
	@echo "🚀 設定開發環境..."
	python3 -m venv $(VENV)
	$(PIP) install --upgrade pip
	$(PIP) install -r requirements-dev.txt
	@echo "✅ 開發環境設定完成"

# TDD 工作流程命令
tdd-red: ## TDD Red 階段 - 寫失敗的測試
	@echo "🔴 TDD Red 階段 - 執行測試（預期失敗）"
	$(PYTEST) --tb=short -x || true

tdd-green: ## TDD Green 階段 - 讓測試通過
	@echo "🟢 TDD Green 階段 - 執行測試（預期通過）"
	$(PYTEST) --tb=short

tdd-refactor: ## TDD Refactor 階段 - 重構程式碼
	@echo "🔵 TDD Refactor 階段 - 重構並確保測試通過"
	$(MAKE) format
	$(MAKE) quality-check
	$(PYTEST) --tb=short

# 程式測試驗證（後端程式碼強制要求）
program-test: ## 執行程式測試驗證
	@echo "🧪 執行程式測試驗證..."
	$(PYTHON) -m src.infrastructure.config.settings --test
	@echo "✅ 程式測試驗證完成"