# task_queue.py

任務佇列介面模組，定義任務佇列管理的抽象介面，支援非同步處理和錯誤恢復。

## TaskStatus

任務狀態枚舉類別，定義任務的各種狀態。

### 枚舉值
- `PENDING`: 等待處理
- `PROCESSING`: 處理中
- `COMPLETED`: 已完成
- `FAILED`: 處理失敗
- `RETRYING`: 重試中
- `CANCELLED`: 已取消

## TaskPriority

任務優先級枚舉類別，定義任務的優先級別。

### 枚舉值
- `LOW`: 低優先級 (1)
- `MEDIUM`: 中等優先級 (2)
- `HIGH`: 高優先級 (3)
- `URGENT`: 緊急優先級 (4)

## TaskInfo

任務資訊資料類別，包含任務的完整資訊。

### 屬性
- `task_id` (str): 任務 ID
- `email_data` (EmailData): 郵件數據
- `status` (TaskStatus): 任務狀態
- `priority` (TaskPriority): 任務優先級
- `created_at` (float): 建立時間戳
- `started_at` (Optional[float]): 開始時間戳，可選
- `completed_at` (Optional[float]): 完成時間戳，可選
- `retry_count` (int): 重試次數，預設為0
- `max_retries` (int): 最大重試次數，預設為3
- `error_message` (Optional[str]): 錯誤訊息，可選
- `result` (Optional[Dict[str, Any]]): 處理結果，可選

## QueueConfig

佇列配置資料類別，用於設定佇列的各種參數。

### 屬性
- `max_queue_size` (int): 最大佇列大小，預設為1000
- `max_workers` (int): 最大工作者數量，預設為5
- `retry_delay` (float): 重試延遲時間（秒），預設為60.0
- `max_retry_attempts` (int): 最大重試次數，預設為3
- `task_timeout` (float): 任務超時時間（秒），預設為300.0
- `enable_priority` (bool): 是否啟用優先級，預設為True
- `enable_persistence` (bool): 是否啟用持久化，預設為False

## TaskQueue

任務佇列抽象基類，定義任務佇列管理的標準介面。

### enqueue

將郵件處理任務加入佇列的抽象方法。

**參數:**
- `email_data` (EmailData): 郵件數據
- `priority` (TaskPriority): 任務優先級，預設為MEDIUM

**返回值:**
- str: 任務 ID

**異常:**
- 由具體實作決定

### dequeue

從佇列中取出任務的抽象方法。

**返回值:**
- Optional[TaskInfo]: 任務資訊，若佇列為空則返回 None

**異常:**
- 由具體實作決定

### get_task_info

獲取任務資訊的抽象方法。

**參數:**
- `task_id` (str): 任務 ID

**返回值:**
- Optional[TaskInfo]: 任務資訊

**異常:**
- 由具體實作決定

### update_task_status

更新任務狀態的抽象方法。

**參數:**
- `task_id` (str): 任務 ID
- `status` (TaskStatus): 新狀態
- `result` (Optional[Dict[str, Any]]): 處理結果，可選
- `error_message` (Optional[str]): 錯誤訊息，可選

**返回值:**
- bool: 更新成功與否

**異常:**
- 由具體實作決定

### cancel_task

取消任務的抽象方法。

**參數:**
- `task_id` (str): 任務 ID

**返回值:**
- bool: 取消成功與否

**異常:**
- 由具體實作決定

### retry_task

重試任務的抽象方法。

**參數:**
- `task_id` (str): 任務 ID

**返回值:**
- bool: 重試成功與否

**異常:**
- 由具體實作決定

### get_queue_size

獲取佇列大小的抽象方法。

**返回值:**
- int: 佇列中的任務數量

**異常:**
- 由具體實作決定

### get_processing_count

獲取正在處理的任務數量的抽象方法。

**返回值:**
- int: 處理中任務數量

**異常:**
- 由具體實作決定

### get_pending_tasks

獲取待處理任務列表的抽象方法。

**返回值:**
- List[TaskInfo]: 待處理任務列表

**異常:**
- 由具體實作決定

### get_failed_tasks

獲取失敗任務列表的抽象方法。

**返回值:**
- List[TaskInfo]: 失敗任務列表

**異常:**
- 由具體實作決定

### clear_completed_tasks

清理已完成任務的抽象方法。

**返回值:**
- int: 清理的任務數量

**異常:**
- 由具體實作決定

### get_statistics

獲取佇列統計資訊的抽象方法。

**返回值:**
- Dict[str, Any]: 統計資訊字典

**異常:**
- 由具體實作決定

### start_workers

啟動工作者的抽象方法。

**參數:**
- `worker_function` (Callable): 工作者函數

**返回值:**
- None

**異常:**
- 由具體實作決定

### stop_workers

停止所有工作者的抽象方法。

**返回值:**
- None

**異常:**
- 由具體實作決定

### is_running

檢查佇列是否運行中的抽象方法。

**返回值:**
- bool: 是否運行中

**異常:**
- 由具體實作決定
