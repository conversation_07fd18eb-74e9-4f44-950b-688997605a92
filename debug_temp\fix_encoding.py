#!/usr/bin/env python3
"""
修復郵件編碼問題
"""

import email.header
import base64
import quopri
from email.utils import parseaddr

def decode_mime_header(header_value):
    """解碼 MIME 編碼的郵件頭"""
    if not header_value:
        return header_value
        
    try:
        # 解碼 MIME 編碼
        decoded_parts = email.header.decode_header(header_value)
        result = ""
        
        for part, encoding in decoded_parts:
            if isinstance(part, bytes):
                if encoding:
                    try:
                        result += part.decode(encoding)
                    except (UnicodeDecodeError, LookupError):
                        # 如果指定編碼失敗，嘗試常見編碼
                        for fallback_encoding in ['utf-8', 'big5', 'gbk', 'latin-1']:
                            try:
                                result += part.decode(fallback_encoding)
                                break
                            except (UnicodeDecodeError, LookupError):
                                continue
                        else:
                            result += part.decode('utf-8', errors='replace')
                else:
                    result += part.decode('utf-8', errors='replace')
            else:
                result += str(part)
        
        return result.strip()
        
    except Exception as e:
        print(f"解碼失敗: {e}")
        return header_value

def decode_sender(sender_value):
    """解碼發件人信息"""
    if not sender_value:
        return sender_value
        
    try:
        # <AUTHOR> <EMAIL>
        name, email_addr = parseaddr(sender_value)
        
        # 解碼姓名部分
        if name:
            decoded_name = decode_mime_header(name)
            if email_addr:
                return f"{decoded_name} <{email_addr}>"
            else:
                return decoded_name
        else:
            return email_addr or sender_value
            
    except Exception as e:
        print(f"解碼發件人失敗: {e}")
        return sender_value

# 測試當前的編碼問題
test_subjects = [
    "=?utf-8?b?5ris6Kmm6YO15Lu2ICMzIC0gMjAyNS0wNy0wOSAyMzoyNDo1OA==?=",
    "=?big5?B?Rlc6IKRVpMiv+ajTpEZ+wee2SyAoIDek6zmk6SAxMDozMLLOrXChQb3QsLIyMA==?=\n\t=?big5?B?pEihQaS9pVg0pEgp?="
]

test_senders = [
    "<EMAIL>",
    "\"Tong.Wu\" <<EMAIL>>"
]

if __name__ == "__main__":
    print("=== 測試郵件編碼解碼 ===")
    
    print("\n[E_MAIL] 測試主題解碼:")
    for i, subject in enumerate(test_subjects, 1):
        print(f"原始 {i}: {subject}")
        decoded = decode_mime_header(subject)
        print(f"解碼 {i}: {decoded}")
        print()
    
    print("\n[BUST_IN_SILHOUETTE] 測試發件人解碼:")
    for i, sender in enumerate(test_senders, 1):
        print(f"原始 {i}: {sender}")
        decoded = decode_sender(sender)
        print(f"解碼 {i}: {decoded}")
        print()