# network_models.py

網路共享瀏覽器資料模型模組，定義網路檔案操作相關的資料模型。

## NetworkFileInfo

網路檔案資訊模型，繼承自 BaseModel，用於表示網路檔案的基本資訊。

### 屬性
- `filename` (str): 檔案名稱
- `size` (int): 檔案大小（位元組）
- `size_mb` (float): 檔案大小（MB）
- `modified_time` (str): 修改時間
- `file_type` (str): 檔案類型
- `is_directory` (bool): 是否為目錄，預設為False

## NetworkFileListResponse

檔案列表回應模型，繼承自 BaseModel，用於回應檔案列表請求。

### 屬性
- `status` (str): 回應狀態
- `path` (str): 路徑
- `files` (List[NetworkFileInfo]): 檔案資訊列表
- `total_count` (int): 總檔案數量
- `total_size_mb` (float): 總檔案大小（MB）

## NetworkPathValidateRequest

路徑驗證請求模型，繼承自 BaseModel，用於驗證網路路徑。

### 屬性
- `path` (str): 要驗證的路徑

## NetworkPathValidateResponse

路徑驗證回應模型，繼承自 BaseModel，用於回應路徑驗證結果。

### 屬性
- `status` (str): 回應狀態
- `valid` (bool): 路徑是否有效
- `path` (str): 驗證的路徑
- `message` (str): 驗證訊息
- `accessible` (bool): 是否可存取，預設為False

## NetworkCredentials

網路認證資訊模型，繼承自 BaseModel，用於儲存網路連接的認證資訊。

### 屬性
- `username` (str): 使用者名稱
- `password` (str): 密碼
- `domain` (str): 網域，預設為空字串
- `server` (str): 伺服器
- `share` (str): 共享名稱

## NetworkConnectRequest

網路連接請求模型，繼承自 BaseModel，用於請求建立網路連接。

### 屬性
- `path` (str): 連接路徑
- `username` (str): 使用者名稱
- `password` (str): 密碼
- `domain` (str): 網域，預設為空字串

## NetworkConnectResponse

網路連接回應模型，繼承自 BaseModel，用於回應網路連接結果。

### 屬性
- `status` (str): 回應狀態
- `connected` (bool): 是否連接成功
- `message` (str): 連接訊息
- `mount_point` (str): 掛載點，預設為空字串
