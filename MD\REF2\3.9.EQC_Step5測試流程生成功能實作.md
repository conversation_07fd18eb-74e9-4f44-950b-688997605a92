# 3.9 EQC Step5 測試流程生成功能實作

## 功能概述

Step 5 測試流程生成功能是 EQC 處理流程的最終階段，專注於**簡化版本的數據行重新排列**，完全保留原始 CSV 格式，不新增任何額外欄位。

### 核心功能特點
- **零額外欄位設計**：不新增 Group_ID、Test_Stage、Original_Row 等欄位
- **完全保留原格式**：維持所有原始 CSV 欄位和格式
- **純數據重排**：僅進行測試流程相關的數據行重新排列
- **格式一致性**：輸出檔案與輸入檔案格式完全相同

## 核心轉換邏輯

### 處理流程示例
```
輸入格式：FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)
處理邏輯：
  1. 識別 FT 基準行：第14行 (15-1=14)
  2. 識別 OnlineEQC 行：第15行
  3. 識別 RT 對應行：第37行
  
輸出排列：
  第14行：FT 基準數據（完全保留原格式）
  第15行：OnlineEQC 數據（完全保留原格式）
  第37行：RT 對應數據（完全保留原格式）
```

### 轉換規則
```python
def generate_test_flow_sequence(fail_analysis_result):
    """
    根據 FAIL 分析結果生成測試流程序列
    
    Args:
        fail_analysis_result: "FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)"
    
    Returns:
        sequence: [14, 15, 37]  # FT基準 + OnlineEQC + RT
    """
    # 解析 FAIL 結果
    eqc_row = extract_eqc_row_number(fail_analysis_result)  # 15
    rt_row = extract_rt_row_number(fail_analysis_result)    # 37
    ft_base_row = eqc_row - 1                               # 14
    
    return [ft_base_row, eqc_row, rt_row]
```

## 檔案格式規範

### 輸入檔案：EQCTOTALDATA.csv
```csv
# 標頭區域（第1-13行）
Header_Row_1,...
Header_Row_2,...
...
Header_Row_13,...

# 數據區域（第14行開始）
FT_Data_Row_1,...     # 第14行
OnlineEQC_Data_Row_1,... # 第15行
...
RT_Data_Row_1,...     # 第37行
```

### 輸出檔案：EQCTOTALDATA_Step5_TestFlow.csv
```csv
# 完全複製的標頭區域（第1-13行）
Header_Row_1,...
Header_Row_2,...
...
Header_Row_13,...

# 重新排列的數據區域
FT_Data_Row_1,...     # 原第14行，保持完全相同格式
OnlineEQC_Data_Row_1,... # 原第15行，保持完全相同格式
RT_Data_Row_1,...     # 原第37行，保持完全相同格式
```

## 技術實作方案

### 核心處理類別
```python
class EQCStep5TestFlowProcessor:
    """EQC Step5 測試流程生成處理器"""
    
    def __init__(self, input_file_path: str):
        self.input_file_path = input_file_path
        self.output_file_path = self._generate_output_path()
        self.fail_analysis_results = []
    
    def process_test_flow_generation(self) -> str:
        """
        執行測試流程生成處理
        
        Returns:
            str: 輸出檔案路徑
        """
        # 1. 讀取原始 CSV 檔案
        df = pd.read_csv(self.input_file_path)
        
        # 2. 分離標頭和數據區域
        header_rows = df.iloc[:13].copy()  # 第1-13行標頭
        data_rows = df.iloc[13:].copy()    # 第14行開始的數據
        
        # 3. 獲取 FAIL 分析結果
        fail_results = self._get_fail_analysis_results()
        
        # 4. 生成測試流程序列
        test_flow_sequences = []
        for fail_result in fail_results:
            sequence = self._generate_test_flow_sequence(fail_result)
            test_flow_sequences.extend(sequence)
        
        # 5. 重新排列數據行
        reordered_data = self._reorder_data_rows(data_rows, test_flow_sequences)
        
        # 6. 合併標頭和重排數據
        final_df = pd.concat([header_rows, reordered_data], ignore_index=True)
        
        # 7. 輸出到新檔案
        final_df.to_csv(self.output_file_path, index=False)
        
        return self.output_file_path
    
    def _generate_test_flow_sequence(self, fail_result: str) -> List[int]:
        """
        根據 FAIL 分析結果生成測試流程序列
        
        Args:
            fail_result: "FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)"
        
        Returns:
            List[int]: 測試流程序列 [FT基準行, OnlineEQC行, RT行]
        """
        import re
        
        # 解析 OnlineEQC 行號
        eqc_match = re.search(r'(\d+)\(OnlineEQC\)', fail_result)
        eqc_row = int(eqc_match.group(1)) if eqc_match else None
        
        # 解析 RT 行號
        rt_match = re.search(r'(\d+)\(RT\)', fail_result)
        rt_row = int(rt_match.group(1)) if rt_match else None
        
        # 計算 FT 基準行（OnlineEQC 前一行）
        ft_base_row = eqc_row - 1 if eqc_row else None
        
        if all([ft_base_row, eqc_row, rt_row]):
            # 轉換為 DataFrame 索引（減去標頭行數）
            ft_index = ft_base_row - 14  # 第14行在數據區域的索引為0
            eqc_index = eqc_row - 14
            rt_index = rt_row - 14
            
            return [ft_index, eqc_index, rt_index]
        
        return []
    
    def _reorder_data_rows(self, data_rows: pd.DataFrame, 
                          sequences: List[int]) -> pd.DataFrame:
        """
        根據序列重新排列數據行
        
        Args:
            data_rows: 原始數據行
            sequences: 重排序列索引
        
        Returns:
            pd.DataFrame: 重新排列的數據行
        """
        reordered_rows = []
        
        for index in sequences:
            if 0 <= index < len(data_rows):
                # 完全複製原始行，保持所有格式
                original_row = data_rows.iloc[index].copy()
                reordered_rows.append(original_row)
        
        return pd.DataFrame(reordered_rows)
    
    def _get_fail_analysis_results(self) -> List[str]:
        """
        獲取 FAIL 分析結果
        
        Returns:
            List[str]: FAIL 分析結果列表
        """
        # 從 Step 3 結果檔案讀取或直接輸入
        # 這裡假設已有 FAIL 分析結果
        return [
            "FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)",
            "FAIL #2: 16(OnlineEQC) [LEFT_RIGHT_ARROW] 38(RT)",
            # 更多 FAIL 結果...
        ]
    
    def _generate_output_path(self) -> str:
        """生成輸出檔案路徑"""
        base_name = os.path.splitext(os.path.basename(self.input_file_path))[0]
        output_name = f"{base_name}_Step5_TestFlow.csv"
        return os.path.join(os.path.dirname(self.input_file_path), output_name)
```

### 使用範例
```python
def main():
    """Step 5 測試流程生成主程式"""
    
    # 初始化處理器
    processor = EQCStep5TestFlowProcessor(
        input_file_path="doc/20250523/EQCTOTALDATA.csv"
    )
    
    # 執行處理
    output_file = processor.process_test_flow_generation()
    
    # 輸出結果
    print(f"[OK] Step 5 測試流程生成完成")
    print(f"[FILE_FOLDER] 輸出檔案：{output_file}")
    
    # 驗證結果
    verify_output_format(output_file)

def verify_output_format(output_file: str):
    """驗證輸出檔案格式"""
    df = pd.read_csv(output_file)
    
    print(f"[CHART] 檔案總行數：{len(df)}")
    print(f"[BOARD] 標頭行數：13")
    print(f"[UP] 數據行數：{len(df) - 13}")
    print(f"[TOOL] 欄位數量：{len(df.columns)}")
    print("[OK] 格式驗證完成")

if __name__ == "__main__":
    main()
```

## 與 Step 1-4 整合

### 整合流程圖
```
Step 1: 基礎統計處理
    ↓
Step 2: 進階分析處理
    ↓
Step 3: FAIL 分析與檢測
    ↓
Step 4: 整合處理與驗證
    ↓
Step 5: 測試流程生成 (本階段)
    ↓
最終輸出: EQCTOTALDATA_Step5_TestFlow.csv
```

### 數據流向
```python
class EQCIntegratedProcessor:
    """EQC 整合處理器"""
    
    def run_complete_pipeline(self, input_file: str) -> str:
        """執行完整的 EQC 處理流程"""
        
        # Step 1-4 處理
        step4_output = self.run_steps_1_to_4(input_file)
        
        # Step 5 處理
        step5_processor = EQCStep5TestFlowProcessor(step4_output)
        final_output = step5_processor.process_test_flow_generation()
        
        return final_output
```

## TDD 開發流程

### 1. 測試案例設計
```python
import pytest
import pandas as pd
from unittest.mock import Mock, patch

class TestEQCStep5TestFlowProcessor:
    """EQC Step5 測試流程生成處理器測試"""
    
    def test_generate_test_flow_sequence_basic(self):
        """測試基本測試流程序列生成"""
        processor = EQCStep5TestFlowProcessor("test_input.csv")
        
        # 測試數據
        fail_result = "FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)"
        
        # 執行
        sequence = processor._generate_test_flow_sequence(fail_result)
        
        # 驗證
        expected = [1, 2, 24]  # 14-13=1, 15-13=2, 37-13=24 (數據區域索引)
        assert sequence == expected
    
    def test_reorder_data_rows_preserves_format(self):
        """測試數據行重排保持原格式"""
        processor = EQCStep5TestFlowProcessor("test_input.csv")
        
        # 創建測試數據
        test_data = pd.DataFrame({
            'Column1': ['A', 'B', 'C', 'D', 'E'],
            'Column2': [1, 2, 3, 4, 5],
            'Column3': ['X', 'Y', 'Z', 'W', 'V']
        })
        
        # 執行重排
        sequences = [0, 2, 4]  # 取第1、3、5行
        result = processor._reorder_data_rows(test_data, sequences)
        
        # 驗證格式完全保持
        assert list(result['Column1']) == ['A', 'C', 'E']
        assert list(result['Column2']) == [1, 3, 5]
        assert list(result['Column3']) == ['X', 'Z', 'V']
        assert len(result.columns) == len(test_data.columns)
    
    def test_process_test_flow_generation_complete(self):
        """測試完整的測試流程生成處理"""
        # 創建測試檔案
        test_input_file = "test_eqctotaldata.csv"
        self._create_test_input_file(test_input_file)
        
        # 執行處理
        processor = EQCStep5TestFlowProcessor(test_input_file)
        with patch.object(processor, '_get_fail_analysis_results') as mock_fail:
            mock_fail.return_value = ["FAIL #1: 15(OnlineEQC) [LEFT_RIGHT_ARROW] 37(RT)"]
            
            output_file = processor.process_test_flow_generation()
        
        # 驗證輸出檔案
        assert os.path.exists(output_file)
        output_df = pd.read_csv(output_file)
        
        # 驗證格式保持
        assert len(output_df.columns) > 0
        assert len(output_df) >= 13  # 至少包含標頭
        
        # 清理測試檔案
        os.remove(test_input_file)
        os.remove(output_file)
    
    def _create_test_input_file(self, filename: str):
        """創建測試輸入檔案"""
        # 創建包含標頭和數據的測試檔案
        test_data = []
        
        # 標頭行 (1-13)
        for i in range(13):
            test_data.append([f"Header_{i+1}", f"Value_{i+1}"])
        
        # 數據行 (14+)
        for i in range(40):
            test_data.append([f"Data_{i+14}", f"Value_{i+14}"])
        
        df = pd.DataFrame(test_data, columns=['Column1', 'Column2'])
        df.to_csv(filename, index=False)
```

### 2. 實作驗證流程
```python
def test_step5_processor_integration():
    """整合測試：驗證 Step5 與前置步驟的整合"""
    
    # Red: 先寫失敗測試
    processor = EQCStep5TestFlowProcessor("nonexistent.csv")
    
    with pytest.raises(FileNotFoundError):
        processor.process_test_flow_generation()
    
    # Green: 實作最小功能讓測試通過
    # (實作 EQCStep5TestFlowProcessor 類別)
    
    # Refactor: 優化程式碼結構
    # (重構為更清晰的模組化設計)
```

### 3. 程式測試驗證
```python
def run_program_test():
    """實際程式執行測試"""
    
    # 準備真實數據
    input_file = "doc/20250523/EQCTOTALDATA.csv"
    
    # 執行處理
    processor = EQCStep5TestFlowProcessor(input_file)
    output_file = processor.process_test_flow_generation()
    
    # 驗證結果
    assert os.path.exists(output_file)
    
    # 格式驗證
    output_df = pd.read_csv(output_file)
    input_df = pd.read_csv(input_file)
    
    # 驗證欄位數量相同
    assert len(output_df.columns) == len(input_df.columns)
    
    # 驗證標頭完全相同
    assert output_df.iloc[:13].equals(input_df.iloc[:13])
    
    print("[OK] 程式測試驗證通過")
```

## 效能考量

### 記憶體優化
```python
def process_large_file_optimized(self, chunk_size: int = 1000):
    """
    大檔案處理記憶體優化版本
    
    Args:
        chunk_size: 每次處理的行數
    """
    # 使用 chunk 方式處理大檔案
    chunk_iterator = pd.read_csv(self.input_file_path, chunksize=chunk_size)
    
    processed_chunks = []
    for chunk in chunk_iterator:
        processed_chunk = self._process_chunk(chunk)
        processed_chunks.append(processed_chunk)
    
    # 合併所有處理過的 chunk
    final_df = pd.concat(processed_chunks, ignore_index=True)
    final_df.to_csv(self.output_file_path, index=False)
```

### 處理時間監控
```python
import time
from functools import wraps

def monitor_processing_time(func):
    """處理時間監控裝飾器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        processing_time = end_time - start_time
        print(f"[TIME] {func.__name__} 處理時間：{processing_time:.2f} 秒")
        
        return result
    return wrapper
```

## 總結

EQC Step5 測試流程生成功能專注於**簡化版本的數據重排**，核心特點：

1. **零額外欄位**：完全保留原始 CSV 格式
2. **純數據重排**：僅進行測試流程相關的行重新排列
3. **格式一致性**：輸入與輸出格式完全相同
4. **整合完整**：與 Step 1-4 無縫整合

透過 TDD 開發流程確保功能的正確性和可維護性，並提供完整的測試覆蓋和程式驗證機制。

---

**檔案位置**：`/mnt/d/project/python/outlook_summary/MD/3.8.EQC_Step5測試流程生成功能實作.md`  
**建立時間**：2025-06-11  
**狀態**：[OK] 完成 - 技術文檔已建立