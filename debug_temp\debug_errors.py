#!/usr/bin/env python3
"""
錯誤診斷腳本
檢查常見的 404 和 500 錯誤
"""

import requests
import sys
import json
from datetime import datetime

def test_endpoints():
    """測試所有端點"""
    
    print("[SEARCH] 開始錯誤診斷...")
    print("=" * 50)
    
    # Flask 服務端點
    flask_endpoints = [
        ('/', 'GET', '首頁'),
        ('/favicon.ico', 'GET', 'Favicon'),
        ('/static/css/email_inbox.css', 'GET', 'CSS 文件'),
        ('/static/js/email_inbox.js', 'GET', 'JavaScript 文件'),
        ('/api/statistics', 'GET', '統計 API'),
        ('/api/senders', 'GET', '寄件者 API'),
        ('/api/sync', 'POST', '同步 API'),
        ('/api/sync/status', 'GET', '同步狀態 API'),
    ]
    
    # FastAPI 服務端點
    fastapi_endpoints = [
        ('http://localhost:8010/', 'GET', 'FastAPI 根路由'),
        ('http://localhost:8010/health', 'GET', 'FastAPI 健康檢查'),
        ('http://localhost:8010/docs', 'GET', 'FastAPI 文檔'),
        ('http://localhost:8010/ui', 'GET', 'FT-EQC 介面'),
    ]
    
    print("[E_MAIL] 測試 Flask 服務 (localhost:5000)")
    print("-" * 30)
    
    for endpoint, method, description in flask_endpoints:
        url = f"http://localhost:5000{endpoint}"
        try:
            if method == 'GET':
                response = requests.get(url, timeout=5)
            elif method == 'POST':
                response = requests.post(url, json={}, timeout=5)
            
            status_code = response.status_code
            if status_code == 200:
                print(f"[OK] {description}: {status_code}")
            elif status_code == 404:
                print(f"[ERROR] {description}: {status_code} - 資源不存在")
            elif status_code == 500:
                print(f"[COLLISION] {description}: {status_code} - 伺服器錯誤")
                try:
                    error_info = response.json()
                    print(f"   錯誤詳情: {error_info}")
                except:
                    print(f"   錯誤內容: {response.text[:200]}")
            else:
                print(f"[WARNING] {description}: {status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"[ERROR] {description}: 連接失敗")
        except requests.exceptions.Timeout:
            print(f"[ERROR] {description}: 請求超時")
        except Exception as e:
            print(f"[ERROR] {description}: {str(e)}")
    
    print("\n[ROCKET] 測試 FastAPI 服務 (localhost:8010)")
    print("-" * 30)
    
    for url, method, description in fastapi_endpoints:
        try:
            if method == 'GET':
                response = requests.get(url, timeout=5)
            
            status_code = response.status_code
            if status_code == 200:
                print(f"[OK] {description}: {status_code}")
            elif status_code == 404:
                print(f"[ERROR] {description}: {status_code} - 資源不存在")
            elif status_code == 500:
                print(f"[COLLISION] {description}: {status_code} - 伺服器錯誤")
            else:
                print(f"[WARNING] {description}: {status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"[ERROR] {description}: 連接失敗")
        except requests.exceptions.Timeout:
            print(f"[ERROR] {description}: 請求超時")
        except Exception as e:
            print(f"[ERROR] {description}: {str(e)}")

def test_sync_functionality():
    """測試同步功能"""
    print("\n[REFRESH] 測試同步功能")
    print("-" * 30)
    
    # 測試同步狀態
    try:
        response = requests.get('http://localhost:5000/api/sync/status', timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"[OK] 同步狀態: {json.dumps(status, indent=2, ensure_ascii=False)}")
        else:
            print(f"[ERROR] 同步狀態 API 失敗: {response.status_code}")
    except Exception as e:
        print(f"[ERROR] 同步狀態檢查失敗: {e}")
    
    # 測試同步執行
    try:
        response = requests.post('http://localhost:5000/api/sync', 
                               json={'max_emails': 1}, 
                               timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"[OK] 同步執行: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"[ERROR] 同步執行失敗: {response.status_code}")
            print(f"   錯誤內容: {response.text}")
    except Exception as e:
        print(f"[ERROR] 同步執行檢查失敗: {e}")

def check_static_files():
    """檢查靜態文件"""
    print("\n[FILE_FOLDER] 檢查靜態文件")
    print("-" * 30)
    
    static_files = [
        '/static/css/email_inbox.css',
        '/static/css/base.css',
        '/static/js/email_inbox.js',
        '/favicon.ico'
    ]
    
    for file_path in static_files:
        try:
            response = requests.get(f'http://localhost:5000{file_path}', timeout=5)
            if response.status_code == 200:
                size = len(response.content)
                print(f"[OK] {file_path}: {response.status_code} ({size} bytes)")
            else:
                print(f"[ERROR] {file_path}: {response.status_code}")
        except Exception as e:
            print(f"[ERROR] {file_path}: {str(e)}")

if __name__ == '__main__':
    print(f"[ONE_OCLOCK] 診斷時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_endpoints()
    test_sync_functionality()
    check_static_files()
    
    print("\n" + "=" * 50)
    print("[TARGET] 診斷完成！")
    print("如果發現 404 錯誤，請檢查靜態文件是否存在")
    print("如果發現 500 錯誤，請檢查伺服器日誌")