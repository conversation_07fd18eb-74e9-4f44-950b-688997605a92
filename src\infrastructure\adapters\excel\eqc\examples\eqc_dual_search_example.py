#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EQC 雙重搜尋機制範例
展示如何利用主要區間和備用區間進行 EQC RT 資料匹配
"""

import logging
from typing import Dict, List, Any, Optional, Tuple

class EQCDualSearchMechanism:
    """
    EQC 雙重搜尋機制
    對應 VBA InsEqcRtData2 的雙重搜尋邏輯
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def perform_dual_search_matching(self, 
                                   eqctotaldata_rows: List[str],
                                   eqc_rt_data: Dict[str, Any],
                                   main_region: Dict[str, Any],
                                   backup_region: Dict[str, Any]) -> Dict[str, Any]:
        """
        執行雙重搜尋機制
        
        Args:
            eqctotaldata_rows: EQCTOTALDATA.csv 的所有行
            eqc_rt_data: 要插入的 EQC RT 資料
            main_region: 主要程式碼區間資訊 (第298-335欄)
            backup_region: 備用程式碼區間資訊 (第1565-1600欄)
            
        Returns:
            Dict: 匹配結果和插入位置資訊
        """
        try:
            self.logger.info("[SEARCH] 開始執行雙重搜尋機制...")
            
            # 第一重搜尋：嘗試主要區間匹配
            main_search_result = self._search_in_main_region(
                eqctotaldata_rows, eqc_rt_data, main_region
            )
            
            if main_search_result['success']:
                self.logger.info("[OK] 主要區間匹配成功")
                return {
                    'search_method': 'main_region',
                    'success': True,
                    'matched_positions': main_search_result['matched_positions'],
                    'insertion_strategy': 'primary_match',
                    'region_used': main_region,
                    'match_confidence': main_search_result['confidence']
                }
            
            # 第二重搜尋：主要區間失敗時使用備用區間
            self.logger.warning("[WARNING] 主要區間匹配失敗，切換到備用區間...")
            
            backup_search_result = self._search_in_backup_region(
                eqctotaldata_rows, eqc_rt_data, backup_region, main_region
            )
            
            if backup_search_result['success']:
                self.logger.info("[OK] 備用區間匹配成功")
                return {
                    'search_method': 'backup_region',
                    'success': True,
                    'matched_positions': backup_search_result['matched_positions'],
                    'insertion_strategy': 'backup_match',
                    'region_used': backup_region,
                    'match_confidence': backup_search_result['confidence'],
                    'fallback_reason': main_search_result['failure_reason']
                }
            
            # 雙重搜尋都失敗
            self.logger.error("[ERROR] 雙重搜尋都失敗")
            return {
                'search_method': 'dual_search_failed',
                'success': False,
                'main_failure_reason': main_search_result['failure_reason'],
                'backup_failure_reason': backup_search_result['failure_reason'],
                'recommendation': 'manual_review_required'
            }
            
        except Exception as e:
            self.logger.error(f"雙重搜尋機制執行失敗: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _search_in_main_region(self, 
                              eqctotaldata_rows: List[str],
                              eqc_rt_data: Dict[str, Any],
                              main_region: Dict[str, Any]) -> Dict[str, Any]:
        """
        在主要區間進行搜尋匹配
        
        Args:
            eqctotaldata_rows: EQCTOTALDATA.csv 的所有行
            eqc_rt_data: 要插入的 EQC RT 資料
            main_region: 主要程式碼區間 (第298-335欄)
        """
        try:
            self.logger.info("[TARGET] 在主要區間進行搜尋...")
            
            # 讀取第8行的欄位名稱 (索引7)
            if len(eqctotaldata_rows) <= 7:
                return {
                    'success': False,
                    'failure_reason': 'insufficient_header_rows'
                }
            
            header_row = eqctotaldata_rows[7].split(',')
            matched_positions = []
            
            # 在主要區間 (第298-335欄) 尋找匹配欄位
            start_col = main_region['start_column'] - 1  # 轉為索引 (297)
            end_col = main_region['end_column']           # 335
            
            for i in range(start_col, min(end_col, len(header_row))):
                column_name = header_row[i].strip()
                column_number = i + 1
                
                # 檢查是否與 EQC RT 資料中的程式碼欄位匹配
                if self._is_matching_program_code(column_name, eqc_rt_data):
                    matched_positions.append({
                        'column_index': i,
                        'column_number': column_number,
                        'column_name': column_name,
                        'region_type': 'main',
                        'match_type': 'exact'
                    })
                    self.logger.info(f"   [OK] 主要區間匹配: 第{column_number}欄 '{column_name}'")
            
            # 評估匹配結果
            if len(matched_positions) >= 5:  # 至少匹配5個欄位才算成功
                confidence = min(100, (len(matched_positions) / 38) * 100)  # 38是主要區間總欄位數
                return {
                    'success': True,
                    'matched_positions': matched_positions,
                    'confidence': confidence,
                    'total_matches': len(matched_positions)
                }
            else:
                return {
                    'success': False,
                    'failure_reason': f'insufficient_matches_in_main_region (only {len(matched_positions)} matches)',
                    'matched_positions': matched_positions
                }
            
        except Exception as e:
            return {
                'success': False,
                'failure_reason': f'main_region_search_error: {str(e)}'
            }
    
    def _search_in_backup_region(self, 
                                eqctotaldata_rows: List[str],
                                eqc_rt_data: Dict[str, Any],
                                backup_region: Dict[str, Any],
                                main_region: Dict[str, Any]) -> Dict[str, Any]:
        """
        在備用區間進行搜尋匹配
        
        Args:
            eqctotaldata_rows: EQCTOTALDATA.csv 的所有行
            eqc_rt_data: 要插入的 EQC RT 資料  
            backup_region: 備用程式碼區間 (第1565-1600欄)
            main_region: 主要程式碼區間 (用於對照)
        """
        try:
            self.logger.info("[REFRESH] 在備用區間進行搜尋...")
            
            header_row = eqctotaldata_rows[7].split(',')
            matched_positions = []
            
            # 在備用區間 (第1565-1600欄) 尋找匹配欄位
            start_col = backup_region['backup_start_column'] - 1  # 轉為索引 (1564)
            end_col = backup_region['backup_end_column']           # 1600
            
            for i in range(start_col, min(end_col, len(header_row))):
                column_name = header_row[i].strip()
                column_number = i + 1
                
                # 檢查是否與 EQC RT 資料中的程式碼欄位匹配
                if self._is_matching_program_code(column_name, eqc_rt_data):
                    matched_positions.append({
                        'column_index': i,
                        'column_number': column_number,
                        'column_name': column_name,
                        'region_type': 'backup',
                        'match_type': 'fallback'
                    })
                    self.logger.info(f"   [OK] 備用區間匹配: 第{column_number}欄 '{column_name}'")
            
            # 評估備用區間匹配結果
            if len(matched_positions) >= 3:  # 備用區間要求較低，至少3個匹配
                confidence = min(100, (len(matched_positions) / 36) * 100)  # 36是備用區間總欄位數
                return {
                    'success': True,
                    'matched_positions': matched_positions,
                    'confidence': confidence,
                    'total_matches': len(matched_positions)
                }
            else:
                return {
                    'success': False,
                    'failure_reason': f'insufficient_matches_in_backup_region (only {len(matched_positions)} matches)',
                    'matched_positions': matched_positions
                }
            
        except Exception as e:
            return {
                'success': False,
                'failure_reason': f'backup_region_search_error: {str(e)}'
            }
    
    def _is_matching_program_code(self, column_name: str, eqc_rt_data: Dict[str, Any]) -> bool:
        """
        檢查欄位名稱是否與 EQC RT 資料中的程式碼匹配
        
        Args:
            column_name: 欄位名稱 (例如: '0X52(w0x00)')
            eqc_rt_data: EQC RT 資料
            
        Returns:
            bool: 是否匹配
        """
        # 這裡實作具體的匹配邏輯
        # 例如檢查是否為程式碼格式：0X開頭的十六進制格式
        if column_name.upper().startswith('0X') and '(' in column_name:
            return True
        
        # 或者檢查是否在 EQC RT 資料的程式碼列表中
        program_codes = eqc_rt_data.get('program_codes', [])
        return column_name in program_codes
    
    def insert_eqc_rt_data_with_dual_search(self,
                                          eqctotaldata_path: str,
                                          eqc_rt_data: Dict[str, Any],
                                          main_region: Dict[str, Any],
                                          backup_region: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用雙重搜尋機制插入 EQC RT 資料
        
        Args:
            eqctotaldata_path: EQCTOTALDATA.csv 檔案路徑
            eqc_rt_data: 要插入的 EQC RT 資料
            main_region: 主要程式碼區間
            backup_region: 備用程式碼區間
            
        Returns:
            Dict: 插入結果
        """
        try:
            # 讀取 EQCTOTALDATA.csv
            with open(eqctotaldata_path, 'r', encoding='utf-8') as f:
                rows = f.readlines()
            
            # 執行雙重搜尋
            search_result = self.perform_dual_search_matching(
                rows, eqc_rt_data, main_region, backup_region
            )
            
            if not search_result['success']:
                return {
                    'status': 'failed',
                    'message': '雙重搜尋機制匹配失敗',
                    'search_result': search_result
                }
            
            # 根據匹配結果插入資料
            insertion_result = self._perform_data_insertion(
                eqctotaldata_path, rows, eqc_rt_data, search_result
            )
            
            return {
                'status': 'success',
                'search_method': search_result['search_method'],
                'region_used': search_result['region_used'],
                'matched_positions': search_result['matched_positions'],
                'insertion_result': insertion_result,
                'confidence': search_result.get('confidence', 0)
            }
            
        except Exception as e:
            self.logger.error(f"雙重搜尋插入失敗: {e}")
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def _perform_data_insertion(self,
                              file_path: str,
                              rows: List[str],
                              eqc_rt_data: Dict[str, Any],
                              search_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        執行實際的資料插入
        
        Args:
            file_path: 檔案路徑
            rows: 檔案行數據
            eqc_rt_data: 要插入的資料
            search_result: 搜尋結果
            
        Returns:
            Dict: 插入結果
        """
        try:
            matched_positions = search_result['matched_positions']
            
            # 根據匹配位置構建新的資料行
            new_data_row = self._build_eqc_rt_data_row(rows, matched_positions, eqc_rt_data)
            
            # 插入到適當的位置（通常是檔案末尾）
            rows.append(new_data_row)
            
            # 寫回檔案
            with open(file_path, 'w', encoding='utf-8') as f:
                f.writelines(rows)
            
            return {
                'success': True,
                'inserted_row': new_data_row.strip(),
                'insertion_method': search_result['search_method'],
                'total_matched_fields': len(matched_positions)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def _build_eqc_rt_data_row(self,
                              rows: List[str],
                              matched_positions: List[Dict[str, Any]],
                              eqc_rt_data: Dict[str, Any]) -> str:
        """
        根據匹配位置構建 EQC RT 資料行
        
        Args:
            rows: 原始檔案行
            matched_positions: 匹配的欄位位置
            eqc_rt_data: EQC RT 資料
            
        Returns:
            str: 構建的新資料行
        """
        # 基於第8行的欄位數量建立空白行
        header_row = rows[7].split(',')
        new_row_data = [''] * len(header_row)
        
        # 填入基本資訊
        new_row_data[0] = eqc_rt_data.get('serial_number', 'EQC_RT_001')  # 序號
        new_row_data[1] = '1'  # BIN
        new_row_data[2] = eqc_rt_data.get('test_time', '2025-06-09')  # 測試時間
        
        # 根據匹配位置填入程式碼資料
        for position in matched_positions:
            col_index = position['column_index']
            col_name = position['column_name']
            
            # 從 EQC RT 資料中找到對應的值
            value = eqc_rt_data.get('program_values', {}).get(col_name, '0')
            new_row_data[col_index] = str(value)
        
        return ','.join(new_row_data) + '\n'


def demonstrate_dual_search_example():
    """展示雙重搜尋機制的簡單例子"""
    
    print("[SEARCH] EQC 雙重搜尋機制範例")
    print("=" * 60)
    
    # 模擬資料設定
    eqc_rt_data = {
        'serial_number': 'EQC_RT_001',
        'test_time': '2025-06-09',
        'program_codes': ['0X52(w0x00)', '0X53(w0x00)', '0X54(w0x00)'],
        'program_values': {
            '0X52(w0x00)': '150',
            '0X53(w0x00)': '75', 
            '0X54(w0x00)': '0'
        }
    }
    
    main_region = {
        'start_column': 298,
        'end_column': 335,
        'range_length': 38
    }
    
    backup_region = {
        'backup_start_column': 1565,
        'backup_end_column': 1600,
        'backup_range_length': 36
    }
    
    # 初始化雙重搜尋機制
    dual_search = EQCDualSearchMechanism()
    
    print("[CHART] 設定資訊:")
    print(f"   主要區間: 第{main_region['start_column']}-{main_region['end_column']}欄")
    print(f"   備用區間: 第{backup_region['backup_start_column']}-{backup_region['backup_end_column']}欄")
    print(f"   EQC RT 程式碼: {eqc_rt_data['program_codes']}")
    print()
    
    print("[TARGET] 雙重搜尋機制運作流程:")
    print("   1⃣ 首先在主要區間(第298-335欄)尋找匹配的程式碼欄位")
    print("   2⃣ 如果主要區間匹配失敗，自動切換到備用區間(第1565-1600欄)")
    print("   3⃣ 根據匹配結果決定插入位置和策略")
    print("   4⃣ 執行實際的 EQC RT 資料插入")
    print()
    
    print("[OK] 雙重搜尋機制的優勢:")
    print("   [SHIELD] 容錯能力: 主要區間失敗時有備用方案")
    print("   [TARGET] 精確匹配: 使用程式碼名稱進行精確定位")
    print("   [CHART] 智能選擇: 根據匹配信心度選擇最佳策略")
    print("   [REFRESH] 一致性保證: 確保 EQC RT 資料插入的正確性")


if __name__ == "__main__":
    demonstrate_dual_search_example()