#!/usr/bin/env python3
"""
整合服務啟動腳本
同時啟動 Flask (郵件收件夾) + FastAPI (FT-EQC 處理) 服務
增強版：包含郵件同步診斷和錯誤監控功能
"""

# 優先設置 Unicode 環境
import sys
sys.path.insert(0, '.')
try:
    from unicode_fix_global import setup_unicode_environment, apply_unicode_patches
    setup_unicode_environment()
    apply_unicode_patches()
except ImportError as e:
    print(f"警告：無法導入 unicode_fix_global：{e}")
    # 設置基本 Unicode 環境
    import os
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'

import os
import subprocess
import sys
import time
import signal
import threading
import asyncio
import json
import re
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, Optional

# 添加專案根目錄到 Python 路徑
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 設定預設端口
FLASK_PORT = 5000
FASTAPI_PORT = 8010

# 顏色代碼用於終端輸出
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

def print_colored(text: str, color: str = Colors.WHITE):
    """印出彩色文字"""
    print(f"{color}{text}{Colors.END}")

def print_status(status: str, message: str, details: str = ""):
    """印出狀態信息"""
    if status == "success":
        print_colored(f"[OK] {message}", Colors.GREEN)
    elif status == "warning":
        print_colored(f"[WARNING] {message}", Colors.YELLOW)
    elif status == "error":
        print_colored(f"[ERROR] {message}", Colors.RED)
    elif status == "info":
        print_colored(f"[INFO] {message}", Colors.BLUE)
    
    if details:
        print_colored(f"   {details}", Colors.WHITE)

async def test_email_connection() -> Dict[str, Any]:
    """測試郵件服務器連接"""
    try:
        from src.infrastructure.adapters.email_inbox.email_sync_service import EmailSyncService
        from src.infrastructure.adapters.database.email_database import EmailDatabase
        
        print_status("info", "正在測試郵件服務器連接...")
        
        # 初始化服務
        database = EmailDatabase()
        sync_service = EmailSyncService(database)
        
        # 測試連接
        result = await sync_service.test_connection()
        
        if result['success']:
            unread_count = result['data'].get('unread_count', 0)
            print_status("success", f"郵件服務器連接成功", f"未讀郵件: {unread_count} 封")
            return {"success": True, "unread_count": unread_count}
        else:
            print_status("error", "郵件服務器連接失敗", result['message'])
            return {"success": False, "error": result['message']}
            
    except ImportError as e:
        print_status("warning", "無法載入郵件服務模組", f"請確認環境設置正確: {e}")
        return {"success": False, "error": f"Import error: {e}"}
    except Exception as e:
        print_status("error", "測試郵件連接時發生錯誤", str(e))
        return {"success": False, "error": str(e)}

def format_log_line(line: str, highlight_errors: bool = True) -> str:
    """格式化和高亮日誌行"""
    if not line.strip():
        return line
    
    # 移[EXCEPT_CHAR] ANSI 顏色代碼（如果有的話）
    clean_line = re.sub(r'\x1b\[[0-9;]*m', '', line)
    
    if not highlight_errors:
        return clean_line
    
    # 高亮錯誤信息
    if any(keyword in clean_line.lower() for keyword in ['error', 'failed', 'exception', '失敗', '錯誤']):
        return f"{Colors.RED}{clean_line}{Colors.END}"
    
    # 高亮警告信息
    if any(keyword in clean_line.lower() for keyword in ['warning', 'warn', '警告']):
        return f"{Colors.YELLOW}{clean_line}{Colors.END}"
    
    # 高亮同步相關信息
    if any(keyword in clean_line for keyword in ['同步完成', 'sync complete', '郵件已同步']):
        return f"{Colors.GREEN}{clean_line}{Colors.END}"
    
    # 高亮重要信息
    if any(keyword in clean_line for keyword in ['INFO', 'info', '資訊']):
        return f"{Colors.CYAN}{clean_line}{Colors.END}"
    
    return clean_line

class SyncMonitor:
    """同步狀態監控器"""
    
    def __init__(self, flask_port: int):
        self.flask_port = flask_port
        self.sync_stats = {
            'total_success': 0,
            'total_errors': 0,
            'last_check': None,
            'error_details': []
        }
        self.running = False
        self.thread = None
    
    def start(self):
        """啟動監控"""
        self.running = True
        self.thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.thread.start()
        print_status("info", "同步狀態監控已啟動")
    
    def stop(self):
        """停止監控"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=2)
    
    def _monitor_loop(self):
        """監控循環"""
        while self.running:
            try:
                self._check_sync_status()
                time.sleep(60)  # 每分鐘檢查一次
            except Exception as e:
                print_status("warning", f"監控檢查失敗: {e}")
                time.sleep(30)  # 錯誤後較短間隔
    
    def _check_sync_status(self):
        """檢查同步狀態"""
        try:
            import requests
            
            # 呼叫同步狀態 API
            response = requests.get(f"http://localhost:{self.flask_port}/api/sync/status", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                stats = data.get('sync_stats', {})
                
                current_errors = stats.get('sync_errors', 0)
                last_error_details = stats.get('last_error_details', [])
                
                # 檢查是否有新的錯誤
                if current_errors > self.sync_stats['total_errors']:
                    new_errors = current_errors - self.sync_stats['total_errors']
                    print_status("warning", f"檢測到 {new_errors} 個新的同步錯誤")
                    
                    # 顯示錯誤詳情
                    for error in last_error_details[-new_errors:]:
                        subject = error.get('subject', 'Unknown')[:50]
                        sender = error.get('sender', 'Unknown')[:30]
                        error_msg = error.get('error', 'Unknown')[:100]
                        print_status("error", f"郵件同步失敗", f"主題: {subject}..., 寄件者: {sender}, 錯誤: {error_msg}")
                
                self.sync_stats['total_errors'] = current_errors
                self.sync_stats['last_check'] = datetime.now().isoformat()
                
        except requests.exceptions.RequestException:
            # 服務可能還沒啟動，忽略連接錯誤
            pass
        except Exception as e:
            print_status("warning", f"同步狀態檢查失敗: {e}")

def display_usage_tips():
    """顯示使用提示"""
    print_colored("\n[TIPS] 使用提示:", Colors.BOLD)
    print_colored("  • 使用 --test-connection 測試郵件連接", Colors.WHITE)
    print_colored("  • 使用 --debug-sync 查看詳細同步日誌", Colors.WHITE)
    print_colored("  • 使用 --no-highlight 禁用顏色輸出", Colors.WHITE)
    print_colored("  • 使用 --no-monitor 禁用同步監控", Colors.WHITE)
    print_colored("  • 按 Ctrl+C 優雅停止所有服務", Colors.WHITE)
    print_colored("\n[TROUBLESHOOTING] 故障排[EXCEPT_CHAR]:", Colors.BOLD)
    print_colored("  • 如果郵件同步失敗，檢查 .env 文件配置", Colors.WHITE)
    print_colored("  • 如果連接失敗，確認網絡和防火牆設置", Colors.WHITE)
    print_colored("  • 查看彩色日誌輸出以快速識別問題", Colors.WHITE)

def start_integrated_service(test_connection: bool = False, highlight_errors: bool = True, 
                             debug_sync: bool = False, monitor_sync: bool = True):
    """啟動整合服務"""
    print_colored("[START] 啟動整合服務...", Colors.BOLD)
    
    # 檢查 email_inbox_app.py 是否存在
    app_path = Path("apps/email_inbox_app.py")
    if not app_path.exists():
        print_status("error", "找不到 apps/email_inbox_app.py 檔案")
        return
    
    # 郵件連接測試
    if test_connection:
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            connection_result = loop.run_until_complete(test_email_connection())
            loop.close()
            
            if not connection_result['success']:
                print_status("warning", "郵件連接測試失敗，但繼續啟動服務", 
                           "建議檢查 .env 文件中的郵件配置")
        except Exception as e:
            print_status("warning", f"無法執行連接測試: {e}")
    
    sync_monitor = None
    process = None
    
    try:
        # 使用整合的應用程式
        cmd = [
            sys.executable, str(app_path),
            "--host", "0.0.0.0",
            "--port", str(FLASK_PORT),
            "--fastapi-port", str(FASTAPI_PORT),
            "--auto-sync",
            "--sync-interval", "60"
        ]
        
        print_status("info", f"執行命令: {' '.join(cmd)}")
        
        # 設定環境變數
        env = os.environ.copy()
        env['PYTHONUNBUFFERED'] = '1'  # 確保輸出不被緩衝
        env['PYTHONIOENCODING'] = 'utf-8'  # 強制使用 UTF-8 編碼
        env['PYTHONUTF8'] = '1'  # 啟用 UTF-8 模式
        
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT, 
            text=True,
            encoding='utf-8',
            errors='replace',
            bufsize=1,  # 行緩衝
            env=env
        )
        
        print_status("success", f"整合服務已啟動 (PID: {process.pid})")
        print_colored(f"[EMAIL] 郵件收件夾: http://localhost:{FLASK_PORT}", Colors.GREEN)
        print_colored(f"[API] FT-EQC 處理: http://localhost:{FASTAPI_PORT}/ui", Colors.GREEN)
        print_colored(f"[DOCS] FT-EQC API 文檔: http://localhost:{FASTAPI_PORT}/docs", Colors.GREEN)
        
        if monitor_sync:
            # 等待服務啟動後再啟動監控
            time.sleep(5)
            sync_monitor = SyncMonitor(FLASK_PORT)
            sync_monitor.start()
        
        print_colored("\n按 Ctrl+C 停止服務...", Colors.YELLOW)
        print_colored("=" * 60, Colors.BLUE)
        
        # 實時輸出日誌
        while True:
            try:
                line = process.stdout.readline()
                if not line and process.poll() is not None:
                    break
                    
                if line:
                    # 安全處理編碼
                    try:
                        safe_line = line.strip()
                    except UnicodeDecodeError:
                        safe_line = line.encode('utf-8', errors='replace').decode('utf-8').strip()
                    
                    formatted_line = format_log_line(safe_line, highlight_errors)
            except UnicodeDecodeError as e:
                formatted_line = f"[ERROR] Unicode 解碼錯誤: {str(e)}"
                if formatted_line:
                    # 如果是調試模式，顯示所有日誌
                    if debug_sync:
                        print(formatted_line)
                    else:
                        # 只顯示重要的日誌
                        if any(keyword in line.lower() for keyword in 
                              ['error', 'warning', 'failed', 'success', '同步', 'sync', '啟動', 'started']):
                            print(formatted_line)
        
        # 檢查退出碼
        return_code = process.poll()
        if return_code is not None and return_code != 0:
            print_status("error", f"服務異常退出，退出碼: {return_code}")
        
    except KeyboardInterrupt:
        print_colored("\n[STOP] 正在停止服務...", Colors.YELLOW)
        
        if sync_monitor:
            sync_monitor.stop()
            
        if process and process.poll() is None:
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
        print_status("success", "服務已停止")
        
    except Exception as e:
        print_status("error", f"啟動服務失敗: {e}")
        import traceback
        traceback.print_exc()
        if sync_monitor:
            sync_monitor.stop()

def start_separate_services():
    """分別啟動兩個服務（傳統方式）"""
    print_colored("[START] 啟動分離服務...", Colors.BOLD)
    
    processes = []
    
    try:
        # 啟動 Flask 服務
        flask_process = subprocess.Popen([
            sys.executable, "apps/email_inbox_app.py",
            "--host", "0.0.0.0",
            "--port", str(FLASK_PORT),
            "--no-fastapi"
        ])
        processes.append(("Flask", flask_process))
        
        # 等待 Flask 服務啟動
        time.sleep(2)
        
        # 啟動 FastAPI 服務
        fastapi_process = subprocess.Popen([
            sys.executable, "-m", "uvicorn",
            "src.presentation.api.ft_eqc_api:app",
            "--host", "0.0.0.0",
            "--port", str(FASTAPI_PORT),
            "--workers", "1"
        ])
        processes.append(("FastAPI", fastapi_process))
        
        print_status("success", f"Flask 服務已啟動 (PID: {flask_process.pid})")
        print_status("success", f"FastAPI 服務已啟動 (PID: {fastapi_process.pid})")
        print_colored(f"[EMAIL] 郵件收件夾: http://localhost:{FLASK_PORT}", Colors.GREEN)
        print_colored(f"[API] FT-EQC 處理: http://localhost:{FASTAPI_PORT}/ui", Colors.GREEN)
        print_colored(f"[DOCS] FT-EQC API 文檔: http://localhost:{FASTAPI_PORT}/docs", Colors.GREEN)
        print_colored("\n按 Ctrl+C 停止所有服務...", Colors.YELLOW)
        
        # 等待所有進程
        while True:
            time.sleep(1)
            for name, process in processes:
                if process.poll() is not None:
                    print_status("warning", f"{name} 服務意外停止")
                    return
        
    except KeyboardInterrupt:
        print_colored("\n[STOP] 正在停止所有服務...", Colors.YELLOW)
        for name, process in processes:
            process.terminate()
            process.wait()
            print_status("success", f"{name} 服務已停止")
    except Exception as e:
        print_status("error", f"啟動服務失敗: {e}")
        for name, process in processes:
            if process.poll() is None:
                process.terminate()

def main():
    """主函數"""
    import argparse
    
    global FLASK_PORT, FASTAPI_PORT
    
    parser = argparse.ArgumentParser(description='整合服務啟動器 - 增強版郵件同步診斷')
    parser.add_argument('--mode', choices=['integrated', 'separate'], default='integrated',
                       help='啟動模式: integrated (整合) 或 separate (分離)')
    parser.add_argument('--flask-port', type=int, default=FLASK_PORT,
                       help=f'Flask 服務端口 (預設: {FLASK_PORT})')
    parser.add_argument('--fastapi-port', type=int, default=FASTAPI_PORT,
                       help=f'FastAPI 服務端口 (預設: {FASTAPI_PORT})')
    
    # 新增的診斷選項
    parser.add_argument('--test-connection', action='store_true',
                       help='啟動前測試郵件服務器連接')
    parser.add_argument('--debug-sync', action='store_true',
                       help='啟用詳細的同步調試輸出')
    parser.add_argument('--no-highlight', action='store_true',
                       help='禁用錯誤高亮顯示')
    parser.add_argument('--no-monitor', action='store_true',
                       help='禁用同步狀態監控')
    
    args = parser.parse_args()
    
    FLASK_PORT = args.flask_port
    FASTAPI_PORT = args.fastapi_port
    
    print_colored("=" * 60, Colors.BLUE)
    print_colored("[SYSTEM] 郵件收件夾 + FT-EQC 整合服務 (增強版)", Colors.BOLD)
    print_colored("=" * 60, Colors.BLUE)
    print_colored(f"模式: {args.mode}", Colors.WHITE)
    print_colored(f"Flask 端口: {FLASK_PORT}", Colors.WHITE)
    print_colored(f"FastAPI 端口: {FASTAPI_PORT}", Colors.WHITE)
    
    # 顯示啟用的診斷功能
    diagnostic_features = []
    if args.test_connection:
        diagnostic_features.append("連接測試")
    if args.debug_sync:
        diagnostic_features.append("詳細同步日誌")
    if not args.no_highlight:
        diagnostic_features.append("錯誤高亮")
    if not args.no_monitor:
        diagnostic_features.append("同步監控")
    
    if diagnostic_features:
        print_colored(f"診斷功能: {', '.join(diagnostic_features)}", Colors.CYAN)
    
    print_colored("=" * 60, Colors.BLUE)
    
    # 顯示使用提示
    display_usage_tips()
    
    if args.mode == 'integrated':
        start_integrated_service(
            test_connection=args.test_connection,
            highlight_errors=not args.no_highlight,
            debug_sync=args.debug_sync,
            monitor_sync=not args.no_monitor
        )
    else:
        start_separate_services()

if __name__ == '__main__':
    main()