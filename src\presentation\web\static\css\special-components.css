/* 
 * 特殊組件樣式模組
 * 包含複雜的組件如摘要面板、測試結果、CODE區間等
 */

/* ==================== Summary 收合狀態樣式 ==================== */
.summary-preview {
    padding: 15px 20px;
    background: var(--gradient-light);
    border-top: 1px solid var(--border-color);
}

.summary-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 12px;
}

.metric-item {
    text-align: center;
    padding: 8px;
    background: white;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.metric-value {
    font-size: 18px;
    font-weight: bold;
    color: var(--secondary-color);
}

.metric-label {
    font-size: var(--font-size-xxs);
    color: var(--text-muted);
    margin-top: 2px;
}

.summary-highlight {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: #fff3cd;
    border-radius: var(--radius-sm);
    border-left: 4px solid var(--warning-color);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* ==================== 處理結果摘要樣式 ==================== */
.processing-summary {
    margin: 20px 0;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-top: 15px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.summary-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.summary-item.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-color: #c3e6cb;
}

.summary-item.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    border-color: #ffeaa7;
}

.summary-item.error {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-color: #f5c6cb;
}

.summary-item.info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    border-color: #bee5eb;
}

.summary-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.summary-item.success .summary-icon {
    background: var(--success-color);
    color: white;
}

.summary-item.warning .summary-icon {
    background: var(--warning-color);
    color: #212529;
}

.summary-item.error .summary-icon {
    background: #dc3545;
    color: white;
}

.summary-item.info .summary-icon {
    background: var(--info-color);
    color: white;
}

.summary-text {
    flex: 1;
}

.summary-title {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 2px;
}

.summary-desc {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    line-height: 1.4;
}

/* ==================== CODE區間資訊卡片 ==================== */
.code-info-card {
    background: linear-gradient(135deg, #f8f9ff 0%, #e9ecff 100%);
    border: 1px solid #d1d9ff;
    border-radius: var(--radius-md);
    padding: 15px;
    margin: 15px 0;
}

.code-info-title {
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-range {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 8px 0;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    border: 1px solid #e0e6ff;
}

/* ==================== CODE區間設定樣式 ==================== */
.code-region-panel {
    background: var(--bg-light);
    border-radius: var(--radius-lg);
    padding: 20px;
    margin-bottom: 25px;
}

.code-region-panel h3 {
    color: var(--secondary-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.region-inputs {
    display: flex;
    flex-direction: column;
    gap: 18px;
}

.region-group {
    background: white;
    padding: 15px;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.input-row {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.input-row input {
    width: 90px;
    padding: 8px 10px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    text-align: center;
    transition: border-color 0.3s;
}

.input-row input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.field-count {
    font-size: var(--font-size-xs);
    color: #666;
    font-weight: 500;
    background: #f0f0f0;
    padding: 4px 8px;
    border-radius: 12px;
    margin-left: 8px;
}

/* ==================== FAIL Summary 卡片樣式 ==================== */
.fail-summary {
    margin-top: 20px;
}

.test-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 12px;
    margin-bottom: 15px;
}

.fail-card, .pass-card {
    padding: 12px 15px;
    border-radius: var(--radius-md);
    border-left: 4px solid;
    transition: var(--transition);
    border: 1px solid;
}

.fail-card {
    background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
    border-left-color: var(--error-color);
    border-color: #f8d7da;
}

.pass-card {
    background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
    border-left-color: var(--success-color);
    border-color: #d4edda;
}

.fail-card:hover, .pass-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.card-header {
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: var(--font-size-sm);
}

.card-content {
    font-size: var(--font-size-xs);
    line-height: 1.5;
}

/* ==================== 控制面板樣式 ==================== */
.control-panel {
    background: var(--bg-light);
    border-radius: var(--radius-lg);
    padding: 25px;
    height: fit-content;
}

.folder-selector {
    margin-bottom: 30px;
}

.folder-selector h3 {
    color: var(--secondary-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.folder-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.folder-input {
    flex: 1;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    transition: border-color 0.3s;
}

.folder-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* ==================== 統計面板樣式 ==================== */
.stats-panel {
    background: white;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid var(--border-color);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    border-radius: var(--radius-md);
    background: linear-gradient(135deg, var(--bg-light) 0%, var(--border-color) 100%);
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: var(--secondary-color);
}

.stat-label {
    font-size: 0.9em;
    color: var(--text-muted);
    margin-top: 5px;
}
