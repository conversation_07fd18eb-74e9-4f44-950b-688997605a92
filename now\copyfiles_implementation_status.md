# CopyFiles 功能實作狀態

## 實作完成時間：2025-01-13

## [OK] 已完成項目

### 1. 基礎架構
- [x] 建立 `src/infrastructure/adapters/file_handlers/` 目錄
- [x] 實作 `BaseFileHandler` 抽象類別（497 行）
- [x] 實作 `FileHandlerFactory` 工廠類別（117 行）

### 2. 廠商檔案處理器
- [x] **GTKFileHandler** - 完整實作，支援：
  - MO 搜尋壓縮檔
  - LOT 搜尋檔案
  - 資料夾複製
- [x] **XAHTFileHandler** - 簡單實作，只支援 MO 搜尋
- [x] **JCETFileHandler** - 簡單實作，只支援 MO 搜尋
- [x] **NFMEFileHandler** - 特殊實作，只複製 lsr 和 data.csv 檔案
- [x] **ETDFileHandler** - 支援資料夾複製優先

### 3. 系統整合
- [x] 整合到 `EmailSyncService`
- [x] 在郵件解析成功後自動觸發檔案處理
- [x] 添加 `_process_vendor_files()` 方法
- [x] 添加 `_update_vendor_files_status()` 方法

### 4. 測試
- [x] 建立完整的測試檔案 `test_file_handlers.py`
- [x] 包含單元測試和整合測試

## [FILE_FOLDER] 檔案結構

```
src/infrastructure/adapters/file_handlers/
├── __init__.py (23 行)
├── base_file_handler.py (497 行)
├── file_handler_factory.py (117 行)
├── gtk_file_handler.py (91 行)
├── xaht_file_handler.py (37 行)
├── jcet_file_handler.py (37 行)
├── nfme_file_handler.py (60 行)
└── etd_file_handler.py (76 行)

tests/
└── test_file_handlers.py (294 行)
```

## [TOOL] 主要功能

### BaseFileHandler 提供的共同功能
1. **建立目標資料夾** - `_create_folders()`
2. **MO 搜尋** - `_copy_by_mo()` 優先搜尋壓縮檔
3. **LOT 搜尋** - `_copy_by_lot()` 
4. **資料夾複製** - `_copy_entire_folder()`
5. **檔案大小檢查** - 避免重複複製
6. **延遲機制** - 對應 VBA DelayOneSecond

### 各廠商特色
- **GTK**: 最完整，支援三種搜尋方式
- **XAHT/JCET**: 簡單，只用 MO 搜尋壓縮檔
- **NFME**: 特殊，只複製特定檔案類型
- **ETD**: 優先使用資料夾複製

## [REFRESH] 處理流程

```
郵件同步 → 郵件解析 → 取得廠商/MO/LOT → 建立檔案處理器 → 複製檔案到暫存目錄
```

## [BUILD] 環境設定

```bash
# .env
FILE_SOURCE_BASE_PATH=/mnt/share  # 來源基礎路徑
FILE_TEMP_BASE_PATH=/tmp/email_processor  # 暫存路徑
```

## [CHART] 與 VBA 對照

| VBA 函數 | Python 類別 | 狀態 |
|---------|------------|------|
| CopyFilesGTK | GTKFileHandler | [OK] 完成 |
| CopyFilesXAHT | XAHTFileHandler | [OK] 完成 |
| CopyFilesJCET | JCETFileHandler | [OK] 完成 |
| CopyFilesNFME | NFMEFileHandler | [OK] 完成 |
| CopyFilesETD | ETDFileHandler | [OK] 完成 |
| CreateFolders | BaseFileHandler._create_folders | [OK] 完成 |
| DelayOneSecond | time.sleep(1) | [OK] 完成 |

## [TARGET] 關鍵實作細節

1. **保持 VBA 邏輯**
   - 相同的搜尋順序（MO → LOT → 資料夾）
   - 相同的檔案大小檢查
   - 相同的延遲機制

2. **Python 改進**
   - 使用 Path 物件處理路徑
   - 完整的異常處理
   - 詳細的日誌記錄
   - 模組化設計

3. **整合方式**
   - 自動在郵件解析後觸發
   - 使用工廠模式管理處理器
   - 記錄處理狀態到資料庫

## [WARNING] 注意事項

1. **路徑設定** - 需要正確設定 FILE_SOURCE_BASE_PATH
2. **權限問題** - 確保有讀取網路磁碟的權限
3. **空間管理** - 定期清理暫存目錄
4. **效能考量** - 大檔案複製可能需要時間

## [ROCKET] 使用範例

```python
# 直接使用處理器
from src.infrastructure.adapters.file_handlers import GTKFileHandler

handler = GTKFileHandler("/mnt/share")
success = handler.copy_files(
    file_name="MO123456",
    file_temp="/tmp/test",
    pd="PRODUCT_A",
    lot="LOT789"
)

# 使用工廠
from src.infrastructure.adapters.file_handlers import FileHandlerFactory

factory = FileHandlerFactory("/mnt/share")
result = factory.process_vendor_files(
    vendor_code="GTK",
    mo="MO123456",
    temp_path="/tmp/test",
    pd="PRODUCT_A",
    lot="LOT789"
)
```

## [OK] 總結

CopyFiles 功能已完整實作，包含：
- 5 個廠商的檔案處理器
- 完整的錯誤處理和日誌
- 與郵件同步服務的整合
- 全面的測試覆蓋

系統現在能夠在解析郵件後，自動從指定目錄複製相關檔案到暫存目錄，為後續處理做準備。