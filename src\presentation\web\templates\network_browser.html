<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>網路共享瀏覽器</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root{--primary:#667eea;--secondary:#2c3e50;--muted:#6c757d;--border:#e9ecef;--bg:#f8f9fa;--success:#28a745;--error:#e74c3c;--radius:8px;--spacing:15px;--shadow:0 4px 12px rgba(0,0,0,0.1)}
        *{margin:0;padding:0;box-sizing:border-box}
        body{font-family:'Segoe UI',sans-serif;background:linear-gradient(135deg,#f5f7fa 0%,#c3cfe2 100%);min-height:100vh;color:var(--secondary)}
        .container{max-width:1200px;margin:0 auto;padding:var(--spacing)}
        .header{background:white;padding:var(--spacing);border-radius:var(--radius);box-shadow:var(--shadow);margin-bottom:var(--spacing);text-align:center}
        .header h1{color:var(--primary);margin-bottom:10px;font-size:2rem}
        .controls{background:white;padding:var(--spacing);border-radius:var(--radius);box-shadow:var(--shadow);margin-bottom:var(--spacing)}
        .path-group{display:flex;gap:10px;margin-bottom:var(--spacing)}
        .path-group input{flex:1;padding:12px;border:2px solid var(--border);border-radius:var(--radius);font-size:1rem}
        .path-group input:focus{outline:none;border-color:var(--primary)}
        .btn{padding:12px 20px;border:none;border-radius:var(--radius);cursor:pointer;font-size:1rem;transition:all 0.3s ease;text-decoration:none;display:inline-block}
        .btn-primary{background:var(--primary);color:white}.btn-secondary{background:var(--muted);color:white}.btn-success{background:var(--success);color:white}
        .btn:hover{transform:translateY(-2px);box-shadow:0 6px 16px rgba(0,0,0,0.2)}
        .status{padding:12px;border-radius:var(--radius);margin-bottom:var(--spacing);font-weight:500}
        .status.success{background:#d4edda;color:#155724}.status.error{background:#f8d7da;color:#721c24}.status.loading{background:#d1ecf1;color:#0c5460}
        .file-container{background:white;border-radius:var(--radius);box-shadow:var(--shadow);overflow:hidden}
        .file-header{background:var(--bg);padding:var(--spacing);border-bottom:1px solid var(--border);display:flex;justify-content:space-between}
        .file-list{max-height:600px;overflow-y:auto}
        .file-item{display:flex;align-items:center;justify-content:space-between;padding:15px var(--spacing);border-bottom:1px solid var(--border);transition:all 0.3s ease}
        .file-item:hover{background:var(--bg)}
        .file-info{display:flex;align-items:center;flex:1}
        .file-icon{width:40px;height:40px;display:flex;align-items:center;justify-content:center;margin-right:15px;border-radius:var(--radius);font-size:1.4rem}
        .file-icon.folder{background:#fff3cd;color:#856404}.file-icon.excel{background:#d4edda;color:#155724}.file-icon.text{background:#d1ecf1;color:#0c5460}.file-icon.archive{background:#f8d7da;color:#721c24}.file-icon.other{background:#e2e3e5;color:#6c757d}
        .file-details h4{margin-bottom:4px;color:var(--secondary)}
        .file-meta{color:var(--muted);font-size:0.9rem}
        .file-actions{display:flex;gap:8px}
        .file-actions .btn{padding:8px 12px;font-size:0.9rem}
        .loading,.empty-state{text-align:center;padding:40px;color:var(--muted)}
        .empty-state i{font-size:3rem;margin-bottom:20px;opacity:0.5}
        .modal{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.7);display:flex;align-items:center;justify-content:center;z-index:1000;}
        .modal-content{background:white;border-radius:var(--radius);box-shadow:0 10px 30px rgba(0,0,0,0.3);max-width:400px;width:90%;animation:modalSlideIn 0.3s ease;}
        .modal-header{background:var(--primary);color:white;padding:var(--spacing);border-radius:var(--radius) var(--radius) 0 0;text-align:center;}
        .modal-header h2{margin:0;font-size:1.3rem;}
        .modal-body{padding:var(--spacing);}
        .login-form .form-group{margin-bottom:15px;}
        .login-form label{display:block;margin-bottom:5px;color:var(--secondary);font-weight:500;}
        .login-form input{width:100%;padding:12px;border:2px solid var(--border);border-radius:var(--radius);font-size:1rem;box-sizing:border-box;}
        .login-form input:focus{outline:none;border-color:var(--primary);}
        .form-actions{text-align:center;margin-top:20px;}
        .form-actions .btn{width:100%;}
        @keyframes modalSlideIn{from{transform:translateY(-50px);opacity:0;}to{transform:translateY(0);opacity:1;}}
        @media (max-width:768px){.path-group{flex-direction:column}.file-item{flex-direction:column;align-items:flex-start;gap:10px}.file-actions{width:100%;justify-content:flex-end}}
    </style>
</head>
<body>
    <!-- 登入模態框 -->
    <div id="loginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-lock"></i> 網路共享瀏覽器 - 登入驗證</h2>
            </div>
            <div class="modal-body">
                <p style="color:var(--muted);margin-bottom:20px;">請輸入您的帳號密碼以存取網路共享瀏覽器</p>
                <div class="login-form">
                    <div class="form-group">
                        <label for="loginUsername"><i class="fas fa-user"></i> 使用者名稱</label>
                        <input type="text" id="loginUsername" placeholder="請輸入使用者名稱" required>
                    </div>
                    <div class="form-group">
                        <label for="loginPassword"><i class="fas fa-key"></i> 密碼</label>
                        <input type="password" id="loginPassword" placeholder="請輸入密碼" required>
                    </div>
                    <div class="form-actions">
                        <button class="btn btn-primary" onclick="performLogin()" id="loginBtn">
                            <i class="fas fa-sign-in-alt"></i> 登入
                        </button>
                    </div>
                </div>
                <div id="loginStatus"></div>
            </div>
        </div>
    </div>
    
    <div class="container" id="mainContainer" style="display:none;">
        <div class="header">
            <div style="display:flex;justify-content:space-between;align-items:center;">
                <div>
                    <h1><i class="fas fa-network-wired"></i> 網路共享瀏覽器</h1>
                    <p>瀏覽和下載網路共享資料夾中的檔案</p>
                </div>
                <button class="btn btn-secondary" onclick="logout()" title="登出系統">
                    <i class="fas fa-sign-out-alt"></i> 登出
                </button>
            </div>
        </div>
        <div class="controls">
            <div class="auth-group" style="display:flex;gap:10px;margin-bottom:15px;">
                <input type="text" id="usernameInput" placeholder="使用者名稱 (例: tong.wu)" style="flex:1;padding:12px;border:2px solid var(--border);border-radius:var(--radius);font-size:1rem;">
                <input type="password" id="passwordInput" placeholder="密碼" style="flex:1;padding:12px;border:2px solid var(--border);border-radius:var(--radius);font-size:1rem;">
                <button class="btn btn-primary" onclick="connectToShare()" id="connectBtn"><i class="fas fa-plug"></i> 連接</button>
            </div>
            <div id="navigationBar" style="display:none;margin-bottom:15px;">
                <div class="breadcrumb" style="display:flex;align-items:center;gap:10px;margin-bottom:10px;">
                    <button class="btn btn-secondary" onclick="navigateUp()"><i class="fas fa-arrow-up"></i> 上一層</button>
                    <span style="color:var(--muted);font-size:0.9rem;"><i class="fas fa-folder-open"></i> <span id="currentPath">等待連接...</span></span>
                </div>
                <div style="display:flex;align-items:center;gap:10px;">
                    <span id="connectionStatus" style="color:var(--success);font-weight:500;">已連接</span>
                    <button class="btn btn-success" onclick="loadFiles()" id="refreshBtn"><i class="fas fa-sync-alt"></i> 重新整理</button>
                </div>
            </div>
            <div id="filterBar" style="display:none;background:white;padding:var(--spacing);border-radius:var(--radius);box-shadow:var(--shadow);margin-bottom:var(--spacing);">
                <h4 style="margin-bottom:10px;color:var(--primary);"><i class="fas fa-filter"></i> 過濾工具</h4>
                <div style="display:flex;gap:10px;margin-bottom:10px;flex-wrap:wrap;">
                    <input type="text" id="searchInput" placeholder="🔍 搜尋檔案名稱..." style="flex:1;min-width:200px;padding:8px;border:2px solid var(--border);border-radius:var(--radius);">
                    <button class="btn btn-primary" onclick="filterFiles()" id="filterBtn"><i class="fas fa-search"></i> 搜尋</button>
                </div>
                <div style="display:flex;gap:10px;margin-bottom:10px;flex-wrap:wrap;align-items:center;">
                    <span style="color:var(--muted);">📅 日期範圍:</span>
                    <input type="date" id="startDate" style="padding:6px;border:2px solid var(--border);border-radius:var(--radius);">
                    <span style="color:var(--muted);">到</span>
                    <input type="date" id="endDate" style="padding:6px;border:2px solid var(--border);border-radius:var(--radius);">
                    <button class="btn btn-secondary" onclick="filterFiles()" id="dateFilterBtn"><i class="fas fa-calendar"></i> 過濾</button>
                    <button class="btn btn-secondary" onclick="clearFilters()" id="clearBtn"><i class="fas fa-times"></i> 清除</button>
                </div>
                <div id="smartSearchPanel" style="display:none;margin-top:10px;padding:10px;background:var(--bg);border-radius:var(--radius);">
                    <input type="text" id="smartSearchInput" placeholder="🧠 描述您要找的檔案 (智能搜尋 - 開發中)" style="width:100%;padding:8px;border:2px solid var(--border);border-radius:var(--radius);" disabled>
                    <button class="btn btn-primary" onclick="smartSearch()" style="margin-top:5px;" disabled>🧠 智能搜尋</button>
                </div>
                <div id="filterStats" style="color:var(--muted);font-size:0.9rem;margin-top:5px;">載入中...</div>
            </div>
        </div>
        <div id="status"></div>
        <div class="file-container">
            <div class="file-header"><h3><i class="fas fa-list"></i> 檔案列表</h3><span id="fileCount">載入中...</span></div>
            <div id="fileList" class="file-list"><div class="loading"><i class="fas fa-spinner fa-spin"></i><p>正在載入...</p></div></div>
        </div>
    </div>
    <script src="/static/js/network_browser.js"></script>
</body>
</html>