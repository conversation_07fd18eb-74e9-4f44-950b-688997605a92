"""
任務佇列介面
定義任務佇列管理的抽象介面，支援非同步處理和錯誤恢復
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from enum import Enum
import asyncio

from src.data_models.email_models import EmailData


class TaskStatus(Enum):
    """任務狀態"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任務優先級"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4


@dataclass
class TaskInfo:
    """任務資訊"""
    task_id: str
    email_data: EmailData
    status: TaskStatus
    priority: TaskPriority
    created_at: float
    started_at: Optional[float] = None
    completed_at: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


@dataclass
class QueueConfig:
    """佇列配置"""
    max_queue_size: int = 1000
    max_workers: int = 5
    retry_delay: float = 60.0
    max_retry_attempts: int = 3
    task_timeout: float = 300.0
    enable_priority: bool = True
    enable_persistence: bool = False


class TaskQueue(ABC):
    """
    任務佇列抽象基類
    定義任務佇列管理的標準介面
    """
    
    @abstractmethod
    async def enqueue(
        self, 
        email_data: EmailData, 
        priority: TaskPriority = TaskPriority.MEDIUM
    ) -> str:
        """
        將郵件處理任務加入佇列
        
        Args:
            email_data: 郵件數據
            priority: 任務優先級
            
        Returns:
            任務 ID
        """
        pass
    
    @abstractmethod
    async def dequeue(self) -> Optional[TaskInfo]:
        """
        從佇列中取出任務
        
        Returns:
            任務資訊，若佇列為空則返回 None
        """
        pass
    
    @abstractmethod
    async def get_task_info(self, task_id: str) -> Optional[TaskInfo]:
        """
        獲取任務資訊
        
        Args:
            task_id: 任務 ID
            
        Returns:
            任務資訊
        """
        pass
    
    @abstractmethod
    async def update_task_status(
        self, 
        task_id: str, 
        status: TaskStatus,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ) -> bool:
        """
        更新任務狀態
        
        Args:
            task_id: 任務 ID
            status: 新狀態
            result: 處理結果
            error_message: 錯誤訊息
            
        Returns:
            更新成功與否
        """
        pass
    
    @abstractmethod
    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任務
        
        Args:
            task_id: 任務 ID
            
        Returns:
            取消成功與否
        """
        pass
    
    @abstractmethod
    async def retry_task(self, task_id: str) -> bool:
        """
        重試任務
        
        Args:
            task_id: 任務 ID
            
        Returns:
            重試成功與否
        """
        pass
    
    @abstractmethod
    def get_queue_size(self) -> int:
        """
        獲取佇列大小
        
        Returns:
            佇列中的任務數量
        """
        pass
    
    @abstractmethod
    def get_processing_count(self) -> int:
        """
        獲取正在處理的任務數量
        
        Returns:
            處理中任務數量
        """
        pass
    
    @abstractmethod
    async def get_pending_tasks(self) -> List[TaskInfo]:
        """
        獲取待處理任務列表
        
        Returns:
            待處理任務列表
        """
        pass
    
    @abstractmethod
    async def get_failed_tasks(self) -> List[TaskInfo]:
        """
        獲取失敗任務列表
        
        Returns:
            失敗任務列表
        """
        pass
    
    @abstractmethod
    async def clear_completed_tasks(self) -> int:
        """
        清理已完成任務
        
        Returns:
            清理的任務數量
        """
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取佇列統計資訊
        
        Returns:
            統計資訊字典
        """
        pass
    
    @abstractmethod
    async def start_workers(self, worker_function: Callable) -> None:
        """
        啟動工作者
        
        Args:
            worker_function: 工作者函數
        """
        pass
    
    @abstractmethod
    async def stop_workers(self) -> None:
        """停止所有工作者"""
        pass
    
    @abstractmethod
    def is_running(self) -> bool:
        """
        檢查佇列是否運行中
        
        Returns:
            是否運行中
        """
        pass