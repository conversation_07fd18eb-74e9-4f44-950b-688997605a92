"""
領域例外處理基礎類別
提供統一的例外處理架構
"""

from typing import Optional, Dict, Any


class OutlookSummaryException(Exception):
    """系統基礎例外類別"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.message = message
        self.error_code = error_code
        self.details = details
        super().__init__(self._format_message())

    def _format_message(self) -> str:
        """格式化例外訊息"""
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message

    def to_dict(self) -> Dict[str, Any]:
        """轉換為字典格式"""
        result = {
            "error_code": self.error_code,
            "message": self.message,
        }

        # 加入子類別特有的屬性
        excluded_attrs = {"message", "error_code", "details", "args"}
        for attr_name in dir(self):
            if (
                not attr_name.startswith("_")
                and attr_name not in excluded_attrs
                and not callable(getattr(self, attr_name))
            ):
                result[attr_name] = getattr(self, attr_name)

        if self.details:
            result["details"] = self.details

        return result

    def __repr__(self) -> str:
        """字串表示"""
        class_name = self.__class__.__name__
        return f"{class_name}(error_code='{self.error_code}', message='{self.message}')"


class DomainException(OutlookSummaryException):
    """領域層例外"""

    def __init__(
        self,
        message: str,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().__init__(message, error_code, details)


class ValidationException(DomainException):
    """驗證例外"""

    def __init__(
        self,
        message: str,
        field_name: Optional[str] = None,
        field_value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.field_name = field_name
        self.field_value = field_value
        super().__init__(message, error_code="VALIDATION_ERROR", details=details)


class ConfigurationException(OutlookSummaryException):
    """配置例外"""

    def __init__(
        self,
        message: str,
        config_key: Optional[str] = None,
        config_value: Optional[Any] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.config_key = config_key
        self.config_value = config_value
        super().__init__(message, error_code="CONFIG_ERROR", details=details)


class EmailProcessingException(DomainException):
    """郵件處理例外"""

    def __init__(
        self,
        message: str,
        email_id: Optional[str] = None,
        vendor: Optional[str] = None,
        processing_step: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.email_id = email_id
        self.vendor = vendor
        self.processing_step = processing_step
        super().__init__(message, error_code="EMAIL_PROCESSING_ERROR", details=details)


class FileProcessingException(DomainException):
    """檔案處理例外"""

    def __init__(
        self,
        message: str,
        file_path: Optional[str] = None,
        operation: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.file_path = file_path
        self.operation = operation
        super().__init__(message, error_code="FILE_PROCESSING_ERROR", details=details)


class ParsingException(DomainException):
    """解析例外"""

    def __init__(
        self,
        message: str,
        content: Optional[str] = None,
        parser_type: Optional[str] = None,
        line_number: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.content = content
        self.parser_type = parser_type
        self.line_number = line_number
        super().__init__(message, error_code="PARSING_ERROR", details=details)