# base.py

領域例外處理基礎類別模組，提供統一的例外處理架構。

## OutlookSummaryException

系統基礎例外類別，作為所有自定義例外的基礎類別。

### 屬性
- `message` (str): 例外訊息
- `error_code` (Optional[str]): 錯誤代碼，可選
- `details` (Optional[Dict[str, Any]]): 詳細資訊，可選

### __init__

初始化例外實例。

**參數:**
- `message` (str): 例外訊息
- `error_code` (Optional[str]): 錯誤代碼，可選
- `details` (Optional[Dict[str, Any]]): 詳細資訊，可選

**返回值:**
- None

### _format_message

格式化例外訊息的私有方法。

**返回值:**
- str: 格式化後的訊息，如果有錯誤代碼則包含在方括號中

### to_dict

轉換為字典格式的方法。

**返回值:**
- Dict[str, Any]: 包含例外資訊的字典，包含錯誤代碼、訊息、詳細資訊和子類別特有屬性

### __repr__

字串表示方法。

**返回值:**
- str: 例外的字串表示，包含類別名稱、錯誤代碼和訊息

## DomainException

領域層例外類別，繼承自 OutlookSummaryException。

### __init__

初始化領域例外實例。

**參數:**
- `message` (str): 例外訊息
- `error_code` (Optional[str]): 錯誤代碼，可選
- `details` (Optional[Dict[str, Any]]): 詳細資訊，可選

**返回值:**
- None

## ValidationException

驗證例外類別，繼承自 DomainException，用於處理資料驗證錯誤。

### 屬性
- `field_name` (Optional[str]): 驗證失敗的欄位名稱，可選
- `field_value` (Optional[Any]): 驗證失敗的欄位值，可選

### __init__

初始化驗證例外實例。

**參數:**
- `message` (str): 例外訊息
- `field_name` (Optional[str]): 驗證失敗的欄位名稱，可選
- `field_value` (Optional[Any]): 驗證失敗的欄位值，可選
- `details` (Optional[Dict[str, Any]]): 詳細資訊，可選

**返回值:**
- None

**注意:**
- 自動設定錯誤代碼為 "VALIDATION_ERROR"

## ConfigurationException

配置例外類別，繼承自 OutlookSummaryException，用於處理配置相關錯誤。

### 屬性
- `config_key` (Optional[str]): 配置鍵名，可選
- `config_value` (Optional[Any]): 配置值，可選

### __init__

初始化配置例外實例。

**參數:**
- `message` (str): 例外訊息
- `config_key` (Optional[str]): 配置鍵名，可選
- `config_value` (Optional[Any]): 配置值，可選
- `details` (Optional[Dict[str, Any]]): 詳細資訊，可選

**返回值:**
- None

**注意:**
- 自動設定錯誤代碼為 "CONFIG_ERROR"

## EmailProcessingException

郵件處理例外類別，繼承自 DomainException，用於處理郵件處理相關錯誤。

### 屬性
- `email_id` (Optional[str]): 郵件 ID，可選
- `vendor` (Optional[str]): 廠商名稱，可選
- `processing_step` (Optional[str]): 處理步驟，可選

### __init__

初始化郵件處理例外實例。

**參數:**
- `message` (str): 例外訊息
- `email_id` (Optional[str]): 郵件 ID，可選
- `vendor` (Optional[str]): 廠商名稱，可選
- `processing_step` (Optional[str]): 處理步驟，可選
- `details` (Optional[Dict[str, Any]]): 詳細資訊，可選

**返回值:**
- None

**注意:**
- 自動設定錯誤代碼為 "EMAIL_PROCESSING_ERROR"

## FileProcessingException

檔案處理例外類別，繼承自 DomainException，用於處理檔案操作相關錯誤。

### 屬性
- `file_path` (Optional[str]): 檔案路徑，可選
- `operation` (Optional[str]): 操作類型，可選

### __init__

初始化檔案處理例外實例。

**參數:**
- `message` (str): 例外訊息
- `file_path` (Optional[str]): 檔案路徑，可選
- `operation` (Optional[str]): 操作類型，可選
- `details` (Optional[Dict[str, Any]]): 詳細資訊，可選

**返回值:**
- None

**注意:**
- 自動設定錯誤代碼為 "FILE_PROCESSING_ERROR"

## ParsingException

解析例外類別，繼承自 DomainException，用於處理資料解析相關錯誤。

### 屬性
- `content` (Optional[str]): 解析內容，可選
- `parser_type` (Optional[str]): 解析器類型，可選
- `line_number` (Optional[int]): 行號，可選

### __init__

初始化解析例外實例。

**參數:**
- `message` (str): 例外訊息
- `content` (Optional[str]): 解析內容，可選
- `parser_type` (Optional[str]): 解析器類型，可選
- `line_number` (Optional[int]): 行號，可選
- `details` (Optional[Dict[str, Any]]): 詳細資訊，可選

**返回值:**
- None

**注意:**
- 自動設定錯誤代碼為 "PARSING_ERROR"
