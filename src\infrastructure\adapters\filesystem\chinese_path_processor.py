"""
中文路徑處理器
自動處理含有中文字或特殊符號的資料夾名稱
集成到EQC處理流程中，在EQCTOTALDATA生成前執行
"""

import os
import re
from typing import List, Tuple

class ChinesePathProcessor:
    """
    中文路徑處理器
    自動處理含有中文字或特殊符號的資料夾名稱
    """
    
    def __init__(self):
        """
        初始化路徑處理器
        """
        self.default_counter = 1
        print(f"[TOOL] 中文路徑處理器初始化")
    
    def has_chinese_or_special_chars(self, text: str) -> bool:
        """
        檢測文字是否包含中文字或特殊符號
        
        Args:
            text (str): 要檢測的文字
            
        Returns:
            bool: 是否包含中文字或特殊符號
        """
        # 中文字符範圍（包括繁簡體）
        chinese_pattern = r'[\u4e00-\u9fff]'
        # 特殊符號（[EXCEPT_CHAR]了字母、數字、底線、連字符、空格）
        special_pattern = r'[^a-zA-Z0-9_\-\s]'
        
        has_chinese = bool(re.search(chinese_pattern, text))
        has_special = bool(re.search(special_pattern, text))
        
        return has_chinese or has_special
    
    def extract_english_only(self, text: str) -> str:
        """
        從文字中提取只有英文字母、數字、底線、連字符的部分
        
        Args:
            text (str): 原始文字
            
        Returns:
            str: 提取的英文部分
        """
        # 只保留英文字母、數字、底線、連字符、空格
        english_only = re.sub(r'[^a-zA-Z0-9_\-\s]', '', text)
        # 清理多餘空格並用底線替換空格
        english_only = re.sub(r'\s+', '_', english_only.strip())
        # 移[EXCEPT_CHAR]開頭結尾的底線或連字符
        english_only = english_only.strip('_-')
        
        return english_only
    
    def generate_new_folder_name(self, original_name: str, base_path: str) -> str:
        """
        生成新的資料夾名稱
        
        Args:
            original_name (str): 原始資料夾名稱
            base_path (str): 基礎路徑（用於檢查名稱衝突）
            
        Returns:
            str: 新的資料夾名稱
        """
        if not self.has_chinese_or_special_chars(original_name):
            # 如果沒有中文字或特殊符號，保持原名
            return original_name
        
        # 提取英文部分
        english_part = self.extract_english_only(original_name)
        
        if english_part and len(english_part) > 0:
            # 有英文部分，使用英文部分作為新名稱
            new_name = english_part
        else:
            # 全中文或特殊符號，使用 default + 數字
            new_name = f"default_{self.default_counter}"
            self.default_counter += 1
        
        # 檢查名稱衝突，如果衝突則添加數字後綴
        counter = 1
        final_name = new_name
        while os.path.exists(os.path.join(base_path, final_name)):
            final_name = f"{new_name}_{counter}"
            counter += 1
        
        return final_name
    
    def auto_rename_folders(self, folder_path: str) -> bool:
        """
        自動重命名包含中文字或特殊符號的資料夾
        
        Args:
            folder_path (str): 要處理的資料夾路徑
            
        Returns:
            bool: 是否成功處理
        """
        try:
            if not os.path.exists(folder_path):
                print(f"[ERROR] 路徑不存在: {folder_path}")
                return False
            
            renamed_count = 0
            rename_list = []
            
            # 收集需要重命名的資料夾
            for root, dirs, files in os.walk(folder_path):
                for dir_name in dirs:
                    if self.has_chinese_or_special_chars(dir_name):
                        old_path = os.path.join(root, dir_name)
                        new_name = self.generate_new_folder_name(dir_name, root)
                        new_path = os.path.join(root, new_name)
                        
                        if new_name != dir_name:
                            rename_list.append((old_path, new_path, dir_name, new_name))
            
            # 執行重命名
            for old_path, new_path, old_name, new_name in rename_list:
                if not os.path.exists(new_path):
                    os.rename(old_path, new_path)
                    print(f"[REFRESH] 資料夾重新命名: {old_name} → {new_name}")
                    renamed_count += 1
                else:
                    print(f"[WARNING] 跳過重命名（目標已存在）: {old_name} → {new_name}")
            
            print(f"[OK] 自動重命名完成，處理了 {renamed_count} 個資料夾")
            return True
            
        except Exception as e:
            print(f"[ERROR] 自動重命名失敗: {e}")
            return False
    
    def scan_folders_with_issues(self, base_path: str) -> List[Tuple[str, str, str]]:
        """
        掃描包含中文字或特殊符號的資料夾
        
        Args:
            base_path (str): 基礎搜尋路徑
            
        Returns:
            List[Tuple[str, str, str]]: (完整路徑, 原始名稱, 建議新名稱) 的列表
        """
        problem_folders = []
        
        try:
            for root, dirs, files in os.walk(base_path):
                for dir_name in dirs:
                    if self.has_chinese_or_special_chars(dir_name):
                        folder_path = os.path.join(root, dir_name)
                        suggested_name = self.generate_new_folder_name(dir_name, root)
                        problem_folders.append((folder_path, dir_name, suggested_name))
                        print(f"[FOLDER] 發現問題資料夾: {dir_name} → 建議: {suggested_name}")
            
            print(f"[BOARD] 共找到 {len(problem_folders)} 個需要處理的資料夾")
            return problem_folders
            
        except Exception as e:
            print(f"[ERROR] 掃描資料夾失敗: {e}")
            return []


def process_chinese_paths_in_directory(directory_path: str, verbose: bool = True) -> bool:
    """
    便捷函數：處理指定目錄中的中文路徑
    
    Args:
        directory_path (str): 要處理的目錄路徑
        verbose (bool): 是否顯示詳細輸出
        
    Returns:
        bool: 是否處理成功
    """
    processor = ChinesePathProcessor()
    
    if verbose:
        print(f"[TOOL] 開始處理路徑: {directory_path}")
    
    # 掃描問題資料夾
    problem_folders = processor.scan_folders_with_issues(directory_path)
    
    if not problem_folders:
        if verbose:
            print("[OK] 未發現需要處理的資料夾")
        return True
    
    # 執行自動重命名
    success = processor.auto_rename_folders(directory_path)
    
    if verbose and success:
        print("[OK] 中文路徑處理完成")
    
    return success


def main():
    """
    主要測試功能
    """
    print("[TEST_TUBE] 中文路徑處理器測試")
    
    # 處理實際的doc/20250523目錄
    base_path = "/mnt/d/project/python/outlook_summary/doc/20250523"
    
    # 使用便捷函數處理
    success = process_chinese_paths_in_directory(base_path, verbose=True)
    
    if success:
        print("[PARTY] 處理完成！")
    else:
        print("[ERROR] 處理失敗")


if __name__ == "__main__":
    main()