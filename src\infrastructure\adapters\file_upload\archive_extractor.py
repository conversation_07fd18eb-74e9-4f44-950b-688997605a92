"""
壓縮檔解壓縮模組

支援多種壓縮格式的解壓縮處理，包含 ZIP、7Z、RAR、TAR、GZ 等。
"""

import os
import zipfile
import tarfile
import logging
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any, List
from .upload_config import UploadConfig, upload_config
from .temp_file_manager import TempFileManager


logger = logging.getLogger(__name__)


class ExtractionError(Exception):
    """解壓縮錯誤"""
    pass


class UnsupportedFormatError(ExtractionError):
    """不支援的壓縮格式錯誤"""
    pass


class ArchiveExtractor:
    """壓縮檔解壓縮器"""
    
    def __init__(self, config: Optional[UploadConfig] = None):
        """
        初始化壓縮檔解壓縮器
        
        Args:
            config: 上傳配置，若為 None 則使用預設配置
        """
        self.config = config or upload_config
        self.temp_manager = TempFileManager(self.config)
    
    def extract_archive(self, archive_path: str) -> Dict[str, Any]:
        """
        解壓縮檔案
        
        Args:
            archive_path: 壓縮檔路徑
            
        Returns:
            Dict[str, Any]: 解壓縮結果資訊
            
        Raises:
            ExtractionError: 解壓縮過程中的錯誤
        """
        archive_path = Path(archive_path)
        
        if not archive_path.exists():
            raise ExtractionError(f"壓縮檔不存在: {archive_path}")
        
        # 取得檔案格式
        file_extension = archive_path.suffix.lower().lstrip('.')
        
        # 創建解壓縮目錄
        extract_dir = self.temp_manager.create_unique_extract_dir()
        
        try:
            # 根據檔案格式選擇解壓縮方法
            if file_extension == 'zip':
                extracted_files = self._extract_zip(archive_path, extract_dir)
            elif file_extension == '7z':
                extracted_files = self._extract_7z(archive_path, extract_dir)
            elif file_extension == 'rar':
                extracted_files = self._extract_rar(archive_path, extract_dir)
            elif file_extension in ['tar', 'gz', 'tgz']:
                extracted_files = self._extract_tar(archive_path, extract_dir)
            else:
                raise UnsupportedFormatError(f"不支援的壓縮格式: {file_extension}")
            
            logger.info(f"成功解壓縮 {archive_path} 到 {extract_dir}")
            
            return {
                'success': True,
                'message': '解壓縮完成',
                'archive_path': str(archive_path),
                'extract_dir': str(extract_dir),
                'extracted_files': extracted_files,
                'file_count': len(extracted_files)
            }
            
        except Exception as e:
            # 清理失敗的解壓縮目錄
            self.temp_manager.remove_file_or_dir(extract_dir)
            error_msg = f"解壓縮失敗: {e}"
            logger.error(error_msg)
            raise ExtractionError(error_msg)
    
    def _extract_zip(self, archive_path: Path, extract_dir: Path) -> List[str]:
        """
        解壓縮 ZIP 檔案
        
        Args:
            archive_path: ZIP 檔案路徑
            extract_dir: 解壓縮目錄
            
        Returns:
            List[str]: 解壓縮的檔案列表
        """
        extracted_files = []
        
        try:
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                # 檢查是否有惡意路徑
                for member in zip_ref.namelist():
                    if os.path.isabs(member) or '..' in member:
                        raise ExtractionError(f"發現不安全的檔案路徑: {member}")
                
                zip_ref.extractall(extract_dir)
                extracted_files = zip_ref.namelist()
                
        except zipfile.BadZipFile:
            raise ExtractionError("無效的 ZIP 檔案")
        
        return extracted_files
    
    def _extract_7z(self, archive_path: Path, extract_dir: Path) -> List[str]:
        """
        解壓縮 7Z 檔案（改善版本，包含進程管理）
        
        Args:
            archive_path: 7Z 檔案路徑
            extract_dir: 解壓縮目錄
            
        Returns:
            List[str]: 解壓縮的檔案列表
        """
        import time
        process = None
        
        try:
            # 嘗試使用 7zip 命令列工具
            cmd = ['7z', 'x', str(archive_path), f'-o{extract_dir}', '-y']
            
            # 使用 Popen 以便更好地控制進程
            process = subprocess.Popen(
                cmd, 
                stdout=subprocess.PIPE, 
                stderr=subprocess.PIPE, 
                text=True
            )
            
            # 等待進程完成，設置超時
            stdout, stderr = process.communicate(timeout=300)  # 5分鐘超時
            
            if process.returncode != 0:
                raise ExtractionError(f"7Z 解壓縮失敗: {stderr}")
            
            # 確保進程完全結束
            self.wait_for_process_completion(process)
            
            # 短暫等待檔案系統同步
            time.sleep(2)
            
            # 列出解壓縮的檔案
            extracted_files = []
            for item in extract_dir.rglob('*'):
                if item.is_file():
                    extracted_files.append(str(item.relative_to(extract_dir)))
            
            logger.info(f"7Z 解壓縮完成，共提取 {len(extracted_files)} 個檔案")
            return extracted_files
            
        except subprocess.TimeoutExpired:
            if process:
                process.kill()
                process.wait()
            raise ExtractionError("7Z 解壓縮超時")
            
        except subprocess.CalledProcessError as e:
            raise ExtractionError(f"7Z 解壓縮失敗: {e.stderr}")
            
        except FileNotFoundError:
            raise ExtractionError("未找到 7zip 工具，請安裝 7zip")
            
        finally:
            # 確保進程資源被釋放
            if process and process.poll() is None:
                try:
                    process.terminate()
                    process.wait(timeout=5)
                except:
                    try:
                        process.kill()
                        process.wait()
                    except:
                        pass
    
    def wait_for_process_completion(self, process) -> None:
        """
        等待進程完全完成
        
        Args:
            process: subprocess.Popen 物件
        """
        import time
        
        # 等待進程完成
        if process and process.poll() is None:
            process.wait()
        
        # 額外等待，確保檔案句柄釋放
        time.sleep(1)
    
    def _extract_rar(self, archive_path: Path, extract_dir: Path) -> List[str]:
        """
        解壓縮 RAR 檔案（需要 unrar 工具）
        
        Args:
            archive_path: RAR 檔案路徑
            extract_dir: 解壓縮目錄
            
        Returns:
            List[str]: 解壓縮的檔案列表
        """
        try:
            # 嘗試使用 unrar 命令列工具
            cmd = ['unrar', 'x', str(archive_path), str(extract_dir) + '/']
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            # 列出解壓縮的檔案
            extracted_files = []
            for item in extract_dir.rglob('*'):
                if item.is_file():
                    extracted_files.append(str(item.relative_to(extract_dir)))
            
            return extracted_files
            
        except subprocess.CalledProcessError as e:
            raise ExtractionError(f"RAR 解壓縮失敗: {e.stderr}")
        except FileNotFoundError:
            raise ExtractionError("未找到 unrar 工具，請安裝 unrar")
    
    def _extract_tar(self, archive_path: Path, extract_dir: Path) -> List[str]:
        """
        解壓縮 TAR/GZ 檔案
        
        Args:
            archive_path: TAR/GZ 檔案路徑
            extract_dir: 解壓縮目錄
            
        Returns:
            List[str]: 解壓縮的檔案列表
        """
        extracted_files = []
        
        try:
            # 根據副檔名選擇開啟模式
            if archive_path.suffix.lower() in ['.gz', '.tgz']:
                mode = 'r:gz'
            else:
                mode = 'r'
            
            with tarfile.open(archive_path, mode) as tar_ref:
                # 檢查是否有惡意路徑
                for member in tar_ref.getnames():
                    if os.path.isabs(member) or '..' in member:
                        raise ExtractionError(f"發現不安全的檔案路徑: {member}")
                
                tar_ref.extractall(extract_dir)
                extracted_files = tar_ref.getnames()
                
        except tarfile.TarError as e:
            raise ExtractionError(f"TAR 檔案錯誤: {e}")
        
        return extracted_files
    
    def get_archive_info(self, archive_path: str) -> Dict[str, Any]:
        """
        取得壓縮檔資訊（不解壓縮）
        
        Args:
            archive_path: 壓縮檔路徑
            
        Returns:
            Dict[str, Any]: 壓縮檔資訊
        """
        archive_path = Path(archive_path)
        
        if not archive_path.exists():
            raise ExtractionError(f"壓縮檔不存在: {archive_path}")
        
        file_extension = archive_path.suffix.lower().lstrip('.')
        info = {
            'filename': archive_path.name,
            'format': file_extension,
            'size': archive_path.stat().st_size,
            'size_mb': round(archive_path.stat().st_size / (1024 * 1024), 2)
        }
        
        try:
            if file_extension == 'zip':
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    info['file_count'] = len(zip_ref.namelist())
                    info['files'] = zip_ref.namelist()[:10]  # 只顯示前10個檔案
            elif file_extension in ['tar', 'gz', 'tgz']:
                mode = 'r:gz' if file_extension in ['gz', 'tgz'] else 'r'
                with tarfile.open(archive_path, mode) as tar_ref:
                    info['file_count'] = len(tar_ref.getnames())
                    info['files'] = tar_ref.getnames()[:10]  # 只顯示前10個檔案
            else:
                info['file_count'] = '未知'
                info['files'] = []
                
        except Exception as e:
            logger.warning(f"無法讀取壓縮檔資訊: {e}")
            info['file_count'] = '錯誤'
            info['files'] = []
        
        return info
    
    def cleanup_extracted_dir(self, extract_dir: str) -> bool:
        """
        清理解壓縮目錄
        
        Args:
            extract_dir: 解壓縮目錄路徑
            
        Returns:
            bool: 清理是否成功
        """
        try:
            path = Path(extract_dir)
            return self.temp_manager.remove_file_or_dir(path)
        except Exception as e:
            logger.error(f"清理解壓縮目錄失敗 {extract_dir}: {e}")
            return False