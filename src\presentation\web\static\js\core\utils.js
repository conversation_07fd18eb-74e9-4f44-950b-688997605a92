/**
 * 工具函數模組
 * 提供通用的工具函數和輔助方法
 */

class Utils {
    /**
     * 路徑轉換：Linux 路徑轉 Windows 路徑
     * @param {string} linuxPath - Linux 路徑
     * @returns {string} Windows 路徑
     */
    static convertToWindowsPath(linuxPath) {
        // 如果已經是Windows格式，避免重複處理
        if (linuxPath.includes(':\\')) {
            // 修正可能的雙反斜線問題
            return linuxPath.replace(/\\\\/g, '\\');
        }
        
        // 統一使用正斜線處理
        let normalizedPath = linuxPath.replace(/\\/g, '/');
        
        // 處理 /mnt/d/ 開頭的路徑
        if (normalizedPath.startsWith('/mnt/d/')) {
            normalizedPath = 'D:/' + normalizedPath.substring(7);
        }
        
        // 轉換為Windows反斜線格式
        let windowsPath = normalizedPath.replace(/\//g, '\\');
        
        // 確保磁碟機代號格式正確
        if (windowsPath.startsWith('D:\\')) {
            // 已經正確
        } else if (windowsPath.startsWith('D:')) {
            windowsPath = 'D:\\' + windowsPath.substring(2).replace(/^\\+/, '');
        } else {
            // 相對路徑，加上基礎路徑
            windowsPath = 'D:\\project\\python\\outlook_summary\\' + windowsPath;
        }
        
        // 最終清理：移除重複的反斜線
        windowsPath = windowsPath.replace(/\\\\/g, '\\');
        
        console.log(`📁 路徑轉換: ${linuxPath} → ${windowsPath}`);
        
        return windowsPath;
    }
    
    /**
     * 路徑轉換：Windows 路徑轉 Linux 路徑
     * @param {string} windowsPath - Windows 路徑
     * @returns {string} Linux 路徑
     */
    static convertToLinuxPath(windowsPath) {
        let linuxPath = windowsPath.replace(/\\/g, '/');
        
        if (linuxPath.startsWith('D:/')) {
            linuxPath = linuxPath.replace('D:/', '/mnt/d/');
        } else if (linuxPath.startsWith('D:')) {
            linuxPath = linuxPath.replace('D:', '/mnt/d');
        }
        
        console.log(`🔄 路徑轉換: ${windowsPath} -> ${linuxPath}`);
        
        return linuxPath;
    }
    
    /**
     * 從檔案路徑提取時間戳
     * @param {string} filePath - 檔案路徑
     * @returns {string} 時間戳字符串
     */
    static extractTimestamp(filePath) {
        const fileName = filePath.split('/').pop() || filePath.split('\\').pop();
        const match = fileName.match(/(\d{8})/);
        if (match) {
            const dateStr = match[1];
            const year = dateStr.substr(0, 4);
            const month = dateStr.substr(4, 2);
            const day = dateStr.substr(6, 2);
            return `${year}-${month}-${day}`;
        }
        return new Date().toISOString().split('T')[0];
    }
    
    /**
     * 檢測 CTA 格式
     * @param {string} filePath - 檔案路徑
     * @returns {boolean} 是否為 CTA 格式
     */
    static detectCTAFormat(filePath) {
        return filePath.toLowerCase().includes('cta') || 
               filePath.toLowerCase().includes('serial') ||
               filePath.toLowerCase().includes('index');
    }
    
    /**
     * 格式化檔案大小
     * @param {number} bytes - 位元組數
     * @returns {string} 格式化的檔案大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 格式化時間
     * @param {Date|string} date - 日期對象或字符串
     * @returns {string} 格式化的時間字符串
     */
    static formatDateTime(date) {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    
    /**
     * 生成唯一 ID
     * @param {string} prefix - 前綴
     * @returns {string} 唯一 ID
     */
    static generateUniqueId(prefix = 'id') {
        return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * 深拷貝對象
     * @param {Object} obj - 要拷貝的對象
     * @returns {Object} 拷貝後的對象
     */
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => this.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = this.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }
    
    /**
     * 防抖函數
     * @param {Function} func - 要防抖的函數
     * @param {number} wait - 等待時間（毫秒）
     * @returns {Function} 防抖後的函數
     */
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 節流函數
     * @param {Function} func - 要節流的函數
     * @param {number} limit - 限制時間（毫秒）
     * @returns {Function} 節流後的函數
     */
    static throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
    
    /**
     * 驗證檔案類型
     * @param {File} file - 檔案對象
     * @param {Array} allowedTypes - 允許的檔案類型
     * @returns {boolean} 是否為允許的類型
     */
    static validateFileType(file, allowedTypes = ['.zip', '.7z', '.rar', '.tar', '.gz']) {
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        return allowedTypes.includes(fileExtension);
    }
    
    /**
     * 驗證檔案大小
     * @param {File} file - 檔案對象
     * @param {number} maxSizeMB - 最大檔案大小（MB）
     * @returns {boolean} 是否在允許的大小範圍內
     */
    static validateFileSize(file, maxSizeMB) {
        const fileSizeMB = file.size / (1024 * 1024);
        return fileSizeMB <= maxSizeMB;
    }
    
    /**
     * 複製文本到剪貼簿
     * @param {string} text - 要複製的文本
     * @returns {Promise<boolean>} 是否複製成功
     */
    static async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            console.error('複製到剪貼簿失敗:', err);
            return false;
        }
    }
    
    /**
     * 下載檔案
     * @param {string} url - 下載 URL
     * @param {string} filename - 檔案名
     */
    static downloadFile(url, filename) {
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    /**
     * 等待指定時間
     * @param {number} ms - 等待時間（毫秒）
     * @returns {Promise} Promise 對象
     */
    static sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    
    /**
     * 檢查是否為有效的 URL
     * @param {string} string - 要檢查的字符串
     * @returns {boolean} 是否為有效 URL
     */
    static isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }
    
    /**
     * 轉義 HTML 字符
     * @param {string} text - 要轉義的文本
     * @returns {string} 轉義後的文本
     */
    static escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }
    
    /**
     * 計算兩個數字之間的百分比
     * @param {number} value - 當前值
     * @param {number} total - 總值
     * @returns {number} 百分比
     */
    static calculatePercentage(value, total) {
        if (total === 0) return 0;
        return Math.round((value / total) * 100);
    }
    
    /**
     * 格式化數字（添加千分位分隔符）
     * @param {number} num - 要格式化的數字
     * @returns {string} 格式化後的數字字符串
     */
    static formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }
}

// 導出 Utils 類
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Utils;
} else if (typeof window !== 'undefined') {
    window.Utils = Utils;
}
