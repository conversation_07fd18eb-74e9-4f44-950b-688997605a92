"""
POP3 適配器
提供底層 POP3 連接和操作功能
"""

import poplib
import socket
import ssl
from typing import List, Optional, Dict, Any, Tuple
from dataclasses import dataclass
from email import message_from_bytes
from email.message import EmailMessage
import logging
from contextlib import contextmanager
import time

from src.infrastructure.logging.logger_manager import LoggerManager


@dataclass
class POP3ConnectionConfig:
    """POP3 連接配置"""
    server: str
    port: int
    username: str
    password: str
    use_ssl: bool = False
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0


class POP3Adapter:
    """
    POP3 適配器
    提供底層 POP3 連接和郵件操作功能
    """
    
    def __init__(self, config: POP3ConnectionConfig):
        """
        初始化 POP3 適配器
        
        Args:
            config: POP3 連接配置
        """
        self.config = config
        self.logger = LoggerManager().get_logger("POP3Adapter")
        self._connection: Optional[poplib.POP3] = None
        self._is_connected = False
        
    def connect(self) -> bool:
        """
        連接到 POP3 伺服器
        
        Returns:
            連接成功與否
        """
        try:
            self.logger.info(f"正在連接到 POP3 伺服器: {self.config.server}:{self.config.port}")
            
            # 建立連接
            if self.config.use_ssl:
                self._connection = poplib.POP3_SSL(
                    self.config.server, 
                    self.config.port, 
                    timeout=self.config.timeout
                )
            else:
                self._connection = poplib.POP3(
                    self.config.server, 
                    self.config.port, 
                    timeout=self.config.timeout
                )
            
            # 取得歡迎訊息
            welcome_msg = self._connection.getwelcome()
            self.logger.info(f"伺服器歡迎訊息: {welcome_msg.decode('utf-8', errors='ignore')}")
            
            # 進行認證
            self._connection.user(self.config.username)
            self._connection.pass_(self.config.password)
            
            self._is_connected = True
            self.logger.info("POP3 連接和認證成功")
            return True
            
        except poplib.error_proto as e:
            self.logger.error(f"POP3 協議錯誤: {e}")
            return False
        except socket.timeout:
            self.logger.error("POP3 連接超時")
            return False
        except Exception as e:
            self.logger.error(f"POP3 連接失敗: {e}")
            return False
    
    def disconnect(self) -> None:
        """斷開 POP3 連接"""
        try:
            if self._connection and self._is_connected:
                self._connection.quit()
                self.logger.info("POP3 連接已斷開")
        except Exception as e:
            self.logger.warning(f"斷開 POP3 連接時發生警告: {e}")
        finally:
            self._connection = None
            self._is_connected = False
    
    def is_connected(self) -> bool:
        """
        檢查連接狀態
        
        Returns:
            是否已連接
        """
        return self._is_connected and self._connection is not None
    
    def get_mail_count(self) -> int:
        """
        取得郵件數量
        
        Returns:
            郵件數量
        """
        try:
            if not self.is_connected():
                raise Exception("POP3 未連接")
            
            # 取得郵件統計
            mail_count, total_size = self._connection.stat()
            self.logger.debug(f"郵件統計: {mail_count} 封郵件，總大小 {total_size} bytes")
            return mail_count
            
        except Exception as e:
            self.logger.error(f"取得郵件數量失敗: {e}")
            return 0
    
    def get_mail_list(self) -> List[Dict[str, Any]]:
        """
        取得郵件清單
        
        Returns:
            郵件清單，包含郵件號碼和大小
        """
        try:
            if not self.is_connected():
                raise Exception("POP3 未連接")
            
            # 取得郵件列表
            response, mail_list, octets = self._connection.list()
            
            mails = []
            for line in mail_list:
                line_str = line.decode('utf-8')
                parts = line_str.split()
                if len(parts) >= 2:
                    mail_num = int(parts[0])
                    mail_size = int(parts[1])
                    mails.append({
                        'number': mail_num,
                        'size': mail_size
                    })
            
            self.logger.debug(f"取得郵件清單: {len(mails)} 封郵件")
            return mails
            
        except Exception as e:
            self.logger.error(f"取得郵件清單失敗: {e}")
            return []
    
    def retrieve_mail(self, mail_number: int) -> Optional[EmailMessage]:
        """
        取得指定郵件
        
        Args:
            mail_number: 郵件號碼
            
        Returns:
            郵件訊息對象
        """
        try:
            if not self.is_connected():
                raise Exception("POP3 未連接")
            
            # 取得郵件內容
            response, lines, octets = self._connection.retr(mail_number)
            
            # 組合郵件內容
            raw_email = b'\n'.join(lines)
            
            # 解析郵件
            email_message = message_from_bytes(raw_email)
            
            self.logger.debug(f"成功取得郵件 #{mail_number}")
            return email_message
            
        except Exception as e:
            self.logger.error(f"取得郵件 #{mail_number} 失敗: {e}")
            return None
    
    def retrieve_mail_headers(self, mail_number: int) -> Optional[Dict[str, str]]:
        """
        取得郵件標頭
        
        Args:
            mail_number: 郵件號碼
            
        Returns:
            郵件標頭字典
        """
        try:
            if not self.is_connected():
                raise Exception("POP3 未連接")
            
            # 取得郵件標頭（通常是前幾行）
            response, lines, octets = self._connection.top(mail_number, 0)
            
            # 組合標頭內容
            header_data = b'\n'.join(lines)
            
            # 解析標頭
            email_message = message_from_bytes(header_data)
            
            headers = {}
            for key, value in email_message.items():
                headers[key.lower()] = value
            
            self.logger.debug(f"成功取得郵件 #{mail_number} 標頭")
            return headers
            
        except Exception as e:
            self.logger.error(f"取得郵件 #{mail_number} 標頭失敗: {e}")
            return None
    
    def delete_mail(self, mail_number: int) -> bool:
        """
        刪[EXCEPT_CHAR]郵件
        
        Args:
            mail_number: 郵件號碼
            
        Returns:
            刪[EXCEPT_CHAR]成功與否
        """
        try:
            if not self.is_connected():
                raise Exception("POP3 未連接")
            
            # 標記郵件為刪[EXCEPT_CHAR]
            self._connection.dele(mail_number)
            
            self.logger.debug(f"已標記郵件 #{mail_number} 為刪[EXCEPT_CHAR]")
            return True
            
        except Exception as e:
            self.logger.error(f"刪[EXCEPT_CHAR]郵件 #{mail_number} 失敗: {e}")
            return False
    
    def reset_session(self) -> bool:
        """
        重置 POP3 會話，取消所有刪[EXCEPT_CHAR]標記
        
        Returns:
            重置成功與否
        """
        try:
            if not self.is_connected():
                raise Exception("POP3 未連接")
            
            self._connection.rset()
            self.logger.debug("POP3 會話已重置")
            return True
            
        except Exception as e:
            self.logger.error(f"重置 POP3 會話失敗: {e}")
            return False
    
    def get_server_info(self) -> Dict[str, Any]:
        """
        取得伺服器資訊
        
        Returns:
            伺服器資訊字典
        """
        try:
            if not self.is_connected():
                return {"connected": False}
            
            mail_count, total_size = self._connection.stat()
            
            return {
                "connected": True,
                "server": self.config.server,
                "port": self.config.port,
                "use_ssl": self.config.use_ssl,
                "mail_count": mail_count,
                "total_size": total_size,
                "username": self.config.username
            }
            
        except Exception as e:
            self.logger.error(f"取得伺服器資訊失敗: {e}")
            return {"connected": False, "error": str(e)}
    
    @contextmanager
    def connection_context(self):
        """
        連接上下文管理器
        
        Usage:
            with adapter.connection_context():
                # 進行 POP3 操作
                pass
        """
        connected = self.connect()
        try:
            if connected:
                yield self
            else:
                raise Exception("無法建立 POP3 連接")
        finally:
            self.disconnect()
    
    def test_connection(self) -> Dict[str, Any]:
        """
        測試連接
        
        Returns:
            測試結果字典
        """
        test_result = {
            "success": False,
            "message": "",
            "server_info": {},
            "error": None
        }
        
        try:
            # 測試連接
            if self.connect():
                test_result["success"] = True
                test_result["message"] = "POP3 連接測試成功"
                test_result["server_info"] = self.get_server_info()
            else:
                test_result["message"] = "POP3 連接失敗"
                
        except Exception as e:
            test_result["error"] = str(e)
            test_result["message"] = f"POP3 連接測試異常: {e}"
        finally:
            self.disconnect()
        
        return test_result