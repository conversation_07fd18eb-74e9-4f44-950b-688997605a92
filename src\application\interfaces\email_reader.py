"""
郵件讀取器介面
定義郵件讀取和解析的抽象介面，支援依賴注入和測試
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.data_models.email_models import EmailData, EmailParsingResult


@dataclass
class EmailReaderConfig:
    """郵件讀取器配置"""
    max_emails_per_batch: int = 100
    include_read_emails: bool = False
    folder_path: Optional[str] = None
    date_filter_days: Optional[int] = None
    enable_caching: bool = True


class EmailReader(ABC):
    """
    郵件讀取器抽象基類
    定義郵件讀取、解析和管理的標準介面
    """
    
    @abstractmethod
    async def read_emails(self, count: Optional[int] = None) -> List[EmailData]:
        """
        讀取郵件列表
        
        Args:
            count: 要讀取的郵件數量，None 表示讀取所有
            
        Returns:
            郵件數據列表
        """
        pass
    
    @abstractmethod
    async def parse_email(self, email_data: EmailData) -> EmailParsingResult:
        """
        解析單一郵件
        
        Args:
            email_data: 郵件數據
            
        Returns:
            解析結果
        """
        pass
    
    @abstractmethod
    async def mark_as_processed(self, email_data: EmailData) -> bool:
        """
        標記郵件為已處理
        
        Args:
            email_data: 郵件數據
            
        Returns:
            操作成功與否
        """
        pass
    
    @abstractmethod
    def get_unread_count(self) -> int:
        """
        獲取未讀郵件數量
        
        Returns:
            未讀郵件數量
        """
        pass
    
    @abstractmethod
    async def search_emails(self, criteria: Dict[str, Any]) -> List[EmailData]:
        """
        搜尋符合條件的郵件
        
        Args:
            criteria: 搜尋條件 (subject, sender, date_range 等)
            
        Returns:
            符合條件的郵件列表
        """
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        檢查連接狀態
        
        Returns:
            是否已連接
        """
        pass
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        建立連接
        
        Returns:
            連接成功與否
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """斷開連接"""
        pass
    
    @abstractmethod
    def get_statistics(self) -> Dict[str, Any]:
        """
        獲取讀取器統計資訊
        
        Returns:
            統計資訊字典
        """
        pass