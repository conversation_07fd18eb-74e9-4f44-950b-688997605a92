"""
同步附件處理器
處理郵件同步時的附件下載和儲存
"""

from typing import List, Dict, Any, Optional
from pathlib import Path

from src.data_models.email_models import EmailData, EmailAttachment
from src.infrastructure.adapters.attachments.attachment_manager import AttachmentManager
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.logging.logger_manager import LoggerManager


class SyncAttachmentHandler:
    """同步附件處理器"""
    
    def __init__(self, database: EmailDatabase, 
                 attachment_manager: AttachmentManager = None):
        """
        初始化附件處理器
        
        Args:
            database: 郵件資料庫
            attachment_manager: 附件管理器
        """
        self.database = database
        self.attachment_manager = attachment_manager or AttachmentManager()
        self.logger = LoggerManager().get_logger("SyncAttachmentHandler")
        
    async def process_email_attachments(self, email_id: int, 
                                      email_data: EmailData) -> Dict[str, Any]:
        """
        處理郵件的所有附件
        
        Args:
            email_id: 郵件 ID
            email_data: 郵件資料
            
        Returns:
            處理結果
        """
        if not email_data.attachments:
            return {
                'success': True,
                'processed_count': 0,
                'failed_count': 0,
                'errors': []
            }
            
        self.logger.info(
            f"開始處理郵件 {email_id} 的 {len(email_data.attachments)} 個附件"
        )
        
        processed_count = 0
        failed_count = 0
        errors = []
        
        for attachment in email_data.attachments:
            try:
                # 取得附件內容
                content = await self._get_attachment_content(email_data, attachment)
                
                if content:
                    # 儲存附件
                    file_path = self.attachment_manager.save_attachment(
                        email_id, attachment, content
                    )
                    
                    if file_path:
                        # 更新資料庫中的附件路徑
                        success = self._update_attachment_path(
                            email_id, 
                            attachment.filename, 
                            str(file_path)
                        )
                        
                        if success:
                            processed_count += 1
                            self.logger.debug(
                                f"附件已處理: {attachment.filename} -> {file_path}"
                            )
                        else:
                            failed_count += 1
                            errors.append(f"無法更新附件路徑: {attachment.filename}")
                    else:
                        failed_count += 1
                        errors.append(f"無法儲存附件: {attachment.filename}")
                else:
                    failed_count += 1
                    errors.append(f"無法取得附件內容: {attachment.filename}")
                    
            except Exception as e:
                failed_count += 1
                error_msg = f"處理附件失敗 {attachment.filename}: {e}"
                errors.append(error_msg)
                self.logger.error(error_msg)
                
        result = {
            'success': failed_count == 0,
            'processed_count': processed_count,
            'failed_count': failed_count,
            'errors': errors
        }
        
        self.logger.info(
            f"附件處理完成: 成功 {processed_count}, 失敗 {failed_count}"
        )
        
        return result
        
    async def _get_attachment_content(self, email_data: EmailData, 
                                    attachment: EmailAttachment) -> Optional[bytes]:
        """
        取得附件內容
        
        從 POP3 讀取器儲存的臨時檔案中讀取內容
        
        Args:
            email_data: 郵件資料
            attachment: 附件資訊
            
        Returns:
            附件內容
        """
        try:
            # 從附件的 file_path 讀取內容（POP3 reader 會設定這個）
            if hasattr(attachment, 'file_path') and attachment.file_path:
                file_path = Path(str(attachment.file_path))
                if file_path.exists():
                    self.logger.debug(f"從臨時檔案讀取附件: {file_path}")
                    content = file_path.read_bytes()
                    
                    # 讀取後可選擇刪[EXCEPT_CHAR]臨時檔案
                    try:
                        file_path.unlink()
                        self.logger.debug(f"已刪[EXCEPT_CHAR]臨時檔案: {file_path}")
                    except Exception as e:
                        self.logger.warning(f"無法刪[EXCEPT_CHAR]臨時檔案 {file_path}: {e}")
                        
                    return content
                else:
                    self.logger.warning(f"附件臨時檔案不存在: {file_path}")
                    
            # 舊版相容性檢查
            if hasattr(attachment, 'content') and attachment.content:
                self.logger.debug(f"使用附件的 content 屬性")
                return attachment.content
                
            # 檢查是否有 get_content 方法
            if hasattr(attachment, 'get_content'):
                try:
                    content = await attachment.get_content()
                    return content
                except Exception as e:
                    self.logger.error(f"呼叫 get_content 失敗: {e}")
                    
            self.logger.warning(f"附件 {attachment.filename} 沒有可用的內容來源")
            return None
            
        except Exception as e:
            self.logger.error(f"讀取附件內容失敗 {attachment.filename}: {e}")
            return None
        
    def _update_attachment_path(self, email_id: int, filename: str, 
                               file_path: str) -> bool:
        """
        更新資料庫中的附件路徑
        
        Args:
            email_id: 郵件 ID
            filename: 附件檔名
            file_path: 檔案路徑
            
        Returns:
            是否成功更新
        """
        try:
            # 這裡需要在 EmailDatabase 中實作 update_attachment_path 方法
            # 暫時直接更新附件記錄
            from src.infrastructure.adapters.database.models import AttachmentDB
            
            with self.database.get_session() as session:
                attachment = session.query(AttachmentDB).filter_by(
                    email_id=email_id,
                    filename=filename
                ).first()
                
                if attachment:
                    attachment.file_path = file_path
                    attachment.is_processed = True
                    session.commit()
                    return True
                else:
                    self.logger.warning(
                        f"找不到附件記錄: email_id={email_id}, filename={filename}"
                    )
                    return False
                    
        except Exception as e:
            self.logger.error(f"更新附件路徑失敗: {e}")
            return False
            
    def cleanup_orphaned_attachments(self) -> Dict[str, int]:
        """
        清理孤立的附件（資料庫中已刪[EXCEPT_CHAR]但檔案系統中仍存在）
        
        Returns:
            清理統計
        """
        cleaned_count = 0
        error_count = 0
        
        try:
            # 取得所有郵件 ID
            with self.database.get_session() as session:
                from src.infrastructure.adapters.database.models import EmailDB
                email_ids = session.query(EmailDB.id).all()
                valid_email_ids = {id[0] for id in email_ids}
                
            # 檢查附件目錄
            for email_dir in self.attachment_manager.base_path.iterdir():
                if email_dir.is_dir():
                    # 提取郵件 ID
                    try:
                        email_id = int(email_dir.name.replace('email_', ''))
                        if email_id not in valid_email_ids:
                            # 刪[EXCEPT_CHAR]孤立的目錄
                            import shutil
                            shutil.rmtree(email_dir)
                            cleaned_count += 1
                            self.logger.info(f"已清理孤立附件目錄: {email_dir}")
                    except (ValueError, Exception) as e:
                        error_count += 1
                        self.logger.error(f"處理目錄 {email_dir} 時發生錯誤: {e}")
                        
        except Exception as e:
            self.logger.error(f"清理孤立附件失敗: {e}")
            
        return {
            'cleaned_count': cleaned_count,
            'error_count': error_count
        }