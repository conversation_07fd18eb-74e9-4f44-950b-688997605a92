#!/usr/bin/env python3
"""
CTA 檔案管理器
處理 CTA 檔案的載入和輸出功能
"""

import pandas as pd
import os
from pathlib import Path
from typing import Dict, List, Optional
import logging

from .cta_converter import CTAConverter

logger = logging.getLogger(__name__)


class CTAFileManager:
    """CTA 檔案管理器"""
    
    def __init__(self):
        self.converter = CTAConverter()
    
    def process_cta_file(self, input_file_path: str, output_dir: str = "logs") -> Dict:
        """
        處理 CTA 檔案的完整流程 (到 Data11 階段)
        
        Args:
            input_file_path: 輸入 CTA 檔案路徑
            output_dir: 輸出目錄
            
        Returns:
            處理結果摘要
        """
        try:
            input_path = Path(input_file_path)
            if not input_path.exists():
                raise FileNotFoundError(f"輸入檔案不存在: {input_file_path}")
                
            logger.info(f"開始處理 CTA 檔案: {input_file_path}")
            
            # 執行 convertOtherDatalog 核心處理
            success = self.converter.convert_other_datalog(str(input_path))
            
            if not success:
                return {
                    'input_file': str(input_path),
                    'processing_success': False,
                    'error': 'convertOtherDatalog 處理失敗'
                }
            
            # 準備輸出
            output_files = []
            output_path = Path(output_dir)
            output_path.mkdir(exist_ok=True)
            
            # 獲取處理摘要
            summary = self.converter.get_processing_summary()
            
            # 輸出 Data11 工作表 (如果存在)
            if "Data11" in summary['worksheets_created']:
                data11_output = self._save_data11_worksheet(input_path.name, output_path)
                if data11_output:
                    output_files.append(data11_output)
            
            # 生成最終結果摘要
            result_summary = {
                'input_file': str(input_path),
                'output_files': output_files,
                'output_directory': str(output_path),
                'tester_type': summary['tester_type'],
                'total_devices': summary['total_devices'],
                'worksheets_created': summary['worksheets_created'],
                'processing_success': True,
                'stage_completed': 'Data11'
            }
            
            logger.info(f"CTA 檔案處理完成，生成 {len(output_files)} 個輸出檔案")
            return result_summary
            
        except Exception as e:
            logger.error(f"處理 CTA 檔案失敗: {e}")
            return {
                'input_file': input_file_path,
                'processing_success': False,
                'error': str(e)
            }
    
    def _save_data11_worksheet(self, original_filename: str, output_dir: Path) -> Optional[str]:
        """
        使用專業的 CSV to Excel 轉換器儲存 Excel 檔案
        
        Args:
            original_filename: 原始檔案名稱
            output_dir: 輸出目錄
            
        Returns:
            輸出檔案路徑或 None
        """
        try:
            # 生成輸出檔名 (改為 Excel 格式)
            base_name = Path(original_filename).stem
            output_file = output_dir / f"{base_name}.xlsx"
            
            # 使用專業的 CSV to Excel 轉換器
            worksheet_names = self.converter.worksheet_manager.get_worksheet_names()
            
            if worksheet_names:
                # 找到主要的 Data11 工作表
                main_sheet_name = "Data11" if "Data11" in worksheet_names else worksheet_names[0]
                main_df = self.converter.worksheet_manager.get_worksheet(main_sheet_name)
                
                if main_df is not None and not main_df.empty:
                    # 使用專業轉換器
                    self._csv_to_excel_professional(main_df, output_file)
                    logger.debug(f"使用專業轉換器儲存: {output_file}")
                else:
                    # 回退到簡單方式
                    self._save_excel_simple(output_file, worksheet_names)
                    logger.debug(f"使用簡單方式儲存: {output_file}")
            else:
                logger.warning("沒有工作表可儲存")
                return None
            
            return str(output_file)
            
        except Exception as e:
            logger.error(f"儲存 Excel 檔案失敗: {e}")
            return None
    
    def _csv_to_excel_professional(self, df: pd.DataFrame, excel_file: Path) -> None:
        """
        專業的 CSV 到 Excel 轉換器 - 基於 CSV_TO_EXCEL_FINAL_BACKUP.md
        """
        
        print(f"[ROCKET] 使用專業轉換器: {excel_file}")
        print(f"[CHART] 處理: {df.shape[0]} 行 x {df.shape[1]} 欄")
        
        # 創建副本避免修改原始數據
        processed_df = df.copy()
        
        # 1. 第13行往下 - 智慧轉數字（保留字串）
        if len(processed_df) > 12:
            for row_idx in range(12, len(processed_df)):
                for col_idx in range(len(processed_df.columns)):
                    processed_df.iloc[row_idx, col_idx] = self._smart_convert_to_number(processed_df.iloc[row_idx, col_idx])
            print("[OK] 第13行往下 → 智慧數字")
        
        # 2. 第7行 - 智慧轉數字（保留字串）
        if len(processed_df) > 6:
            for col_idx in range(len(processed_df.columns)):
                processed_df.iloc[6, col_idx] = self._smart_convert_to_number(processed_df.iloc[6, col_idx])
            print("[OK] 第7行 → 智慧數字")
        
        # 3. 第6、9、10、11行 - 智慧轉數字（保留字串）
        for row_idx in [5, 8, 9, 10]:  # pandas index (6-1, 9-1, 10-1, 11-1)
            if len(processed_df) > row_idx:
                for col_idx in range(len(processed_df.columns)):
                    processed_df.iloc[row_idx, col_idx] = self._smart_convert_to_number(processed_df.iloc[row_idx, col_idx])
        print("[OK] 第6、9、10、11行 → 智慧數字")
        
        # 儲存為 Excel
        processed_df.to_excel(excel_file, index=False, header=False, engine='openpyxl')
        print(f"[OK] 完成: {excel_file}")
        
        # 快速驗證
        self._verify_excel_result(excel_file)
    
    def _verify_excel_result(self, excel_file: Path) -> None:
        """驗證 Excel 結果"""
        try:
            print(f"\n[SEARCH] 驗證 {excel_file}")
            
            import openpyxl
            wb = openpyxl.load_workbook(excel_file)
            ws = wb.active
            
            print(f"[RULER] Excel 範圍: {ws.calculate_dimension()}")
            
            # 檢查關鍵位置
            checks = [
                (13, 1, "A13"),
                (13, 2, "B13"), 
                (7, 4, "D7"),
                (6, 4, "D6"),
                (9, 4, "D9"),  # 特別檢查第9行
            ]
            
            for row, col, cell_name in checks:
                if row <= ws.max_row and col <= ws.max_column:
                    cell = ws.cell(row, col)
                    value = cell.value
                    excel_type = cell.data_type
                    status = "[OK] 數字" if excel_type == 'n' else "[WARNING] 字串"
                    print(f"  {cell_name}: {value} ({status})")
            
            wb.close()
            
        except Exception as e:
            logger.debug(f"Excel 驗證失敗: {e}")
    
    def _save_excel_simple(self, output_file: Path, worksheet_names: list) -> None:
        """簡單的多工作表 Excel 儲存（回退選項）"""
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            for sheet_name in worksheet_names:
                df = self.converter.worksheet_manager.get_worksheet(sheet_name)
                if df is not None and not df.empty:
                    safe_sheet_name = sheet_name[:31] if len(sheet_name) > 31 else sheet_name
                    df.to_excel(writer, sheet_name=safe_sheet_name, index=False, header=False)
                    logger.debug(f"工作表 '{sheet_name}' 已儲存到 Excel")
    
    def _is_pure_number(self, value) -> bool:
        """檢查是否為純數字字串，保留有意義的字串如 'none'"""
        if pd.isna(value) or value is None:
            return False
        
        if not isinstance(value, str):
            return isinstance(value, (int, float)) and not isinstance(value, bool)
        
        # 跳過明顯的文字
        clean_value = str(value).strip().lower()
        if clean_value in ['none', 'na', 'n/a', 'null', 'test_time', 'index_time', 'site_no', '']:
            return False
        
        # 檢查是否為純數字
        try:
            float(clean_value)
            return True
        except (ValueError, TypeError):
            return False
    
    def _smart_convert_to_number(self, value):
        """聰明轉換：只轉換純數字，保留文字字串"""
        # 處理空值
        if pd.isna(value) or value is None or str(value).strip() == '':
            return ""  # 空值轉為空字串
        
        if not self._is_pure_number(value):
            return value  # 保持原值
        
        clean_value = str(value).strip()
        try:
            # 嘗試轉換為整數
            if '.' not in clean_value and 'e' not in clean_value.lower():
                return int(clean_value)
            else:
                return float(clean_value)
        except (ValueError, TypeError):
            return value  # 保持原值
    
    def export_all_worksheets(self, output_dir: str = "logs") -> List[str]:
        """
        匯出所有工作表為 CSV 檔案
        
        Args:
            output_dir: 輸出目錄
            
        Returns:
            輸出檔案路徑列表
        """
        output_files = []
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        try:
            for sheet_name in self.converter.worksheet_manager.get_worksheet_names():
                df = self.converter.worksheet_manager.get_worksheet(sheet_name)
                if df is not None and not df.empty:
                    output_file = output_path / f"{sheet_name}.csv"
                    df.to_csv(output_file, index=False, encoding='utf-8')
                    output_files.append(str(output_file))
                    logger.debug(f"工作表已匯出: {output_file}")
                    
        except Exception as e:
            logger.error(f"匯出工作表失敗: {e}")
            
        return output_files
    
    def get_worksheet_info(self) -> Dict:
        """
        獲取工作表資訊
        
        Returns:
            工作表資訊字典
        """
        info = {}
        
        for sheet_name in self.converter.worksheet_manager.get_worksheet_names():
            df = self.converter.worksheet_manager.get_worksheet(sheet_name)
            if df is not None:
                info[sheet_name] = {
                    'rows': len(df),
                    'columns': len(df.columns),
                    'empty': df.empty
                }
                
        return info
    
    def validate_cta_format(self, file_path: str) -> bool:
        """
        驗證檔案是否為有效的 CTA 格式
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            True 如果是有效的 CTA 格式
        """
        try:
            # 簡單的格式檢查
            if not Path(file_path).suffix.lower() == '.csv':
                return False
                
            # 使用與轉換器相同的載入方式檢查
            cta_data = []
            with open(file_path, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    if line_num > 100:  # 只檢查前100行
                        break
                    line = line.strip()
                    if line:
                        # 分割鍵值對
                        parts = line.split(',', 1)
                        if len(parts) == 2:
                            key, value = parts
                            cta_data.append([key.strip(), value.rstrip(',').strip()])
                        else:
                            cta_data.append([parts[0].rstrip(',').strip(), ''])
            
            # 檢查 CTA 關鍵標記
            keys = [row[0] for row in cta_data]
            cta_markers = ['[GENERAL]', 'Tester_Type', 'Software_Name', 'Site_Count']
            found_markers = sum(1 for marker in cta_markers if marker in keys)
            
            # 至少要找到 2 個標記才認為是 CTA 格式
            return found_markers >= 2
            
        except Exception as e:
            logger.debug(f"CTA 格式驗證失敗: {e}")
            return False