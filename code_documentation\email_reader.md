# email_reader.py

郵件讀取器介面模組，定義郵件讀取和解析的抽象介面，支援依賴注入和測試。

## EmailReaderConfig

郵件讀取器配置資料類別，用於設定郵件讀取的各種參數。

### 屬性
- `max_emails_per_batch` (int): 每批次最大郵件數量，預設為100
- `include_read_emails` (bool): 是否包含已讀郵件，預設為False
- `folder_path` (Optional[str]): 資料夾路徑，可選
- `date_filter_days` (Optional[int]): 日期過濾天數，可選
- `enable_caching` (bool): 是否啟用快取，預設為True

## EmailReader

郵件讀取器抽象基類，定義郵件讀取、解析和管理的標準介面。

### read_emails

讀取郵件列表的抽象方法。

**參數:**
- `count` (Optional[int]): 要讀取的郵件數量，None 表示讀取所有

**返回值:**
- List[EmailData]: 郵件數據列表

**異常:**
- 由具體實作決定

### parse_email

解析單一郵件的抽象方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- EmailParsingResult: 解析結果

**異常:**
- 由具體實作決定

### mark_as_processed

標記郵件為已處理的抽象方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- bool: 操作成功與否

**異常:**
- 由具體實作決定

### get_unread_count

獲取未讀郵件數量的抽象方法。

**返回值:**
- int: 未讀郵件數量

**異常:**
- 由具體實作決定

### search_emails

搜尋符合條件的郵件的抽象方法。

**參數:**
- `criteria` (Dict[str, Any]): 搜尋條件 (subject, sender, date_range 等)

**返回值:**
- List[EmailData]: 符合條件的郵件列表

**異常:**
- 由具體實作決定

### is_connected

檢查連接狀態的抽象方法。

**返回值:**
- bool: 是否已連接

**異常:**
- 由具體實作決定

### connect

建立連接的抽象方法。

**返回值:**
- bool: 連接成功與否

**異常:**
- 由具體實作決定

### disconnect

斷開連接的抽象方法。

**返回值:**
- None

**異常:**
- 由具體實作決定

### get_statistics

獲取讀取器統計資訊的抽象方法。

**返回值:**
- Dict[str, Any]: 統計資訊字典

**異常:**
- 由具體實作決定
