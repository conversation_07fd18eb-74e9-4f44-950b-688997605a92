"""
FT-EQC 分組處理 FastAPI 應用程式
結構化、邏輯性、可讀性、維護性提升的模組化架構
完全採用 FastAPI 依賴注入機制
"""

# 優先設置 Unicode 環境
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..'))
try:
    from unicode_fix_global import setup_unicode_environment, apply_unicode_patches
    setup_unicode_environment()
    apply_unicode_patches()
except ImportError as e:
    print(f"警告：無法導入 unicode_fix_global：{e}")
    # 設置基本 Unicode 環境
    os.environ['PYTHONIOENCODING'] = 'utf-8'
    os.environ['PYTHONUTF8'] = '1'
import asyncio
import traceback
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from fastapi import FastAPI, HTTPException, Request, UploadFile, File, Form, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger

# 模型導入
from .models import (
    # 核心模型
    FTEQCGroupingRequest, FTEQCGroupingResponse, GroupingData, StatisticsData,
    EQCFailResult, HealthCheckResponse, ErrorResponse,
    # EQC 處理模型
    EQCBin1ScanRequest, EQCBin1ScanResponse, EQCBin1ScanData, EQCBin1Info,
    EQCStandardProcessRequest, EQCStandardProcessResponse, EQCStandardProcessData,
    EQCAdvancedProcessResponse,  # 新增進階處理回應模型
    EQCRealDataAnalysisResponse, ReportReadResponse, FileExistsCheckResponse,  # 新增回應模型
    OnlineEQCProcessRequest, OnlineEQCProcessResponse, OnlineEQCProcessData,
    EQCStep5TestFlowRequest, EQCStep5TestFlowResponse,
    # FT Summary 模型
    FTSummaryProcessRequest, FTSummaryProcessResponse,
    # 檔案上傳模型
    UploadConfigResponse, UploadResult, ArchiveInfo, ExtractionResult,
    UploadAndProcessRequest, UploadAndProcessResponse
)

# 服務模組導入
from .services.eqc_processing_service import EQCProcessingService
from .services.file_management_service import FileManagementService
from .services.cleanup_service import CleanupService
from .services.api_utils import (
    SystemConfig, DataParser, ResponseFormatter, ErrorHandler, APIUtils
)

# 舊版本處理器保留 (用於向下相容) 
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '../../..'))
from src.infrastructure.adapters.excel.ft_eqc_grouping_processor import (
    FTEQCGroupingProcessor, GroupingResult, OnlineEQCFailProcessor, CSVFileDiscovery
)

# FT Summary 批量處理器匯入
try:
    from batch_csv_to_excel_processor import BatchCsvToExcelProcessor
    FT_SUMMARY_AVAILABLE = True
    logger.info("[OK] FT Summary 處理器載入成功")
except ImportError as e:
    logger.error(f"FT Summary 處理器無法匯入: {e}")
    FT_SUMMARY_AVAILABLE = False
    BatchCsvToExcelProcessor = None

# ================================
# FastAPI 應用程式設定
# ================================

app = FastAPI(
    title="FT-EQC 分組處理 API",
    description="結構化、邏輯性、可讀性、維護性提升的模組化 API",
    version="2.0.0",
    docs_url="/docs", 
    redoc_url="/redoc"
)

# CORS 中介軟體設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 靜態檔案服務設定
static_dir = Path(__file__).parent.parent / "web" / "static"
if static_dir.exists():
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    logger.info(f"[OK] 靜態檔案服務已啟用: {static_dir}")
else:
    logger.warning(f"[WARNING] 靜態檔案目錄不存在: {static_dir}")

# favicon 路由
@app.get("/favicon.ico")
async def favicon():
    favicon_path = static_dir / "favicon.ico"
    if favicon_path.exists():
        return FileResponse(favicon_path)
    else:
        raise HTTPException(status_code=404, detail="Favicon not found")

# ================================
# 依賴注入提供器 (Dependency Providers)
# ================================

# 全域單例實例
_cleanup_service_instance = None
_system_config_instance = None

def get_system_config() -> SystemConfig:
    """取得系統配置 (單例模式)"""
    global _system_config_instance
    if _system_config_instance is None:
        _system_config_instance = SystemConfig()
    return _system_config_instance

def get_eqc_processing_service() -> EQCProcessingService:
    """取得 EQC 處理服務"""
    return EQCProcessingService()

def get_file_management_service() -> FileManagementService:
    """取得檔案管理服務"""
    return FileManagementService()

def get_cleanup_service() -> CleanupService:
    """取得清理服務 (單例模式)"""
    global _cleanup_service_instance
    if _cleanup_service_instance is None:
        _cleanup_service_instance = CleanupService()
    return _cleanup_service_instance

def get_data_parser() -> DataParser:
    """取得資料解析器"""
    return DataParser()

def get_response_formatter() -> ResponseFormatter:
    """取得回應格式化器"""
    return ResponseFormatter()

def get_error_handler() -> ErrorHandler:
    """取得錯誤處理器"""
    return ErrorHandler()

# 向下相容處理器 (漸進式移轉)
def get_legacy_grouping_processor() -> FTEQCGroupingProcessor:
    """取得舊版本分組處理器 (向下相容)"""
    return FTEQCGroupingProcessor()

def get_legacy_online_fail_processor() -> OnlineEQCFailProcessor:
    """取得舊版本線上失敗處理器 (向下相容)"""
    return OnlineEQCFailProcessor()

def get_csv_file_discovery() -> CSVFileDiscovery:
    """取得 CSV 檔案探索器 (向下相容)"""
    return CSVFileDiscovery()

# ================================
# 全域異常處理器
# ================================

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """全域異常處理器 - 統一錯誤處理"""
    logger.error(f"API 請求發生未預期錯誤: {str(exc)}")
    logger.error(f"錯誤追蹤: {traceback.format_exc()}")
    
    return JSONResponse(
        status_code=500,
        content={
            "error": str(exc), 
            "type": "internal_error",
            "timestamp": datetime.now().isoformat()
        }
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """HTTP 異常處理器"""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": exc.detail, 
            "type": "http_error",
            "timestamp": datetime.now().isoformat()
        }
    )

# ================================
# 核心 API 端點
# ================================

@app.get("/health", response_model=HealthCheckResponse)
async def health_check(
    config: SystemConfig = Depends(get_system_config),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> HealthCheckResponse:
    """健康檢查端點"""
    return formatter.format_health_response(config)

@app.get("/api/health", response_model=HealthCheckResponse)
async def health_check_legacy(
    config: SystemConfig = Depends(get_system_config),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> HealthCheckResponse:
    """健康檢查端點 (向下相容)"""
    return formatter.format_health_response(config)

@app.get("/")
async def root():
    """根路由"""
    return {"message": "FT-EQC 分組處理 API 服務正常運行", "version": "2.0.0"}

@app.get("/ui")
async def get_ui():
    """提供 FT-EQC 分組 UI 介面 (模組化版本)"""
    import os
    from pathlib import Path

    # 獲取模組化版本的 HTML 檔案路徑
    current_dir = Path(__file__).parent.parent
    ui_file = current_dir / "web" / "templates" / "ft_eqc_grouping_ui_modular.html"

    logger.info(f"[SEARCH] 檢查模組化 UI 檔案路徑: {ui_file}")
    logger.info(f"[SEARCH] 檔案是否存在: {ui_file.exists()}")

    if not ui_file.exists():
        # 如果模組化版本不存在，回退到原始版本
        ui_file = current_dir / "web" / "templates" / "ft_eqc_grouping_ui.html"
        logger.info(f"[SEARCH] 回退到原始版本路徑: {ui_file}")
        logger.info(f"[SEARCH] 原始版本是否存在: {ui_file.exists()}")

        if not ui_file.exists():
            raise HTTPException(status_code=404, detail="UI 檔案未找到")
        logger.warning("[WARNING] 模組化 UI 檔案不存在，使用原始版本")
    else:
        logger.info("[OK] 使用模組化 UI 版本")

    logger.info(f"[PAGE_FACING_UP] 最終使用的檔案: {ui_file}")

    return FileResponse(
        path=str(ui_file),
        media_type="text/html"
    )

# ================================
# EQC 處理相關端點
# ================================

@app.post("/api/scan_eqc_bin1", response_model=EQCBin1ScanResponse)
async def scan_eqc_bin1(
    request: EQCBin1ScanRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service)
) -> EQCBin1ScanResponse:
    """掃描 EQC BIN1 檔案"""
    try:
        result = await eqc_service.scan_eqc_bin1(request)
        return result
    except Exception as e:
        logger.error(f"EQC BIN1 掃描失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/process_eqc_standard", response_model=EQCStandardProcessResponse)
async def process_eqc_standard(
    request: EQCStandardProcessRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCStandardProcessResponse:
    """處理 EQC 標準流程"""
    try:
        # 將 Pydantic 模型轉換為 dict
        result = await eqc_service.process_eqc_standard(request.dict())
        return formatter.format_eqc_standard_response(result)
    except Exception as e:
        logger.error(f"EQC 標準處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/process_eqc_advanced", response_model=EQCAdvancedProcessResponse)
async def process_eqc_advanced(
    request: EQCStandardProcessRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCAdvancedProcessResponse:
    """處理 EQC 進階流程 (雙階段處理)"""
    try:
        # 將 Pydantic 模型轉換為 dict
        result = await eqc_service.process_eqc_advanced(request.dict())
        
        # 進階流程會回傳一個包含多個階段結果的複雜物件。
        # 我們需要從中提取第二階段 (標準處理) 的結果，
        # 因為 `formatter.format_eqc_standard_response` 和 `EQCStandardProcessResponse` 模型
        # 是針對標準流程的輸出所設計的。
        integrated_result = result.get('integrated_result', {})
        results_list = integrated_result.get('results', [])

        # 檢查是否存在第二階段的成功結果
        if len(results_list) > 1 and results_list[1].get('status') == 'success':
            stage2_data = results_list[1].get('data', {})
            # 使用第二階段的資料來格式化回應，構建正確的回應模型
            formatted_response = formatter.format_eqc_standard_response(stage2_data)
            
            # 構建符合EQCAdvancedProcessResponse模型的回應
            advanced_response = EQCAdvancedProcessResponse(
                status=formatted_response.get("status", "success"),
                message=formatted_response.get("message", "EQC 進階處理完成 (雙階段)"),
                data=formatted_response.get("data"),
                processing_time=result.get('processing_time', 0.0),
                code_regions=result.get('code_regions'),  # 包含使用者指定的 CODE 區間
                api_version="2.0.0",
                timestamp=ResponseFormatter.get_current_timestamp()
            )
            
            return advanced_response
        else:
            # 如果找不到預期的第二階段資料或處理失敗，則記錄錯誤並引發 HTTP 異常
            error_message = "EQC 進階處理的第二階段失敗或結果格式不符預期。"
            logger.error(f"{error_message} Result: {result}")
            raise HTTPException(status_code=500, detail=error_message)
            
    except Exception as e:
        logger.error(f"EQC 進階處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/process_online_eqc", response_model=OnlineEQCProcessResponse)
async def process_online_eqc(
    request: OnlineEQCProcessRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service)
) -> OnlineEQCProcessResponse:
    """處理線上 EQC 流程"""
    try:
        # EQCProcessingService.process_online_eqc 已經返回正確的 OnlineEQCProcessResponse 物件
        result = await eqc_service.process_online_eqc(request)
        # [TOOL] 統一化：直接回傳 Pydantic 物件，讓 FastAPI 處理序列化
        return result
    except Exception as e:
        logger.error(f"線上 EQC 處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/generate_eqc_step5_flow", response_model=EQCStep5TestFlowResponse)
async def generate_eqc_step5_flow(
    request: EQCStep5TestFlowRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCStep5TestFlowResponse:
    """生成 EQC Step5 測試流程"""
    try:
        result = await eqc_service.generate_test_flow(request)
        return formatter.format_step5_flow_response(result)
    except Exception as e:
        logger.error(f"EQC Step5 流程生成失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/eqc/generate_test_flow", response_model=EQCStep5TestFlowResponse)
async def generate_test_flow_legacy(
    request: EQCStep5TestFlowRequest,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCStep5TestFlowResponse:
    """生成 EQC 測試流程 (向下相容)"""
    try:
        result = await eqc_service.generate_test_flow(request)
        return formatter.format_step5_flow_response(result)
    except Exception as e:
        logger.error(f"EQC 測試流程生成失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analyze_eqc_real_data", response_model=EQCRealDataAnalysisResponse)
async def analyze_eqc_real_data(
    request: dict,
    eqc_service: EQCProcessingService = Depends(get_eqc_processing_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> EQCRealDataAnalysisResponse:
    """分析 EQC 真實資料"""
    try:
        # 從請求中提取資料夾路徑，如果沒有則使用預設邏輯
        folder_path = request.get('folder_path', '')
        result = await eqc_service.analyze_real_data(request)
        
        # [TOOL] 優化：使用統一的Pydantic回應模型
        analysis_response = EQCRealDataAnalysisResponse(
            status=result.get("status", "success"),
            online_eqc_fail=result.get("online_eqc_fail", 0),
            eqc_rt_pass=result.get("eqc_rt_pass", 0),
            match_rate=result.get("match_rate", "0%"),
            total_matches=result.get("total_matches", 0),
            total_rows=result.get("total_rows", 0),
            matched_count=result.get("matched_count", 0),
            search_method=result.get("search_method", "未知"),
            search_status=result.get("search_status", "未知"),
            folder_path=result.get("folder_path", folder_path),
            summary_data=result.get("summary_data"),
            api_version="2.0.0",
            timestamp=ResponseFormatter.get_current_timestamp()
        )
        
        return analysis_response
    except Exception as e:
        logger.error(f"EQC 真實資料分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 檔案管理相關端點
# ================================

@app.get("/api/upload_config", response_model=UploadConfigResponse)
async def get_upload_config(
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> UploadConfigResponse:
    """取得上傳配置"""
    try:
        config = await file_service.get_upload_config()
        return formatter.format_upload_config_response(config)
    except Exception as e:
        logger.error(f"取得上傳配置失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upload_file", response_model=UploadResult)
async def upload_file(
    file: UploadFile = File(...),
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> UploadResult:
    """上傳檔案"""
    try:
        result = await file_service.upload_archive(file)
        return formatter.format_upload_result(result)
    except Exception as e:
        logger.error(f"檔案上傳失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/upload_and_process", response_model=UploadAndProcessResponse)
async def upload_and_process(
    request: UploadAndProcessRequest,
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> UploadAndProcessResponse:
    """上傳並處理檔案"""
    try:
        result = await file_service.upload_and_process(request)
        return formatter.format_upload_process_response(result)
    except Exception as e:
        logger.error(f"上傳並處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/download_file")
async def download_file(
    file_path: str,
    file_service: FileManagementService = Depends(get_file_management_service)
) -> FileResponse:
    """通用檔案下載端點"""
    try:
        # 使用檔案管理服務處理下載
        processed_path = await file_service.process_download_path(file_path)

        # 檢查檔案是否存在
        if not Path(processed_path).exists():
            raise HTTPException(status_code=404, detail=f"檔案不存在：{Path(file_path).name}")

        # 根據檔案副檔名設定 MIME 類型
        if file_path.endswith('.xlsx'):
            media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        elif file_path.endswith('.csv'):
            media_type = 'text/csv'
        elif file_path.endswith('.txt'):
            media_type = 'text/plain'
        else:
            media_type = 'application/octet-stream'

        return FileResponse(
            path=processed_path,
            filename=Path(processed_path).name,
            media_type=media_type
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"檔案下載失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下載失敗: {str(e)}")

@app.get("/api/today_processed_files")
async def get_today_processed_files(
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """取得今日處理的檔案"""
    try:
        result = await file_service.get_today_processed_files()
        # get_today_processed_files 已經回傳格式化的結果，直接回傳
        return result
    except Exception as e:
        logger.error(f"取得今日檔案失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/read_report", response_model=ReportReadResponse)
async def read_report(
    request: dict,
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> ReportReadResponse:
    """讀取 EQC 處理報告內容"""
    try:
        report_path = request.get('report_path', '')
        if not report_path:
            raise HTTPException(status_code=400, detail="報告路徑不能為空")

        content = await file_service.read_report_content(report_path)
        
        # [TOOL] 優化：使用統一的Pydantic回應模型
        report_response = ReportReadResponse(
            status="success",
            content=content,
            file_name=Path(report_path).name,
            api_version="2.0.0",
            timestamp=ResponseFormatter.get_current_timestamp()
        )
        
        return report_response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"讀取報告失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"讀取報告時發生錯誤: {str(e)}")

@app.get("/api/download_report")
async def download_report(
    report_path: str,
    file_service: FileManagementService = Depends(get_file_management_service)
):
    """下載 EQC 處理報告檔案"""
    try:
        processed_path = await file_service.process_download_path(report_path)

        if not Path(processed_path).exists():
            raise HTTPException(status_code=404, detail="報告檔案不存在")

        return FileResponse(
            path=processed_path,
            filename=Path(processed_path).name,
            media_type='application/octet-stream'
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"下載報告失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"下載失敗: {str(e)}")

@app.post("/api/upload_archive")
async def upload_archive(
    file: UploadFile = File(...),
    file_service: FileManagementService = Depends(get_file_management_service)
):
    """檔案上傳端點（單純上傳+解壓縮）"""
    try:
        result = await file_service.upload_archive(file)
        return result
    except Exception as e:
        logger.error(f"檔案上傳失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/clear_duplicate_cache")
async def clear_duplicate_cache(
    file_service: FileManagementService = Depends(get_file_management_service)
):
    """清[EXCEPT_CHAR]重複上傳快取記錄"""
    try:
        result = await file_service.clear_duplicate_cache()
        return {
            "status": "success",
            "message": "重複上傳快取已清[EXCEPT_CHAR]",
            "cleared_count": result.get("cleared_count", 0)
        }
    except Exception as e:
        logger.error(f"清[EXCEPT_CHAR]快取失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/archive_info")
async def get_archive_info(
    archive_path: str,
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """取得壓縮檔資訊（不解壓縮）"""
    try:
        info = await file_service.get_archive_info(archive_path)
        return formatter.format_archive_info_response(info)
    except Exception as e:
        logger.error(f"取得壓縮檔資訊失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/check_file_exists", response_model=FileExistsCheckResponse)
async def check_file_exists(
    request: dict,
    file_service: FileManagementService = Depends(get_file_management_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> FileExistsCheckResponse:
    """檢查檔案是否存在"""
    try:
        file_path = request.get('file_path', '')
        result = await file_service.check_file_exists(file_path)
        
        # [TOOL] 統一化：使用 Pydantic 回應模型
        response = FileExistsCheckResponse(
            exists=result.get('file_exists', False),
            file_path=file_path,
            api_version="2.0.0",
            timestamp=ResponseFormatter.get_current_timestamp(),
            report_file=None  # 根據實際需求可能有處理報告檔案路徑
        )
        
        return response
    except Exception as e:
        logger.error(f"檔案檢查失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 清理服務相關端點
# ================================


@app.get("/api/temp_files_info")
async def get_temp_files_info(
    cleanup_service: CleanupService = Depends(get_cleanup_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """取得暫存檔案資訊"""
    try:
        info = await cleanup_service.get_temp_files_info()
        return formatter.format_temp_files_info(info)
    except Exception as e:
        logger.error(f"取得暫存檔案資訊失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/cleanup_temp_files")
async def cleanup_temp_files(
    cleanup_service: CleanupService = Depends(get_cleanup_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """清理臨時檔案"""
    try:
        result = await cleanup_service.cleanup_temp_files()
        return formatter.format_cleanup_response(result)
    except Exception as e:
        logger.error(f"清理臨時檔案失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/cleanup_files_manual")
async def manual_cleanup_files(
    cleanup_service: CleanupService = Depends(get_cleanup_service)
):
    """手動觸發檔案清理"""
    try:
        result = await cleanup_service.manual_cleanup_files()
        return {
            "status": "success",
            "message": "手動清理完成",
            "data": result.get("data", {}),
            "total_cleaned": result.get("data", {}).get("total_cleaned", 0)
        }
    except HTTPException:
        # 保持原始HTTP狀態碼，不轉換為500
        raise
    except Exception as e:
        logger.error(f"手動清理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/cleanup_status")
async def get_cleanup_status(
    cleanup_service: CleanupService = Depends(get_cleanup_service),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """取得清理狀態"""
    try:
        status = await cleanup_service.get_cleanup_status()
        return formatter.format_status_response(status)
    except Exception as e:
        logger.error(f"取得清理狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/start_cleanup_scheduler")
async def start_cleanup_scheduler(
    cleanup_service: CleanupService = Depends(get_cleanup_service)
):
    """啟動清理調度器"""
    try:
        result = await cleanup_service.start_scheduler()
        return {"message": "清理調度器已啟動", "result": result}
    except Exception as e:
        logger.error(f"啟動清理調度器失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/stop_cleanup_scheduler")
async def stop_cleanup_scheduler(
    cleanup_service: CleanupService = Depends(get_cleanup_service)
):
    """停止清理調度器"""
    try:
        result = await cleanup_service.stop_scheduler()
        return {"message": "清理調度器已停止", "result": result}
    except Exception as e:
        logger.error(f"停止清理調度器失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# 向下相容端點 (漸進式移轉)
# ================================

@app.post("/api/ft_eqc_grouping", response_model=FTEQCGroupingResponse)
async def ft_eqc_grouping(
    request: FTEQCGroupingRequest,
    processor: FTEQCGroupingProcessor = Depends(get_legacy_grouping_processor),
    formatter: ResponseFormatter = Depends(get_response_formatter)
) -> FTEQCGroupingResponse:
    """FT-EQC 分組處理 (向下相容)"""
    try:
        # 修復方法名：process_grouping -> process_folder
        folder_path = request.dict().get('folder_path', '')
        result = processor.process_folder(folder_path)
        return formatter.format_grouping_response(result)
    except Exception as e:
        logger.error(f"FT-EQC 分組處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/online_eqc_fail_analysis")
async def online_eqc_fail_analysis(
    processor: OnlineEQCFailProcessor = Depends(get_legacy_online_fail_processor),
    formatter: ResponseFormatter = Depends(get_response_formatter)
):
    """Online EQC 失敗分析 (向下相容)"""
    try:
        result = processor.analyze_failures()
        return formatter.format_analysis_response(result)
    except Exception as e:
        logger.error(f"Online EQC 失敗分析失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ================================
# FT Summary 批量處理端點
# ================================

@app.post("/api/process_ft_summary", response_model=FTSummaryProcessResponse)
async def process_ft_summary(
    request: FTSummaryProcessRequest
) -> FTSummaryProcessResponse:
    """FT Summary 批量處理：輸入資料夾路徑，自動生成 FT_SUMMARY.csv"""
    try:
        logger.info(f"開始 FT Summary 批量處理：{request.folder_path}")
        
        # 檢查 FT Summary 處理器是否可用
        if not FT_SUMMARY_AVAILABLE or BatchCsvToExcelProcessor is None:
            raise HTTPException(
                status_code=503, 
                detail="FT Summary 處理器無法使用，請安裝相關依賴：pip install pandas openpyxl numpy xlsxwriter"
            )
        
        # 使用現有的路徑轉換邏輯 (與 /ui 端點相同)
        original_path, folder_path = APIUtils.process_folder_path(request.folder_path)
        
        # 驗證轉換後的路徑
        is_valid, error_msg = APIUtils.validate_folder_path(folder_path)
        if not is_valid:
            logger.error(f"路徑驗證失敗: {error_msg} (原路徑: {original_path})")
            return FTSummaryProcessResponse(
                status="error",
                message=f"路徑驗證失敗: {error_msg}",
                error_message=f"原始路徑: {original_path}, 轉換路徑: {folder_path}"
            )
        
        # 直接執行 csv_to_summary.py 命令
        import subprocess
        import time
        
        # 轉換為相對路徑（如同您直接使用的方式）
        # 從絕對路徑轉換為相對路徑
        import os
        relative_path = os.path.relpath(folder_path, os.getcwd())
        
        # 構建命令
        cmd = [sys.executable, "csv_to_summary.py", relative_path]
        if request.processing_mode == "full":
            cmd.append("--excel")
        cmd.append("--verbose")
        
        logger.info(f"執行命令: {' '.join(cmd)}")
        logger.info(f"工作目錄: {os.getcwd()}")
        logger.info(f"相對路徑: {relative_path}")
        
        # 記錄開始時間
        start_time = time.perf_counter()
        
        # 執行命令
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.getcwd())
        
        # 計算處理時間
        processing_time = time.perf_counter() - start_time
        
        if result.returncode == 0:
                # 成功：構建回應
                logger.info("csv_to_summary.py 執行成功")
                
                # 檢查生成的檔案
                ft_summary_csv = os.path.join(folder_path, "FT_SUMMARY.csv")
                ft_summary_xlsx = os.path.join(folder_path, "FT_SUMMARY.xlsx")
                eqc_summary_csv = os.path.join(folder_path, "EQC_SUMMARY.csv")
                eqc_summary_xlsx = os.path.join(folder_path, "EQC_SUMMARY.xlsx")
                
                # 統計檔案數量
                import glob
                csv_files = glob.glob(os.path.join(folder_path, "*.csv"))
                ft_files = [f for f in csv_files if any(keyword in f.lower() for keyword in ["ft1", "ft2", "ft3", "final_test"])]
                eqc_files = [f for f in csv_files if any(keyword in f.lower() for keyword in ["eqc", "onlieeqc"])]
                
                # 構建回應
                return FTSummaryProcessResponse(
                    status="success",
                    message=f"成功處理 {len(csv_files)} 個檔案",
                    total_files=len(csv_files),
                    processed_files=len(ft_files) + len(eqc_files),
                    skipped_files=0,
                    failed_files=0,
                    ft_summary_files=len(ft_files),
                    ft_summary_output_file=ft_summary_xlsx if os.path.exists(ft_summary_xlsx) else ft_summary_csv,
                    processing_time_seconds=processing_time,
                    eqc_summary_files=len(eqc_files),
                    eqc_summary_output_file=eqc_summary_xlsx if os.path.exists(eqc_summary_xlsx) else eqc_summary_csv,
                    eqc_all_pass_file=None,
                    ft_file_list=[os.path.basename(f) for f in ft_files],
                    eqc_file_list=[os.path.basename(f) for f in eqc_files]
                )
        else:
            # 失敗：回傳錯誤訊息
            logger.error(f"csv_to_summary.py 執行失敗，返回代碼: {result.returncode}")
            logger.error(f"錯誤輸出: {result.stderr}")
            return FTSummaryProcessResponse(
                status="error",
                message=f"處理失敗，返回代碼: {result.returncode}",
                total_files=0,
                processed_files=0,
                failed_files=0,
                processing_time_seconds=processing_time,
                error_message=result.stderr or f"返回代碼: {result.returncode}",
                eqc_summary_files=0,
                eqc_summary_output_file=None,
                eqc_all_pass_file=None,
                ft_file_list=[],
                eqc_file_list=[]
            )
            
    except Exception as e:
        logger.error(f"FT Summary 批量處理異常：{str(e)}")
        raise HTTPException(status_code=500, detail=f"處理失敗：{str(e)}")

@app.get("/api/ft-summary-status")
async def ft_summary_status():
    """檢查 FT Summary 功能狀態"""
    try:
        status = {
            "ft_summary_available": FT_SUMMARY_AVAILABLE,
            "required_files": {},
            "processor_status": "unknown",
            "description": "FT Summary 批量處理：CSV 到 Excel 轉換和 Summary 生成"
        }
        
        # 檢查關鍵檔案
        key_files = [
            "batch_csv_to_excel_processor.py",
            "src/presentation/web/templates/ft_summary_ui.html"
        ]
        
        for file_path in key_files:
            status["required_files"][file_path] = os.path.exists(file_path)
        
        # 如果 FT Summary 可用，測試處理器初始化
        if FT_SUMMARY_AVAILABLE and BatchCsvToExcelProcessor is not None:
            try:
                processor = BatchCsvToExcelProcessor(enable_logging=False)
                status["processor_status"] = "ready"
            except Exception as e:
                status["processor_status"] = f"error: {str(e)}"
        else:
            status["processor_status"] = "unavailable"
        
        return status
        
    except Exception as e:
        logger.error(f"檢查 FT Summary 狀態失敗：{str(e)}")
        return {"error": str(e)}

@app.get("/ft-summary-ui")
async def ft_summary_ui():
    """提供 FT Summary 處理 Web 介面"""
    try:
        # 先檢查 FT Summary 是否可用
        if not FT_SUMMARY_AVAILABLE or BatchCsvToExcelProcessor is None:
            error_html = """
            <!DOCTYPE html>
            <html lang="zh-TW">
            <head>
                <meta charset="UTF-8">
                <title>FT Summary - 服務不可用</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    .error { color: #d32f2f; }
                </style>
            </head>
            <body>
                <h1 class="error">FT Summary 服務目前不可用</h1>
                <p>請安裝相關依賴：pip install pandas openpyxl numpy xlsxwriter</p>
                <a href="/api/ft-summary-status">檢查狀態</a>
            </body>
            </html>
            """
            return HTMLResponse(content=error_html, status_code=503)
        
        template_path = os.path.join(
            os.path.dirname(__file__), 
            "../web/templates/ft_summary_ui.html"
        )
        
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return HTMLResponse(content=content)
        else:
            # 如果模板檔案不存在，返回簡單的 HTML 介面
            simple_html = """
            <!DOCTYPE html>
            <html lang="zh-TW">
            <head>
                <meta charset="UTF-8">
                <title>FT Summary 批量處理</title>
            </head>
            <body>
                <h1>FT Summary 批量處理</h1>
                <p>模板檔案載入中...</p>
            </body>
            </html>
            """
            return HTMLResponse(content=simple_html)
    except Exception as e:
        logger.error(f"載入 FT Summary 介面失敗：{str(e)}")
        raise HTTPException(status_code=500, detail="無法載入介面")

# ================================
# 應用程式生命週期事件
# ================================

@app.on_event("startup")
async def startup_event():
    """應用程式啟動事件"""
    logger.info("[START] FT-EQC API 服務啟動中...")
    logger.info("[OK] 模組化架構已載入")
    logger.info("[OK] FastAPI 依賴注入機制已啟用")
    
    # 非同步初始化清理服務，避免阻塞主線程
    try:
        # 啟用自動初始化清理服務
        asyncio.create_task(initialize_cleanup_service_async())
        logger.info("[OK] 檔案清理服務初始化已啟動")
    except Exception as e:
        logger.error(f"[ERROR] 檔案清理服務初始化失敗: {str(e)}")
    
    logger.info("[OK] 所有服務模組已初始化")

async def initialize_cleanup_service_async():
    """非同步初始化清理服務，避免阻塞啟動流程"""
    try:
        await asyncio.sleep(0.1)  # 讓主線程繼續
        from .services.file_cleanup_scheduler import FileCleanupScheduler
        cleanup_scheduler = FileCleanupScheduler()
        cleanup_service = get_cleanup_service()
        cleanup_service.set_cleanup_scheduler(cleanup_scheduler)
        logger.info("[OK] 清理服務非同步初始化完成")
    except Exception as e:
        logger.error(f"[ERROR] 清理服務非同步初始化失敗: {str(e)}")

@app.on_event("shutdown")
async def shutdown_event():
    """應用程式關閉事件"""
    logger.info("🛑 FT-EQC API 服務正在關閉...")
    
    # 優雅關閉背景清理服務
    try:
        cleanup_service = get_cleanup_service()
        if cleanup_service.cleanup_scheduler:
            result = cleanup_service.cleanup_scheduler.stop_scheduler()
            logger.info("[OK] 背景清理調度器已優雅關閉")
        else:
            logger.info("[BOARD] 清理調度器未運行，無需關閉")
    except Exception as e:
        logger.error(f"[ERROR] 關閉背景調度器時發生錯誤: {str(e)}")
    
    logger.info("[OK] 服務關閉完成")

# ================================
# 開發環境執行設定
# ================================

if __name__ == "__main__":
    import uvicorn
    
    # 檢查是否為開發環境
    is_dev = os.getenv("ENVIRONMENT", "development") == "development"
    
    uvicorn.run(
        "ft_eqc_api:app",
        host="127.0.0.1",  # 修復：使用本地回環地址避免網路綁定問題
        port=8010,
        reload=is_dev,
        log_level="info"
    )