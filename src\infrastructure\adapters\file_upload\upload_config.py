"""
檔案上傳配置管理模組

從 .env 檔案載入檔案上傳相關的配置設定。
"""

import os
from pathlib import Path
from typing import List
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv


class UploadConfig(BaseModel):
    """檔案上傳配置類別"""
    
    upload_temp_dir: Path = Field(default=Path("temp/uploads"))
    extract_temp_dir: Path = Field(default=Path("temp/extracted"))
    max_upload_size_mb: int = Field(default=1000, gt=0)
    auto_cleanup_hours: int = Field(default=24, gt=0)
    supported_archive_formats: List[str] = Field(default=["zip", "7z", "rar", "tar", "gz"])
    
    @validator('upload_temp_dir', 'extract_temp_dir', pre=True)
    def convert_to_path(cls, v):
        """將字串轉換為 Path 物件"""
        if isinstance(v, str):
            return Path(v)
        return v
    
    @validator('supported_archive_formats', pre=True)
    def split_formats(cls, v):
        """將逗號分隔的字串轉換為列表"""
        if isinstance(v, str):
            return [fmt.strip().lower() for fmt in v.split(',') if fmt.strip()]
        return v
    
    @property
    def max_upload_size_bytes(self) -> int:
        """取得檔案上傳大小限制（位元組）"""
        return self.max_upload_size_mb * 1024 * 1024
    
    def is_supported_format(self, file_extension: str) -> bool:
        """檢查檔案格式是否支援"""
        return file_extension.lower().lstrip('.') in self.supported_archive_formats
    
    class Config:
        """Pydantic 配置"""
        validate_assignment = True


def load_upload_config() -> UploadConfig:
    """
    從 .env 檔案載入檔案上傳配置
    
    Returns:
        UploadConfig: 檔案上傳配置物件
    """
    # 載入 .env 檔案
    load_dotenv()
    
    # 從環境變數讀取配置
    config_data = {
        'upload_temp_dir': os.getenv('UPLOAD_TEMP_DIR', 'temp/uploads'),
        'extract_temp_dir': os.getenv('EXTRACT_TEMP_DIR', 'temp/extracted'),
        'max_upload_size_mb': int(os.getenv('MAX_UPLOAD_SIZE_MB', '1000')),
        'auto_cleanup_hours': int(os.getenv('AUTO_CLEANUP_HOURS', '24')),
        'supported_archive_formats': os.getenv('SUPPORTED_ARCHIVE_FORMATS', 'zip,7z,rar,tar,gz')
    }
    
    return UploadConfig(**config_data)


# 全域配置實例
upload_config = load_upload_config()