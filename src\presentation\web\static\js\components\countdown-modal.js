/**
 * 倒數計時模態框組件
 * 檔案上傳成功後顯示5秒倒數計時，用戶可選擇立即執行或取消
 */
class CountdownModal {
    constructor() {
        this.countdownInterval = null;
        this.currentCount = 5;
        this.isCountdownActive = false;
        this.extractDir = null;
    }

    /**
     * 啟動倒數計時
     * @param {string} extractDir - 解壓縮目錄路徑
     */
    startCountdown(extractDir) {
        this.extractDir = extractDir;
        this.currentCount = 5;
        this.isCountdownActive = true;
        
        console.log('🕐 啟動倒數計時，目標路徑:', extractDir);
        
        // 創建並顯示模態框
        this.createCountdownModal();
        this.startCountdownTimer();
    }

    /**
     * 創建倒數計時模態框
     */
    createCountdownModal() {
        // 移除現有的模態框（如果存在）
        this.removeExistingModal();

        const modalHTML = `
            <div id="countdownModal" style="
                position: fixed; 
                top: 0; 
                left: 0; 
                width: 100%; 
                height: 100%; 
                background: rgba(0,0,0,0.5); 
                z-index: 9999; 
                display: flex; 
                justify-content: center; 
                align-items: center;
            ">
                <div style="
                    background: white; 
                    border-radius: 12px; 
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3); 
                    max-width: 500px; 
                    width: 90%; 
                    padding: 0;
                ">
                    <div style="
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 20px; 
                        border-radius: 12px 12px 0 0; 
                        text-align: center;
                    ">
                        <h5 style="margin: 0; font-size: 18px;">
                            <i class="fas fa-upload"></i> 檔案上傳成功！
                        </h5>
                    </div>
                    <div style="padding: 30px; text-align: center;">
                        <div style="margin-bottom: 20px;">
                            <i class="fas fa-folder-open" style="font-size: 48px; color: #28a745; margin-bottom: 15px;"></i>
                            <h6 style="color: #495057; margin-bottom: 10px;">📁 解壓縮路徑：</h6>
                            <div style="
                                background: #f8f9fa; 
                                padding: 10px; 
                                border-radius: 6px; 
                                font-family: monospace; 
                                font-size: 12px; 
                                word-break: break-all;
                            ">
                                ${this.extractDir}
                            </div>
                        </div>
                        
                        <div style="margin: 25px 0;">
                            <div style="color: #667eea; font-size: 16px; margin-bottom: 10px;">
                                <i class="fas fa-clock"></i> 自動執行「一鍵完成程式碼對比」
                            </div>
                            <div id="countdownDisplay" style="
                                font-size: 48px; 
                                font-weight: bold; 
                                color: #dc3545; 
                                margin: 15px 0;
                            ">
                                ${this.currentCount}
                            </div>
                            <div style="color: #6c757d; font-size: 14px;">
                                秒後自動開始
                            </div>
                        </div>
                        
                        <div style="display: flex; justify-content: center; gap: 15px;">
                            <button onclick="countdownModal.cancelCountdown()" style="
                                padding: 10px 20px; 
                                border: 1px solid #6c757d; 
                                background: white; 
                                color: #6c757d; 
                                border-radius: 5px; 
                                cursor: pointer;
                            ">
                                <i class="fas fa-times"></i> 取消自動執行
                            </button>
                            <button onclick="countdownModal.executeImmediately()" style="
                                padding: 10px 20px; 
                                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                                border: none; 
                                color: white; 
                                border-radius: 5px; 
                                cursor: pointer;
                            ">
                                <i class="fas fa-play"></i> 立即開始
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到頁面
        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * 開始倒數計時器
     */
    startCountdownTimer() {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
        }

        this.countdownInterval = setInterval(() => {
            if (!this.isCountdownActive) {
                this.stopCountdown();
                return;
            }

            this.currentCount--;
            this.updateCountdownDisplay();

            if (this.currentCount <= 0) {
                this.executeAutomatically();
            }
        }, 1000);
    }

    /**
     * 更新倒數顯示
     */
    updateCountdownDisplay() {
        const display = document.getElementById('countdownDisplay');
        if (display) {
            display.textContent = this.currentCount;
            
            // 最後3秒變紅色並加大
            if (this.currentCount <= 3) {
                display.style.color = '#dc3545';
                display.style.fontSize = '56px';
            }
        }
    }

    /**
     * 取消倒數計時
     */
    cancelCountdown() {
        console.log('⏹️ 用戶取消倒數計時');
        this.isCountdownActive = false;
        this.stopCountdown();
        this.closeModal();
        
        // 只設定路徑，不自動執行
        if (this.extractDir && typeof setFolderPath === 'function') {
            setFolderPath(this.extractDir);
        }
        
        StatusManager.showToast('已取消自動執行，請手動點擊處理按鈕', 'info');
    }

    /**
     * 立即執行
     */
    executeImmediately() {
        console.log('⚡ 用戶選擇立即執行');
        this.isCountdownActive = false;
        this.stopCountdown();
        this.closeModal();
        this.executeWorkflow();
    }

    /**
     * 自動執行（倒數結束）
     */
    executeAutomatically() {
        console.log('🚀 倒數計時結束，自動執行');
        this.isCountdownActive = false;
        this.stopCountdown();
        this.closeModal();
        this.executeWorkflow();
    }

    /**
     * 執行完整工作流程
     */
    executeWorkflow() {
        try {
            // 設定資料夾路徑
            if (this.extractDir && typeof setFolderPath === 'function') {
                setFolderPath(this.extractDir);
            }

            // 執行完整 EQC 工作流程
            if (typeof processCompleteEQCWorkflow === 'function') {
                console.log('🎯 執行 processCompleteEQCWorkflow');
                processCompleteEQCWorkflow();
            } else {
                console.error('❌ processCompleteEQCWorkflow 函數不存在');
                StatusManager.showToast('無法找到處理函數，請手動執行', 'error');
            }
        } catch (error) {
            console.error('❌ 執行工作流程時發生錯誤:', error);
            StatusManager.showToast('執行時發生錯誤: ' + error.message, 'error');
        }
    }

    /**
     * 停止倒數計時器
     */
    stopCountdown() {
        if (this.countdownInterval) {
            clearInterval(this.countdownInterval);
            this.countdownInterval = null;
        }
    }

    /**
     * 關閉模態框
     */
    closeModal() {
        const modal = document.getElementById('countdownModal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 移除現有模態框
     */
    removeExistingModal() {
        const existingModal = document.getElementById('countdownModal');
        if (existingModal) {
            existingModal.remove();
        }
    }

    /**
     * 清理資源
     */
    destroy() {
        this.stopCountdown();
        this.removeExistingModal();
        this.isCountdownActive = false;
        this.extractDir = null;
    }
}

// 立即創建全域實例
window.CountdownModal = CountdownModal;
window.countdownModal = new CountdownModal();