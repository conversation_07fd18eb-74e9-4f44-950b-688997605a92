"""
Outlook COM API 適配器
支援 Outlook 2019+ 版本，提供多版本相容性和收件夾監控
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Callable
from dataclasses import dataclass
from pathlib import Path
import asyncio
import threading
from datetime import datetime, timedelta
import time

from src.data_models.email_models import EmailData, EmailAttachment
from src.infrastructure.logging.logger_manager import LoggerManager


@dataclass
class OutlookVersion:
    """Outlook 版本資訊"""
    version: str
    build: str
    is_supported: bool
    features: List[str]


@dataclass
class MonitoringConfig:
    """監控配置"""
    check_interval: float = 30.0  # 檢查間隔（秒）
    max_emails_per_check: int = 50
    enable_real_time: bool = True
    monitor_subfolders: bool = False
    folder_path: Optional[str] = None


class OutlookAdapter(ABC):
    """
    Outlook 適配器抽象基類
    定義 Outlook 操作的標準介面
    """
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        連接到 Outlook
        
        Returns:
            連接成功與否
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """斷開 Outlook 連接"""
        pass
    
    @abstractmethod
    def is_connected(self) -> bool:
        """
        檢查連接狀態
        
        Returns:
            是否已連接
        """
        pass
    
    @abstractmethod
    def get_outlook_version(self) -> OutlookVersion:
        """
        獲取 Outlook 版本資訊
        
        Returns:
            版本資訊
        """
        pass
    
    @abstractmethod
    async def get_inbox_emails(
        self, 
        count: Optional[int] = None,
        unread_only: bool = True
    ) -> List[EmailData]:
        """
        獲取收件夾郵件
        
        Args:
            count: 要獲取的郵件數量
            unread_only: 只獲取未讀郵件
            
        Returns:
            郵件數據列表
        """
        pass
    
    @abstractmethod
    async def download_attachments(self, email_data: EmailData) -> List[str]:
        """
        下載郵件附件
        
        Args:
            email_data: 郵件數據
            
        Returns:
            下載的附件路徑列表
        """
        pass
    
    @abstractmethod
    async def mark_as_read(self, email_data: EmailData) -> bool:
        """
        標記郵件為已讀
        
        Args:
            email_data: 郵件數據
            
        Returns:
            操作成功與否
        """
        pass
    
    @abstractmethod
    async def start_monitoring(
        self, 
        callback: Callable[[List[EmailData]], None],
        config: MonitoringConfig
    ) -> None:
        """
        開始監控收件夾
        
        Args:
            callback: 新郵件回調函數
            config: 監控配置
        """
        pass
    
    @abstractmethod
    async def stop_monitoring(self) -> None:
        """停止監控"""
        pass
    
    @abstractmethod
    def is_monitoring(self) -> bool:
        """
        檢查是否正在監控
        
        Returns:
            是否正在監控
        """
        pass


class OutlookComAdapter(OutlookAdapter):
    """
    Outlook COM API 實作
    支援 Outlook 2016+，重點優化 2019 版本
    """
    
    def __init__(self):
        """初始化適配器"""
        self.logger = LoggerManager().get_logger("OutlookComAdapter")
        self._outlook_app = None
        self._namespace = None
        self._inbox = None
        self._is_connected = False
        self._is_monitoring = False
        self._monitoring_task = None
        self._monitoring_config = None
        self._last_check_time = None
        
        # 版本相容性對應
        self._version_compatibility = {
            "16.0": {"version": "2016", "supported": True},
            "16.1": {"version": "2019", "supported": True},
            "16.2": {"version": "2021", "supported": True},
            "16.3": {"version": "365", "supported": True},
        }
    
    async def connect(self) -> bool:
        """連接到 Outlook"""
        try:
            self.logger.info("正在連接到 Outlook...")
            
            # 動態導入避免非 Windows 平台錯誤
            import win32com.client
            import pythoncom
            
            # 初始化 COM
            pythoncom.CoInitialize()
            
            try:
                # 嘗試連接到現有 Outlook 實例
                self._outlook_app = win32com.client.GetActiveObject("Outlook.Application")
                self.logger.info("已連接到現有 Outlook 實例")
            except:
                # 如果沒有現有實例，創建新的
                self._outlook_app = win32com.client.Dispatch("Outlook.Application")
                self.logger.info("已創建新的 Outlook 實例")
            
            # 獲取 MAPI 命名空間
            self._namespace = self._outlook_app.GetNamespace("MAPI")
            
            # 獲取收件夾
            self._inbox = self._namespace.GetDefaultFolder(6)  # 6 = olFolderInbox
            
            # 檢查版本相容性
            version_info = self.get_outlook_version()
            if not version_info.is_supported:
                self.logger.warning(f"Outlook 版本 {version_info.version} 可能不完全支援")
            
            self._is_connected = True
            self.logger.info(f"成功連接到 Outlook {version_info.version}")
            return True
            
        except ImportError:
            self.logger.error("無法導入 win32com，請確保已安裝 pywin32")
            return False
        except Exception as e:
            self.logger.error(f"連接 Outlook 失敗: {str(e)}")
            return False
    
    async def disconnect(self) -> None:
        """斷開 Outlook 連接"""
        try:
            if self._is_monitoring:
                await self.stop_monitoring()
            
            self._outlook_app = None
            self._namespace = None
            self._inbox = None
            self._is_connected = False
            
            # 清理 COM
            import pythoncom
            pythoncom.CoUninitialize()
            
            self.logger.info("已斷開 Outlook 連接")
            
        except Exception as e:
            self.logger.error(f"斷開 Outlook 連接時發生錯誤: {str(e)}")
    
    def is_connected(self) -> bool:
        """檢查連接狀態"""
        return self._is_connected and self._outlook_app is not None
    
    def get_outlook_version(self) -> OutlookVersion:
        """獲取 Outlook 版本資訊"""
        try:
            if not self._outlook_app:
                return OutlookVersion("unknown", "unknown", False, [])
            
            version_str = self._outlook_app.Version
            build_info = version_str.split('.')
            major_minor = f"{build_info[0]}.{build_info[1]}" if len(build_info) >= 2 else version_str
            
            # 查找版本對應
            version_info = self._version_compatibility.get(major_minor, {
                "version": "unknown", 
                "supported": False
            })
            
            supported_features = ["email_reading", "attachment_download", "folder_monitoring"]
            if version_info["supported"]:
                supported_features.extend(["advanced_search", "real_time_monitoring"])
            
            return OutlookVersion(
                version=version_info["version"],
                build=version_str,
                is_supported=version_info["supported"],
                features=supported_features
            )
            
        except Exception as e:
            self.logger.warning(f"無法獲取 Outlook 版本: {str(e)}")
            return OutlookVersion("unknown", "unknown", False, [])
    
    async def get_inbox_emails(
        self, 
        count: Optional[int] = None,
        unread_only: bool = True
    ) -> List[EmailData]:
        """獲取收件夾郵件"""
        try:
            if not self.is_connected():
                raise Exception("Outlook 未連接")
            
            emails = []
            items = self._inbox.Items
            
            # 排序郵件（最新的先）
            items.Sort("[ReceivedTime]", True)
            
            processed_count = 0
            
            for item in items:
                try:
                    # 檢查是否為郵件項目
                    if not hasattr(item, 'Subject'):
                        continue
                    
                    # 如果只要未讀郵件且該郵件已讀，跳過
                    if unread_only and getattr(item, 'UnRead', True) == False:
                        continue
                    
                    # 轉換為 EmailData
                    email_data = self._convert_outlook_item_to_email_data(item)
                    emails.append(email_data)
                    
                    processed_count += 1
                    
                    # 檢查數量限制
                    if count and processed_count >= count:
                        break
                        
                except Exception as e:
                    self.logger.warning(f"處理郵件項目時發生錯誤: {str(e)}")
                    continue
            
            self.logger.info(f"獲取了 {len(emails)} 封郵件")
            return emails
            
        except Exception as e:
            self.logger.error(f"獲取收件夾郵件失敗: {str(e)}")
            return []
    
    def _convert_outlook_item_to_email_data(self, item) -> EmailData:
        """將 Outlook 項目轉換為 EmailData"""
        try:
            # 獲取基本資訊
            message_id = getattr(item, 'EntryID', f"outlook_{int(time.time())}")
            subject = getattr(item, 'Subject', '')
            sender = getattr(item, 'SenderEmailAddress', '')
            body = getattr(item, 'Body', '')
            received_time = getattr(item, 'ReceivedTime', datetime.now())
            
            # 處理附件
            attachments = []
            if hasattr(item, 'Attachments'):
                for attachment in item.Attachments:
                    try:
                        att_info = EmailAttachment(
                            filename=getattr(attachment, 'FileName', 'unknown'),
                            size_bytes=getattr(attachment, 'Size', 0),
                            content_type=self._guess_content_type(
                                getattr(attachment, 'FileName', '')
                            ),
                            file_path=Path("/tmp")  # 臨時路徑，稍後更新
                        )
                        attachments.append(att_info)
                    except Exception as e:
                        self.logger.warning(f"處理附件時發生錯誤: {str(e)}")
            
            return EmailData(
                message_id=message_id,
                subject=subject,
                sender=sender,
                body=body,
                received_time=received_time,
                attachments=attachments,
                raw_data={"outlook_item": item}
            )
            
        except Exception as e:
            self.logger.error(f"轉換 Outlook 項目失敗: {str(e)}")
            raise
    
    def _guess_content_type(self, filename: str) -> str:
        """根據檔名猜測內容類型"""
        ext_mapping = {
            '.csv': 'text/csv',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.xls': 'application/vnd.ms-excel',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.zip': 'application/zip',
            '.rar': 'application/x-rar-compressed'
        }
        
        for ext, content_type in ext_mapping.items():
            if filename.lower().endswith(ext):
                return content_type
        
        return 'application/octet-stream'
    
    async def download_attachments(self, email_data: EmailData) -> List[str]:
        """下載郵件附件"""
        try:
            downloaded_paths = []
            
            # 從原始數據中獲取 Outlook 項目
            raw_data = email_data.raw_data or {}
            outlook_item = raw_data.get("outlook_item")
            
            if not outlook_item or not hasattr(outlook_item, 'Attachments'):
                return downloaded_paths
            
            import tempfile
            import os
            
            # 創建臨時目錄
            temp_dir = tempfile.mkdtemp(prefix="outlook_attachments_")
            
            for attachment in outlook_item.Attachments:
                try:
                    filename = getattr(attachment, 'FileName', 'unknown_attachment')
                    file_path = os.path.join(temp_dir, filename)
                    
                    # 保存附件
                    attachment.SaveAsFile(file_path)
                    downloaded_paths.append(file_path)
                    
                    self.logger.info(f"已下載附件: {filename}")
                    
                except Exception as e:
                    self.logger.warning(f"下載附件失敗: {str(e)}")
            
            return downloaded_paths
            
        except Exception as e:
            self.logger.error(f"下載附件過程失敗: {str(e)}")
            return []
    
    async def mark_as_read(self, email_data: EmailData) -> bool:
        """標記郵件為已讀"""
        try:
            raw_data = email_data.raw_data or {}
            outlook_item = raw_data.get("outlook_item")
            
            if outlook_item and hasattr(outlook_item, 'UnRead'):
                outlook_item.UnRead = False
                outlook_item.Save()
                self.logger.debug(f"已標記郵件為已讀: {email_data.subject}")
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"標記郵件為已讀失敗: {str(e)}")
            return False
    
    async def start_monitoring(
        self, 
        callback: Callable[[List[EmailData]], None],
        config: MonitoringConfig
    ) -> None:
        """開始監控收件夾"""
        try:
            if self._is_monitoring:
                self.logger.warning("監控已在運行中")
                return
            
            self._monitoring_config = config
            self._last_check_time = datetime.now()
            self._is_monitoring = True
            
            # 啟動監控任務
            self._monitoring_task = asyncio.create_task(
                self._monitoring_loop(callback)
            )
            
            self.logger.info(f"開始監控收件夾，檢查間隔: {config.check_interval} 秒")
            
        except Exception as e:
            self.logger.error(f"啟動監控失敗: {str(e)}")
            self._is_monitoring = False
    
    async def _monitoring_loop(self, callback: Callable[[List[EmailData]], None]) -> None:
        """監控循環"""
        while self._is_monitoring:
            try:
                # 獲取新郵件
                new_emails = await self._check_for_new_emails()
                
                if new_emails:
                    self.logger.info(f"發現 {len(new_emails)} 封新郵件")
                    # 調用回調函數
                    if asyncio.iscoroutinefunction(callback):
                        await callback(new_emails)
                    else:
                        callback(new_emails)
                
                # 等待下次檢查
                await asyncio.sleep(self._monitoring_config.check_interval)
                
            except Exception as e:
                self.logger.error(f"監控循環發生錯誤: {str(e)}")
                await asyncio.sleep(5)  # 錯誤後短暫等待
    
    async def _check_for_new_emails(self) -> List[EmailData]:
        """檢查新郵件"""
        try:
            if not self.is_connected():
                return []
            
            current_time = datetime.now()
            time_filter = self._last_check_time - timedelta(minutes=1)  # 添加緩衝時間
            
            new_emails = []
            items = self._inbox.Items
            
            # 根據時間篩選
            items.Sort("[ReceivedTime]", True)  # 最新的先
            
            count = 0
            for item in items:
                try:
                    if not hasattr(item, 'ReceivedTime'):
                        continue
                    
                    received_time = item.ReceivedTime
                    
                    # 只處理在上次檢查後收到的郵件
                    if received_time <= time_filter:
                        break
                    
                    # 轉換郵件
                    email_data = self._convert_outlook_item_to_email_data(item)
                    new_emails.append(email_data)
                    
                    count += 1
                    if count >= self._monitoring_config.max_emails_per_check:
                        break
                        
                except Exception as e:
                    self.logger.warning(f"檢查新郵件時發生錯誤: {str(e)}")
                    continue
            
            self._last_check_time = current_time
            return new_emails
            
        except Exception as e:
            self.logger.error(f"檢查新郵件失敗: {str(e)}")
            return []
    
    async def stop_monitoring(self) -> None:
        """停止監控"""
        try:
            self._is_monitoring = False
            
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                try:
                    await self._monitoring_task
                except asyncio.CancelledError:
                    pass
            
            self._monitoring_task = None
            self._monitoring_config = None
            
            self.logger.info("已停止收件夾監控")
            
        except Exception as e:
            self.logger.error(f"停止監控時發生錯誤: {str(e)}")
    
    def is_monitoring(self) -> bool:
        """檢查是否正在監控"""
        return self._is_monitoring