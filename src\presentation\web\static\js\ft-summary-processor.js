/**
 * FT Summary 批量處理 JavaScript
 * 處理表單提交、API 呼叫、進度顯示等功能
 */

class FTSummaryProcessor {
    constructor() {
        this.form = document.getElementById('ftSummaryForm');
        this.processBtn = document.getElementById('processBtn');
        this.progressContainer = document.getElementById('progressContainer');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');
        this.resultContainer = document.getElementById('resultContainer');
        this.statsContainer = document.getElementById('statsContainer');
        this.downloadContainer = document.getElementById('downloadContainer');
        
        this.init();
    }
    
    init() {
        // 檢查服務狀態
        this.checkServiceStatus();
        
        // 綁定表單提交事件
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.startProcessing();
        });
        
        // 綁定資料夾路徑輸入框事件
        const folderInput = document.getElementById('folderPath');
        folderInput.addEventListener('input', this.validateInput.bind(this));
        
        // 初始驗證
        this.validateInput();
    }
    
    async checkServiceStatus() {
        // 檢查服務狀態
        try {
            const response = await fetch('/api/ft-summary-status');
            const status = await response.json();
            
            const modeIndicator = document.getElementById('modeIndicator');
            const modeText = document.getElementById('modeText');
            
            if (status.ft_summary_available) {
                modeIndicator.style.display = 'block';
                modeIndicator.style.background = '#d4edda';
                modeIndicator.style.border = '1px solid #c3e6cb';
                modeIndicator.style.color = '#155724';
                modeText.textContent = 'FT Summary 服務正常：支援 CSV 到 Excel 轉換';
            } else {
                modeIndicator.style.display = 'block';
                modeIndicator.style.background = '#f8d7da';
                modeIndicator.style.border = '1px solid #f5c6cb';
                modeIndicator.style.color = '#721c24';
                modeText.textContent = '服務不可用：請安裝依賴套件';
                
                // 禁用表單
                this.processBtn.disabled = true;
                this.processBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 服務不可用';
            }
        } catch (error) {
            console.error('檢查服務狀態失敗:', error);
        }
    }
    
    validateInput() {
        const folderPath = document.getElementById('folderPath').value.trim();
        const isValid = folderPath.length > 0;
        
        this.processBtn.disabled = !isValid;
        
        if (!isValid) {
            this.processBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 請輸入資料夾路徑';
        } else {
            this.processBtn.innerHTML = '<i class="fas fa-play"></i> 開始批量處理';
        }
    }
    
    async startProcessing() {
        const folderPath = document.getElementById('folderPath').value.trim();
        // 移除勾選框，預設為覆寫模式
        const forceOverwrite = true;
        // 獲取處理模式
        const processingMode = document.querySelector('input[name="processing_mode"]:checked').value;
        
        if (!folderPath) {
            this.showError('請輸入資料夾路徑');
            return;
        }
        
        try {
            // 顯示處理狀態
            this.showProcessing();
            
            // 呼叫 API
            const response = await fetch('/api/process_ft_summary', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    folder_path: folderPath,
                    force_overwrite: forceOverwrite,
                    processing_mode: processingMode
                })
            });
            
            const result = await response.json();
            
            if (response.ok && result.status === 'success') {
                this.showSuccess(result);
            } else {
                this.showError(result.message || result.error_message || '處理失敗');
            }
            
        } catch (error) {
            console.error('處理錯誤:', error);
            this.showError(`網路錯誤: ${error.message}`);
        }
    }
    
    showProcessing() {
        // 禁用按鈕
        this.processBtn.disabled = true;
        this.processBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 處理中...';
        
        // 顯示進度條
        this.progressContainer.style.display = 'block';
        this.progressFill.style.width = '0%';
        this.progressText.textContent = '正在掃描檔案...';
        
        // 隱藏結果
        this.resultContainer.style.display = 'none';
        
        // 模擬進度更新
        this.simulateProgress();
    }
    
    simulateProgress() {
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) {
                progress = 90;
                clearInterval(interval);
            }
            
            this.progressFill.style.width = `${progress}%`;
            
            if (progress < 30) {
                this.progressText.textContent = '正在掃描 CSV 檔案...';
            } else if (progress < 60) {
                this.progressText.textContent = '正在處理檔案...';
            } else {
                this.progressText.textContent = '正在生成 FT Summary...';
            }
        }, 500);
    }
    
    showSuccess(result) {
        // 完成進度條
        this.progressFill.style.width = '100%';
        this.progressText.textContent = '處理完成！';
        
        setTimeout(() => {
            this.progressContainer.style.display = 'none';
            
            // 顯示成功結果
            this.resultContainer.className = 'ft-summary-result success';
            this.resultContainer.style.display = 'block';
            
            document.getElementById('resultMessage').innerHTML = `
                <h4><i class="fas fa-check-circle"></i> 處理成功！</h4>
                <p>${result.message}</p>
            `;
            
            // 顯示統計資料
            this.updateStats(result);
            this.statsContainer.style.display = 'grid';
            
            // 顯示檔案清單
            this.showFileLists(result);
            document.getElementById('fileListContainer').style.display = 'block';
            
            // 顯示下載連結
            if (result.ft_summary_output_file) {
                this.showDownloadLink(result.ft_summary_output_file);
            }
            
            // 恢復按鈕
            this.resetButton();
        }, 1000);
    }
    
    showError(message) {
        // 隱藏進度條
        this.progressContainer.style.display = 'none';
        
        // 顯示錯誤結果
        this.resultContainer.className = 'ft-summary-result error';
        this.resultContainer.style.display = 'block';
        
        document.getElementById('resultMessage').innerHTML = `
            <h4><i class="fas fa-exclamation-triangle"></i> 處理失敗</h4>
            <p>${message}</p>
        `;
        
        // 隱藏統計和下載
        this.statsContainer.style.display = 'none';
        this.downloadContainer.style.display = 'none';
        
        // 恢復按鈕
        this.resetButton();
    }
    
    updateStats(result) {
        document.getElementById('totalFiles').textContent = result.total_files || 0;
        document.getElementById('processedFiles').textContent = result.processed_files || 0;
        document.getElementById('skippedFiles').textContent = result.skipped_files || 0;
        document.getElementById('ftSummaryFiles').textContent = result.ft_summary_files || 0;
        document.getElementById('eqcSummaryFiles').textContent = result.eqc_summary_files || 0;
        document.getElementById('processingTime').textContent = 
            `${(result.processing_time_seconds || 0).toFixed(1)}s`;
    }
    
    showFileLists(result) {
        // 顯示FT檔案清單
        const ftFileList = document.getElementById('ftFileList');
        if (result.ft_file_list && result.ft_file_list.length > 0) {
            ftFileList.innerHTML = result.ft_file_list
                .map(filename => `<div class="ft-summary-file-item">${filename}</div>`)
                .join('');
        } else {
            ftFileList.innerHTML = '<div class="ft-summary-file-item empty">無 FT 檔案</div>';
        }
        
        // 顯示EQC檔案清單
        const eqcFileList = document.getElementById('eqcFileList');
        if (result.eqc_file_list && result.eqc_file_list.length > 0) {
            eqcFileList.innerHTML = result.eqc_file_list
                .map(filename => `<div class="ft-summary-file-item">${filename}</div>`)
                .join('');
        } else {
            eqcFileList.innerHTML = '<div class="ft-summary-file-item empty">無 EQC 檔案</div>';
        }
    }
    
    showDownloadLink(filePath) {
        this.downloadContainer.style.display = 'block';
        
        const downloadBtn = document.getElementById('downloadBtn');
        
        // 建立下載連結
        downloadBtn.onclick = () => {
            // 由於檔案在伺服器端，我們需要提供一個下載端點
            // 這裡暫時顯示檔案路徑，後續可以實作檔案下載功能
            this.copyToClipboard(filePath);
        };
    }
    
    copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // 暫時修改按鈕文字
            const downloadBtn = document.getElementById('downloadBtn');
            const originalText = downloadBtn.innerHTML;
            downloadBtn.innerHTML = '<i class="fas fa-check"></i> 路徑已複製到剪貼板';
            
            setTimeout(() => {
                downloadBtn.innerHTML = originalText;
            }, 2000);
        }).catch(err => {
            console.error('無法複製到剪貼板:', err);
            alert(`檔案路徑: ${text}`);
        });
    }
    
    resetButton() {
        this.processBtn.disabled = false;
        this.processBtn.innerHTML = '<i class="fas fa-play"></i> 開始批量處理';
        this.validateInput();
    }
}

// DOM 載入完成後初始化
document.addEventListener('DOMContentLoaded', () => {
    new FTSummaryProcessor();
});

// 工具函數：格式化檔案大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 工具函數：格式化時間
function formatDuration(seconds) {
    if (seconds < 60) {
        return `${seconds.toFixed(1)}秒`;
    } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = (seconds % 60).toFixed(1);
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}小時${minutes}分鐘`;
    }
}