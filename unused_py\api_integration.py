"""
API 整合啟動器 - 功能替換原則實作
同時啟動主API(8010)和網路瀏覽器API(8009)服務
遵循CLAUDE.md規定：獨立模組，不影響現有功能，檔案≤500行
"""

import os
import sys
import asyncio
import signal
import subprocess
import time
from typing import List
from pathlib import Path
from loguru import logger


class APIIntegrator:
    """API服務整合器 - 功能替換原則：獨立管理多個服務"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.services = {
            "主API服務": {
                "module": "src.presentation.api.ft_eqc_api:app",
                "port": 8010,
                "host": "0.0.0.0"
            },
            "網路瀏覽器API": {
                "module": "src.presentation.api.network_browser_api:app", 
                "port": 8009,
                "host": "0.0.0.0"
            }
        }
    
    def setup_signal_handlers(self):
        """設定信號處理器以優雅關閉服務"""
        def signal_handler(signum, frame):
            logger.info("🛑 收到關閉信號，正在停止所有服務...")
            self.stop_all_services()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def check_port_available(self, port: int) -> bool:
        """檢查端口是否可用"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                return result != 0
        except Exception:
            return True
    
    def start_service(self, name: str, config: dict) -> bool:
        """啟動單個API服務"""
        try:
            # 檢查端口
            if not self.check_port_available(config["port"]):
                logger.warning(f"[WARNING]  端口 {config['port']} 已被佔用，嘗試停止現有服務")
                self.kill_process_on_port(config["port"])
                time.sleep(2)
            
            # 啟動服務
            cmd = [
                sys.executable, "-m", "uvicorn",
                config["module"],
                "--host", config["host"],
                "--port", str(config["port"]),
                "--reload"
            ]
            
            logger.info(f"[ROCKET] 啟動 {name} (端口: {config['port']})")
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=Path(__file__).parent
            )
            
            self.processes.append(process)
            
            # 等待服務啟動
            time.sleep(3)
            
            if process.poll() is None:
                logger.info(f"[OK] {name} 啟動成功 - http://{config['host']}:{config['port']}")
                return True
            else:
                stdout, stderr = process.communicate()
                logger.error(f"[ERROR] {name} 啟動失敗: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"[ERROR] 啟動 {name} 時發生錯誤: {str(e)}")
            return False
    
    def kill_process_on_port(self, port: int):
        """停止佔用指定端口的進程"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.run(f"taskkill /F /IM python.exe", shell=True, capture_output=True)
            else:  # Linux/Mac
                result = subprocess.run(f"lsof -ti:{port}", shell=True, capture_output=True, text=True)
                if result.stdout.strip():
                    subprocess.run(f"kill -9 {result.stdout.strip()}", shell=True)
        except Exception as e:
            logger.debug(f"清理端口 {port} 時發生錯誤: {e}")
    
    def start_all_services(self) -> bool:
        """啟動所有API服務"""
        logger.info("[TOOL] 開始啟動整合API服務...")
        
        success_count = 0
        for name, config in self.services.items():
            if self.start_service(name, config):
                success_count += 1
        
        if success_count == len(self.services):
            logger.info("[PARTY] 所有API服務啟動成功！")
            self.show_service_info()
            return True
        else:
            logger.error(f"[ERROR] 部分服務啟動失敗 ({success_count}/{len(self.services)})")
            return False
    
    def show_service_info(self):
        """顯示服務資訊"""
        logger.info("[BOARD] 服務端點一覽:")
        logger.info("=" * 50)
        for name, config in self.services.items():
            port = config["port"]
            logger.info(f"[GLOBE_WITH_MERIDIANS] {name}: http://localhost:{port}")
            if port == 8010:
                logger.info(f"   [CHART] 主API文檔: http://localhost:{port}/docs")
                logger.info(f"   [DESKTOP]  主UI介面: http://localhost:{port}/ui")
            elif port == 8009:
                logger.info(f"   [EARTH_GLOBE_EUROPE_AFRICA] 網路瀏覽器UI: http://localhost:{port}/network/ui")
                logger.info(f"   [SEARCH] 健康檢查: http://localhost:{port}/network/health")
        logger.info("=" * 50)
    
    def monitor_services(self):
        """監控服務狀態"""
        try:
            while True:
                # 檢查所有進程是否還在運行
                for i, process in enumerate(self.processes):
                    if process.poll() is not None:
                        service_name = list(self.services.keys())[i]
                        logger.error(f"[ERROR] {service_name} 意外停止")
                        # 可以在這裡加入重啟邏輯
                
                time.sleep(30)  # 每30秒檢查一次
                
        except KeyboardInterrupt:
            logger.info("🛑 監控中斷，正在停止服務...")
    
    def stop_all_services(self):
        """停止所有服務"""
        logger.info("🛑 正在停止所有API服務...")
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            except Exception as e:
                logger.error(f"停止服務時發生錯誤: {e}")
        
        self.processes.clear()
        logger.info("[OK] 所有服務已停止")


def main():
    """主函式 - 整合API啟動入口"""
    logger.info("[ROCKET] API整合服務啟動器")
    logger.info("功能替換原則：獨立管理多個API服務，不影響現有功能")
    
    integrator = APIIntegrator()
    integrator.setup_signal_handlers()
    
    try:
        # 啟動所有服務
        if integrator.start_all_services():
            logger.info("[REFRESH] 開始監控服務狀態...")
            integrator.monitor_services()
        else:
            logger.error("[ERROR] 服務啟動失敗，程式結束")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("🛑 收到中斷信號")
    except Exception as e:
        logger.error(f"[ERROR] 整合服務時發生錯誤: {str(e)}")
    finally:
        integrator.stop_all_services()


if __name__ == "__main__":
    main()