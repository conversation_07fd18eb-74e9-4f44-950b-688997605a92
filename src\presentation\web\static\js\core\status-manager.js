/**
 * 狀態管理器模組
 * 統一管理應用程序的狀態顯示和進度更新
 */

class StatusManager {
    static currentStatus = 'idle';
    static currentProgress = 0;
    
    /**
     * 更新進度顯示
     * @param {string} step - 步驟描述
     * @param {string} message - 詳細訊息
     * @param {number|null} percentage - 進度百分比
     * @param {string} status - 狀態類型 ('processing', 'success', 'error')
     */
    static updateProgress(step, message, percentage = null, status = 'processing') {
        const icons = {
            processing: 'fas fa-spinner fa-spin',
            success: 'fas fa-check-circle',
            error: 'fas fa-times-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        
        const stepElement = DOMManager.get('progressStep');
        const detailElement = DOMManager.get('progressDetail');
        
        if (stepElement) {
            stepElement.className = `progress-step ${status}`;
            stepElement.innerHTML = `<i class="${icons[status]}"></i> ${step}`;
        }
        
        if (detailElement) {
            detailElement.textContent = message;
        }
        
        if (percentage !== null) {
            this.updateProgressBar(percentage);
        }
        
        this.currentStatus = status;
        this.currentProgress = percentage || 0;
        
        console.log(`📊 進度更新: ${step} - ${message} (${percentage}%)`);
    }
    
    /**
     * 更新進度條
     * @param {number} percentage - 進度百分比
     */
    static updateProgressBar(percentage) {
        const progressContainer = DOMManager.get('progressBarContainer');
        const progressFill = DOMManager.get('progressFill');
        const progressText = DOMManager.get('progressText');
        
        if (progressContainer) progressContainer.style.display = 'block';
        if (progressFill) progressFill.style.width = percentage + '%';
        if (progressText) progressText.textContent = percentage + '%';
    }
    
    /**
     * 重置進度顯示
     */
    static resetProgress() {
        this.updateProgress('等待開始...', '點擊「一鍵完成到程式碼對比」開始處理', 0, 'info');
        
        const progressContainer = DOMManager.get('progressBarContainer');
        if (progressContainer) progressContainer.style.display = 'none';
        
        this.currentStatus = 'idle';
        this.currentProgress = 0;
    }
    
    /**
     * 顯示成功狀態
     * @param {string} message - 成功訊息
     */
    static showSuccess(message) {
        this.updateProgress('完成', message, 100, 'success');
    }
    
    /**
     * 顯示錯誤狀態
     * @param {string} message - 錯誤訊息
     */
    static showError(message) {
        this.updateProgress('錯誤', message, null, 'error');
    }
    
    /**
     * 顯示警告狀態
     * @param {string} message - 警告訊息
     */
    static showWarning(message) {
        this.updateProgress('警告', message, null, 'warning');
    }
    
    /**
     * 顯示處理指示器
     * @param {boolean} show - 是否顯示
     */
    static showProcessingIndicator(show) {
        const indicator = DOMManager.get('processingIndicator');
        if (indicator) {
            indicator.style.display = show ? 'block' : 'none';
        }
    }
    
    /**
     * 更新 EQC BIN=1 狀態燈
     * @param {string} color - 狀態燈顏色
     * @param {string} status - 狀態文字
     */
    static updateEqcBin1StatusLight(color, status) {
        const canvas = DOMManager.get('eqcBin1StatusLight');
        const statusElement = DOMManager.get('eqcBin1Status');
        
        if (!canvas || !statusElement) {
            console.log('EQC BIN1 狀態燈元素不存在，跳過更新');
            return;
        }
        
        const ctx = canvas.getContext('2d');
        
        // 清除畫布
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // 繪製圓形狀態燈
        ctx.beginPath();
        ctx.arc(10, 10, 8, 0, 2 * Math.PI);
        ctx.fillStyle = color;
        ctx.fill();
        ctx.strokeStyle = '#333';
        ctx.lineWidth = 1;
        ctx.stroke();
        
        // 更新狀態文字
        statusElement.textContent = status;
        statusElement.style.color = color;
    }
    
    /**
     * 獲取當前狀態
     * @returns {Object} 當前狀態信息
     */
    static getCurrentStatus() {
        return {
            status: this.currentStatus,
            progress: this.currentProgress
        };
    }
    
    /**
     * 檢查是否正在處理
     * @returns {boolean} 是否正在處理
     */
    static isProcessing() {
        return this.currentStatus === 'processing';
    }
    
    /**
     * 檢查是否已完成
     * @returns {boolean} 是否已完成
     */
    static isCompleted() {
        return this.currentStatus === 'success' && this.currentProgress === 100;
    }
    
    /**
     * 檢查是否有錯誤
     * @returns {boolean} 是否有錯誤
     */
    static hasError() {
        return this.currentStatus === 'error';
    }
    
    /**
     * 顯示臨時訊息
     * @param {string} message - 訊息內容
     * @param {string} type - 訊息類型 ('success', 'error', 'warning', 'info')
     * @param {number} duration - 顯示時長（毫秒）
     */
    static showToast(message, type = 'info', duration = 3000) {
        const toast = DOMManager.createElement('div', {
            className: `toast toast-${type}`,
            textContent: message,
            styles: {
                position: 'fixed',
                top: '30px',
                right: '30px',
                background: this.getToastColor(type),
                color: 'white',
                padding: '15px 20px',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                zIndex: '10000',
                fontSize: '14px',
                fontWeight: '500',
                maxWidth: '400px',
                opacity: '0',
                transform: 'translateX(100%)',
                transition: 'all 0.3s ease'
            }
        });
        
        document.body.appendChild(toast);
        
        // 動畫顯示
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateX(0)';
        }, 10);
        
        // 自動隱藏
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }
    
    /**
     * 獲取 Toast 顏色
     * @param {string} type - 訊息類型
     * @returns {string} 顏色值
     */
    static getToastColor(type) {
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };
        return colors[type] || colors.info;
    }
    
    /**
     * 顯示確認對話框
     * @param {string} message - 確認訊息
     * @param {Function} onConfirm - 確認回調
     * @param {Function} onCancel - 取消回調
     */
    static showConfirm(message, onConfirm, onCancel = null) {
        const confirmed = confirm(message);
        if (confirmed && onConfirm) {
            onConfirm();
        } else if (!confirmed && onCancel) {
            onCancel();
        }
    }
    
    /**
     * 顯示載入狀態
     * @param {string} message - 載入訊息
     */
    static showLoading(message = '載入中...') {
        this.updateProgress('載入中', message, null, 'processing');
        this.showProcessingIndicator(true);
    }
    
    /**
     * 隱藏載入狀態
     */
    static hideLoading() {
        this.showProcessingIndicator(false);
    }
}

// 導出 StatusManager 類
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StatusManager;
} else if (typeof window !== 'undefined') {
    window.StatusManager = StatusManager;
}
