/* 
 * CSS 變數定義模組
 * 包含顏色系統、尺寸系統、陰影系統和過渡動畫
 */

:root {
    /* ==================== 顏色系統 ==================== */
    --primary-color: #667eea;
    --secondary-color: #2c3e50;
    --text-muted: #6c757d;
    --border-color: #e9ecef;
    --bg-light: #f8f9fa;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --error-color: #e74c3c;
    --info-color: #17a2b8;
    
    /* ==================== 尺寸系統 ==================== */
    --radius-xl: 15px;
    --radius-lg: 12px;
    --radius-md: 8px;
    --radius-sm: 6px;
    --radius-xs: 4px;
    --spacing-xl: 30px;
    --spacing-lg: 20px;
    --spacing-md: 15px;
    --spacing-sm: 12px;
    --spacing-xs: 10px;
    --spacing-xxs: 8px;
    
    /* ==================== 陰影系統 ==================== */
    --shadow-xl: 0 20px 60px rgba(0,0,0,0.1);
    --shadow-lg: 0 4px 16px rgba(0,0,0,0.15);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.1);
    --shadow-sm: 0 2px 8px rgba(0,0,0,0.1);
    --shadow-xs: 0 2px 4px rgba(0,0,0,0.05);
    
    /* ==================== 過渡動畫 ==================== */
    --transition: all 0.3s ease;
    --transition-fast: all 0.2s ease;
    
    /* ==================== 字體系統 ==================== */
    --font-family-primary: 'Microsoft JhengHei', Arial, sans-serif;
    --font-size-xl: 2em;
    --font-size-lg: 1.8em;
    --font-size-md: 16px;
    --font-size-sm: 14px;
    --font-size-xs: 12px;
    --font-size-xxs: 11px;
    
    /* ==================== 漸變系統 ==================== */
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
    --gradient-success: linear-gradient(135deg, #28a745, #20c997);
    --gradient-info: linear-gradient(135deg, #17a2b8, #138496);
    --gradient-light: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    
    /* ==================== 布局系統 ==================== */
    --container-max-width: 1400px;
    --sidebar-width: 300px;
    --header-height: 80px;
    --footer-height: 60px;
    
    /* ==================== Z-Index 層級 ==================== */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 10000;
}

/* ==================== 深色模式變數 (預留) ==================== */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #7c8aed;
        --secondary-color: #34495e;
        --text-muted: #8e9297;
        --border-color: #2d3748;
        --bg-light: #1a202c;
        --success-color: #38a169;
        --warning-color: #d69e2e;
        --error-color: #e53e3e;
        --info-color: #3182ce;
    }
}
