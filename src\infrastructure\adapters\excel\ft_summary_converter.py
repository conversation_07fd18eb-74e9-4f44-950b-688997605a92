#!/usr/bin/env python3
"""
FT_SUMMARY.csv 專用轉換器
簡化版直接轉 Excel，避免複雜的 Summary 邏輯

功能：
1. 使用 xlsxwriter 直接寫入
2. 套用 _smart_convert_to_number 智慧轉換規則
3. Definition 欄位自動調整寬度
4. 避免綠色警告
"""

import pandas as pd
import numpy as np
import os
import time
import xlsxwriter
from typing import List, Tuple
from pathlib import Path
from .ft_summary_colorizer import FTSummaryColorizer


class FTSummaryConverter:
    """
    FT_SUMMARY.csv 專用轉換器
    
    專門處理 FT_SUMMARY.csv 格式的檔案，提供簡化的 Excel 輸出
    """
    
    def __init__(self):
        print("初始化 FT_Summary 專用轉換器")

    def convert_to_excel(self, csv_path: str, output_path: str = None, enable_coloring: bool = True) -> bool:
        """
        簡化版 FT_SUMMARY.csv 直接轉 Excel 轉換器
        
        功能：
        1. 使用 xlsxwriter 直接寫入，避免複雜的 Summary 邏輯
        2. 套用 _smart_convert_to_number 智慧轉換規則
        3. Definition 欄位自動調整寬度
        4. 避免綠色警告
        5. 可選的 BIN 上色功能（All Pass 綠色，Fail Bins 不同顏色）
        
        Args:
            csv_path: FT_SUMMARY.csv 檔案路徑
            output_path: 輸出 Excel 路徑（可選，預設在輸入檔案的相同目錄）
            enable_coloring: 是否啟用 BIN 上色功能（預設 True）
            
        Returns:
            轉換是否成功
        """
        try:
            print(f"[START] 開始簡化版 FT_SUMMARY 轉換: {csv_path}")
            
            # 1. 設定輸出路徑
            if output_path is None:
                # 取得輸入檔案的目錄
                input_dir = os.path.dirname(csv_path)
                input_filename = os.path.basename(csv_path)
                
                # 生成輸出檔名：.csv → .xlsx (防止重複加上副檔名)
                if input_filename.lower().endswith('.csv'):
                    output_filename = input_filename[:-4] + '.xlsx'
                elif input_filename.lower().endswith('.xlsx'):
                    # 如果已經是.xlsx檔案，直接使用原檔名
                    output_filename = input_filename
                else:
                    output_filename = input_filename + '.xlsx'
                
                # 輸出到與輸入相同的目錄
                output_path = os.path.join(input_dir, output_filename)
            
            print(f"[FOLDER] 輸出路徑: {output_path}")
            
            # 2. 讀取 CSV 資料
            df = self._safe_read_csv(csv_path)
            if df.empty:
                print("[ERROR] 無法讀取 CSV 檔案")
                return False
            
            print(f"[CHART] 讀取成功: {len(df)} 行 x {len(df.columns)} 欄")
            
            # 3. 智慧轉換資料
            print("[DNA_SCIENCE] 執行智慧轉換...")
            df_converted = self._apply_smart_conversion_to_dataframe(df)
            
            # 4. 找到所有 Definition 欄位並計算最佳寬度
            print("[TRIANGLE] 分析 Definition 欄位寬度...")
            definition_columns, max_definition_length = self._analyze_definition_columns(df_converted)
            
            # 5. 使用 xlsxwriter 創建 Excel 檔案
            print("[EDIT] 創建 Excel 檔案...")
            if enable_coloring:
                print("[ART] 啟用 BIN 上色功能")
                success = self._create_colored_ft_summary_excel(df_converted, output_path, definition_columns, max_definition_length)
            else:
                print("[WHITE] 使用標準版本（無上色）")
                success = self._create_ft_summary_excel(df_converted, output_path, definition_columns, max_definition_length)
            
            if success:
                print(f"[OK] FT_SUMMARY Excel 轉換完成: {output_path}")
                # 驗證檔案
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"[FOLDER] 檔案大小: {file_size:,} bytes")
                return True
            else:
                print("[ERROR] Excel 檔案創建失敗")
                return False
                
        except Exception as e:
            print(f"[ERROR] FT_SUMMARY 轉換失敗: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def _create_colored_ft_summary_excel(self, df: pd.DataFrame, output_path: str, 
                                        definition_columns: List[int], max_definition_length: int) -> bool:
        """
        創建帶色彩的 FT_SUMMARY Excel 檔案
        
        Args:
            df: 處理後的 DataFrame
            output_path: 輸出檔案路徑
            definition_columns: Definition 欄位索引列表
            max_definition_length: 最大 Definition 字串長度
            
        Returns:
            bool: 創建是否成功
        """
        try:
            # xlsxwriter 配置：避免綠色警告
            workbook_options = {
                'strings_to_numbers': False,  # 避免自動數字轉換警告
                'nan_inf_to_errors': True
            }
            
            workbook = xlsxwriter.Workbook(output_path, workbook_options)
            worksheet = workbook.add_worksheet("FT_SUMMARY")
            
            print("  [EDIT] 批量寫入資料...")
            
            # 先寫入所有資料（保持原有邏輯）
            for row_idx in range(len(df)):
                row_data = df.iloc[row_idx].tolist()
                
                # 處理 NaN 值
                processed_row = []
                for value in row_data:
                    if pd.isna(value):
                        processed_row.append("")
                    else:
                        processed_row.append(value)
                
                worksheet.write_row(row_idx, 0, processed_row)
            
            print("  [TRIANGLE] 設定欄位寬度...")
            
            # 設定 Definition 欄位寬度（保持原有邏輯）
            definition_padding = 4
            definition_width = max_definition_length + definition_padding
            for col_idx in definition_columns:
                worksheet.set_column(col_idx, col_idx, definition_width)
                print(f"    設定欄位 {col_idx+1} (Definition) 寬度: {definition_width}")
            
            # 動態設定其他欄位寬度（保持原有邏輯）
            column_widths = self._calculate_dynamic_column_widths(df, definition_columns)
            for col_idx, width in column_widths.items():
                if col_idx not in definition_columns:
                    worksheet.set_column(col_idx, col_idx, width)
                    if width != 10:
                        print(f"    設定欄位 {col_idx+1} 寬度: {width}")
            
            # 新增：應用 BIN 上色功能
            print("  [ART] 應用 BIN 上色...")
            colorizer = FTSummaryColorizer()
            coloring_success = colorizer.colorize_excel(workbook, worksheet, df)
            
            if not coloring_success:
                print("  [WARNING] BIN 上色失敗，但 Excel 檔案仍會創建")
            
            workbook.close()
            print("  [OK] 帶色彩的 Excel 檔案創建完成")
            return True
            
        except Exception as e:
            print(f"  [ERROR] 帶色彩的 Excel 檔案創建失敗: {str(e)}")
            try:
                if 'workbook' in locals():
                    workbook.close()
            except:
                pass
            return False

    def _safe_read_csv(self, csv_file_path: str) -> pd.DataFrame:
        """安全讀取 CSV 檔案"""
        try:
            lines = []
            max_cols = 0
            
            with open(csv_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    parts = line.strip().split(',')
                    lines.append(parts)
                    max_cols = max(max_cols, len(parts))
            
            # 統一欄數
            for parts in lines:
                while len(parts) < max_cols:
                    parts.append('')
            
            df = pd.DataFrame(lines)
            print(f"CSV 讀取成功: {len(df)} 行 x {len(df.columns)} 欄")
            return df
            
        except Exception as e:
            print(f"CSV 讀取失敗: {str(e)}")
            return pd.DataFrame()

    def _apply_smart_conversion_to_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        對整個 DataFrame 套用智慧轉換
        
        保留文字：'All Pass', 'Definition', 'Note', 'Site 1', 'Total', 'Pass', 'Fail', 'Yield' 等
        轉換純數字：'123' → 123, '45.67' → 45.67
        """
        print("  [REFRESH] 對所有資料套用智慧轉換...")
        df_converted = df.copy()
        
        conversion_count = 0
        total_cells = len(df) * len(df.columns)
        
        # 逐行逐欄套用智慧轉換
        for row_idx in range(len(df)):
            for col_idx in range(len(df.columns)):
                original_value = df.iloc[row_idx, col_idx]
                converted_value = self._smart_convert_to_number(original_value)
                
                if converted_value != original_value:
                    conversion_count += 1
                
                df_converted.iloc[row_idx, col_idx] = converted_value
        
        print(f"  [OK] 智慧轉換完成: {conversion_count:,} / {total_cells:,} 個儲存格已轉換")
        return df_converted

    def _smart_convert_to_number(self, value):
        """聰明轉換：只轉換純數字，保留文字字串"""
        # 處理空值
        if pd.isna(value) or value is None or str(value).strip() == '':
            return ""  # 空值轉為空字串
        
        if not self._is_pure_number(value):
            return value  # 保持原值
        
        clean_value = str(value).strip()
        try:
            # 嘗試轉換為整數
            if '.' not in clean_value and 'e' not in clean_value.lower():
                return int(clean_value)
            else:
                return float(clean_value)
        except (ValueError, TypeError):
            return value  # 保持原值

    def _is_pure_number(self, value):
        """檢查是否為純數字字串，保留有意義的字串如 'none'"""
        if pd.isna(value) or value is None:
            return False
        
        if not isinstance(value, str):
            return isinstance(value, (int, float)) and not isinstance(value, bool)
        
        # 跳過明顯的文字
        clean_value = str(value).strip().lower()
        if clean_value in ['none', 'na', 'n/a', 'null', 'test_time', 'index_time', 'site_no', '', 
                          'all pass', 'definition', 'note', 'total', 'pass', 'fail',
                          'bin', 'count', '%', 'date:', 'computer:', 'lot id:', 'multiple files']:
            return False
        
        # 檢查是否包含文字字符 (排[EXCEPT_CHAR]百分比符號)
        if any(c.isalpha() for c in clean_value.replace('%', '')):
            return False
        
        # 檢查是否為純數字或百分比
        try:
            # 移[EXCEPT_CHAR]百分比符號後檢查
            test_value = clean_value.replace('%', '')
            float(test_value)
            return True
        except (ValueError, TypeError):
            return False

    def _analyze_definition_columns(self, df: pd.DataFrame) -> tuple:
        """
        分析所有 Definition 欄位位置並計算最佳寬度（動態掃描，無硬編碼）
        
        Returns:
            (definition_columns: List[int], max_definition_length: int)
        """
        definition_columns = []
        min_definition_width = len("Definition")  # 動態最小寬度：至少要容納標題文字
        max_definition_length = min_definition_width
        
        # 動態找到 Definition 標頭行
        definition_header_row = None
        for row_idx in range(min(10, len(df))):  # 掃描前10行找標頭
            row_data = df.iloc[row_idx]
            if any(str(cell).strip().lower() == 'definition' for cell in row_data):
                definition_header_row = row_idx
                break
        
        if definition_header_row is not None:
            print(f"  [LOCATION] 在第{definition_header_row + 1}行找到 Definition 標頭")
            header_row = df.iloc[definition_header_row]
            
            for col_idx, cell_value in enumerate(header_row):
                if str(cell_value).strip().lower() == 'definition':
                    definition_columns.append(col_idx)
                    
                    # 計算該 Definition 欄位下方最長的字串
                    for row_idx in range(definition_header_row + 1, len(df)):
                        if row_idx < len(df) and col_idx < len(df.columns):
                            cell_value = df.iloc[row_idx, col_idx]
                            if pd.notna(cell_value) and str(cell_value).strip():
                                text_length = len(str(cell_value).strip())
                                max_definition_length = max(max_definition_length, text_length)
        
        print(f"  [LOCATION] 找到 {len(definition_columns)} 個 Definition 欄位: {definition_columns}")
        print(f"  [RULER] 最大 Definition 字串長度: {max_definition_length} (最小: {min_definition_width})")
        
        return definition_columns, max_definition_length

    def _calculate_dynamic_column_widths(self, df: pd.DataFrame, definition_columns: List[int]) -> dict:
        """
        動態計算所有欄位的最佳寬度（基於內容長度）
        
        Args:
            df: DataFrame
            definition_columns: Definition 欄位列表（已單獨處理）
            
        Returns:
            字典 {col_idx: width}
        """
        column_widths = {}
        default_min_width = 6  # 最小寬度
        default_max_width = 15  # 最大寬度，避免過寬
        
        # 動態找到標頭行
        header_row_idx = None
        for row_idx in range(min(10, len(df))):
            row_data = df.iloc[row_idx]
            if any(str(cell).strip().lower() in ['bin', 'count', '%', 'definition', 'note'] for cell in row_data):
                header_row_idx = row_idx
                break
        
        for col_idx in range(len(df.columns)):
            if col_idx in definition_columns:
                continue  # Definition 欄位已單獨處理
            
            max_width = default_min_width
            
            # 計算該欄位所有內容的最大長度
            for row_idx in range(len(df)):
                if col_idx < len(df.columns):
                    cell_value = df.iloc[row_idx, col_idx]
                    if pd.notna(cell_value) and str(cell_value).strip():
                        text_length = len(str(cell_value).strip())
                        max_width = max(max_width, text_length)
            
            # 根據標頭內容調整寬度策略
            if header_row_idx is not None and col_idx < len(df.columns):
                header_value = str(df.iloc[header_row_idx, col_idx]).strip().lower()
                
                if header_value in ['bin']:
                    # BIN 欄位：數字，較窄
                    max_width = min(max_width, 8)
                elif header_value in ['count']:
                    # Count 欄位：數字，較窄
                    max_width = min(max_width, 8)
                elif header_value == '%':
                    # 百分比欄位：數字加%，中等寬度
                    max_width = min(max_width, 10)
                elif header_value in ['note']:
                    # Note 欄位：可能較長，但限制最大寬度
                    max_width = min(max_width, default_max_width)
                elif 'site' in header_value:
                    # Site 欄位：中等寬度
                    max_width = min(max_width, 12)
                else:
                    # 其他欄位：使用計算出的寬度，但限制範圍
                    max_width = min(max(max_width, default_min_width), default_max_width)
            else:
                # 無標頭信息，使用預設範圍
                max_width = min(max(max_width, default_min_width), default_max_width)
            
            column_widths[col_idx] = max_width
        
        return column_widths

    def _create_ft_summary_excel(self, df: pd.DataFrame, output_path: str, definition_columns: List[int], max_definition_length: int) -> bool:
        """
        使用 xlsxwriter 創建 FT_SUMMARY Excel 檔案
        
        避免綠色警告，自動調整 Definition 欄位寬度
        """
        try:
            # xlsxwriter 配置：避免綠色警告
            workbook_options = {
                'strings_to_numbers': False,  # 避免自動數字轉換警告
                'nan_inf_to_errors': True
            }
            
            workbook = xlsxwriter.Workbook(output_path, workbook_options)
            worksheet = workbook.add_worksheet("FT_SUMMARY")
            
            print("  [EDIT] 批量寫入資料...")
            
            # 批量寫入所有資料
            for row_idx in range(len(df)):
                row_data = df.iloc[row_idx].tolist()
                
                # 處理 NaN 值
                processed_row = []
                for value in row_data:
                    if pd.isna(value):
                        processed_row.append("")
                    else:
                        processed_row.append(value)
                
                worksheet.write_row(row_idx, 0, processed_row)
            
            print("  [TRIANGLE] 設定欄位寬度...")
            
            # 設定 Definition 欄位寬度 - 動態計算邊距
            definition_padding = 4  # 可調整的邊距
            definition_width = max_definition_length + definition_padding
            for col_idx in definition_columns:
                worksheet.set_column(col_idx, col_idx, definition_width)
                print(f"    設定欄位 {col_idx+1} (Definition) 寬度: {definition_width}")
            
            # 動態設定其他欄位寬度
            column_widths = self._calculate_dynamic_column_widths(df, definition_columns)
            for col_idx, width in column_widths.items():
                if col_idx not in definition_columns:
                    worksheet.set_column(col_idx, col_idx, width)
                    if width != 10:  # 只顯示非預設寬度的
                        print(f"    設定欄位 {col_idx+1} 寬度: {width}")
            
            workbook.close()
            print("  [OK] Excel 檔案創建完成")
            return True
            
        except Exception as e:
            print(f"  [ERROR] Excel 檔案創建失敗: {str(e)}")
            try:
                if 'workbook' in locals():
                    workbook.close()
            except:
                pass
            return False