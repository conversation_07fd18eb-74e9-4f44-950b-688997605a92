# scheduler.py

檔案清理調度器模組，提供自動定時清理功能。

## FileCleanupScheduler

檔案清理調度器類別，負責管理定時清理任務。

### 屬性
- `logger` (logging.Logger): 日誌記錄器
- `scheduler` (BackgroundScheduler): APScheduler 背景調度器
- `file_cleaner` (FileCleaner): 檔案清理器實例
- `is_running` (bool): 調度器運行狀態

### __init__

初始化調度器。

**參數:**
- `logger` (Optional[logging.Logger]): 可選的日誌記錄器，如果未提供則使用模組預設記錄器

**返回值:**
- None

**功能:**
- 初始化 APScheduler 背景調度器
- 建立 FileCleaner 實例
- 設定初始運行狀態

### start_cleanup_job

啟動定時清理任務。

**參數:**
- `target_directories` (list): 要清理的目錄列表
- `cleanup_interval_hours` (int): 清理檢查間隔（小時），預設為1
- `file_retention_hours` (int): 檔案保留時間（小時），預設為24

**返回值:**
- None

**功能:**
- 檢查調度器是否已在運行
- 配置定時任務參數
- 使用 IntervalTrigger 設定執行間隔
- 啟動背景調度器
- 記錄啟動資訊

**任務配置:**
- 任務 ID: 'file_cleanup_job'
- 任務名稱: 'File Cleanup Job'
- 替換現有任務: True

### stop_cleanup_job

停止清理任務。

**返回值:**
- None

**功能:**
- 檢查調度器運行狀態
- 關閉調度器
- 更新運行狀態
- 記錄停止資訊

### manual_cleanup

手動執行清理。

**參數:**
- `target_directories` (list): 目錄列表
- `retention_hours` (int): 保留時間，預設為24

**返回值:**
- dict: 清理結果字典，包含：
  - `total_cleaned` (int): 總清理檔案數
  - `directories` (dict): 各目錄的清理結果
  - `errors` (list): 錯誤訊息列表

**功能:**
- 立即執行檔案清理
- 收集詳細的清理統計
- 記錄每個目錄的清理結果
- 處理和記錄錯誤

**目錄結果格式:**
```python
{
    'directory_path': {
        'cleaned_count': int,
        'cleaned_files': [relative_paths]
    }
}
```

### get_status

獲取調度器狀態。

**返回值:**
- dict: 狀態資訊字典，包含：
  - `status` (str): 'running' 或 'stopped'
  - `jobs` (list): 任務列表

**任務資訊格式:**
```python
{
    'id': str,
    'name': str,
    'next_run_time': str,
    'trigger': str
}
```

**功能:**
- 檢查調度器運行狀態
- 收集所有任務的詳細資訊
- 提供下次執行時間
- 顯示觸發器配置

## 私有方法

### _cleanup_files

執行檔案清理的私有方法。

**參數:**
- `target_directories` (list): 目錄列表
- `retention_hours` (int): 保留時間

**返回值:**
- None

**功能:**
- 遍歷所有目標目錄
- 使用遞歸清理方法
- 統計清理結果
- 處理目錄不存在的情況
- 記錄詳細的清理日誌

## 使用範例

### 基本使用
```python
# 建立調度器
scheduler = FileCleanupScheduler()

# 啟動定時清理
target_dirs = ['/tmp/logs', '/var/cache/app']
scheduler.start_cleanup_job(
    target_directories=target_dirs,
    cleanup_interval_hours=2,
    file_retention_hours=48
)

# 檢查狀態
status = scheduler.get_status()
print(f"調度器狀態: {status['status']}")

# 手動清理
results = scheduler.manual_cleanup(target_dirs, 24)
print(f"清理了 {results['total_cleaned']} 個檔案")

# 停止調度器
scheduler.stop_cleanup_job()
```

## 設計特點

### 調度管理
- **背景執行**: 使用 APScheduler 的 BackgroundScheduler
- **間隔觸發**: 支援自訂清理間隔
- **任務替換**: 自動替換現有同名任務
- **狀態追蹤**: 實時追蹤調度器狀態

### 錯誤處理
- **目錄檢查**: 驗證目錄存在性
- **異常隔離**: 單個目錄錯誤不影響其他目錄
- **詳細記錄**: 記錄所有錯誤和警告
- **結果收集**: 收集並返回詳細的執行結果

### 彈性配置
- **多目錄支援**: 同時清理多個目錄
- **可調間隔**: 自訂檢查和保留時間
- **手動觸發**: 支援立即執行清理
- **狀態查詢**: 提供詳細的運行狀態

### 整合性
- **FileCleaner 整合**: 使用 FileCleaner 的遞歸清理功能
- **日誌整合**: 統一的日誌記錄系統
- **結果統計**: 詳細的清理統計和報告
