# models.py (Database)

郵件資料庫模型模組，使用 SQLAlchemy 定義郵件相關的資料庫表結構。

## EmailDB

郵件資料庫模型，繼承自 SQLAlchemy Base。

### 表名
- `emails`

### 欄位
- `id` (Integer): 主鍵，自動遞增
- `message_id` (String(255)): 郵件 ID，唯一，不可為空，有索引
- `sender` (String(255)): 寄件者，不可為空，有索引
- `sender_display_name` (String(255)): 寄件者顯示名稱，可為空
- `subject` (Text): 郵件主題，不可為空
- `body` (Text): 郵件內容，可為空
- `received_time` (DateTime): 接收時間，不可為空，有索引
- `created_at` (DateTime): 建立時間，預設為當前時間，不可為空
- `is_read` (Boolean): 是否已讀，預設為False，不可為空
- `is_processed` (Boolean): 是否已處理，預設為False，不可為空
- `has_attachments` (Boolean): 是否有附件，預設為False，不可為空
- `attachment_count` (Integer): 附件數量，預設為0，不可為空

### 關聯
- `attachments`: 與 AttachmentDB 的一對多關係，級聯刪[EXCEPT_CHAR]
- `process_status`: 與 EmailProcessStatusDB 的一對多關係，級聯刪[EXCEPT_CHAR]

### 索引
- `idx_email_sender_time`: 寄件者和接收時間的複合索引
- `idx_email_subject`: 主題索引
- `idx_email_created`: 建立時間索引

### __repr__

字串表示方法。

**返回值:**
- str: 包含 ID、主題（前50字符）和寄件者的字串表示

## SenderDB

寄件者資料庫模型，繼承自 SQLAlchemy Base。

### 表名
- `senders`

### 欄位
- `id` (Integer): 主鍵，自動遞增
- `email_address` (String(255)): 郵件地址，唯一，不可為空，有索引
- `display_name` (String(255)): 顯示名稱，可為空
- `total_emails` (Integer): 總郵件數，預設為0，不可為空
- `last_email_time` (DateTime): 最後郵件時間，可為空
- `first_email_time` (DateTime): 首次郵件時間，可為空
- `created_at` (DateTime): 建立時間，預設為當前時間，不可為空
- `updated_at` (DateTime): 更新時間，預設為當前時間，自動更新，不可為空

### __repr__

字串表示方法。

**返回值:**
- str: 包含郵件地址和總郵件數的字串表示

## AttachmentDB

附件資料庫模型，繼承自 SQLAlchemy Base。

### 表名
- `attachments`

### 欄位
- `id` (Integer): 主鍵，自動遞增
- `email_id` (Integer): 郵件 ID 外鍵，參照 emails.id，不可為空，有索引
- `filename` (String(255)): 檔案名稱，不可為空
- `content_type` (String(100)): 內容類型，可為空
- `size_bytes` (Integer): 檔案大小（位元組），不可為空
- `file_path` (Text): 檔案路徑，可為空
- `checksum` (String(64)): 檔案校驗和，可為空
- `is_processed` (Boolean): 是否已處理，預設為False，不可為空
- `created_at` (DateTime): 建立時間，預設為當前時間，不可為空

### 關聯
- `email`: 與 EmailDB 的多對一關係

### 索引
- `idx_attachment_email`: 郵件 ID 索引
- `idx_attachment_filename`: 檔案名稱索引

### __repr__

字串表示方法。

**返回值:**
- str: 包含檔案名稱和大小的字串表示

## EmailProcessStatusDB

郵件處理狀態資料庫模型，繼承自 SQLAlchemy Base。

### 表名
- `email_process_status`

### 欄位
- `id` (Integer): 主鍵，自動遞增
- `email_id` (Integer): 郵件 ID 外鍵，參照 emails.id，不可為空，有索引
- `step_name` (String(100)): 處理步驟名稱，不可為空
- `status` (String(50)): 狀態，不可為空
- `started_at` (DateTime): 開始時間，可為空
- `completed_at` (DateTime): 完成時間，可為空
- `error_message` (Text): 錯誤訊息，可為空
- `progress_percentage` (Integer): 進度百分比，預設為0，不可為空
- `created_at` (DateTime): 建立時間，預設為當前時間，不可為空
- `updated_at` (DateTime): 更新時間，預設為當前時間，自動更新，不可為空

### 關聯
- `email`: 與 EmailDB 的多對一關係

### 索引
- `idx_process_status_email`: 郵件 ID 索引
- `idx_process_status_step`: 步驟名稱索引

### __repr__

字串表示方法。

**返回值:**
- str: 包含步驟名稱和狀態的字串表示

## DatabaseEngine

資料庫引擎管理類別。

### 屬性
- `database_url` (str): 資料庫連接 URL
- `engine`: SQLAlchemy 引擎
- `SessionLocal`: 會話工廠

### initialize

初始化資料庫引擎和建立表格。

**返回值:**
- None

**功能:**
- 建立 SQLAlchemy 引擎
- 建立所有表格
- 設定會話工廠

### get_session

取得資料庫會話。

**返回值:**
- Session: SQLAlchemy 會話物件

### close

關閉資料庫引擎。

**返回值:**
- None

## 全域物件

### Base
- SQLAlchemy 宣告式基類

### db_engine
- DatabaseEngine 的全域實例

## 設計特點

### 關聯設計
- **一對多關係**: 郵件與附件、處理狀態
- **級聯刪[EXCEPT_CHAR]**: 刪[EXCEPT_CHAR]郵件時自動刪[EXCEPT_CHAR]相關附件和狀態
- **外鍵約束**: 確保資料完整性

### 索引策略
- **單欄索引**: 常用查詢欄位
- **複合索引**: 多欄位查詢優化
- **唯一約束**: 防止重複資料

### 資料類型
- **String**: 固定長度文字欄位
- **Text**: 不限長度文字欄位
- **DateTime**: 時間戳記欄位
- **Boolean**: 布林值欄位
- **Integer**: 整數欄位

### 預設值
- **時間戳**: 自動設定建立和更新時間
- **布林值**: 合理的預設狀態
- **計數器**: 從零開始的計數

### 表現層
- **__repr__ 方法**: 提供有意義的字串表示
- **簡潔格式**: 包含關鍵識別資訊
- **長度限制**: 避免過長的輸出
