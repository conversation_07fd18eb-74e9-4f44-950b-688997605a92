"""
檔案清理服務
提供24小時自動清理功能，遵循CLAUDE.md的反假測試原則
"""

import os
import time
import logging
from typing import List, Optional
from datetime import datetime


class FileCleaner:
    """檔案清理服務類別"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化檔案清理器
        
        Args:
            logger: 可選的日誌記錄器
        """
        self.logger = logger or logging.getLogger(__name__)
        
    def clean_old_files(self, directory: str, hours: int = 24, max_items: int = 1000) -> List[str]:
        """
        清理超過指定時間的檔案
        
        Args:
            directory: 要清理的目錄路徑
            hours: 檔案保留時間（小時）
            max_items: 最大處理項目數（防止無限循環）
            
        Returns:
            被清理的檔案名稱列表
            
        Raises:
            FileNotFoundError: 目錄不存在
            OSError: 檔案操作失敗
        """
        if not os.path.exists(directory):
            error_msg = f"目錄不存在: {directory}"
            self.logger.error(error_msg)
            raise FileNotFoundError(error_msg)
            
        if not os.path.isdir(directory):
            error_msg = f"路徑不是目錄: {directory}"
            self.logger.error(error_msg)
            raise OSError(error_msg)
        
        # 計算截止時間
        cutoff_time = time.time() - (hours * 3600)
        cleaned_files = []
        processed_items = 0
        
        self.logger.info(f"開始清理目錄: {directory}, 保留時間: {hours}小時")
        
        try:
            # 使用 os.scandir 提高性能並添加安全檢查
            with os.scandir(directory) as entries:
                for entry in entries:
                    processed_items += 1
                    
                    # 防止無限循環 - 限制處理項目數
                    if processed_items > max_items:
                        self.logger.warning(f"[WARNING] 達到最大處理項目數限制 ({max_items})，停止清理")
                        break
                    
                    # 只處理檔案，不處理目錄或符號連結
                    if entry.is_file(follow_symlinks=False):
                        try:
                            # 獲取檔案統計資訊
                            stat_info = entry.stat(follow_symlinks=False)
                            file_mtime = stat_info.st_mtime
                            
                            # 檢查是否超過保留時間
                            if file_mtime < cutoff_time:
                                # 刪[EXCEPT_CHAR]檔案
                                os.remove(entry.path)
                                cleaned_files.append(entry.name)
                                
                                # 計算檔案年齡
                                age_hours = (time.time() - file_mtime) / 3600
                                self.logger.info(f"已刪[EXCEPT_CHAR]檔案: {entry.name} (年齡: {age_hours:.1f}小時)")
                                
                        except (OSError, FileNotFoundError) as e:
                            self.logger.warning(f"無法處理檔案 {entry.name}: {e}")
                            continue
                    elif entry.is_dir(follow_symlinks=False):
                        # 記錄遇到的目錄但不處理
                        self.logger.debug(f"跳過目錄: {entry.name}")
                    elif entry.is_symlink():
                        # 記錄符號連結但不處理
                        self.logger.debug(f"跳過符號連結: {entry.name}")
                        
        except (OSError, PermissionError) as e:
            error_msg = f"清理目錄失敗: {e}"
            self.logger.error(error_msg)
            raise OSError(error_msg)
        
        self.logger.info(f"清理完成，處理 {processed_items} 個項目，清理 {len(cleaned_files)} 個檔案")
        return cleaned_files
    
    def clean_old_files_recursive(self, directory: str, hours: int = 24, max_items: int = 10000) -> List[str]:
        """
        遞歸清理目錄中的舊檔案（包含子目錄）
        
        Args:
            directory: 要清理的目錄路徑
            hours: 檔案保留時間（小時）
            max_items: 最大處理項目數（防止無限循環）
            
        Returns:
            被清理的檔案路徑列表
        """
        if not os.path.exists(directory):
            error_msg = f"目錄不存在: {directory}"
            self.logger.error(error_msg)
            raise FileNotFoundError(error_msg)
            
        if not os.path.isdir(directory):
            error_msg = f"路徑不是目錄: {directory}"
            self.logger.error(error_msg)
            raise OSError(error_msg)
        
        # 計算截止時間
        cutoff_time = time.time() - (hours * 3600)
        cleaned_files = []
        processed_items = 0
        
        self.logger.info(f"開始遞歸清理目錄: {directory}, 保留時間: {hours}小時")
        
        # 使用 os.walk 遞歸遍歷所有子目錄
        try:
            for root, dirs, files in os.walk(directory):
                # 清理當前目錄中的檔案
                for filename in files:
                    processed_items += 1
                    
                    # 防止無限循環
                    if processed_items > max_items:
                        self.logger.warning(f"[WARNING] 達到最大處理項目數限制 ({max_items})，停止清理")
                        return cleaned_files
                    
                    file_path = os.path.join(root, filename)
                    
                    try:
                        # 獲取檔案統計資訊
                        stat_info = os.stat(file_path)
                        file_mtime = stat_info.st_mtime
                        
                        # 檢查是否超過保留時間
                        if file_mtime < cutoff_time:
                            # 刪[EXCEPT_CHAR]檔案
                            os.remove(file_path)
                            cleaned_files.append(file_path)
                            
                            # 計算檔案年齡
                            age_hours = (time.time() - file_mtime) / 3600
                            relative_path = os.path.relpath(file_path, directory)
                            self.logger.info(f"已刪[EXCEPT_CHAR]檔案: {relative_path} (年齡: {age_hours:.1f}小時)")
                            
                    except (OSError, FileNotFoundError) as e:
                        self.logger.warning(f"無法處理檔案 {file_path}: {e}")
                        continue
                
                # 嘗試刪[EXCEPT_CHAR]空的子目錄
                try:
                    if root != directory and not os.listdir(root):
                        os.rmdir(root)
                        relative_path = os.path.relpath(root, directory)
                        self.logger.info(f"已刪[EXCEPT_CHAR]空目錄: {relative_path}")
                except OSError:
                    # 目錄不為空或無法刪[EXCEPT_CHAR]，跳過
                    pass
                        
        except (OSError, PermissionError) as e:
            error_msg = f"遞歸清理目錄失敗: {e}"
            self.logger.error(error_msg)
            raise OSError(error_msg)
        
        self.logger.info(f"遞歸清理完成，處理 {processed_items} 個項目，清理 {len(cleaned_files)} 個檔案")
        return cleaned_files

    def get_file_age_info(self, directory: str) -> List[dict]:
        """
        獲取目錄中檔案的年齡資訊
        
        Args:
            directory: 目錄路徑
            
        Returns:
            包含檔案資訊的字典列表
        """
        if not os.path.exists(directory):
            return []
            
        file_info = []
        current_time = time.time()
        
        try:
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                if os.path.isfile(item_path):
                    try:
                        stat_info = os.stat(item_path)
                        age_hours = (current_time - stat_info.st_mtime) / 3600
                        
                        file_info.append({
                            'name': item,
                            'path': item_path,
                            'size': stat_info.st_size,
                            'modified_time': stat_info.st_mtime,
                            'age_hours': age_hours,
                            'creation_time': stat_info.st_ctime
                        })
                        
                    except OSError as e:
                        self.logger.warning(f"無法獲取檔案資訊 {item}: {e}")
                        continue
                        
        except OSError as e:
            self.logger.error(f"無法讀取目錄 {directory}: {e}")
            
        return file_info
    
    def preview_cleanup(self, directory: str, hours: int = 24) -> List[str]:
        """
        預覽將被清理的檔案（不實際刪[EXCEPT_CHAR]）
        
        Args:
            directory: 目錄路徑
            hours: 檔案保留時間（小時）
            
        Returns:
            將被清理的檔案名稱列表
        """
        if not os.path.exists(directory):
            return []
            
        cutoff_time = time.time() - (hours * 3600)
        files_to_clean = []
        
        try:
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)
                
                if os.path.isfile(item_path):
                    try:
                        file_mtime = os.path.getmtime(item_path)
                        if file_mtime < cutoff_time:
                            files_to_clean.append(item)
                    except OSError:
                        continue
                        
        except OSError as e:
            self.logger.error(f"預覽清理失敗: {e}")
            
        return files_to_clean