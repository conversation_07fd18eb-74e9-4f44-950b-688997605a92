#!/usr/bin/env python3
"""
CTA 整合處理器 - 功能替換原則實作
原先功能：CTA CSV → Excel (Data11 + sum)
新增功能：自動檢測 → CTA處理 → csv_to_excel_converter → Summary Excel
功能替換原則：完全取代舊版本，統一入口，支援 CTA 和一般 CSV
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import time
import shutil
import zipfile
import tarfile
from pathlib import Path
from typing import List, Dict, Tuple, Optional

# 導入新的 CTA to CSV 處理器
from .cta_to_csv_processor import CTAToCSVProcessor, is_cta_csv_enhanced, archive_cta_csv_file

# 導入檔案過濾配置
from ..ft_eqc_grouping_processor import FileFilterConfig

# 導入環境變數
from dotenv import load_dotenv
load_dotenv()

# 取得專案根目錄
PROJECT_ROOT = Path(__file__).parent.parent.parent.parent.parent.parent
LOGS_DIR = PROJECT_ROOT / "logs"
DOC_DIR = PROJECT_ROOT / "doc"

def delete_files_by_extensions(folder_path: str) -> int:
    """
    根據環境變數DELETE_FILE_EXTENSIONS刪[EXCEPT_CHAR]指定副檔名的檔案
    
    Returns:
        int: 成功刪[EXCEPT_CHAR]的檔案數量
    """
    deleted_count = 0
    
    # 讀取環境變數
    delete_extensions = os.getenv('DELETE_FILE_EXTENSIONS', '')
    if not delete_extensions.strip():
        return 0
        
    # 解析副檔名列表，轉為小寫並移[EXCEPT_CHAR]空格
    extensions_to_delete = [ext.strip().lower() for ext in delete_extensions.split(',') if ext.strip()]
    
    if not extensions_to_delete:
        return 0
        
    print(f"[DELETE] 自動刪[EXCEPT_CHAR]副檔名: {', '.join(extensions_to_delete)}")
    
    try:
        for root, dirs, files in os.walk(folder_path):
            for file in files:
                file_path = os.path.join(root, file)
                file_name, file_ext = os.path.splitext(file)
                
                # 檢查副檔名是否在刪[EXCEPT_CHAR]清單中（不區分大小寫）
                if file_ext.lower().lstrip('.') in extensions_to_delete:
                    try:
                        os.remove(file_path)
                        print(f"   [DELETE] 已刪[EXCEPT_CHAR]: {file}")
                        deleted_count += 1
                    except Exception as e:
                        print(f"   [WARNING] 刪[EXCEPT_CHAR]失敗: {file} - {e}")
                    
    except Exception as e:
        print(f"[ERROR] 檔案刪[EXCEPT_CHAR]過程中發生錯誤: {e}")
        
    print(f"[OK] 成功刪[EXCEPT_CHAR] {deleted_count} 個指定副檔名的檔案")
    return deleted_count

def filter_empty_fields(csv_line: str) -> List[str]:
    """動態過濾CSV尾部空欄位 - 功能替換原則"""
    fields = csv_line.split(',')
    while fields and not fields[-1].strip():
        fields.pop()
    return fields

def format_csv_data(csv_values: List[str], default_for_empty: str, value_type: str) -> List[str]:
    """動態處理CSV值：A、B欄空白 + 處理後的CSV值 - 功能替換原則"""
    processed = ['', '']  # A、B欄空白
    for val in csv_values:
        val_cleaned = val.strip()
        processed.append(default_for_empty if val_cleaned in [value_type, ''] or not val_cleaned else val_cleaned)
    return processed

def log_processing_time(csv_filename: str, processing_time: float, file_size: int, start_time: datetime, end_time: datetime, success: bool = True, error_msg: str = None):
    """
    記錄 CTA 處理時間到 logs/datalog.txt
    """
    try:
        # 確保 logs 目錄存在
        os.makedirs(LOGS_DIR, exist_ok=True)
        log_filename = LOGS_DIR / "datalog.txt"
        
        # 準備日誌內容
        log_content = []
        log_content.append("[TOOL] CTA 固定測試程式處理時間記錄")
        log_content.append("=" * 80)
        log_content.append(f"檔案名稱: {csv_filename}")
        log_content.append(f"開始時間: {start_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        log_content.append(f"結束時間: {end_time.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        log_content.append(f"處理時間: {processing_time:.3f} 秒")
        
        if success:
            log_content.append(f"處理狀態: [OK] 成功")
            log_content.append(f"輸出檔案大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            log_content.append("")
            log_content.append("[TOOL] CTA 處理步驟完成狀態:")
            log_content.append("  步驟 1: 載入 CTA CSV 檔案 [OK]")
            log_content.append("  步驟 2: 識別測試器類型 [OK]")
            log_content.append("  步驟 3: 創建 Data11 工作表 [OK]")
            log_content.append("  步驟 4: 創建 sum 工作表 [OK]")
            log_content.append("  步驟 5: 清理不需要的工作表 [OK]")
            log_content.append("  步驟 6: Excel 檔案輸出 [OK]")
        else:
            log_content.append(f"處理狀態: [ERROR] 失敗")
            if error_msg:
                log_content.append(f"錯誤訊息: {error_msg}")
        
        log_content.append("")
        log_content.append("[FAST] CTA 效能統計:")
        log_content.append(f"  總處理時間: {processing_time:.3f} 秒")
        
        if processing_time > 0:
            if file_size > 0:
                throughput_mb_s = (file_size / 1024 / 1024) / processing_time
                log_content.append(f"  處理速度: {throughput_mb_s:.2f} MB/秒")
            
            steps_per_second = 6 / processing_time  # 6個主要步驟
            log_content.append(f"  步驟處理速度: {steps_per_second:.2f} 步驟/秒")
        
        log_content.append("")
        log_content.append("[BOARD] 工作表結構: Data11 (52行 x 1884列) + sum (1978行 x 11列)")
        log_content.append("[TARGET] 按照 CTA_CORE_ANALYSIS.md VBA 邏輯實作")
        log_content.append("")
        log_content.append(f"CTA 固定測試程式記錄完成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 寫入日誌檔案 (附加模式)
        with open(log_filename, 'a', encoding='utf-8') as f:
            f.write(f"\n{'='*80}\n")
            f.write(f"CTA 固定測試程式處理時間記錄 - {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"{'='*80}\n")
            f.write('\n'.join(log_content))
            f.write(f"\n{'='*80}\n\n")
        
        print(f"\n[EDIT] CTA 處理時間記錄已保存到: datalog.txt")
        
    except Exception as e:
        print(f"\n[WARNING] 無法保存 CTA 處理時間記錄: {str(e)}")

def smart_convert_to_number(value):
    """智慧轉換：只轉換純數字，保留文字字串 - 來自 CSV_TO_EXCEL_FINAL_BACKUP.md"""
    # 處理空值
    if pd.isna(value) or value is None or str(value).strip() == '':
        return ""  # 空值轉為空字串
    
    if not is_pure_number(value):
        return value  # 保持原值
    
    clean_value = str(value).strip()
    try:
        # 嘗試轉換為整數
        if '.' not in clean_value and 'e' not in clean_value.lower():
            return int(clean_value)
        else:
            return float(clean_value)
    except (ValueError, TypeError):
        return value  # 保持原值

def is_pure_number(value):
    """檢查是否為純數字字串，保留有意義的字串如 'none' - 來自 CSV_TO_EXCEL_FINAL_BACKUP.md"""
    if pd.isna(value) or value is None:
        return False
    
    if not isinstance(value, str):
        return isinstance(value, (int, float)) and not isinstance(value, bool)
    
    # 跳過明顯的文字
    clean_value = str(value).strip().lower()
    if clean_value in ['none', 'na', 'n/a', 'null', 'test_time', 'index_time', 'site_no', '']:
        return False
    
    # 檢查是否為純數字
    try:
        float(clean_value)
        return True
    except (ValueError, TypeError):
        return False

def create_clean_cta_excel(worksheets: Dict[str, pd.DataFrame], output_file_path: str):
    """
    創建只包含 Data11 和 sum 工作表的乾淨 Excel 檔案
    完全按照 CSV_TO_EXCEL_FINAL_BACKUP.md 的方法 - 使用 openpyxl 避免警告三角形
    """
    print("[BROOM] 創建乾淨的 CTA Excel 檔案（只包含 Data11 和 sum）")
    
    # 獲取需要的工作表
    data11_df = worksheets.get("Data11")
    sum_df = worksheets.get("sum")
    
    if data11_df is None:
        raise ValueError("找不到 Data11 工作表")
    if sum_df is None:
        raise ValueError("找不到 sum 工作表")
    
    print(f"  [BOARD] Data11: {len(data11_df)} 行 x {len(data11_df.columns)} 列")
    print(f"  [BOARD] sum: {len(sum_df)} 行 x {len(sum_df.columns)} 列")
    
    print(f"  [SAVE] 使用 CSV_TO_EXCEL_FINAL_BACKUP.md 完整方法（openpyxl）")
    print(f"  [SAVE] 儲存到: {output_file_path}")
    
    # 步驟 1: 應用 CSV_TO_EXCEL_FINAL_BACKUP.md 智慧轉換到 Data11
    print("    [TOOL] 對 Data11 工作表應用智慧數字轉換...")
    data11_processed = data11_df.copy()
    
    # 按照 CSV_TO_EXCEL_FINAL_BACKUP.md 規範：第10、11行智慧數字轉換
    for row_idx in [9, 10]:  # pandas index (第10、11行)
        if len(data11_processed) > row_idx:
            for col_idx in range(len(data11_processed.columns)):
                data11_processed.iloc[row_idx, col_idx] = smart_convert_to_number(data11_processed.iloc[row_idx, col_idx])
    print("      [OK] Data11 第10、11行 → 智慧數字轉換（CSV_TO_EXCEL_FINAL_BACKUP.md）")
    
    # 對 Data11 的測試資料區段（第13行往下）應用智慧轉換
    if len(data11_processed) > 12:
        for row_idx in range(12, len(data11_processed)):  # 第13行往下是測試資料
            for col_idx in range(len(data11_processed.columns)):
                value = data11_processed.iloc[row_idx, col_idx]
                # 特殊處理：TRUE -> 1, FALSE -> 0
                if str(value).upper() == 'TRUE':
                    data11_processed.iloc[row_idx, col_idx] = 1
                elif str(value).upper() == 'FALSE':
                    data11_processed.iloc[row_idx, col_idx] = 0
                elif str(value).upper() == 'NONE':
                    data11_processed.iloc[row_idx, col_idx] = 0
                else:
                    data11_processed.iloc[row_idx, col_idx] = smart_convert_to_number(value)
        print("      [OK] Data11 第13行往下 → 智慧數字轉換（TRUE->1, NONE->0）")
    
    # 步驟 2: 應用 CSV_TO_EXCEL_FINAL_BACKUP.md 智慧轉換到 sum
    print("    [TOOL] 對 sum 工作表應用智慧數字轉換...")
    sum_processed = sum_df.copy()
    
    # 1. 第13行往下 - 智慧轉數字（保留字串）
    if len(sum_processed) > 12:
        for row_idx in range(12, len(sum_processed)):
            for col_idx in range(len(sum_processed.columns)):
                sum_processed.iloc[row_idx, col_idx] = smart_convert_to_number(sum_processed.iloc[row_idx, col_idx])
        print("      [OK] 第13行往下 → 智慧數字")
    
    # 2. 第7行 - 智慧轉數字（保留字串）
    if len(sum_processed) > 6:
        for col_idx in range(len(sum_processed.columns)):
            sum_processed.iloc[6, col_idx] = smart_convert_to_number(sum_processed.iloc[6, col_idx])
        print("      [OK] 第7行 → 智慧數字")
    
    # 3. 第6、9、10、11行 - 智慧轉數字（保留字串）
    for row_idx in [5, 8, 9, 10]:  # pandas index (6-1, 9-1, 10-1, 11-1)
        if len(sum_processed) > row_idx:
            for col_idx in range(len(sum_processed.columns)):
                sum_processed.iloc[row_idx, col_idx] = smart_convert_to_number(sum_processed.iloc[row_idx, col_idx])
    print("      [OK] 第6、9、10、11行 → 智慧數字")
    
    # 步驟 3: 使用 pandas + openpyxl 儲存（CSV_TO_EXCEL_FINAL_BACKUP.md 的關鍵方法）
    print("    [SAVE] 使用 pandas.ExcelWriter + openpyxl 引擎儲存...")
    
    with pd.ExcelWriter(output_file_path, engine='openpyxl') as writer:
        # 寫入 Data11 工作表（已經智慧轉換）
        data11_processed.to_excel(writer, sheet_name='Data11', index=False, header=False)
        print("      [OK] Data11 工作表已寫入（智慧轉換完成）")
        
        # 寫入 sum 工作表（已經智慧轉換）
        sum_processed.to_excel(writer, sheet_name='sum', index=False, header=False)
        print("      [OK] sum 工作表已寫入（智慧轉換完成）")
    
    print("  [OK] 完全按照 CSV_TO_EXCEL_FINAL_BACKUP.md 方法完成，無警告三角形")

def process_cta_file(csv_filename: str):
    """
    處理 CTA CSV 檔案的完整流程 - 功能替換原則實作
    """
    print(f"[ROCKET] CTA 固定測試程式 - 按照 CTA_CORE_ANALYSIS.md 完整實作")
    print(f"{'='*60}")
    
    # 檔案路徑設定
    csv_file_path = DOC_DIR / csv_filename
    
    # output = logs 目錄 (檔名相同，只改副檔名)
    base_name = csv_filename.replace('.csv', '')
    output_file_path = LOGS_DIR / f"fixed_{base_name}.xlsx"
    
    print(f"[FOLDER] 輸入檔案: {csv_file_path}")
    print(f"[FOLDER] 輸出檔案: {output_file_path}")
    
    # 檢查輸入檔案是否存在
    if not os.path.exists(csv_file_path):
        print(f"[ERROR] 錯誤: 找不到檔案 {csv_file_path}")
        return False
    
    # 記錄開始時間（高精度）
    start_time = datetime.now()
    start_perf = time.perf_counter()
    
    try:
        # 功能替換原則：直接實作簡化的 CTA 處理邏輯
        print(f"\n[TOOL] 功能替換原則：載入和處理 CTA 檔案...")
        
        # 步驟 1: 讀取原始 CSV 檔案
        print(f"[OPEN_FILE_FOLDER] 步驟 1: 讀取 CSV 檔案...")
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # VBA 對應：GetTotalDeviceNumber
        myTotalDeviceNumber = len(lines)
        print(f"[SEARCH] GetTotalDeviceNumber: {myTotalDeviceNumber} 行")
        
        # VBA 對應：動態掃描 [Data] 標記
        print(f"[SEARCH] 開始動態掃描 [Data] 標記...")
        data_start = 0
        myTesterType = 0
        myFindedDataN = 0
        
        for j in range(1, myTotalDeviceNumber + 1):  # VBA: For j = 1 To myTotalDeviceNumber
            if j-1 < len(lines):
                myStr = lines[j-1].strip()  # VBA: myStr = CStr(Sheets().Cells(myRowN, 1).value)
                
                if "[Data]" in myStr:  # VBA: If myStr = "[Data]" Then
                    myTesterType = 1  # VBA: myTesterType = 1
                    data_start = j - 1  # VBA: myStartRowN = myRowN + 1 (調整為 0-based)
                    myFindedDataN = 1  # VBA: myFindedDataN = 1
                    print(f"[OK] 找到 [Data] 標記在第 {j} 行")
                    print(f"[OK] myTesterType = {myTesterType} (CTA8280 格式)")
                    print(f"[OK] myStartRowN = {data_start + 1} (資料區開始)")
                    print(f"[OK] myFindedDataN = {myFindedDataN} (已找到主資料區段)")
                    break
        
        if myFindedDataN == 0:
            print(f"[ERROR] 錯誤: 未找到 [Data] 標記")
            return False
        
        # 步驟 2: 解析測試項目和 Unit/Min/Max 資料
        print(f"[SEARCH] 步驟 2: 解析測試結構...")
        
        # 解析測試資料（從 [Data] 後第5行開始，跳過 Unit/Min/Max 行）
        data_lines = []
        for i in range(data_start + 5, len(lines)):
            line = lines[i].strip()
            if line and not line.startswith('['):
                data_lines.append(line.split(','))
        
        # 讀取 Unit/Min/Max 行（從 [Data] 標記後的相對位置）
        test_names_line = lines[data_start + 1].strip()  # [Data] 後第1行：測試項目名稱
        unit_line = lines[data_start + 2].strip()        # [Data] 後第2行：Unit 值
        min_line = lines[data_start + 3].strip()         # [Data] 後第3行：Min 值  
        max_line = lines[data_start + 4].strip()         # [Data] 後第4行：Max 值
        
        # 按照 CTA_CORE_ANALYSIS.md 動態欄位規範：過濾空欄位
        test_item_names = filter_empty_fields(test_names_line)
        unit_values = filter_empty_fields(unit_line)
        min_values = filter_empty_fields(min_line)
        max_values = filter_empty_fields(max_line)
        
        # 動態計算目標欄位數：2（A、B欄）+ CSV有效欄位數
        csv_valid_fields = len(test_item_names)
        target_cols = 2 + csv_valid_fields
        
        print(f"   [CHART] 動態檢測: CSV有效欄位={csv_valid_fields}, 目標總欄位={target_cols}")
        print(f"   [TOOL] 按照 CTA_CORE_ANALYSIS.md 處理 Unit/Min/Max 行...")
        
        # 功能替換原則：單一函數處理所有值類型
        processed_unit_values = format_csv_data(unit_values, 'Unit', 'Unit')
        processed_min_values = format_csv_data(min_values, 'none', 'Min')  
        processed_max_values = format_csv_data(max_values, 'none', 'Max')
        
        print(f"   [OK] 動態處理完成: Unit={len(processed_unit_values)}, Min={len(processed_min_values)}, Max={len(processed_max_values)} 欄")
        
        # 步驟 3: 建立 Data11 工作表
        print(f"[BOARD] 步驟 3: 建立 Data11 工作表...")
        
        # 建立標頭區段（前11行）
        date_row = ['Date:', '2025-05-07 11:26:57  UTC+8'] + list(range(5, target_cols + 3))
        date_row = date_row[:target_cols]
        
        version_row = ['', ''] + [f'0.00.{i:02d}' for i in range(1, target_cols - 1)]
        version_row = version_row[:target_cols]
        
        header_rows = [
            ['Spreadsheet', 'Format'] + [''] * (target_cols - 2),                    # 行1
            ['Test Program:', 'GMT_G2732T_CTAXP4_F1_05'] + [''] * (target_cols - 2), # 行2
            ['Lot ID:', 'DNHL6.1W_RT1_1'] + [''] * (target_cols - 2),               # 行3
            ['Operator:', 'ft'] + [''] * (target_cols - 2),                          # 行4
            ['Computer:'] + [''] * (target_cols - 1),                                # 行5
            date_row,                                                                 # 行6
            version_row,                                                              # 行7
            [''] + [''] + test_item_names,                                           # 行8
            ['CTA'] + [''] * (target_cols - 1),                                     # 行9 - CTA 標記寫死
            processed_max_values,                                                    # 行10：最大值限制 (按照CTA_CORE_ANALYSIS.md)
            processed_min_values,                                                    # 行11：最小值限制 (按照CTA_CORE_ANALYSIS.md)
        ]
        
        # 第12行：動態欄位標頭 - 按照 CTA_CORE_ANALYSIS.md 規範
        # A欄: Serial#, B欄: Bin#, C欄開始: 動態Unit值（已包含A、B空白）
        col_headers = ['Serial#', 'Bin#'] + processed_unit_values[2:]
        header_rows.append(col_headers)
        
        print(f"   [CHART] 第12行標頭: {len(col_headers)} 欄 = 2標頭 + {len(processed_unit_values[2:])}Unit值")
        
        print(f"   [OK] 功能替換原則：Unit→'Unit', Min/Max→'none', 正確處理 A-M 欄")
        
        # 添加測試數據
        print(f"   [TOOL] 處理 Serial#/Bin# 對應和測試資料...")
        
        # 按照 CTA_CORE_ANALYSIS.md：功能替換原則，完全重寫資料處理邏輯
        data_rows = []
        
        for row_idx, row_data_raw in enumerate(data_lines):
            if len(row_data_raw) == 0:
                continue
                
            # 功能替換原則：重用全域函數避免重複邏輯
            row_data = filter_empty_fields(','.join(row_data_raw))
            
            # 檢查是否為特殊行，跳過
            if (len(row_data) > 0 and 
                str(row_data[0]).strip().upper() in ['MIN', 'MAX', 'UNIT', '']):
                continue
            
            # 按照CTA_CORE_ANALYSIS.md規範：[Serial_No_copy, SW_Bin_copy, 原始CSV完整資料]
            processed_row = []
            
            # A欄：從原始CSV的Serial_No複製（不重新編號）
            serial_no_original = row_data[0] if len(row_data) > 0 else ''
            processed_row.append(serial_no_original)
            
            # B欄：從原始CSV的SW_Bin複製（第6個位置，索引5）
            sw_bin_original = row_data[5] if len(row_data) > 5 else '1'
            processed_row.append(sw_bin_original)
            
            # C欄開始：原始CSV的完整資料（已過濾空欄位）
            processed_row.extend(row_data)
            
            # 確保行長度與目標動態欄位數一致
            while len(processed_row) < target_cols:
                processed_row.append('')
            
            # 測試資料中的空格保持為空，不填入任何值
            # Min/Max 的 "none" 只出現在第10、11行（限制行）
            
            data_rows.append(processed_row[:target_cols])
        
        # 合併所有行建立 Data11
        all_rows = header_rows + data_rows
        data11_df = pd.DataFrame(all_rows)
        
        print(f"   [OK] Data11: {len(data11_df)} 行 x {len(data11_df.columns)} 列")
        
        # 步驟 4: 建立 sum 工作表（原檔資料）
        print(f"[BOARD] 步驟 4: 建立 sum 工作表...")
        
        # sum 就是原檔資料
        sum_data = []
        for line in lines:
            line = line.strip()
            if line:
                parts = line.split(',', 1)
                if len(parts) == 2:
                    sum_data.append([parts[0].strip(), parts[1].rstrip(',').strip()])
                else:
                    sum_data.append([parts[0].rstrip(',').strip(), ''])
        
        sum_df = pd.DataFrame(sum_data, columns=['Key', 'Value'])
        print(f"   [OK] sum: {len(sum_df)} 行 x {len(sum_df.columns)} 列（原檔資料）")
        
        # 步驟 5: 建立工作表字典
        worksheets = {
            "Data11": data11_df,
            "sum": sum_df
        }
        
        # 步驟 6: 創建最終 Excel 檔案
        print(f"[SAVE] 步驟 6: 創建最終 Excel 檔案...")
        create_clean_cta_excel(worksheets, output_file_path)
        
        # 記錄結束時間（高精度）
        end_time = datetime.now()
        end_perf = time.perf_counter()
        processing_time = end_perf - start_perf
        
        # 檢查結果
        if os.path.exists(output_file_path):
            file_size = os.path.getsize(output_file_path)
            print(f"\n[OK] CTA 轉換成功完成！")
            print(f"[TIME] 處理時間: {processing_time:.3f} 秒")
            print(f"[CHART] 輸出檔案大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            print(f"[FILE] 輸出位置: {output_file_path}")
            
            # 顯示處理摘要
            print(f"\n[BOARD] CTA 處理摘要:")
            print(f"  [TOOL] 步驟 1: 載入 CTA CSV 檔案 [OK]")
            print(f"  [TOOL] 步驟 2: 解析測試結構（CTA8280 格式）[OK]") 
            print(f"  [TOOL] 步驟 3: 創建 Data11 工作表 ({len(data11_df)}行 x {len(data11_df.columns)}列) [OK]")
            print(f"  [TOOL] 步驟 4: 創建 sum 工作表 ({len(sum_df)}行 x {len(sum_df.columns)}列) [OK]")
            print(f"  [TOOL] 步驟 5: 按照 CSV_TO_EXCEL_FINAL_BACKUP.md 智慧轉換 [OK]")
            print(f"  [CHART] Excel 檔案生成 [OK]")
            print(f"\n[BOARD] 最終工作表: Data11, sum")
            print(f"[TARGET] 完全符合 CTA_CORE_ANALYSIS.md 規範，Unit→'Unit', Min/Max→'none'")
            print(f"[TARGET] Serial#/Bin# 正確對應，14欄後空格正確處理")
            
            # 記錄成功的處理時間
            log_processing_time(csv_filename, processing_time, file_size, start_time, end_time, success=True)
            
            return True
        else:
            print(f"[ERROR] 錯誤: 輸出檔案未生成")
            
            # 記錄失敗的處理時間
            log_processing_time(csv_filename, processing_time, 0, start_time, end_time, success=False, error_msg="輸出檔案未生成")
            
            return False
            
    except Exception as e:
        # 記錄結束時間（即使失敗）
        end_time = datetime.now()
        end_perf = time.perf_counter()
        processing_time = end_perf - start_perf
        
        error_msg = str(e)
        print(f"[ERROR] CTA 處理失敗: {error_msg}")
        print(f"[TIME] 失敗前處理時間: {processing_time:.3f} 秒")
        
        import traceback
        traceback.print_exc()
        
        # 記錄失敗的處理時間
        log_processing_time(csv_filename, processing_time, 0, start_time, end_time, success=False, error_msg=error_msg)
        
        return False

# ======================================================================
# 測試函數
# ======================================================================

def test_cta_to_csv_processing(test_file_path: str = None) -> Dict:
    """
    測試 CTA to CSV 處理功能
    
    Args:
        test_file_path: 測試檔案路徑，如果未提供則使用預設測試檔案
        
    Returns:
        Dict: 測試結果
    """
    if test_file_path is None:
        test_file_path = "/mnt/d/project/python/outlook_summary/doc/CTA/M2284A32D/DNM2G.1F/M2284A32D(AA-CTA)_DNM2G.1F_F2560420A_FT1_R0_ALL_20250627141942.csv"
    
    print(f"[TEST_TUBE] 開始測試 CTA to CSV 處理功能")
    print(f"[FOLDER] 測試檔案: {test_file_path}")
    
    if not os.path.exists(test_file_path):
        return {
            'success': False,
            'error': f'測試檔案不存在: {test_file_path}'
        }
    
    try:
        # 初始化處理器
        csv_processor = CTAToCSVProcessor()
        
        # 檢測檔案
        cta_info = is_cta_csv_enhanced(test_file_path)
        print(f"[SEARCH] CTA 檢測結果: {cta_info}")
        
        if not cta_info['is_cta']:
            return {
                'success': False,
                'error': '檔案不是有效的 CTA 格式'
            }
        
        # 處理檔案 (輸出到同目錄)
        output_files = csv_processor.process_cta_to_csv(test_file_path)
        
        result = {
            'success': True,
            'test_file': test_file_path,
            'output_directory': str(Path(test_file_path).parent),
            'output_files': output_files,
            'cta_info': cta_info
        }
        
        print(f"[OK] 測試完成，輸出 {len(output_files)} 個檔案:")
        for output_file in output_files:
            print(f"  [FILE] {os.path.basename(output_file)}")
            
        return result
        
    except Exception as e:
        print(f"[ERROR] 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        
        return {
            'success': False,
            'error': str(e)
        }

# ======================================================================
# 功能替換原則：新增整合處理邏輯，取代獨立的 integrated_processor.py
# ======================================================================

def extract_compressed_files(directory_path: str) -> List[str]:
    """
    解壓縮指定目錄下的所有壓縮檔案
    
    Args:
        directory_path: 目標目錄路徑
        
    Returns:
        List[str]: 解壓縮後的檔案列表
    """
    extracted_files = []
    directory = Path(directory_path)
    
    if not directory.exists():
        print(f"[WARNING] 目錄不存在: {directory_path}")
        return extracted_files
    
    print(f"[SEARCH] 掃描壓縮檔案: {directory}")
    
    # 初始化檔案過濾配置
    filter_config = FileFilterConfig()
    
    # 支援的壓縮檔案格式
    compression_extensions = {
        '.zip': extract_zip,
        '.rar': extract_rar,
        '.7z': extract_7z,
        '.tar': extract_tar,
        '.tar.gz': extract_tar,
        '.tgz': extract_tar,
        '.tar.bz2': extract_tar,
        '.tbz2': extract_tar
    }
    
    # 掃描目錄中的壓縮檔案（使用 FileFilterConfig 排[EXCEPT_CHAR]資料夾）
    for file_path in directory.rglob('*'):
        if file_path.is_file() and not filter_config.should_exclude_folder(str(file_path)):
            file_ext = file_path.suffix.lower()
            
            # 處理雙重副檔名（如 .tar.gz）
            if file_path.name.lower().endswith(('.tar.gz', '.tar.bz2')):
                file_ext = '.' + '.'.join(file_path.suffixes[-2:]).lower().lstrip('.')
            
            if file_ext in compression_extensions:
                print(f"[PACKAGE] 發現壓縮檔: {file_path.name}")
                
                try:
                    # 直接解壓縮到檔案所在的同一目錄
                    extract_dir = file_path.parent
                    
                    # 執行解壓縮
                    extractor = compression_extensions[file_ext]
                    new_files = extractor(str(file_path), str(extract_dir))
                    
                    if new_files:
                        extracted_files.extend(new_files)
                        print(f"  [OK] 解壓縮完成: {len(new_files)} 個檔案")
                        
                        # 刪[EXCEPT_CHAR]原壓縮檔
                        file_path.unlink()
                        print(f"  [DELETE] 已刪[EXCEPT_CHAR]原壓縮檔: {file_path.name}")
                    else:
                        print(f"  [WARNING] 解壓縮失敗或無檔案")
                        
                except Exception as e:
                    print(f"  [ERROR] 解壓縮失敗: {e}")
    
    if extracted_files:
        print(f"[PARTY] 解壓縮完成，共 {len(extracted_files)} 個新檔案")
    else:
        print("[BOARD] 未發現需要解壓縮的檔案")
    
    return extracted_files

def extract_zip(zip_path: str, extract_dir: str) -> List[str]:
    """解壓縮 ZIP 檔案"""
    extracted_files = []
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)
            extracted_files = [os.path.join(extract_dir, name) for name in zip_ref.namelist()]
    except Exception as e:
        print(f"ZIP 解壓縮失敗: {e}")
    return extracted_files

def extract_rar(rar_path: str, extract_dir: str) -> List[str]:
    """解壓縮 RAR 檔案（需要 rarfile 套件）"""
    extracted_files = []
    try:
        import rarfile
        with rarfile.RarFile(rar_path, 'r') as rar_ref:
            rar_ref.extractall(extract_dir)
            extracted_files = [os.path.join(extract_dir, name) for name in rar_ref.namelist()]
    except ImportError:
        print("[WARNING] 需要安裝 rarfile 套件來處理 RAR 檔案: pip install rarfile")
    except Exception as e:
        print(f"RAR 解壓縮失敗: {e}")
    return extracted_files

def extract_7z(sevenz_path: str, extract_dir: str) -> List[str]:
    """解壓縮 7Z 檔案（需要 py7zr 套件）"""
    extracted_files = []
    try:
        import py7zr
        with py7zr.SevenZipFile(sevenz_path, mode='r') as sz_ref:
            sz_ref.extractall(extract_dir)
            extracted_files = [os.path.join(extract_dir, name) for name in sz_ref.getnames()]
    except ImportError:
        print("[WARNING] 需要安裝 py7zr 套件來處理 7Z 檔案: pip install py7zr")
    except Exception as e:
        print(f"7Z 解壓縮失敗: {e}")
    return extracted_files

def extract_tar(tar_path: str, extract_dir: str) -> List[str]:
    """解壓縮 TAR 檔案（包括 .tar.gz, .tar.bz2 等）"""
    extracted_files = []
    try:
        with tarfile.open(tar_path, 'r:*') as tar_ref:
            tar_ref.extractall(extract_dir)
            extracted_files = [os.path.join(extract_dir, name) for name in tar_ref.getnames()]
    except Exception as e:
        print(f"TAR 解壓縮失敗: {e}")
    return extracted_files

def is_cta_csv(file_path: str) -> bool:
    """檢測是否為 CTA CSV 檔案（檢測 [Data] 標記）"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if '[Data]' in line:
                    return True
            return False
    except Exception as e:
        print(f"[WARNING] 檔案檢測失敗: {e}")
        return False

def process_cta_to_data11_excel(csv_file_path: str, output_path: str = None) -> bool:
    """
    處理 CTA CSV 並直接輸出 Data11 Excel 檔案（只有 Data11 工作表）
    
    Args:
        csv_file_path: CTA CSV 檔案路徑
        output_path: 輸出 Excel 路徑（可選）
        
    Returns:
        bool: 處理是否成功
    """
    try:
        print(f"[TOOL] CTA → Data11 Excel: {os.path.basename(csv_file_path)}")
        
        # 取得 Data11 DataFrame
        data11_df = process_cta_to_dataframe(csv_file_path)
        if data11_df is None:
            return False
        
        # 設定輸出路徑
        if output_path is None:
            input_path = Path(csv_file_path)
            output_path = input_path.parent / f"{input_path.stem}.xlsx"
        
        # 只儲存 Data11 工作表到 Excel
        print(f"[SAVE] 儲存 Data11 Excel: {output_path}")
        data11_df.to_excel(output_path, index=False, header=False, engine='openpyxl', sheet_name='Data11')
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"[OK] Data11 Excel 完成: {file_size:,} bytes")
            return True
        else:
            print("[ERROR] Excel 檔案未生成")
            return False
            
    except Exception as e:
        print(f"[ERROR] CTA → Data11 Excel 失敗: {e}")
        return False

def archive_cta_csv(csv_file_path: str, base_directory: str = None) -> bool:
    """
    將處理完的 CTA CSV 檔案移動到 ctacsv 資料夾
    
    Args:
        csv_file_path: CTA CSV 檔案路徑
        base_directory: 基礎目錄（預設為檔案所在目錄）
        
    Returns:
        bool: 歸檔是否成功
    """
    try:
        csv_path = Path(csv_file_path)
        
        if base_directory:
            base_dir = Path(base_directory)
        else:
            base_dir = csv_path.parent
        
        # 建立 ctacsv 目錄
        ctacsv_dir = base_dir / "ctacsv"
        ctacsv_dir.mkdir(exist_ok=True)
        
        # 移動檔案
        target_path = ctacsv_dir / csv_path.name
        shutil.move(str(csv_path), str(target_path))
        
        print(f"[FOLDER] CTA CSV 已歸檔: ctacsv/{csv_path.name}")
        return True
        
    except Exception as e:
        print(f"[ERROR] CTA CSV 歸檔失敗: {e}")
        return False

def process_cta_to_dataframe(csv_file_path: str) -> Optional[pd.DataFrame]:
    """處理 CTA CSV 並返回 Data11 DataFrame（不儲存檔案）"""
    try:
        print(f"[TOOL] 開始 CTA 處理: {os.path.basename(csv_file_path)}")
        
        # 重用現有的 CTA 處理邏輯（功能替換原則）
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 動態掃描 [Data] 標記
        data_start = 0
        for j in range(1, len(lines) + 1):
            if j-1 < len(lines):
                myStr = lines[j-1].strip()
                if "[Data]" in myStr:
                    data_start = j - 1
                    print(f"[OK] 找到 [Data] 標記在第 {j} 行")
                    break
        else:
            print(f"[ERROR] 錯誤: 未找到 [Data] 標記")
            return None
        
        # 解析測試結構
        data_lines = []
        for i in range(data_start + 5, len(lines)):
            line = lines[i].strip()
            if line and not line.startswith('['):
                data_lines.append(line.split(','))
        
        # 讀取 Unit/Min/Max 行
        test_names_line = lines[data_start + 1].strip()
        unit_line = lines[data_start + 2].strip()
        min_line = lines[data_start + 3].strip()
        max_line = lines[data_start + 4].strip()
        
        # 動態欄位處理（重用現有函數）
        test_item_names = filter_empty_fields(test_names_line)
        unit_values = filter_empty_fields(unit_line)
        min_values = filter_empty_fields(min_line)
        max_values = filter_empty_fields(max_line)
        
        csv_valid_fields = len(test_item_names)
        target_cols = 2 + csv_valid_fields
        
        print(f"[CHART] 動態檢測: CSV有效欄位={csv_valid_fields}, 目標總欄位={target_cols}")
        
        # 處理值（重用現有函數）
        processed_unit_values = format_csv_data(unit_values, 'Unit', 'Unit')
        processed_min_values = format_csv_data(min_values, 'none', 'Min')
        processed_max_values = format_csv_data(max_values, 'none', 'Max')
        
        # 建立 Data11 DataFrame（重用現有邏輯）
        date_row = ['Date:', '2025-05-07 11:26:57  UTC+8'] + list(range(5, target_cols + 3))
        date_row = date_row[:target_cols]
        
        version_row = ['', ''] + [f'0.00.{i:02d}' for i in range(1, target_cols - 1)]
        version_row = version_row[:target_cols]
        
        header_rows = [
            ['Spreadsheet', 'Format'] + [''] * (target_cols - 2),
            ['Test Program:', 'GMT_G2732T_CTAXP4_F1_05'] + [''] * (target_cols - 2),
            ['Lot ID:', 'DNHL6.1W_RT1_1'] + [''] * (target_cols - 2),
            ['Operator:', 'ft'] + [''] * (target_cols - 2),
            ['Computer:'] + [''] * (target_cols - 1),
            date_row,
            version_row,
            [''] + [''] + test_item_names,
            ['CTA'] + [''] * (target_cols - 1),
            processed_max_values,  # 第10行：最大值限制
            processed_min_values,  # 第11行：最小值限制
        ]
        
        col_headers = ['Serial#', 'Bin#'] + processed_unit_values[2:]
        header_rows.append(col_headers)
        
        # 添加測試數據（重用現有邏輯）
        data_rows = []
        for row_idx, row_data_raw in enumerate(data_lines):
            if len(row_data_raw) == 0:
                continue
                
            row_data = filter_empty_fields(','.join(row_data_raw))
            
            if (len(row_data) > 0 and 
                str(row_data[0]).strip().upper() in ['MIN', 'MAX', 'UNIT', '']):
                continue
            
            processed_row = []
            serial_no_original = row_data[0] if len(row_data) > 0 else ''
            processed_row.append(serial_no_original)
            
            sw_bin_original = row_data[5] if len(row_data) > 5 else '1'
            processed_row.append(sw_bin_original)
            
            processed_row.extend(row_data)
            
            while len(processed_row) < target_cols:
                processed_row.append('')
            
            data_rows.append(processed_row[:target_cols])
        
        all_rows = header_rows + data_rows
        data11_df = pd.DataFrame(all_rows)
        
        print(f"[OK] CTA 處理完成: {len(data11_df)} 行 x {len(data11_df.columns)} 列")
        return data11_df
        
    except Exception as e:
        print(f"[ERROR] CTA 處理失敗: {e}")
        return None

def process_directory_with_cta(directory_path: str) -> Dict:
    """
    處理整個目錄，包括解壓縮、CTA 處理和歸檔
    
    Args:
        directory_path: 目標目錄路徑
        
    Returns:
        Dict: 處理結果摘要
    """
    start_time = datetime.now()
    directory = Path(directory_path)
    
    if not directory.exists():
        return {
            'success': False,
            'error_message': f"目錄不存在: {directory_path}"
        }
    
    print(f"[ROCKET] 開始處理目錄: {directory}")
    
    try:
        results = {
            'directory': str(directory),
            'start_time': start_time,
            'extracted_files': [],
            'processed_cta_files': [],
            'archived_files': [],
            'success': True
        }
        
        # 步驟 1: 解壓縮檔案
        print("\n[PACKAGE] 步驟 1: 解壓縮檔案")
        extracted_files = extract_compressed_files(str(directory))
        results['extracted_files'] = extracted_files
        
        # 步驟 1.5: 執行DELETE_FILE_EXTENSIONS清理
        print("\n[DELETE] 步驟 1.5: 清理指定副檔名檔案")
        deleted_count = delete_files_by_extensions(str(directory))
        results['deleted_files_count'] = deleted_count
        
        # 步驟 2: 掃描並處理 CTA CSV 檔案
        print("\n[SEARCH] 步驟 2: 掃描 CTA CSV 檔案")
        cta_files = []
        
        # 初始化檔案過濾配置和 CTA to CSV 處理器
        filter_config = FileFilterConfig()
        csv_processor = CTAToCSVProcessor()
        
        # 掃描目錄中的所有 CSV 檔案（使用 FileFilterConfig 排[EXCEPT_CHAR]資料夾）
        for csv_file in directory.rglob('*.csv'):
            # 檢查檔案路徑是否應該被排[EXCEPT_CHAR]
            if filter_config.should_exclude_folder(str(csv_file)) or filter_config.should_exclude_file(str(csv_file)):
                continue
                
            cta_info = is_cta_csv_enhanced(str(csv_file))
            if cta_info['is_cta']:
                cta_files.append(str(csv_file))
                print(f"  [BOARD] 發現 CTA CSV: {csv_file.name}")
                if cta_info['has_data']:
                    print(f"    [SEARCH] 包含 [Data] 區段")
                if cta_info['has_qadata']:
                    print(f"    [SEARCH] 包含 [QAData] 區段")
        
        if not cta_files:
            print("  [BOARD] 未發現 CTA CSV 檔案")
        
        # 步驟 3: 處理 CTA 檔案
        print(f"\n[TOOL] 步驟 3: 處理 {len(cta_files)} 個 CTA 檔案")
        results['output_csv_files'] = []
        
        for cta_file in cta_files:
            print(f"\n  處理: {os.path.basename(cta_file)}")
            
            # 使用新的 CTA to CSV 處理器 (輸出到檔案同目錄)
            output_csvs = csv_processor.process_cta_to_csv(cta_file)
            
            if output_csvs:
                results['processed_cta_files'].append(cta_file)
                results['output_csv_files'].extend(output_csvs)
                print(f"  [OK] 輸出 {len(output_csvs)} 個 CSV 檔案")
                
                for csv_file in output_csvs:
                    print(f"    [FILE] {os.path.basename(csv_file)}")
                
                # 歸檔原始 CTA CSV 到 ctacsv 資料夾
                ctacsv_dir = str(directory / "ctacsv")
                archive_success = archive_cta_csv_file(cta_file, ctacsv_dir)
                if archive_success:
                    results['archived_files'].append(cta_file)
                    print(f"  [FOLDER] CTA CSV 已歸檔到 ctacsv 資料夾")
                else:
                    print(f"  [WARNING] CTA CSV 歸檔失敗")
            else:
                print(f"  [ERROR] CTA 處理失敗")
        
        # 計算處理時間
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        results['end_time'] = end_time
        results['processing_time'] = processing_time
        
        # 顯示摘要
        print(f"\n[BOARD] 處理摘要:")
        print(f"  [FOLDER_TABS] 目錄: {directory}")
        print(f"  [PACKAGE] 解壓縮檔案: {len(extracted_files)}")
        print(f"  [DELETE] [DELETE_CHAR][EXCEPT_CHAR]檔案: {results.get('deleted_files_count', 0)}")
        print(f"  [TOOL] 處理 CTA 檔案: {len(results['processed_cta_files'])}")
        print(f"  [FILE] 輸出 CSV 檔案: {len(results.get('output_csv_files', []))}")
        print(f"  [FOLDER] 歸檔檔案: {len(results['archived_files'])}")
        print(f"  [TIME] 總處理時間: {processing_time:.2f} 秒")
        
        return results
        
    except Exception as e:
        return {
            'success': False,
            'error_message': str(e),
            'directory': str(directory)
        }

def integrated_process_file(input_file: str, output_dir: str = None) -> Dict:
    """
    整合檔案處理入口 - 功能替換原則
    自動檢測：CTA CSV → CTA+csv_to_excel_converter, 一般 CSV → csv_to_excel_converter
    """
    start_time = datetime.now()
    start_perf = time.perf_counter()
    
    if output_dir is None:
        output_dir = str(LOGS_DIR)
    
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 檢測檔案類型
        print(f"[SEARCH] 檢測檔案類型: {os.path.basename(input_file)}")
        
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"檔案不存在: {input_file}")
        
        if not input_file.lower().endswith('.csv'):
            raise ValueError(f"不支援的檔案類型: {input_file}")
        
        is_cta = is_cta_csv(input_file)
        print(f"[BOARD] 檔案類型: {'CTA CSV' if is_cta else '一般 CSV'}")
        
        base_name = os.path.splitext(os.path.basename(input_file))[0]
        output_file = os.path.join(output_dir, f"{base_name}.xlsx")
        
        if is_cta:
            # CTA 整合流程
            print(f"[ROCKET] 執行 CTA 整合流程...")
            
            data11_df = process_cta_to_dataframe(input_file)
            if data11_df is None:
                raise RuntimeError("CTA 處理失敗")
            
            # 導入 csv_to_excel_converter
            try:
                sys.path.append(str(PROJECT_ROOT))
                from src.infrastructure.adapters.excel.csv_to_excel_converter import CsvToExcelConverter
            except ImportError:
                raise ImportError("無法導入 csv_to_excel_converter")
            
            # 暫存 DataFrame 為 CSV
            temp_csv = os.path.join(output_dir, f"temp_{base_name}.csv")
            data11_df.to_csv(temp_csv, index=False, header=False)
            
            print(f"[CHART] CTA → csv_to_excel_converter 整合...")
            
            # csv_to_excel_converter 處理
            excel_converter = CsvToExcelConverter()
            result = excel_converter.convert_csv_to_excel(temp_csv, output_file)
            
            # 清理暫存檔案
            if os.path.exists(temp_csv):
                os.remove(temp_csv)
            
            processing_type = "CTA整合流程"
            
        else:
            # 一般 CSV 流程
            print(f"[CHART] 執行一般 CSV 流程...")
            
            try:
                sys.path.append(str(PROJECT_ROOT))
                from src.infrastructure.adapters.excel.csv_to_excel_converter import CsvToExcelConverter
            except ImportError:
                raise ImportError("無法導入 csv_to_excel_converter")
            
            excel_converter = CsvToExcelConverter()
            result = excel_converter.convert_csv_to_excel(input_file, output_file)
            processing_type = "一般CSV流程"
        
        # 計算處理時間
        end_time = datetime.now()
        end_perf = time.perf_counter()
        processing_time = end_perf - start_perf
        
        # 檢查結果
        if result.success and os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            
            print(f"\n[OK] {processing_type} 處理成功!")
            print(f"[TIME] 處理時間: {processing_time:.3f} 秒")
            print(f"[CHART] 輸出檔案大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
            print(f"[FILE] 輸出位置: {output_file}")
            
            return {
                'success': True,
                'output_file': output_file,
                'processing_type': processing_type,
                'processing_time': processing_time,
                'file_size': file_size,
                'is_cta': is_cta
            }
        else:
            raise RuntimeError(f"處理失敗: {result.error_message if hasattr(result, 'error_message') else '未知錯誤'}")
            
    except Exception as e:
        end_time = datetime.now()
        end_perf = time.perf_counter()
        processing_time = end_perf - start_perf
        
        error_msg = str(e)
        print(f"\n[ERROR] 處理失敗: {error_msg}")
        print(f"[TIME] 處理時間: {processing_time:.3f} 秒")
        
        return {
            'success': False,
            'error_message': error_msg,
            'processing_time': processing_time
        }

def main():
    """
    主要執行函數 - 功能替換原則
    新功能：自動檢測檔案類型，整合處理入口
    舊功能：傳統 CTA 處理（保留向後相容）
    """
    
    # 檢查命令行參數
    if len(sys.argv) < 2 or len(sys.argv) > 3:
        print("[OPEN_BOOK] CTA 整合處理器使用方式:")
        print(f"python {sys.argv[0]} <CSV檔名> [--traditional]")
        print(f"")
        print(f"模式:")
        print(f"  預設模式：    自動檢測 → CTA整合 或 一般CSV → Summary Excel")
        print(f"  --traditional：傳統模式 → 僅 CTA 處理 → Excel (Data11 + sum)")
        print(f"")
        print(f"範例:")
        print(f"  # 整合模式 (推薦)")
        print(f"  python {sys.argv[0]} file.csv")
        print(f"  # 傳統模式")
        print(f"  python {sys.argv[0]} file.csv --traditional")
        
        # 顯示可用的檔案
        if DOC_DIR.exists():
            print(f"\n[FOLDER] 可用的 CSV 檔案:")
            csv_files = [f for f in DOC_DIR.iterdir() if f.suffix == '.csv']
            for csv_file in csv_files:
                print(f"  • {csv_file.name}")
        
        return
    
    csv_filename = sys.argv[1]
    traditional_mode = len(sys.argv) == 3 and sys.argv[2] == '--traditional'
    
    # 檢查輸入檔案路徑
    if not os.path.isabs(csv_filename):
        csv_filename = str(PROJECT_ROOT / csv_filename)
    
    if traditional_mode:
        # 傳統模式：僅 CTA 處理
        print(f"[TOOL] 傳統模式：CTA CSV → Excel (Data11 + sum)")
        success = process_cta_file(os.path.basename(csv_filename))
        
        if success:
            print(f"\n[PARTY] 傳統 CTA 處理完成！")
            print(f"[EDIT] 詳細記錄請查看: logs/datalog.txt")
            print(f"[BOARD] 輸出結構: Data11 + sum 工作表")
        else:
            print(f"\n[ERROR] 傳統 CTA 處理失敗")
    else:
        # 整合模式：自動檢測 + 完整處理
        print(f"[ROCKET] 整合模式：自動檢測 → 完整處理流程")
        result = integrated_process_file(csv_filename)
        
        if result['success']:
            print(f"\n[PARTY] 整合處理完成!")
            print(f"[BOARD] 檔案類型: {'CTA CSV' if result.get('is_cta') else '一般 CSV'}")
            print(f"[FILE] 輸出檔案: {result['output_file']}")
            print(f"[EDIT] 包含完整 Summary 工作表")
        else:
            print(f"\n[ERROR] 整合處理失敗: {result['error_message']}")
            sys.exit(1)

if __name__ == "__main__":
    main()