/**
 * 郵件詳情頁面模組
 * 負責郵件詳情頁面的功能和業務操作
 */

/**
 * 郵件詳情頁面類
 */
class EmailDetail {
    constructor(emailData) {
        this.email = emailData;
        this.elements = {};
    }
    
    /**
     * 初始化郵件詳情頁面
     */
    initialize() {
        this.initializeElements();
        this.setupEventListeners();
        this.renderEmailDetail();
    }
    
    /**
     * 初始化 DOM 元素
     */
    initializeElements() {
        this.elements = {
            // 基本郵件操作
            markReadBtn: document.getElementById('mark-read-btn'),
            deleteBtn: document.getElementById('delete-btn'),
            processBtn: document.getElementById('process-btn'),
            
            // 業務流程操作
            startEqcBtn: document.getElementById('start-eqc-process'),
            generateReportBtn: document.getElementById('generate-report'),
            codeAnalysisBtn: document.getElementById('code-analysis'),
            
            // 系統整合
            openGtkBtn: document.getElementById('open-gtk'),
            ftSummaryBtn: document.getElementById('ft-summary'),
            oneClickBtn: document.getElementById('one-click-complete'),
            
            // 郵件詳情顯示元素
            emailSubject: document.getElementById('email-subject'),
            emailSender: document.getElementById('email-sender'),
            emailTime: document.getElementById('email-time'),
            emailBody: document.getElementById('email-body'),
            emailAttachments: document.getElementById('email-attachments'),
            emailStatus: document.getElementById('email-status')
        };
    }
    
    /**
     * 設置事件監聽器
     */
    setupEventListeners() {
        // 基本郵件操作
        this.elements.markReadBtn?.addEventListener('click', () => this.markAsRead());
        this.elements.deleteBtn?.addEventListener('click', () => this.deleteEmail());
        this.elements.processBtn?.addEventListener('click', () => this.processEmail());
        
        // 業務流程操作
        this.elements.startEqcBtn?.addEventListener('click', () => this.startEqcProcess());
        this.elements.generateReportBtn?.addEventListener('click', () => this.generateReport());
        this.elements.codeAnalysisBtn?.addEventListener('click', () => this.codeAnalysis());
        
        // 系統整合
        this.elements.openGtkBtn?.addEventListener('click', () => this.openGtkInterface());
        this.elements.ftSummaryBtn?.addEventListener('click', () => this.openFtSummary());
        this.elements.oneClickBtn?.addEventListener('click', () => this.oneClickComplete());
    }
    
    /**
     * 渲染郵件詳情
     */
    renderEmailDetail() {
        if (!this.email) return;
        
        // 更新郵件基本資訊
        if (this.elements.emailSubject) {
            this.elements.emailSubject.textContent = this.email.subject || '(無主題)';
        }
        
        if (this.elements.emailSender) {
            this.elements.emailSender.textContent = this.email.sender || '(未知寄件者)';
        }
        
        if (this.elements.emailTime) {
            const time = this.email.received_time 
                ? new Date(this.email.received_time).toLocaleString('zh-TW')
                : '(未知時間)';
            this.elements.emailTime.textContent = time;
        }
        
        if (this.elements.emailBody) {
            this.elements.emailBody.textContent = this.email.body || '(無內容)';
        }
        
        // 渲染附件
        this.renderAttachments();
        
        // 更新狀態
        this.updateEmailStatus();
    }
    
    /**
     * 渲染附件
     */
    renderAttachments() {
        if (!this.elements.emailAttachments) return;
        
        if (this.email.attachments && this.email.attachments.length > 0) {
            // 使用附件管理器渲染附件
            window.emailAttachments.renderAttachmentsList(
                this.email.attachments, 
                this.elements.emailAttachments.id
            );
        } else {
            this.elements.emailAttachments.innerHTML = '<p>無附件</p>';
        }
    }
    
    /**
     * 更新郵件狀態顯示
     */
    updateEmailStatus() {
        if (!this.elements.emailStatus) return;
        
        const statusMap = {
            'pending': '待處理',
            'processing': '處理中',
            'completed': '已完成',
            'failed': '處理失敗'
        };
        
        const statusText = statusMap[this.email.status] || '未知狀態';
        const statusClass = this.email.status || 'unknown';
        
        this.elements.emailStatus.innerHTML = `
            <span class="status-indicator ${statusClass}">${statusText}</span>
        `;
    }
    
    /**
     * 標記為已讀
     */
    async markAsRead() {
        if (!this.email.id) return;
        
        try {
            const response = await fetch(`/api/emails/${this.email.id}/read`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ is_read: true })
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showSuccessMessage('郵件已標記為已讀');
                this.email.is_read = true;
                this.updateReadStatus();
            } else {
                EmailUIUtils.showErrorMessage('標記已讀失敗: ' + result.message);
            }
            
        } catch (error) {
            console.error('標記已讀失敗:', error);
            EmailUIUtils.showErrorMessage('標記已讀失敗');
        }
    }
    
    /**
     * 刪除郵件
     */
    async deleteEmail() {
        if (!this.email.id) return;
        
        const confirmed = await EmailUIUtils.showConfirmDialog(
            {},
            '確認刪除',
            '您確定要刪除這封郵件嗎？此操作無法撤銷。'
        );
        
        if (confirmed) {
            try {
                const response = await fetch(`/api/emails/${this.email.id}`, {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    EmailUIUtils.showSuccessMessage('郵件已刪除');
                    // 返回郵件列表
                    this.goBackToList();
                } else {
                    EmailUIUtils.showErrorMessage('刪除失敗: ' + result.message);
                }
                
            } catch (error) {
                console.error('刪除郵件失敗:', error);
                EmailUIUtils.showErrorMessage('刪除郵件失敗');
            }
        }
    }
    
    /**
     * 處理郵件
     */
    async processEmail() {
        if (!this.email.id) return;
        
        try {
            const response = await fetch(`/api/emails/${this.email.id}/process`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showSuccessMessage('已啟動郵件處理流程');
                this.email.status = 'processing';
                this.updateEmailStatus();
            } else {
                EmailUIUtils.showErrorMessage('處理失敗: ' + result.message);
            }
            
        } catch (error) {
            console.error('處理郵件失敗:', error);
            EmailUIUtils.showErrorMessage('處理郵件失敗');
        }
    }
    
    /**
     * 啟動 EQC 流程
     */
    async startEqcProcess() {
        if (!this.email.id) return;
        
        try {
            EmailUIUtils.showLoadingMessage('正在啟動 EQC 流程...');
            
            const response = await fetch(`/api/emails/${this.email.id}/eqc-process`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showSuccessMessage('EQC 流程已啟動');
                this.email.eqc_status = 'started';
                this.updateEmailStatus();
            } else {
                EmailUIUtils.showErrorMessage('啟動 EQC 流程失敗: ' + result.message);
            }
            
        } catch (error) {
            console.error('啟動 EQC 流程失敗:', error);
            EmailUIUtils.showErrorMessage('啟動 EQC 流程失敗');
        }
    }
    
    /**
     * 生成報告
     */
    async generateReport() {
        if (!this.email.id) return;
        
        try {
            EmailUIUtils.showLoadingMessage('正在生成報告...');
            
            const response = await fetch(`/api/emails/${this.email.id}/generate-report`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showSuccessMessage('報告生成成功');
                if (result.data.report_url) {
                    // 如果有報告 URL，可以提供下載連結
                    this.showReportDownloadLink(result.data.report_url);
                }
            } else {
                EmailUIUtils.showErrorMessage('生成報告失敗: ' + result.message);
            }
            
        } catch (error) {
            console.error('生成報告失敗:', error);
            EmailUIUtils.showErrorMessage('生成報告失敗');
        }
    }
    
    /**
     * 代碼分析
     */
    async codeAnalysis() {
        if (!this.email.id) return;
        
        try {
            EmailUIUtils.showLoadingMessage('正在進行代碼分析...');
            
            const response = await fetch(`/api/emails/${this.email.id}/code-analysis`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showSuccessMessage('代碼分析完成');
                this.showAnalysisResults(result.data);
            } else {
                EmailUIUtils.showErrorMessage('代碼分析失敗: ' + result.message);
            }
            
        } catch (error) {
            console.error('代碼分析失敗:', error);
            EmailUIUtils.showErrorMessage('代碼分析失敗');
        }
    }
    
    /**
     * 開啟 GTK 介面
     */
    async openGtkInterface() {
        try {
            const response = await fetch(`/api/gtk/open/${this.email.id}`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                EmailUIUtils.showSuccessMessage('GTK 介面已開啟');
            } else {
                EmailUIUtils.showErrorMessage('開啟 GTK 介面失敗: ' + result.message);
            }
            
        } catch (error) {
            console.error('開啟 GTK 介面失敗:', error);
            EmailUIUtils.showErrorMessage('開啟 GTK 介面失敗');
        }
    }
    
    /**
     * 開啟 FT Summary
     */
    async openFtSummary() {
        try {
            const response = await fetch(`/api/ft-summary/${this.email.id}`, {
                method: 'GET'
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 在新視窗或頁面中顯示 FT Summary
                window.open(`/ft-summary/${this.email.id}`, '_blank');
            } else {
                EmailUIUtils.showErrorMessage('開啟 FT Summary 失敗: ' + result.message);
            }
            
        } catch (error) {
            console.error('開啟 FT Summary 失敗:', error);
            EmailUIUtils.showErrorMessage('開啟 FT Summary 失敗');
        }
    }
    
    /**
     * 一鍵完成
     */
    async oneClickComplete() {
        if (!this.email.id) return;
        
        const confirmed = await EmailUIUtils.showConfirmDialog(
            {},
            '確認執行',
            '您確定要執行一鍵完成流程嗎？此操作將自動處理所有相關業務流程。'
        );
        
        if (confirmed) {
            try {
                EmailUIUtils.showLoadingMessage('正在執行一鍵完成流程...');
                
                const response = await fetch(`/api/emails/${this.email.id}/one-click-complete`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    EmailUIUtils.showSuccessMessage('一鍵完成流程已啟動');
                    this.email.status = 'processing';
                    this.updateEmailStatus();
                    
                    // 如果有進度追蹤，可以顯示進度
                    if (result.data.progress_id) {
                        this.trackProgress(result.data.progress_id);
                    }
                } else {
                    EmailUIUtils.showErrorMessage('一鍵完成失敗: ' + result.message);
                }
                
            } catch (error) {
                console.error('一鍵完成失敗:', error);
                EmailUIUtils.showErrorMessage('一鍵完成失敗');
            }
        }
    }
    
    /**
     * 更新已讀狀態顯示
     */
    updateReadStatus() {
        const readIndicator = document.querySelector('.read-indicator');
        if (readIndicator) {
            readIndicator.classList.toggle('read', this.email.is_read);
            readIndicator.classList.toggle('unread', !this.email.is_read);
        }
    }
    
    /**
     * 顯示報告下載連結
     */
    showReportDownloadLink(reportUrl) {
        const downloadLink = document.createElement('a');
        downloadLink.href = reportUrl;
        downloadLink.download = `report_${this.email.id}.pdf`;
        downloadLink.textContent = '下載報告';
        downloadLink.className = 'btn btn-primary';
        
        const container = document.getElementById('report-actions');
        if (container) {
            container.appendChild(downloadLink);
        }
    }
    
    /**
     * 顯示分析結果
     */
    showAnalysisResults(data) {
        const resultsContainer = document.getElementById('analysis-results');
        if (resultsContainer && data) {
            resultsContainer.innerHTML = `
                <div class="analysis-summary">
                    <h4>分析結果</h4>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
        }
    }
    
    /**
     * 追蹤進度
     */
    async trackProgress(progressId) {
        const checkProgress = async () => {
            try {
                const response = await fetch(`/api/progress/${progressId}`);
                const result = await response.json();
                
                if (result.success) {
                    const progress = result.data;
                    
                    if (progress.status === 'completed') {
                        EmailUIUtils.showSuccessMessage('處理完成');
                        this.email.status = 'completed';
                        this.updateEmailStatus();
                    } else if (progress.status === 'failed') {
                        EmailUIUtils.showErrorMessage('處理失敗: ' + progress.error);
                        this.email.status = 'failed';
                        this.updateEmailStatus();
                    } else {
                        // 繼續追蹤
                        setTimeout(checkProgress, 2000);
                    }
                }
                
            } catch (error) {
                console.error('追蹤進度失敗:', error);
            }
        };
        
        checkProgress();
    }
    
    /**
     * 返回郵件列表
     */
    goBackToList() {
        // 根據實際的路由結構調整
        if (window.history && window.history.back) {
            window.history.back();
        } else {
            window.location.href = '/';
        }
    }
    
    /**
     * 清理資源
     */
    cleanup() {
        // 清理事件監聽器和其他資源
        this.elements = {};
        this.email = null;
    }
}

// 全域可用
window.EmailDetail = EmailDetail;