"""
郵件同步服務
整合 POP3 郵件讀取器和資料庫存儲
"""

import asyncio
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import threading
import time
import os
import tempfile
from pathlib import Path

from src.infrastructure.adapters.email_reader_factory import EmailReaderFactory, EmailReaderType
from src.infrastructure.adapters.database.email_database import EmailDatabase
from src.infrastructure.logging.logger_manager import LoggerManager
from src.data_models.email_models import EmailData
from src.infrastructure.adapters.email_inbox.sync_attachment_handler import SyncAttachmentHandler
from src.infrastructure.parsers.base_parser import ParserFactory, ParserRegistry


class EmailSyncService:
    """
    郵件同步服務
    負責從 POP3 服務器同步郵件到本地資料庫
    """
    
    def __init__(self, database: EmailDatabase = None):
        """
        初始化郵件同步服務
        
        Args:
            database: 郵件資料庫實例
        """
        self.logger = LoggerManager().get_logger("EmailSyncService")
        self.database = database or EmailDatabase()
        self.email_reader = None
        
        # 初始化附件處理器和解析服務
        self.attachment_handler = SyncAttachmentHandler(self.database)
        self.parser_factory = ParserFactory()
        self._register_parsers()
        
        # 同步狀態
        self.is_syncing = False
        self.last_sync_time = None
        self.sync_stats = {
            'total_synced': 0,
            'last_sync_count': 0,
            'sync_errors': 0,
            'last_error': None,
            'last_error_details': []
        }
        
        # 自動同步設定
        self.auto_sync_enabled = False
        self.auto_sync_interval = 60  # 1分鐘
        self.auto_sync_thread = None
        
        self.logger.info("郵件同步服務已初始化")
        
    def _register_parsers(self):
        """註冊所有解析器"""
        try:
            from src.infrastructure.parsers.gtk_parser import GTKParser
            from src.infrastructure.parsers.etd_parser import ETDParser
            from src.infrastructure.parsers.jcet_parser import JCETParser
            from src.infrastructure.parsers.lingsen_parser import LINGSENParser
            from src.infrastructure.parsers.xaht_parser import XAHTParser
            
            registry = ParserRegistry()
            
            # 檢查並註冊解析器（支援安全重複註冊）
            parsers_to_register = [
                ("GTK", GTKParser()),
                ("ETD", ETDParser()),
                ("JCET", JCETParser()),
                ("LINGSEN", LINGSENParser()),
                ("XAHT", XAHTParser())
            ]
            
            registered_count = 0
            for vendor_code, parser in parsers_to_register:
                if not registry.is_registered(vendor_code):
                    registry.register_parser(vendor_code, parser)
                    registered_count += 1
                    self.logger.debug(f"已註冊 {vendor_code} 解析器")
                else:
                    self.logger.debug(f"{vendor_code} 解析器已存在，跳過註冊")
            
            if registered_count > 0:
                self.logger.info(f"已註冊 {registered_count} 個新解析器")
            else:
                self.logger.debug("所有解析器都已註冊，無需重複註冊")
                
        except Exception as e:
            self.logger.error(f"註冊解析器失敗: {e}")
    
    async def initialize_email_reader(self) -> bool:
        """
        初始化郵件讀取器
        
        Returns:
            初始化成功與否
        """
        try:
            if not self.email_reader:
                self.email_reader = EmailReaderFactory.create_from_env_config(EmailReaderType.POP3)
                self.logger.info("郵件讀取器已初始化")
            
            return True
            
        except Exception as e:
            self.logger.error(f"初始化郵件讀取器失敗: {e}")
            return False
    
    async def sync_emails_once(self, max_emails: int = 100) -> Dict[str, Any]:
        """
        執行一次郵件同步
        
        Args:
            max_emails: 最大同步郵件數量
            
        Returns:
            同步結果
        """
        if self.is_syncing:
            return {
                'success': False,
                'message': '正在同步中，請稍後再試',
                'data': {
                    'sync_status': 'already_running'
                }
            }
        
        self.is_syncing = True
        sync_start_time = datetime.now()
        
        try:
            self.logger.info(f"開始同步郵件 (最多 {max_emails} 封)")
            
            # 初始化郵件讀取器
            if not await self.initialize_email_reader():
                raise Exception("無法初始化郵件讀取器")
            
            # 連接到 POP3 服務器
            if not await self.email_reader.connect():
                raise Exception("無法連接到 POP3 服務器")
            
            try:
                # 讀取郵件
                emails = await self.email_reader.read_emails(count=max_emails)
                
                if not emails:
                    self.logger.info("沒有新郵件可同步")
                    return {
                        'success': True,
                        'message': '沒有新郵件',
                        'data': {
                            'sync_count': 0,
                            'sync_time': sync_start_time.isoformat(),
                            'sync_duration': (datetime.now() - sync_start_time).total_seconds()
                        }
                    }
                
                # 儲存郵件到資料庫
                sync_count = 0
                sync_errors = 0
                error_details = []
                
                for i, email in enumerate(emails):
                    try:
                        email_id = self.database.save_email(email)
                        if email_id:
                            sync_count += 1
                            self.logger.debug(f"郵件已同步: {email.subject} (ID: {email_id})")
                            
                            # 處理附件
                            if email.attachments:
                                try:
                                    attachment_result = await self.attachment_handler.process_email_attachments(
                                        email_id, email
                                    )
                                    if not attachment_result['success']:
                                        self.logger.warning(
                                            f"郵件 {email_id} 的附件處理部分失敗: "
                                            f"{attachment_result['failed_count']} 個失敗"
                                        )
                                except Exception as e:
                                    self.logger.error(f"處理附件時發生錯誤: {e}")
                                    
                            # 解析郵件
                            try:
                                vendor_result, parsing_result = self.parser_factory.parse_email(email)
                                
                                # 轉換結果格式
                                parse_result = {
                                    'success': parsing_result.is_success,
                                    'vendor': vendor_result.vendor_code,
                                    'pd': parsing_result.extracted_data.get('product_name'),
                                    'lot': parsing_result.lot_number,
                                    'yield': parsing_result.extracted_data.get('yield_value'),
                                    'error': parsing_result.error_message
                                }
                                
                                if parse_result['success']:
                                    # 更新資料庫中的解析結果
                                    self._update_email_parse_result(email_id, parse_result)
                                    self.logger.debug(
                                        f"郵件已解析: {email_id} - "
                                        f"廠商: {parse_result.get('vendor')}, "
                                        f"LOT: {parse_result.get('lot')}"
                                    )
                                    
                                    # 處理廠商檔案（新增）
                                    self._process_vendor_files(email_id, parse_result)
                                else:
                                    self.logger.debug(f"郵件解析失敗: {parse_result.get('error')}")
                            except Exception as e:
                                self.logger.error(f"解析郵件時發生錯誤: {e}")
                                
                        else:
                            sync_errors += 1
                            error_msg = f"郵件同步失敗 ({i+1}/{len(emails)}): subject='{email.subject[:50]}...', sender='{email.sender}'"
                            self.logger.warning(error_msg)
                            error_details.append({
                                'index': i+1,
                                'subject': email.subject[:100],
                                'sender': email.sender,
                                'message_id': email.message_id,
                                'error': 'save_email returned None'
                            })
                    except Exception as e:
                        sync_errors += 1
                        error_msg = f"儲存郵件失敗 ({i+1}/{len(emails)}): subject='{email.subject[:50]}...', sender='{email.sender}', error={e}"
                        self.logger.error(error_msg)
                        error_details.append({
                            'index': i+1,
                            'subject': email.subject[:100] if hasattr(email, 'subject') else 'Unknown',
                            'sender': email.sender if hasattr(email, 'sender') else 'Unknown',
                            'message_id': email.message_id if hasattr(email, 'message_id') else 'Unknown',
                            'error': str(e)
                        })
                
                # 更新統計
                self.sync_stats['total_synced'] += sync_count
                self.sync_stats['last_sync_count'] = sync_count
                self.sync_stats['sync_errors'] += sync_errors
                self.sync_stats['last_error_details'] = error_details
                self.last_sync_time = datetime.now()
                
                # 記錄詳細的同步結果
                if sync_errors > 0:
                    self.logger.warning(f"郵件同步完成: {sync_count} 封成功, {sync_errors} 封失敗")
                    self.logger.warning(f"失敗郵件詳情: {error_details}")
                else:
                    self.logger.info(f"郵件同步完成: {sync_count} 封成功, {sync_errors} 封失敗")
                
                return {
                    'success': True,
                    'message': f'成功同步 {sync_count} 封郵件' + (f', {sync_errors} 封失敗' if sync_errors > 0 else ''),
                    'data': {
                        'sync_count': sync_count,
                        'sync_errors': sync_errors,
                        'error_details': error_details if sync_errors > 0 else [],
                        'sync_time': sync_start_time.isoformat(),
                        'sync_duration': (datetime.now() - sync_start_time).total_seconds()
                    }
                }
                
            finally:
                # 斷開連接
                await self.email_reader.disconnect()
                
        except Exception as e:
            self.logger.error(f"郵件同步失敗: {e}")
            self.sync_stats['sync_errors'] += 1
            self.sync_stats['last_error'] = str(e)
            
            return {
                'success': False,
                'message': f'郵件同步失敗: {str(e)}',
                'data': {
                    'error': str(e),
                    'sync_time': sync_start_time.isoformat(),
                    'sync_duration': (datetime.now() - sync_start_time).total_seconds()
                }
            }
            
        finally:
            self.is_syncing = False
    
    def sync_emails_background(self, max_emails: int = 100) -> Dict[str, Any]:
        """
        在背景執行郵件同步
        
        Args:
            max_emails: 最大同步郵件數量
            
        Returns:
            同步請求結果
        """
        if self.is_syncing:
            return {
                'success': False,
                'message': '正在同步中，請稍後再試',
                'data': {
                    'sync_status': 'already_running'
                }
            }
        
        self.logger.info(f"開始背景同步，最大郵件數: {max_emails}")
        
        # 在背景執行同步
        def run_sync():
            try:
                self.logger.info("背景同步線程已啟動")
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                result = loop.run_until_complete(self.sync_emails_once(max_emails))
                loop.close()
                
                if result['success']:
                    self.logger.info(f"背景同步完成: 成功同步 {result['data']['sync_count']} 封郵件")
                else:
                    self.logger.warning(f"背景同步失敗: {result['message']}")
                
            except Exception as e:
                self.logger.error(f"背景同步失敗: {e}")
        
        sync_thread = threading.Thread(target=run_sync, daemon=True)
        sync_thread.start()
        
        self.logger.info("背景同步線程已啟動")
        
        return {
            'success': True,
            'message': '郵件同步已在背景啟動',
            'data': {
                'sync_status': 'started',
                'sync_time': datetime.now().isoformat()
            }
        }
    
    def start_auto_sync(self, interval_seconds: int = 60) -> Dict[str, Any]:
        """
        啟動自動同步
        
        Args:
            interval_seconds: 同步間隔（秒）
            
        Returns:
            啟動結果
        """
        if self.auto_sync_enabled:
            return {
                'success': False,
                'message': '自動同步已啟動',
                'data': {
                    'auto_sync_enabled': True,
                    'interval_seconds': self.auto_sync_interval
                }
            }
        
        self.auto_sync_enabled = True
        self.auto_sync_interval = interval_seconds
        
        def auto_sync_loop():
            """自動同步循環"""
            try:
                # 首次立即執行同步
                if not self.is_syncing:
                    self.logger.info("開始首次自動同步...")
                    
                    # 使用同步方式執行異步函數
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(self.sync_emails_once(50))
                    loop.close()
                    
                    if result['success']:
                        self.logger.info(f"首次自動同步完成: {result['data']['sync_count']} 封郵件")
                    else:
                        self.logger.warning(f"首次自動同步失敗: {result['message']}")
                else:
                    self.logger.info("跳過首次同步（已有同步進行中）")
                    
            except Exception as e:
                self.logger.error(f"首次自動同步錯誤: {e}")
            
            # 定期同步循環
            while self.auto_sync_enabled:
                try:
                    # 等待下次同步
                    self.logger.info(f"等待 {self.auto_sync_interval} 秒後進行下次同步...")
                    time.sleep(self.auto_sync_interval)
                    
                    if not self.auto_sync_enabled:
                        break
                    
                    if not self.is_syncing:
                        self.logger.info("執行定期自動同步...")
                        
                        # 使用同步方式執行異步函數
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        result = loop.run_until_complete(self.sync_emails_once(50))
                        loop.close()
                        
                        if result['success']:
                            self.logger.info(f"定期自動同步完成: {result['data']['sync_count']} 封郵件")
                        else:
                            self.logger.warning(f"定期自動同步失敗: {result['message']}")
                    else:
                        self.logger.info("跳過定期同步（已有同步進行中）")
                    
                except Exception as e:
                    self.logger.error(f"定期自動同步循環錯誤: {e}")
                    time.sleep(60)  # 錯誤後等待較短時間
        
        self.auto_sync_thread = threading.Thread(target=auto_sync_loop, daemon=True)
        self.auto_sync_thread.start()
        
        self.logger.info(f"自動同步已啟動，間隔 {interval_seconds} 秒（首次立即執行）")
        
        return {
            'success': True,
            'message': f'自動同步已啟動，間隔 {interval_seconds} 秒（首次立即執行）',
            'data': {
                'auto_sync_enabled': True,
                'interval_seconds': interval_seconds
            }
        }
    
    def stop_auto_sync(self) -> Dict[str, Any]:
        """
        停止自動同步
        
        Returns:
            停止結果
        """
        if not self.auto_sync_enabled:
            self.logger.info("自動同步未啟動，無需停止")
            return {
                'success': False,
                'message': '自動同步未啟動',
                'data': {
                    'auto_sync_enabled': False
                }
            }
        
        self.logger.info("正在停止自動同步...")
        self.auto_sync_enabled = False
        
        # 等待線程結束
        if self.auto_sync_thread and self.auto_sync_thread.is_alive():
            self.logger.info("等待自動同步線程結束...")
            self.auto_sync_thread.join(timeout=5)
            if self.auto_sync_thread.is_alive():
                self.logger.warning("自動同步線程未能在時限內結束")
            else:
                self.logger.info("自動同步線程已成功結束")
        
        self.logger.info("自動同步已停止")
        
        return {
            'success': True,
            'message': '自動同步已停止',
            'data': {
                'auto_sync_enabled': False
            }
        }
    
    def get_sync_status(self) -> Dict[str, Any]:
        """
        取得同步狀態
        
        Returns:
            同步狀態資訊
        """
        return {
            'is_syncing': self.is_syncing,
            'auto_sync_enabled': self.auto_sync_enabled,
            'auto_sync_interval': self.auto_sync_interval,
            'last_sync_time': self.last_sync_time.isoformat() if self.last_sync_time else None,
            'sync_stats': self.sync_stats.copy()
        }
    
    def get_connection_status(self) -> Dict[str, Any]:
        """
        取得連接狀態
        
        Returns:
            連接狀態資訊
        """
        try:
            if self.email_reader:
                stats = self.email_reader.get_statistics()
                return {
                    'connected': self.email_reader.is_connected(),
                    'connection_info': stats.get('connection_info', {}),
                    'reader_stats': stats
                }
            else:
                return {
                    'connected': False,
                    'connection_info': {},
                    'reader_stats': {}
                }
                
        except Exception as e:
            self.logger.error(f"取得連接狀態失敗: {e}")
            return {
                'connected': False,
                'error': str(e)
            }
    
    async def test_connection(self) -> Dict[str, Any]:
        """
        測試郵件伺服器連接
        
        Returns:
            測試結果
        """
        try:
            if not await self.initialize_email_reader():
                raise Exception("無法初始化郵件讀取器")
            
            if await self.email_reader.connect():
                try:
                    unread_count = self.email_reader.get_unread_count()
                    stats = self.email_reader.get_statistics()
                    
                    return {
                        'success': True,
                        'message': '連接測試成功',
                        'data': {
                            'connected': True,
                            'unread_count': unread_count,
                            'server_info': stats.get('connection_info', {})
                        }
                    }
                finally:
                    await self.email_reader.disconnect()
            else:
                return {
                    'success': False,
                    'message': '無法連接到郵件伺服器',
                    'data': {
                        'connected': False
                    }
                }
                
        except Exception as e:
            self.logger.error(f"連接測試失敗: {e}")
            return {
                'success': False,
                'message': f'連接測試失敗: {str(e)}',
                'data': {
                    'connected': False,
                    'error': str(e)
                }
            }
    
    def cleanup(self):
        """清理資源"""
        try:
            # 停止自動同步
            if self.auto_sync_enabled:
                self.stop_auto_sync()
            
            # 清理郵件讀取器
            if self.email_reader:
                if hasattr(self.email_reader, 'disconnect'):
                    asyncio.run(self.email_reader.disconnect())
                self.email_reader = None
            
            self.logger.info("郵件同步服務已清理")
            
        except Exception as e:
            self.logger.error(f"清理郵件同步服務失敗: {e}")
    
    def __del__(self):
        """析構函數"""
        self.cleanup()
        
    def _update_email_parse_result(self, email_id: int, parse_result: Dict[str, Any]):
        """
        更新郵件的解析結果
        
        Args:
            email_id: 郵件 ID
            parse_result: 解析結果
        """
        try:
            from src.infrastructure.adapters.database.models import EmailDB
            
            with self.database.get_session() as session:
                email = session.query(EmailDB).filter_by(id=email_id).first()
                if email:
                    if parse_result['success']:
                        email.vendor_code = parse_result.get('vendor')
                        email.pd = parse_result.get('pd')
                        email.lot = parse_result.get('lot')
                        email.yield_value = parse_result.get('yield')
                        email.parsed_at = datetime.now()
                        email.parse_status = 'parsed'
                        email.parse_error = None
                    else:
                        email.parse_status = 'failed'
                        email.parse_error = parse_result.get('error')
                        
                    session.commit()
                    
        except Exception as e:
            self.logger.error(f"更新解析結果失敗: {e}")
            
    def _process_vendor_files(self, email_id: int, parse_result: Dict[str, Any]):
        """
        處理廠商相關檔案（新增功能）
        在郵件解析成功後，根據廠商複製相關檔案
        
        Args:
            email_id: 郵件 ID
            parse_result: 解析結果
        """
        try:
            # 檢查是否有必要資訊
            vendor = parse_result.get('vendor')
            mo = parse_result.get('mo') or parse_result.get('mo_number')
            lot = parse_result.get('lot') or parse_result.get('lot_number')
            pd = parse_result.get('pd') or parse_result.get('product_name', 'default')
            
            if not vendor:
                self.logger.debug("無廠商資訊，跳過檔案處理")
                return
                
            if not mo and not lot:
                self.logger.debug("無 MO 或 LOT 資訊，跳過檔案處理")
                return
                
            # 導入檔案處理器工廠
            from src.infrastructure.adapters.file_handlers import FileHandlerFactory
            
            # 建立工廠（使用環境變數或預設路徑）
            source_base_path = os.getenv('FILE_SOURCE_BASE_PATH', '/mnt/share')
            factory = FileHandlerFactory(source_base_path)
            
            # 建立暫存目錄
            temp_base_path = os.getenv('FILE_TEMP_BASE_PATH', tempfile.gettempdir())
            temp_dir = Path(temp_base_path) / f"email_{email_id}" / vendor
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            # 執行檔案處理
            result = factory.process_vendor_files(
                vendor_code=vendor,
                mo=mo or lot,  # 如果沒有 MO 則使用 LOT
                temp_path=str(temp_dir),
                pd=pd,
                lot=lot or mo  # 如果沒有 LOT 則使用 MO
            )
            
            if result['success']:
                self.logger.info(
                    f"成功處理 {vendor} 檔案，郵件 ID: {email_id}, "
                    f"暫存路徑: {temp_dir}"
                )
                
                # 更新資料庫記錄檔案路徑
                self._update_vendor_files_status(email_id, str(temp_dir), True)
            else:
                self.logger.warning(
                    f"處理 {vendor} 檔案失敗: {result.get('error')}"
                )
                self._update_vendor_files_status(email_id, None, False)
                
        except Exception as e:
            self.logger.error(f"處理廠商檔案時發生錯誤: {e}")
            
    def _update_vendor_files_status(self, email_id: int, file_path: Optional[str], success: bool):
        """
        更新郵件的廠商檔案處理狀態
        
        Args:
            email_id: 郵件 ID
            file_path: 檔案路徑
            success: 是否成功
        """
        try:
            from src.infrastructure.adapters.database.models import EmailDB
            
            with self.database.get_session() as session:
                email = session.query(EmailDB).filter_by(id=email_id).first()
                if email:
                    # 這裡可以添加新的欄位來記錄檔案處理狀態
                    # 暫時使用 raw_data 欄位
                    if not email.raw_data:
                        email.raw_data = {}
                        
                    email.raw_data['vendor_files_copied'] = success
                    if file_path:
                        email.raw_data['vendor_files_path'] = file_path
                    email.raw_data['files_copied_at'] = datetime.now().isoformat()
                    
                    session.commit()
                    
        except Exception as e:
            self.logger.error(f"更新廠商檔案狀態失敗: {e}")