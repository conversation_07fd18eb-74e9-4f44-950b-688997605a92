2025-07-07 18:01:28.750 | INFO     | src.presentation.api.ft_eqc_api:<module>:59 - ✅ FT Summary 處理器載入成功
2025-07-07 18:01:28.758 | INFO     | src.presentation.api.ft_eqc_api:<module>:90 - ✅ 靜態檔案服務已啟用: /mnt/d/download/project/outlook_summary/src/presentation/web/static
INFO:     Started server process [34434]
INFO:     Waiting for application startup.
2025-07-07 18:01:28.778 | INFO     | src.presentation.api.ft_eqc_api:startup_event:920 - 🚀 FT-EQC API 服務啟動中...
2025-07-07 18:01:28.779 | INFO     | src.presentation.api.ft_eqc_api:startup_event:921 - ✅ 模組化架構已載入
2025-07-07 18:01:28.779 | INFO     | src.presentation.api.ft_eqc_api:startup_event:922 - ✅ FastAPI 依賴注入機制已啟用
2025-07-07 18:01:28.780 | INFO     | src.presentation.api.ft_eqc_api:startup_event:928 - ✅ 檔案清理服務初始化已啟動
2025-07-07 18:01:28.780 | INFO     | src.presentation.api.ft_eqc_api:startup_event:932 - ✅ 所有服務模組已初始化
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8010 (Press CTRL+C to quit)
2025-07-07 18:01:28.905 | INFO     | src.presentation.api.services.cleanup_service:set_cleanup_scheduler:25 - 🔧 清理調度器已設定
2025-07-07 18:01:28.905 | INFO     | src.presentation.api.ft_eqc_api:initialize_cleanup_service_async:942 - ✅ 清理服務非同步初始化完成
2025-07-07 18:11:56.158 | INFO     | src.presentation.api.services.api_utils:log_api_start:536 - 🚀 API 端點開始: process_online_eqc
2025-07-07 18:11:56.159 | DEBUG    | src.presentation.api.services.api_utils:log_api_start:538 -    📋 請求數據: {'folder_path': 'D:\\download\\project\\outlook_summary\\doc\\20250523', 'processing_mode': '1'}
2025-07-07 18:11:56.160 | INFO     | src.presentation.api.services.api_utils:process_folder_path:65 - 🔄 路徑轉換: D:\download\project\outlook_summary\doc\20250523 -> /mnt/d/download/project/outlook_summary/doc/20250523
2025-07-07 18:11:56.161 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:147 - 🔍 [PATH_DEBUG] process_online_eqc 開始
2025-07-07 18:11:56.161 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:148 - 🔍 [PATH_DEBUG] original_path: D:\download\project\outlook_summary\doc\20250523
2025-07-07 18:11:56.162 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:149 - 🔍 [PATH_DEBUG] folder_path: /mnt/d/download/project/outlook_summary/doc/20250523
2025-07-07 18:11:56.162 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:150 - 🔍 [PATH_DEBUG] processing_mode: 1
2025-07-07 18:11:56.167 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:161 - 🔧 步驟0: 處理中文路徑與特殊符號
2025-07-07 18:11:56.271 | INFO     | src.presentation.api.services.eqc_processing_service:_process_chinese_paths:613 - ✅ 路徑標準化完成
2025-07-07 18:11:56.272 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:164 -    ✅ 路徑標準化完成
2025-07-07 18:11:56.917 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:183 - 🔄 執行 EQC BIN1 整合統計處理...
🔧 中文路徑處理器初始化
📋 共找到 0 個需要處理的資料夾
🔍 [eqc_processing_service.py] 調用 EQCBin1FinalProcessorV2
🔍 [eqc_bin1_final_processor.py] process_complete_eqc_integration() - 開始執行
⚠️ 程式碼對比組件導入失敗: attempted relative import with no known parent package
🔗 超連結處理器已初始化，本地路征: /mnt/d/download/project/outlook_summary/doc/20250523
🌐 網路共享路徑: \\\\192.168.1.60\\temp_7days
📋 詳細日誌記錄已啟用

🔥 開始 EQC BIN1 完整整合處理系統（超時限制: 120秒）
============================================================
🔧 步驟0: 處理中文路徑與特殊符號
🔧 中文路徑處理器初始化
📋 共找到 0 個需要處理的資料夾
   ✅ 路徑標準化完成

🗑️ 步驟0A: 特定副檔名檔案自動刪除
🗑️ 自動刪除副檔名: dlx, mdb
ℹ️ 未發現需要刪除的指定副檔名檔案

🔄 步驟0B: SPD檔案自動轉換
ℹ️ 未發現需要轉換的 SPD 檔案

🔧 步驟0C: CTA All-in-One 處理
🚀 開始處理目錄: /mnt/d/download/project/outlook_summary/doc/20250523

📦 步驟 1: 解壓縮檔案
🔍 掃描壓縮檔案: /mnt/d/download/project/outlook_summary/doc/20250523
📋 未發現需要解壓縮的檔案

🗑️ 步驟 1.5: 清理指定副檔名檔案
🗑️ 自動刪除副檔名: dlx, mdb
✅ 成功刪除 0 個指定副檔名的檔案

🔍 步驟 2: 掃描 CTA CSV 檔案
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
🔍 CTA 檢測結果: {'is_cta': False, 'sections': {}, 'has_data': False, 'has_qadata': False}
  📋 未發現 CTA CSV 檔案

🔧 步驟 3: 處理 0 個 CTA 檔案

📋 處理摘要:
  🗂️ 目錄: /mnt/d/download/project/outlook_summary/doc/20250523
  📦 解壓縮檔案: 0
  🗑️ 删除檔案: 0
  🔧 處理 CTA 檔案: 0
  📄 輸出 CSV 檔案: 0
  📁 歸檔檔案: 0
  ⏱️ 總處理時間: 1.25 秒
   ✅ CTA處理完成: 解壓縮0個檔案
   📄 生成Data11: 0個檔案
   📁 歸檔CTA: 0個檔案

📊 檔案掃描統計:
   ✅ 有效CSV檔案: 17 個
   🚫 排除檔案: 15 個
   📁 排除資料夾檔案: 6 個
🔄 開始執行檔案配對:
   📊 FT檔案數量: 7
   📊 EQC檔案數量: 10
   🎯 時間戳閾值: 3600

🔍 處理 FT檔案: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   🔍 FT檔案時間戳: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv → 20250522230014
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv → 20250523023632, 時間差: 793618
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv → 20250523024027, 時間差: 794013
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv → 20250522230033, 時間差: 19
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv → 20250522232554, 時間差: 2540
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522233905.csv → 20250522233905, 時間差: 3891
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522234808.csv → 20250522234808, 時間差: 4794
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522235619.csv → 20250522235619, 時間差: 5605
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523020811.csv → 20250523020811, 時間差: 790797
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv → 20250523021530, 時間差: 791516
      📊 EQC檔案: RQ_F2550176A_EQC1R.spd_20250523035527.csv → 20250523035527, 時間差: 805513
   ✅ 最佳配對: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv ↔ KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv (時間差: 19)
   ✅ 配對成功!

🔍 處理 FT檔案: KDD0530D3.D_F2550176A_FT1R1_20250522232553.csv
   🔍 FT檔案時間戳: KDD0530D3.D_F2550176A_FT1R1_20250522232553.csv → 20250522232553
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv → 20250523023632, 時間差: 791079
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv → 20250523024027, 時間差: 791474
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv → 20250522232554, 時間差: 1
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522233905.csv → 20250522233905, 時間差: 1352
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522234808.csv → 20250522234808, 時間差: 2255
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522235619.csv → 20250522235619, 時間差: 3066
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523020811.csv → 20250523020811, 時間差: 788258
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv → 20250523021530, 時間差: 788977
      📊 EQC檔案: RQ_F2550176A_EQC1R.spd_20250523035527.csv → 20250523035527, 時間差: 802974
   ✅ 最佳配對: KDD0530D3.D_F2550176A_FT1R1_20250522232553.csv ↔ KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv (時間差: 1)
   ✅ 配對成功!

🔍 處理 FT檔案: KDD0530D3.D_F2550176A_FT1R2_20250522233904.csv
   🔍 FT檔案時間戳: KDD0530D3.D_F2550176A_FT1R2_20250522233904.csv → 20250522233904
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv → 20250523023632, 時間差: 789728
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv → 20250523024027, 時間差: 790123
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522233905.csv → 20250522233905, 時間差: 1
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522234808.csv → 20250522234808, 時間差: 904
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522235619.csv → 20250522235619, 時間差: 1715
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523020811.csv → 20250523020811, 時間差: 786907
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv → 20250523021530, 時間差: 787626
      📊 EQC檔案: RQ_F2550176A_EQC1R.spd_20250523035527.csv → 20250523035527, 時間差: 801623
   ✅ 最佳配對: KDD0530D3.D_F2550176A_FT1R2_20250522233904.csv ↔ KDD0530D3.D_F2550176A_onlieEQC_20250522233905.csv (時間差: 1)
   ✅ 配對成功!

🔍 處理 FT檔案: KDD0530D3.D_F2550176A_FT1R3_20250522234807.csv
   🔍 FT檔案時間戳: KDD0530D3.D_F2550176A_FT1R3_20250522234807.csv → 20250522234807
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv → 20250523023632, 時間差: 788825
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv → 20250523024027, 時間差: 789220
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522234808.csv → 20250522234808, 時間差: 1
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522235619.csv → 20250522235619, 時間差: 812
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523020811.csv → 20250523020811, 時間差: 786004
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv → 20250523021530, 時間差: 786723
      📊 EQC檔案: RQ_F2550176A_EQC1R.spd_20250523035527.csv → 20250523035527, 時間差: 800720
   ✅ 最佳配對: KDD0530D3.D_F2550176A_FT1R3_20250522234807.csv ↔ KDD0530D3.D_F2550176A_onlieEQC_20250522234808.csv (時間差: 1)
   ✅ 配對成功!

🔍 處理 FT檔案: KDD0530D3.D_F2550176A_FT1R4_20250522235619.csv
   🔍 FT檔案時間戳: KDD0530D3.D_F2550176A_FT1R4_20250522235619.csv → 20250522235619
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv → 20250523023632, 時間差: 788013
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv → 20250523024027, 時間差: 788408
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250522235619.csv → 20250522235619, 時間差: 0
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523020811.csv → 20250523020811, 時間差: 785192
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv → 20250523021530, 時間差: 785911
      📊 EQC檔案: RQ_F2550176A_EQC1R.spd_20250523035527.csv → 20250523035527, 時間差: 799908
   ✅ 最佳配對: KDD0530D3.D_F2550176A_FT1R4_20250522235619.csv ↔ KDD0530D3.D_F2550176A_onlieEQC_20250522235619.csv (時間差: 0)
   ✅ 配對成功!

🔍 處理 FT檔案: KDD0530D3.D_F2550176A_FT1R5_20250523020811.csv
   🔍 FT檔案時間戳: KDD0530D3.D_F2550176A_FT1R5_20250523020811.csv → 20250523020811
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv → 20250523023632, 時間差: 2821
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv → 20250523024027, 時間差: 3216
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523020811.csv → 20250523020811, 時間差: 0
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv → 20250523021530, 時間差: 719
      📊 EQC檔案: RQ_F2550176A_EQC1R.spd_20250523035527.csv → 20250523035527, 時間差: 14716
   ✅ 最佳配對: KDD0530D3.D_F2550176A_FT1R5_20250523020811.csv ↔ KDD0530D3.D_F2550176A_onlieEQC_20250523020811.csv (時間差: 0)
   ✅ 配對成功!

🔍 處理 FT檔案: KDD0530D3.D_F2550176A_FT1R6_20250523021530.csv
   🔍 FT檔案時間戳: KDD0530D3.D_F2550176A_FT1R6_20250523021530.csv → 20250523021530
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv → 20250523023632, 時間差: 2102
      📊 EQC檔案: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv → 20250523024027, 時間差: 2497
      📊 EQC檔案: KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv → 20250523021530, 時間差: 0
      📊 EQC檔案: RQ_F2550176A_EQC1R.spd_20250523035527.csv → 20250523035527, 時間差: 13997
   ✅ 最佳配對: KDD0530D3.D_F2550176A_FT1R6_20250523021530.csv ↔ KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv (時間差: 0)
   ✅ 配對成功!

📋 配對結果總結:
   ✅ 成功配對: 7 組
   ❌ 未配對FT檔案: 0 個
   ❌ 未配對EQC檔案: 3 個
📊 FT-EQC 配對結果:
   🔗 成功配對: 7 對
   📄 未配對 EQC: 3 個

🐛 DEBUG: 詳細 FT-EQC 配對日誌
============================================================
   配對  1: FT=[KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv] ↔ EQC=[KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv]
   配對  2: FT=[KDD0530D3.D_F2550176A_FT1R1_20250522232553.csv] ↔ EQC=[KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv]
   配對  3: FT=[KDD0530D3.D_F2550176A_FT1R2_20250522233904.csv] ↔ EQC=[KDD0530D3.D_F2550176A_onlieEQC_20250522233905.csv]
   配對  4: FT=[KDD0530D3.D_F2550176A_FT1R3_20250522234807.csv] ↔ EQC=[KDD0530D3.D_F2550176A_onlieEQC_20250522234808.csv]
   配對  5: FT=[KDD0530D3.D_F2550176A_FT1R4_20250522235619.csv] ↔ EQC=[KDD0530D3.D_F2550176A_onlieEQC_20250522235619.csv]
   配對  6: FT=[KDD0530D3.D_F2550176A_FT1R5_20250523020811.csv] ↔ EQC=[KDD0530D3.D_F2550176A_onlieEQC_20250523020811.csv]
   配對  7: FT=[KDD0530D3.D_F2550176A_FT1R6_20250523021530.csv] ↔ EQC=[KDD0530D3.D_F2550176A_onlieEQC_20250523021530.csv]

🐛 DEBUG: 未配對的 EQC 檔案:
   未配對 1: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   未配對 2: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   未配對 3: RQ_F2550176A_EQC1R.spd_20250523035527.csv
============================================================
📊 檔案掃描統計:
   ✅ 有效CSV檔案: 17 個
   🚫 排除檔案: 15 個
   📁 排除資料夾檔案: 6 個
🔥 開始 EQC BIN1 完整整合處理系統（超時限制: 120秒）
============================================================
📊 發現 EQC 檔案總數: 10
🔍 計算 Online EQC FAIL 統計:
   📄 KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv: 9 個 FAIL
   📄 KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv: 1 個 FAIL
🔍 計算 EQC RT PASS 統計:
   📄 KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv: 6 個 PASS (2025-05-23 02:33:15)
   📄 KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv: 0 個 PASS (2025-05-23 02:38:57)
   📄 RQ_F2550176A_EQC1R.spd_20250523035527.csv: 1 個 PASS (2025-05-23 03:32:14)

📊 統計結果:
   🔴 Online EQC FAIL: 10 個
   🟢 EQC RT PASS: 7 個
🔍 搜尋 EQC BIN=1 資料...
🔍 開始搜尋 EQC BIN=1 資料
📊 總檔案數: 10, 最大掃描行數: 10000
🔄 處理檔案 1/10: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   ⏱️ 預估剩餘: 43秒 | 速度: 0.21 檔案/秒
📄 掃描檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv (22 行)
✅ 找到 EQC BIN=1 資料: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv (第13行，掃描了1行)

📊 處理完成摘要:
   ✅ 成功處理: 1/1
   ⏱️ 總時間: 4.8秒
   📈 平均速度: 0.21 檔案/秒
🔄 處理 FT-EQC 配對失敗資料 (含超連結):
   📊 已按時間戳排序 7 個配對
   📄 KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv: 9 個失敗行
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 113 (使用 A 欄配對)
   ✅ 找到匹配！第 125 行，Serial_No: 113
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 113
   📋 總共掃描了 113 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 196 (使用 A 欄配對)
   ✅ 找到匹配！第 208 行，Serial_No: 196
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 196
   📋 總共掃描了 196 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 198 (使用 A 欄配對)
   ✅ 找到匹配！第 210 行，Serial_No: 198
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 198
   📋 總共掃描了 198 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 257 (使用 A 欄配對)
   ✅ 找到匹配！第 269 行，Serial_No: 257
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 257
   📋 總共掃描了 257 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 511 (使用 A 欄配對)
   ✅ 找到匹配！第 523 行，Serial_No: 511
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 511
   📋 總共掃描了 511 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 544 (使用 A 欄配對)
   ✅ 找到匹配！第 556 行，Serial_No: 544
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 544
   📋 總共掃描了 544 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 588 (使用 A 欄配對)
   ✅ 找到匹配！第 600 行，Serial_No: 588
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 588
   📋 總共掃描了 588 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 600 (使用 A 欄配對)
   ✅ 找到匹配！第 612 行，Serial_No: 600
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 600
   📋 總共掃描了 600 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
🔍 開始搜尋 FT 檔案(傳統格式): 911 行，從第 13 行開始
🎯 目標 Serial_No: 701 (使用 A 欄配對)
   ✅ 找到匹配！第 713 行，Serial_No: 701
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230014.csv
✅ 傳統格式配對成功 (BIN=1): 701
   📋 總共掃描了 701 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522230033.csv
   📄 KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv: 1 個失敗行
🔍 開始搜尋 FT 檔案(傳統格式): 81 行，從第 13 行開始
🎯 目標 Serial_No: 63 (使用 A 欄配對)
   ✅ 找到匹配！第 75 行，Serial_No: 63
🔗 路徑轉換: KDD0530D3.D_F2550176A_FT1R1_20250522232553.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_FT1R1_20250522232553.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R1_20250522232553.csv
✅ 傳統格式配對成功 (BIN=1): 63
   📋 總共掃描了 63 行
🔗 路徑轉換: KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_20250522232554.csv
📋 添加 20 行 FT-EQC 配對失敗資料
📊 EQC RT 檔案總數: 3
   📄 1/3: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv: 2025-05-23 02:33:15
   📄 2/3: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv: 2025-05-23 02:38:57
   📄 3/3: RQ_F2550176A_EQC1R.spd_20250523035527.csv: 2025-05-23 03:32:14
🔄 EQC RT 檔案已按時間排序 (早→晚):
   1. KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv - 02:33:15
   2. KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv - 02:38:57
   3. RQ_F2550176A_EQC1R.spd_20250523035527.csv - 03:32:14
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
🔗 路徑轉換: KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Data/KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_EQC1R1_20250523024027.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
🔗 路徑轉換: RQ_F2550176A_EQC1R.spd_20250523035527.csv
   本地: /mnt/d/download/project/outlook_summary/doc/20250523/Production Verification/RQ_F2550176A_EQC1R.spd_20250523035527.csv
   網路: \\\\192.168.1.60\\temp_7days\Production Verification\RQ_F2550176A_EQC1R.spd_20250523035527.csv
📋 EQC RT 資料處理完成: 總共 303 行資料
📋 添加 303 行 EQC RT 資料 (已按時間排序)

💾 整合檔案已生成:
   📄 EQCTOTALDATA.csv
   📄 EQCTOTALDATA_RAW.csv
📏 檔案大小: 1937930 字元
📄 總行數: 336 行

🎯 最終統計結果:
   📊 A9/B9 (Online EQC FAIL): 10
   📊 A10/B10 (EQC RT PASS): 7
   📊 FT-EQC 配對失敗資料: 20 行
   📊 EQC RT 資料: 303 行
   📊 配對成功: 7 對
   📊 未配對 EQC: 3 個

📝 關鍵內容預覽:
   第 9行: OnlineEQC_Fail:,10,,20250521,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,...
   第10行: EQC_RT_FINAL_PASS:,7,,none,none,55,55,55,55,55,55,none,2,none,none,none,2,none,none,none,2,none,none...
2025-07-07 18:12:04.247 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:196 - ✅ EQC BIN1 處理完成: EQCTOTALDATA.csv, EQCTOTALDATA_RAW.csv
2025-07-07 18:12:04.248 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:244 - 🔍 [PATH_DEBUG] 統一結束點檢查
2025-07-07 18:12:04.248 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:245 - 🔍 [PATH_DEBUG] 處理目錄: /mnt/d/download/project/outlook_summary/doc/20250523
2025-07-07 18:12:04.252 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:248 - 🔍 [PATH_DEBUG] EQCTOTALDATA.csv 存在: True
2025-07-07 18:12:04.253 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:249 - 🔍 [PATH_DEBUG] EQCTOTALDATA.xlsx 存在: False
2025-07-07 18:12:04.253 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:250 - 🔍 [PATH_DEBUG] eqc_total_file: EQCTOTALDATA.csv
2025-07-07 18:12:04.254 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:251 - 🔍 [PATH_DEBUG] eqc_raw_file: EQCTOTALDATA_RAW.csv
2025-07-07 18:12:04.254 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:254 - 🔍 [PATH_DEBUG] 檢查後備生成條件:
2025-07-07 18:12:04.255 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:255 - 🔍 [PATH_DEBUG]   csv_exists: True
2025-07-07 18:12:04.255 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:256 - 🔍 [PATH_DEBUG]   not xlsx_exists: True
2025-07-07 18:12:04.256 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:257 - 🔍 [PATH_DEBUG]   eqc_total_file: 'EQCTOTALDATA.csv'
2025-07-07 18:12:04.256 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:258 - 🔍 [PATH_DEBUG]   bool(eqc_total_file): True
2025-07-07 18:12:04.256 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:261 - 🔧 [PATH_DEBUG] 檢測到缺少 EQCTOTALDATA.xlsx，開始生成...
2025-07-07 18:12:04.257 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:266 - 🔧 [PATH_DEBUG] 使用 CsvToExcelConverter 轉換: /mnt/d/download/project/outlook_summary/doc/20250523/EQCTOTALDATA.csv
   第13行: 9876543210,1,8682.045898,222393,2,26.0225372,27.5251865,31.2093906,27.9144268,26.5113506,27.443718,2...
   第14行: 113,1,HYPERLINK:\\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_FT1R0_20250522230...
   第15行: 113,31,HYPERLINK:\\\\192.168.1.60\\temp_7days\Production Data\KDD0530D3.D_F2550176A_onlieEQC_2025052...
📋 詳細日誌已保存: EQCTOTALDATA_DEBUG_20250707_181156.log
⏱️ 總處理時間: 7.3 秒
初始化 Summary 工作表生成器
初始化 FT_Summary 專用轉換器
初始化核心 4 步驟轉換器 (BIN1 保護: 開啟)
🗑️ 執行定期垃圾收集...
🚀 開始 CSV到Excel轉換 (記憶體: 128.6 MB)
🚀 開始核心 7 步驟轉換 (進階性能管理): /mnt/d/download/project/outlook_summary/doc/20250523/EQCTOTALDATA.csv
================================================================================
CSV 讀取成功: 336 行 x 1694 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16896 → BIN 1691
BIN 分配: Col16906 → BIN 1692
BIN 分配: Col16916 → BIN 1693
BIN 分配: Col16926 → BIN 1694
BIN 分配: Col16936 → BIN 1695
BIN 分配: Col16946 → BIN 1696
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 18 個 BIN1 設備，識別時間: 0.004 秒
📊 批量資料提取完成: 0.005 秒，18 設備 × 1692 測試項目
✅ 限值有效性檢查完成: 0.004 秒，1269 個有效測試項目
🛡️ 保護邏輯檢查完成: 0.047 秒，保護 28 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 18 個 BIN1 設備
  ⚡ 總處理時間: 0.060 秒
  📊 檢查次數: 22,842 次
  🚀 處理速度: 377,878 次檢查/秒
  🛡️ 保護項目: 28 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 56 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 324 設備 × 1692 測試項目 = 548,208 次比較
  預處理完成: 0.327 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.003 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.001 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.064 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 324 個設備的 BIN 分配
  ⚡ 總處理時間: 0.396 秒
  📊 比較次數: 548,208 次
  🚀 處理速度: 1,383,522 次比較/秒
  🔴 需要標紅色的位置: 139291 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
\n🎯 執行策略 B - Site 統計分析 (存檔前)
📊 執行 Site 統計分析 - 純統計模式
📁 檔案: EQCTOTALDATA.csv
🔍 使用指定的 Site 欄位: 第5欄
📊 策略 B - 步驟 3: 掃描 B 欄 BIN + Site 統計
🔍 開始掃描 B 欄 BIN 號碼...
  設備 13: Site 2, BIN 1
  設備 14: Site 1, BIN 1
  設備 15: Site 1, BIN 601
  設備 16: Site 1, BIN 1
  設備 17: Site 1, BIN 601
✅ 完成 324 個設備的 B 欄掃描
📊 發現 2 個不同的 Site
🎯 總計: PASS 18 個, FAIL 306 個

📋 各 Site 統計摘要:
  Site 2: 總計 157, PASS 8, FAIL 149
  Site 1: 總計 167, PASS 10, FAIL 157
📝 Site 統計記錄已附加到: datalog.txt
✅ Site 統計分析完成！處理時間: 0.01 秒
\n📊 生成 Summary 工作表
🎯 生成 Summary 工作表: EQCTOTALDATA.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 324 個，BIN 分佈 {1: 18, 601: 141, 1367: 1, 1565: 2, 1277: 1, 1333: 1, 298: 157, 102: 2, 127: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {2: {'total': 157, 'pass': 8, 'fail': 149, 'pass_rate': 5.095541401273886, 'bins': {1: 8, 1565: 2, 1277: 1, 298: 141, 601: 2, 102: 2, 127: 1}}, 1: {'total': 167, 'pass': 10, 'fail': 157, 'pass_rate': 5.9880239520958085, 'bins': {1: 10, 601: 139, 1367: 1, 1333: 1, 298: 16}}}
📋 BIN Definition 對應: 1684 個 BIN 定義
✅ Summary 生成完成: 9 個 BIN, 2 個 Site
🚀 使用 xlsxwriter 建立包含 Summary 的 Excel 檔案: /mnt/d/download/project/outlook_summary/doc/20250523/EQCTOTALDATA.xlsx
📋 步驟 1: 創建主資料工作表...
  📊 寫入主資料工作表數據...
🚀 執行向量化智慧轉換...
  📊 處理第13行往下 324 行資料...
  ✅ 向量化智慧轉換完成: 1.755 秒
  🔍 紅色位置分析: BIN1保護 56 個, 測試失敗 139291 個, 總計 139347 個
    🚀 混合模式寫入 336 行 × 1694 欄的數據...
    ⚡ 批量寫入完成: 1.496 秒
    🔴 紅色格式重寫完成: 4.014 秒，處理 139347 個位置
    🎨 EQC 自訂格式完成: 0.000 秒，處理 0 個位置
    🔍 分析 139347 個失敗位置...
    🎯 發現 306 個設備有失敗項目
        調試 1: 設備行14 失敗欄位[598, 601, 602]...
        調試 2: 設備行16 失敗欄位[598, 601, 602]...
        調試 3: 設備行18 失敗欄位[1364, 1386, 1388]...
        調試超連結: 設備行12 BIN=1 在失敗列表=False
        調試超連結: 設備行13 BIN=1 在失敗列表=False
        調試超連結: 設備行14 BIN=601 在失敗列表=True
      🔗 創建超連結 1: 設備15 BIN 601 → internal:'EQCTOTALDATA'!WA15
        調試超連結: 設備行15 BIN=1 在失敗列表=False
        調試超連結: 設備行16 BIN=601 在失敗列表=True
      🔗 創建超連結 2: 設備17 BIN 601 → internal:'EQCTOTALDATA'!WA17
        調試超連結: 設備行17 BIN=1 在失敗列表=False
        調試超連結: 設備行18 BIN=1367 在失敗列表=True
      🔗 創建超連結 3: 設備19 BIN 1367 → internal:'EQCTOTALDATA'!AZM19
      🔗 創建超連結 4: 設備21 BIN 1565 → internal:'EQCTOTALDATA'!BHC21
      🔗 創建超連結 5: 設備23 BIN 1277 → internal:'EQCTOTALDATA'!AWA23
    ✅ 超連結創建完成，共 306 個
    🔗 BIN 超連結完成: 0.141 秒，處理 306 個連結
    🧊 凍結視窗完成: 0.000 秒 (凍結 C12 到 Site 欄位第5欄，左上角顯示 C6)
    ✅ 主資料寫入完成，總時間: 5.651 秒
✅ 主資料工作表創建完成
📋 步驟 2: 創建 Summary 工作表...
  📊 寫入 Summary 工作表數據...
    批量填入基本統計行 (第1-4行)...
    批量填入 Site 總計行 (第5行)...
    批量填入標頭行 (第6行)...
    批量填入 BIN 資料行...
    ⚡ 批量數據寫入完成: 0.000 秒
    批量填入完整 BIN 列表...
    📊 空 BIN 列表填入完成: 0.103 秒，添加 1675 個空 BIN
    調整欄位寬度...
    📐 欄位寬度調整完成: 0.000 秒
    ✅ Summary 數據寫入完成，總時間: 0.103 秒，Definition 欄寬度: 23
✅ Summary 工作表創建完成
📋 步驟 3: 保存 Excel 檔案...
✅ xlsxwriter Excel 檔案保存完成

🔍 驗證 Excel 檔案: /mnt/d/download/project/outlook_summary/doc/20250523/EQCTOTALDATA.xlsx
✅ Excel 檔案已成功創建，大小: 2,235,872 bytes
\n================================================================================
⚡ 詳細性能分析報告
================================================================================
📊 各步驟處理時間 (按耗時排序):
   1. 步驟8_Excel輸出 :    9.447 秒 ( 92.3%)
   2. 步驟5_設備BIN分類 :    0.396 秒 (  3.9%)
   3. 步驟1_CSV讀取   :    0.152 秒 (  1.5%)
   4. 步驟7_Summary生成:    0.074 秒 (  0.7%)
2025-07-07 18:12:13.396 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:274 - ✅ [PATH_DEBUG] Excel 後備生成成功（含 Summary）: /mnt/d/download/project/outlook_summary/doc/20250523/EQCTOTALDATA.xlsx
2025-07-07 18:12:13.396 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:275 - 🔧 [PATH_DEBUG] 處理時間: 10.24 秒
2025-07-07 18:12:13.397 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:276 - 🔧 [PATH_DEBUG] 總行數: 336, 總欄數: 1694
2025-07-07 18:12:13.400 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:282 - 🔧 [PATH_DEBUG] Excel 後備生成後，EQCTOTALDATA.xlsx 存在: True
2025-07-07 18:12:13.401 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:297 - 📋 EQCTOTALDATA.xlsx 下載路徑: D:\download\project\outlook_summary\doc\20250523\EQCTOTALDATA.xlsx
2025-07-07 18:12:13.401 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:311 - 🎉 處理完成 - 耗時 8.09秒
2025-07-07 18:12:13.401 | INFO     | src.presentation.api.services.api_utils:log_api_success:543 - ✅ API 端點成功: process_online_eqc
2025-07-07 18:12:13.402 | INFO     | src.presentation.api.services.api_utils:log_api_success:545 -    📊 結果摘要: 處理完成，耗時 8.09秒
2025-07-07 18:12:13.402 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:333 - 🔍 [PATH_DEBUG] process_online_eqc 即將返回
2025-07-07 18:12:13.403 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:334 - 🔍 [PATH_DEBUG] 最終狀態: success
2025-07-07 18:12:13.403 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:335 - 🔍 [PATH_DEBUG] 最終訊息: ✅ Online EQC 處理完成: EQCTOTALDATA 生成
2025-07-07 18:12:13.405 | INFO     | src.presentation.api.services.eqc_processing_service:process_online_eqc:338 - 🔍 [PATH_DEBUG] 最終 EQCTOTALDATA.xlsx 存在: True
   5. 步驟4_BIN1保護  :    0.073 秒 (  0.7%)
   6. 步驟3_BIN分配   :    0.053 秒 (  0.5%)
   7. 步驟6_Site統計  :    0.039 秒 (  0.4%)
   8. 步驟2_結構補全    :    0.002 秒 (  0.0%)
\n🎯 總處理時間: 10.236 秒
\n🔍 性能瓶頸分析:
  前 3 大耗時步驟佔總時間: 97.6%
    🎯 瓶頸 1: 步驟8_Excel輸出 (92.3%)
    🎯 瓶頸 2: 步驟5_設備BIN分類 (3.9%)
    🎯 瓶頸 3: 步驟1_CSV讀取 (1.5%)
\n💡 進一步優化建議:
  🔧 Excel 輸出是主要瓶頸，建議優化 openpyxl 操作
================================================================================

📝 性能記錄已附加到: logs/datalog.txt
✅ CSV到Excel轉換 完成 - 耗時: 10.319秒, 記憶體: 233.1MB
INFO:     127.0.0.1:44996 - "POST /api/process_online_eqc HTTP/1.1" 200 OK
2025-07-07 18:12:13.996 | INFO     | src.presentation.api.services.api_utils:log_api_start:536 - 🚀 API 端點開始: process_eqc_advanced
2025-07-07 18:12:13.996 | DEBUG    | src.presentation.api.services.api_utils:log_api_start:538 -    📋 請求數據: {'folder_path': 'D:\\download\\project\\outlook_summary\\doc\\20250523', 'stage': '2_only'}
2025-07-07 18:12:13.997 | INFO     | src.presentation.api.services.api_utils:process_folder_path:65 - 🔄 路徑轉換: D:\download\project\outlook_summary\doc\20250523 -> /mnt/d/download/project/outlook_summary/doc/20250523
2025-07-07 18:12:14.056 | INFO     | src.presentation.api.services.eqc_processing_service:process_eqc_advanced:432 - 🎯 使用者指定主要區間: 298-335
2025-07-07 18:12:14.057 | INFO     | src.presentation.api.services.eqc_processing_service:process_eqc_advanced:439 - 🎯 使用者指定備用區間: 1565-1600
2025-07-07 18:12:14.057 | INFO     | src.presentation.api.services.eqc_processing_service:process_eqc_advanced:446 - ✅ 使用指定的 CODE 區間設定: {'main_start': 298, 'main_end': 335, 'backup_start': 1565, 'backup_end': 1600}
🚀 執行第二階段EQC處理流程（跳過EQCTOTALDATA.csv生成）
2025-07-07 18:12:14,058 - INFO - 🚀 執行第二階段EQC處理流程（跳過EQCTOTALDATA.csv生成）
   資料目錄: /mnt/d/download/project/outlook_summary/doc/20250523
2025-07-07 18:12:14,058 - INFO -    資料目錄: /mnt/d/download/project/outlook_summary/doc/20250523
   InsEqcRtData2: ✅ 啟用
2025-07-07 18:12:14,059 - INFO -    InsEqcRtData2: ✅ 啟用
   Step 5 測試流程: ✅ 啟用
2025-07-07 18:12:14,060 - INFO -    Step 5 測試流程: ✅ 啟用
   Step 6 Excel 生成: ✅ 啟用
2025-07-07 18:12:14,060 - INFO -    Step 6 Excel 生成: ✅ 啟用
   最終 Excel 轉換: ✅ 啟用
2025-07-07 18:12:14,061 - INFO -    最終 Excel 轉換: ✅ 啟用

2025-07-07 18:12:14,061 - INFO - 
🔧 步驟0: 處理中文路徑與特殊符號
2025-07-07 18:12:14,062 - INFO - 🔧 步驟0: 處理中文路徑與特殊符號
   ✅ 路徑標準化完成
2025-07-07 18:12:14,086 - INFO -    ✅ 路徑標準化完成
✅ EQCTOTALDATA.csv存在，繼續第二階段處理
2025-07-07 18:12:14,089 - INFO - ✅ EQCTOTALDATA.csv存在，繼續第二階段處理
🔍 步驟2: 執行程式碼區間檢測
2025-07-07 18:12:14,089 - INFO - 🔍 步驟2: 執行程式碼區間檢測
🔍 [eqc_processing_service.py] process_eqc_advanced() - 開始執行
🔍 [eqc_processing_service.py] 調用 StandardEQCProcessor.process_from_stage2_only()
初始化 Summary 工作表生成器
初始化 FT_Summary 專用轉換器
初始化核心 4 步驟轉換器 (BIN1 保護: 開啟)
🔍 [eqc_standard_processor.py] process_from_stage2_only() - 開始執行
🔧 中文路徑處理器初始化
📋 共找到 0 個需要處理的資料夾
🔍 [eqc_standard_processor.py] 第二次調用 CodeRegionDetailedDetector
🔍 [code_region_detailed_detector.py] __init__() - 開始執行
🔍 [code_region_detailed_detector.py] detect_and_log_all_regions() - 開始執行
🔍 [code_region_detailed_detector.py] _find_golden_eqc_file() - 開始執行
   🔍 掃描到 17 個候選 CSV 檔案
   ✅ 找到 Golden EQC: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
🔍 [code_region_detailed_detector.py] _scan_all_consecutive_regions() - 開始執行
   🔍 連續整數區間完整掃描結果
   ============================================================
   📊 檢測檔案: KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv
   📊 掃描範圍: 第11欄開始
   
   🎯 發現的連續整數區間:
      區間1: 第13-36欄 (24個連續整數) 🔵 (中型區間)
      區間2: 第105-105欄 (1個連續整數) ⚪ (微型區間)
      區間3: 第146-335欄 (190個連續整數) ✅ (大型區間)
      區間4: 第338-338欄 (1個連續整數) ⚪ (微型區間)
      區間5: 第340-341欄 (2個連續整數) ⚪ (微型區間)
      區間6: 第344-344欄 (1個連續整數) ⚪ (微型區間)
      區間7: 第346-348欄 (3個連續整數) ⚪ (微型區間)
      區間8: 第350-353欄 (4個連續整數) ⚪ (微型區間)
      區間9: 第356-356欄 (1個連續整數) ⚪ (微型區間)
      區間10: 第358-359欄 (2個連續整數) ⚪ (微型區間)
      區間11: 第362-362欄 (1個連續整數) ⚪ (微型區間)
      區間12: 第364-368欄 (5個連續整數) 🟡 (小型區間)
      區間13: 第370-374欄 (5個連續整數) 🟡 (小型區間)
      區間14: 第376-380欄 (5個連續整數) 🟡 (小型區間)
      區間15: 第382-386欄 (5個連續整數) 🟡 (小型區間)
      區間16: 第388-392欄 (5個連續整數) 🟡 (小型區間)
      區間17: 第394-398欄 (5個連續整數) 🟡 (小型區間)
      區間18: 第400-410欄 (11個連續整數) 🟡 (小型區間)
      區間19: 第412-413欄 (2個連續整數) ⚪ (微型區間)
      區間20: 第419-419欄 (1個連續整數) ⚪ (微型區間)
      區間21: 第426-427欄 (2個連續整數) ⚪ (微型區間)
      區間22: 第433-433欄 (1個連續整數) ⚪ (微型區間)
      區間23: 第439-441欄 (3個連續整數) ⚪ (微型區間)
      區間24: 第447-447欄 (1個連續整數) ⚪ (微型區間)
      區間25: 第453-455欄 (3個連續整數) ⚪ (微型區間)
      區間26: 第458-469欄 (12個連續整數) 🟡 (小型區間)
      區間27: 第475-475欄 (1個連續整數) ⚪ (微型區間)
      區間28: 第481-483欄 (3個連續整數) ⚪ (微型區間)
      區間29: 第486-486欄 (1個連續整數) ⚪ (微型區間)
      區間30: 第488-489欄 (2個連續整數) ⚪ (微型區間)
      區間31: 第492-492欄 (1個連續整數) ⚪ (微型區間)
      區間32: 第494-495欄 (2個連續整數) ⚪ (微型區間)
      區間33: 第498-498欄 (1個連續整數) ⚪ (微型區間)
      區間34: 第500-501欄 (2個連續整數) ⚪ (微型區間)
      區間35: 第504-504欄 (1個連續整數) ⚪ (微型區間)
      區間36: 第506-507欄 (2個連續整數) ⚪ (微型區間)
      區間37: 第510-510欄 (1個連續整數) ⚪ (微型區間)
      區間38: 第512-513欄 (2個連續整數) ⚪ (微型區間)
      區間39: 第516-516欄 (1個連續整數) ⚪ (微型區間)
      區間40: 第518-519欄 (2個連續整數) ⚪ (微型區間)
      區間41: 第522-522欄 (1個連續整數) ⚪ (微型區間)
      區間42: 第524-525欄 (2個連續整數) ⚪ (微型區間)
      區間43: 第528-528欄 (1個連續整數) ⚪ (微型區間)
      區間44: 第530-531欄 (2個連續整數) ⚪ (微型區間)
      區間45: 第534-534欄 (1個連續整數) ⚪ (微型區間)
      區間46: 第536-537欄 (2個連續整數) ⚪ (微型區間)
      區間47: 第540-540欄 (1個連續整數) ⚪ (微型區間)
      區間48: 第542-543欄 (2個連續整數) ⚪ (微型區間)
      區間49: 第546-546欄 (1個連續整數) ⚪ (微型區間)
      區間50: 第548-549欄 (2個連續整數) ⚪ (微型區間)
      區間51: 第552-552欄 (1個連續整數) ⚪ (微型區間)
      區間52: 第554-555欄 (2個連續整數) ⚪ (微型區間)
      區間53: 第558-558欄 (1個連續整數) ⚪ (微型區間)
      區間54: 第560-561欄 (2個連續整數) ⚪ (微型區間)
      區間55: 第564-564欄 (1個連續整數) ⚪ (微型區間)
      區間56: 第566-567欄 (2個連續整數) ⚪ (微型區間)
      區間57: 第570-570欄 (1個連續整數) ⚪ (微型區間)
      區間58: 第572-573欄 (2個連續整數) ⚪ (微型區間)
      區間59: 第576-576欄 (1個連續整數) ⚪ (微型區間)
      區間60: 第578-579欄 (2個連續整數) ⚪ (微型區間)
      區間61: 第582-582欄 (1個連續整數) ⚪ (微型區間)
      區間62: 第584-585欄 (2個連續整數) ⚪ (微型區間)
      區間63: 第588-588欄 (1個連續整數) ⚪ (微型區間)
      區間64: 第590-591欄 (2個連續整數) ⚪ (微型區間)
      區間65: 第594-594欄 (1個連續整數) ⚪ (微型區間)
      區間66: 第596-597欄 (2個連續整數) ⚪ (微型區間)
      區間67: 第600-600欄 (1個連續整數) ⚪ (微型區間)
      區間68: 第603-609欄 (7個連續整數) 🟡 (小型區間)
      區間69: 第624-625欄 (2個連續整數) ⚪ (微型區間)
      區間70: 第627-627欄 (1個連續整數) ⚪ (微型區間)
      區間71: 第664-691欄 (28個連續整數) 🔵 (中型區間)
      區間72: 第748-749欄 (2個連續整數) ⚪ (微型區間)
      區間73: 第752-757欄 (6個連續整數) 🟡 (小型區間)
      區間74: 第766-767欄 (2個連續整數) ⚪ (微型區間)
      區間75: 第772-773欄 (2個連續整數) ⚪ (微型區間)
      區間76: 第796-797欄 (2個連續整數) ⚪ (微型區間)
      區間77: 第811-815欄 (5個連續整數) 🟡 (小型區間)
      區間78: 第818-822欄 (5個連續整數) 🟡 (小型區間)
      區間79: 第828-832欄 (5個連續整數) 🟡 (小型區間)
      區間80: 第835-839欄 (5個連續整數) 🟡 (小型區間)
      區間81: 第1261-1261欄 (1個連續整數) ⚪ (微型區間)
      區間82: 第1264-1266欄 (3個連續整數) ⚪ (微型區間)
      區間83: 第1279-1282欄 (4個連續整數) ⚪ (微型區間)
      區間84: 第1286-1286欄 (1個連續整數) ⚪ (微型區間)
      區間85: 第1289-1289欄 (1個連續整數) ⚪ (微型區間)
      區間86: 第1291-1293欄 (3個連續整數) ⚪ (微型區間)
      區間87: 第1295-1302欄 (8個連續整數) 🟡 (小型區間)
      區間88: 第1310-1312欄 (3個連續整數) ⚪ (微型區間)
      區間89: 第1332-1333欄 (2個連續整數) ⚪ (微型區間)
      區間90: 第1366-1367欄 (2個連續整數) ⚪ (微型區間)
      區間91: 第1381-1381欄 (1個連續整數) ⚪ (微型區間)
      區間92: 第1383-1386欄 (4個連續整數) ⚪ (微型區間)
      區間93: 第1407-1408欄 (2個連續整數) ⚪ (微型區間)
      區間94: 第1424-1424欄 (1個連續整數) ⚪ (微型區間)
      區間95: 第1426-1426欄 (1個連續整數) ⚪ (微型區間)
      區間96: 第1428-1428欄 (1個連續整數) ⚪ (微型區間)
      區間97: 第1430-1430欄 (1個連續整數) ⚪ (微型區間)
      區間98: 第1432-1432欄 (1個連續整數) ⚪ (微型區間)
      區間99: 第1434-1434欄 (1個連續整數) ⚪ (微型區間)
      區間100: 第1436-1436欄 (1個連續整數) ⚪ (微型區間)
      區間101: 第1438-1438欄 (1個連續整數) ⚪ (微型區間)
2025-07-07 18:12:14,144 - INFO - 開始檢測程式碼區間: /mnt/d/download/project/outlook_summary/doc/20250523
2025-07-07 18:12:14,144 - INFO - 🎯 使用前端指定的程式碼區間設定
2025-07-07 18:12:14,144 - INFO -    前端設定: 第298-335欄
2025-07-07 18:12:14,144 - INFO -    內部索引: start1=297, end1=334
2025-07-07 18:12:14,145 - INFO - 🔍 條件驗證:
2025-07-07 18:12:14,145 - INFO -    start1 > 10: 297 > 10 = True
2025-07-07 18:12:14,145 - INFO -    (end1 - start1) > 5: (334 - 297) > 5 = True
2025-07-07 18:12:14,146 - INFO -    整體驗證結果: True
2025-07-07 18:12:14,146 - INFO -    備用區間設定: 第1565-1600欄
   ✅ 主要區間: 第298-335欄
2025-07-07 18:12:14,146 - INFO -    ✅ 主要區間: 第298-335欄
   ✅ 備用區間: 第1565-1600欄
2025-07-07 18:12:14,147 - INFO -    ✅ 備用區間: 第1565-1600欄
🔄 步驟3: 執行雙重搜尋機制
2025-07-07 18:12:14,147 - INFO - 🔄 步驟3: 執行雙重搜尋機制
2025-07-07 18:12:14,203 - INFO - 🔍 開始執行修正版雙重搜尋機制...
2025-07-07 18:12:14,203 - INFO - 🎯 使用前端自定義 CODE 區間設定
2025-07-07 18:12:14,204 - INFO -    主要區間: 第298-335欄 (用戶設定)
2025-07-07 18:12:14,204 - INFO -    備用區間: 第1565-1600欄 (用戶設定)
2025-07-07 18:12:14,204 - INFO - 🎯 在主要區間進行100%完全匹配檢查...
2025-07-07 18:12:14,204 - INFO -    ✅ 主要區間第298欄: '0X52(w0x00)'
2025-07-07 18:12:14,205 - INFO -    ✅ 主要區間第299欄: '0X53(w0x00)'
2025-07-07 18:12:14,205 - INFO -    ✅ 主要區間第300欄: '0X54(w0x00)'
2025-07-07 18:12:14,205 - INFO -    ✅ 主要區間第301欄: '0X55(w0x00)'
2025-07-07 18:12:14,205 - INFO -    ✅ 主要區間第302欄: '0X56(w0x00)'
2025-07-07 18:12:14,206 - INFO -    ✅ 主要區間第303欄: '0X57(w0x00)'
2025-07-07 18:12:14,206 - INFO -    ✅ 主要區間第304欄: '0X58(w0x00)'
2025-07-07 18:12:14,206 - INFO -    ✅ 主要區間第305欄: '0X59(w0x00)'
2025-07-07 18:12:14,207 - INFO -    ✅ 主要區間第306欄: '0X5A(w0x00)'
2025-07-07 18:12:14,207 - INFO -    ✅ 主要區間第307欄: '0X5B(w0x00)'
2025-07-07 18:12:14,207 - INFO -    ✅ 主要區間第308欄: '0X5C(w0x00)'
2025-07-07 18:12:14,208 - INFO -    ✅ 主要區間第309欄: '0X5D(w0x00)'
2025-07-07 18:12:14,208 - INFO -    ✅ 主要區間第310欄: '0X5E(w0x00)'
2025-07-07 18:12:14,208 - INFO -    ✅ 主要區間第311欄: '0X5F(w0x00)'
2025-07-07 18:12:14,209 - INFO -    ✅ 主要區間第312欄: '0X60(w0x00)'
2025-07-07 18:12:14,209 - INFO -    ✅ 主要區間第313欄: '0X61(w0x55)'
2025-07-07 18:12:14,209 - INFO -    ✅ 主要區間第314欄: '0X62(w0x75)'
2025-07-07 18:12:14,210 - INFO -    ✅ 主要區間第315欄: '0X63(w0x41)'
2025-07-07 18:12:14,210 - INFO -    ✅ 主要區間第316欄: '0X64(w0x00)'
2025-07-07 18:12:14,210 - INFO -    ✅ 主要區間第317欄: '0X65(w0x81)'
2025-07-07 18:12:14,211 - INFO -    ✅ 主要區間第318欄: '0X66(w0x00)'
2025-07-07 18:12:14,211 - INFO -    ✅ 主要區間第319欄: '0X67(w0x54)'
2025-07-07 18:12:14,211 - INFO -    ✅ 主要區間第320欄: '0X68(w0x00)'
2025-07-07 18:12:14,212 - INFO -    ✅ 主要區間第321欄: '0X69(w0x40)'
2025-07-07 18:12:14,212 - INFO -    ✅ 主要區間第322欄: '0X6A(w0x05)'
2025-07-07 18:12:14,212 - INFO -    ✅ 主要區間第323欄: '0X6B(w0x54)'
2025-07-07 18:12:14,213 - INFO -    ✅ 主要區間第324欄: '0X6C(w0x01)'
2025-07-07 18:12:14,213 - INFO -    ✅ 主要區間第325欄: '0X6D(w0x18)'
2025-07-07 18:12:14,213 - INFO -    ✅ 主要區間第326欄: '0X70(W0X00)'
2025-07-07 18:12:14,214 - INFO -    ✅ 主要區間第327欄: '0X71(W0X00)'
2025-07-07 18:12:14,214 - INFO -    ✅ 主要區間第328欄: '0X72(W0X00)'
2025-07-07 18:12:14,214 - INFO -    ✅ 主要區間第329欄: '0X73(W0X00)'
2025-07-07 18:12:14,214 - INFO -    ✅ 主要區間第330欄: '0X74(W0X00)'
2025-07-07 18:12:14,215 - INFO -    ✅ 主要區間第331欄: '0X75(W0X81)'
2025-07-07 18:12:14,215 - INFO -    ✅ 主要區間第332欄: '0X76(W0X18)'
2025-07-07 18:12:14,215 - INFO -    ✅ 主要區間第333欄: '0X77(W0X18)'
2025-07-07 18:12:14,216 - INFO -    ✅ 主要區間第334欄: 'V800_default_new'
2025-07-07 18:12:14,216 - INFO -    ✅ 主要區間第335欄: 'V800_CODE_new'
2025-07-07 18:12:14,216 - INFO -    🎯 主要區間100%完全匹配: 38/38
2025-07-07 18:12:14,217 - INFO - ✅ 主要區間100%完全匹配成功
   ✅ 雙重搜尋成功完成
2025-07-07 18:12:14,217 - INFO -    ✅ 雙重搜尋成功完成
   搜尋方法: main_region_complete
2025-07-07 18:12:14,217 - INFO -    搜尋方法: main_region_complete
   匹配率: 100%
2025-07-07 18:12:14,218 - INFO -    匹配率: 100%
🔄 步驟4: 執行 InsEqcRtData2 Step 1-2-3-4 處理
2025-07-07 18:12:14,218 - INFO - 🔄 步驟4: 執行 InsEqcRtData2 Step 1-2-3-4 處理
   原始檔案行數: 336
2025-07-07 18:12:14,277 - INFO -    原始檔案行數: 336
🔧 InsEqcRtData2 Step 1-2-3-4: 完整處理...
2025-07-07 18:12:14,278 - INFO - 🔧 InsEqcRtData2 Step 1-2-3-4: 完整處理...
2025-07-07 18:12:14,279 - INFO - 🎯 開始完整 InsEqcRtData2 處理流程...
2025-07-07 18:12:14,279 - INFO - 🔧 Step 1-2: 執行 ALL0 移動功能...
2025-07-07 18:12:14,279 - INFO -    EQC RT 開始位置: 第34行
2025-07-07 18:12:14,280 - INFO -    找到 ALL0 行: 第43行
2025-07-07 18:12:14,280 - INFO -    找到 ALL0 行: 第45行
2025-07-07 18:12:14,281 - INFO -    找到 ALL0 行: 第46行
2025-07-07 18:12:14,287 - INFO -    ✅ ALL0 移動完成: 移動 3 行到檔案最後
2025-07-07 18:12:14,387 - INFO -    ✅ 檔案已更新: 3 行 ALL0 移動到最後
2025-07-07 18:12:14,391 - INFO - 🔍 Step 3: 開始 Online EQC FAIL 檢測...
2025-07-07 18:12:14,395 - INFO -    Online EQC 區間: 第14-33行
2025-07-07 18:12:14,398 - INFO -    找到 FAIL 行: 第15行, BIN=31
2025-07-07 18:12:14,403 - INFO -    找到 FAIL 行: 第17行, BIN=31
2025-07-07 18:12:14,407 - INFO -    找到 FAIL 行: 第19行, BIN=31
2025-07-07 18:12:14,410 - INFO -    找到 FAIL 行: 第21行, BIN=31
2025-07-07 18:12:14,412 - INFO -    找到 FAIL 行: 第23行, BIN=31
2025-07-07 18:12:14,415 - INFO -    找到 FAIL 行: 第25行, BIN=31
2025-07-07 18:12:14,420 - INFO -    找到 FAIL 行: 第27行, BIN=31
2025-07-07 18:12:14,424 - INFO -    找到 FAIL 行: 第29行, BIN=31
2025-07-07 18:12:14,432 - INFO -    找到 FAIL 行: 第31行, BIN=31
2025-07-07 18:12:14,440 - INFO -    找到 FAIL 行: 第33行, BIN=31
2025-07-07 18:12:14,445 - INFO -    ✅ FAIL 檢測完成: 找到 10 行 FAIL
2025-07-07 18:12:14,460 - INFO -    ✅ Step 3 DEBUG LOG 已完成: EQCTOTALDATA_Step3_DEBUG.log
   ✅ InsEqcRtData2 Step 1-2-3 處理完成: ALL0移動 3 行, FAIL檢測 10 行
2025-07-07 18:12:14,461 - INFO -    ✅ InsEqcRtData2 Step 1-2-3 處理完成: ALL0移動 3 行, FAIL檢測 10 行
🔄 Step 4: 執行 CODE 區間匹配搜尋...
2025-07-07 18:12:14,462 - INFO - 🔄 Step 4: 執行 CODE 區間匹配搜尋...
   🎯 動態邊界計算: B9 FAIL數=10, Online EQC範圍=第14-33行, RT開始=第34行
2025-07-07 18:12:14,463 - INFO -    🎯 動態邊界計算: B9 FAIL數=10, Online EQC範圍=第14-33行, RT開始=第34行
🔍 Step 4: 開始 CODE 區間匹配搜尋 DEBUG LOG...
2025-07-07 18:12:14,464 - INFO - 🔍 Step 4: 開始 CODE 區間匹配搜尋 DEBUG LOG...
   📋 重新讀取 ALL0 移動後的檔案以確保行數正確...
2025-07-07 18:12:14,465 - INFO -    📋 重新讀取 ALL0 移動後的檔案以確保行數正確...
   ✅ 重新讀取完成: 336 行 (原始: 336 行)
2025-07-07 18:12:14,536 - INFO -    ✅ 重新讀取完成: 336 行 (原始: 336 行)
2025-07-07 18:12:14,540 - INFO - 🔍 Step 4: 開始 CODE 區間匹配搜尋...
2025-07-07 18:12:14,544 - INFO -    主要 CODE 區間: 第298-335欄 (38個欄位)
2025-07-07 18:12:14,548 - INFO -    備用 CODE 區間: 第1565-1600欄 (36個欄位) - 強制比對
2025-07-07 18:12:14,552 - INFO -    EQC RT 搜尋範圍: 第34行 到 第336行
2025-07-07 18:12:14,555 - INFO - 
🎯 【FAIL #1】匹配搜尋:
2025-07-07 18:12:14,559 - INFO -    目標: 第15行, BIN=31
2025-07-07 18:12:14,564 - INFO -    FT行(第14行)備用區間樣本: ['4', '0', '0', '240', '1']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:14,568 - INFO -    CODE 區間樣本: ['4', '0', '0', '240', '1']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:14,572 - INFO -    ✅ 匹配 #1: 第37行, Serial=4, BIN=1
2025-07-07 18:12:14,576 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,580 - INFO -        RT行前5個CODE: ['4', '0', '0', '240', '1']
2025-07-07 18:12:14,584 - INFO -        FAIL行前5個CODE: ['4', '0', '0', '240', '1']
2025-07-07 18:12:14,586 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,600 - INFO -    🎯 FAIL #1 搜尋結果: 找到 1 個匹配的同一顆 IC
2025-07-07 18:12:14,605 - INFO - 
🎯 【FAIL #2】匹配搜尋:
2025-07-07 18:12:14,610 - INFO -    目標: 第17行, BIN=31
2025-07-07 18:12:14,615 - INFO -    FT行(第16行)備用區間樣本: ['17', '16', '0', '240', '1']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:14,619 - INFO -    CODE 區間樣本: ['17', '16', '0', '240', '1']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:14,622 - INFO -    ✅ 匹配 #1: 第34行, Serial=1, BIN=1
2025-07-07 18:12:14,624 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,628 - INFO -        RT行前5個CODE: ['17', '16', '0', '240', '1']
2025-07-07 18:12:14,631 - INFO -        FAIL行前5個CODE: ['17', '16', '0', '240', '1']
2025-07-07 18:12:14,634 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,647 - INFO -    🎯 FAIL #2 搜尋結果: 找到 1 個匹配的同一顆 IC
2025-07-07 18:12:14,653 - INFO - 
🎯 【FAIL #3】匹配搜尋:
2025-07-07 18:12:14,657 - INFO -    目標: 第19行, BIN=31
2025-07-07 18:12:14,662 - INFO -    FT行(第18行)備用區間樣本: ['0', '0', '0', '0', '1']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:14,667 - INFO -    CODE 區間樣本: ['0', '0', '0', '0', '1']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:14,672 - INFO -    ✅ 匹配 #1: 第41行, Serial=8, BIN=1
2025-07-07 18:12:14,676 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,680 - INFO -        RT行前5個CODE: ['0', '0', '0', '0', '1']
2025-07-07 18:12:14,685 - INFO -        FAIL行前5個CODE: ['0', '0', '0', '0', '1']
2025-07-07 18:12:14,688 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,702 - INFO -    🎯 FAIL #3 搜尋結果: 找到 1 個匹配的同一顆 IC
2025-07-07 18:12:14,707 - INFO - 
🎯 【FAIL #4】匹配搜尋:
2025-07-07 18:12:14,711 - INFO -    目標: 第21行, BIN=31
2025-07-07 18:12:14,716 - INFO -    FT行(第20行)備用區間樣本: ['116', '17', '0', '0', '2']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:14,719 - INFO -    CODE 區間樣本: ['116', '17', '0', '0', '2']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:14,723 - INFO -    ✅ 匹配 #1: 第38行, Serial=5, BIN=9
2025-07-07 18:12:14,727 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,731 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,734 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,738 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,741 - INFO -    ✅ 匹配 #2: 第43行, Serial=1, BIN=9
2025-07-07 18:12:14,743 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,748 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,752 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,755 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,759 - INFO -    ✅ 匹配 #3: 第46行, Serial=2, BIN=9
2025-07-07 18:12:14,762 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,766 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,769 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,772 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,775 - INFO -    ✅ 匹配 #4: 第47行, Serial=3, BIN=9
2025-07-07 18:12:14,778 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,781 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,785 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,789 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,792 - INFO -    ✅ 匹配 #5: 第48行, Serial=4, BIN=9
2025-07-07 18:12:14,795 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,798 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,801 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,805 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,808 - INFO -    ✅ 匹配 #6: 第49行, Serial=5, BIN=9
2025-07-07 18:12:14,811 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,814 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,818 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,822 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,826 - INFO -    ✅ 匹配 #7: 第50行, Serial=6, BIN=9
2025-07-07 18:12:14,829 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,833 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,837 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,840 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,843 - INFO -    ✅ 匹配 #8: 第51行, Serial=7, BIN=9
2025-07-07 18:12:14,846 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,849 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,853 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,857 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,863 - INFO -    ✅ 匹配 #9: 第52行, Serial=8, BIN=9
2025-07-07 18:12:14,867 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,872 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,877 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,881 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,886 - INFO -    ✅ 匹配 #10: 第53行, Serial=9, BIN=9
2025-07-07 18:12:14,890 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,894 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,897 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,899 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,902 - INFO -    ✅ 匹配 #11: 第54行, Serial=10, BIN=9
2025-07-07 18:12:14,905 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,908 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,912 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,915 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,919 - INFO -    ✅ 匹配 #12: 第55行, Serial=11, BIN=9
2025-07-07 18:12:14,923 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,926 - INFO -        RT行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,930 - INFO -        FAIL行前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:14,933 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,947 - INFO -    🎯 FAIL #4 搜尋結果: 找到 12 個匹配的同一顆 IC
2025-07-07 18:12:14,953 - INFO - 
🎯 【FAIL #5】匹配搜尋:
2025-07-07 18:12:14,958 - INFO -    目標: 第23行, BIN=31
2025-07-07 18:12:14,963 - INFO -    FT行(第22行)備用區間樣本: ['4', '0', '0', '240', '0']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:14,967 - INFO -    CODE 區間樣本: ['4', '0', '0', '240', '0']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:14,971 - INFO -    ✅ 匹配 #1: 第45行, Serial=1, BIN=1
2025-07-07 18:12:14,975 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:14,978 - INFO -        RT行前5個CODE: ['4', '0', '0', '240', '0']
2025-07-07 18:12:14,981 - INFO -        FAIL行前5個CODE: ['4', '0', '0', '240', '0']
2025-07-07 18:12:14,985 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:14,999 - INFO -    🎯 FAIL #5 搜尋結果: 找到 1 個匹配的同一顆 IC
2025-07-07 18:12:15,019 - INFO - 
🎯 【FAIL #6】匹配搜尋:
2025-07-07 18:12:15,023 - INFO -    目標: 第25行, BIN=31
2025-07-07 18:12:15,028 - INFO -    FT行(第24行)備用區間樣本: ['5', '17', '1', '240', '0']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:15,033 - INFO -    CODE 區間樣本: ['5', '17', '1', '240', '0']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:15,038 - INFO -    ✅ 匹配 #1: 第42行, Serial=9, BIN=12
2025-07-07 18:12:15,043 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,049 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,054 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,060 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,065 - INFO -    ✅ 匹配 #2: 第44行, Serial=4, BIN=12
2025-07-07 18:12:15,069 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,073 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,077 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,082 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,088 - INFO -    ✅ 匹配 #3: 第56行, Serial=12, BIN=12
2025-07-07 18:12:15,093 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,099 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,104 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,108 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,112 - INFO -    ✅ 匹配 #4: 第57行, Serial=13, BIN=12
2025-07-07 18:12:15,116 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,120 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,124 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,129 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,133 - INFO -    ✅ 匹配 #5: 第58行, Serial=14, BIN=12
2025-07-07 18:12:15,138 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,142 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,147 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,151 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,155 - INFO -    ✅ 匹配 #6: 第59行, Serial=15, BIN=12
2025-07-07 18:12:15,160 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,165 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,170 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,175 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,181 - INFO -    ✅ 匹配 #7: 第75行, Serial=31, BIN=12
2025-07-07 18:12:15,187 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,191 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,196 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,201 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,205 - INFO -    ✅ 匹配 #8: 第76行, Serial=32, BIN=12
2025-07-07 18:12:15,209 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,212 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,216 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,220 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,224 - INFO -    ✅ 匹配 #9: 第78行, Serial=34, BIN=12
2025-07-07 18:12:15,227 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,231 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,235 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,240 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,244 - INFO -    ✅ 匹配 #10: 第80行, Serial=36, BIN=12
2025-07-07 18:12:15,248 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,252 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,258 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,262 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,266 - INFO -    ✅ 匹配 #11: 第82行, Serial=38, BIN=12
2025-07-07 18:12:15,271 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,275 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,280 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,283 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,289 - INFO -    ✅ 匹配 #12: 第84行, Serial=40, BIN=12
2025-07-07 18:12:15,293 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,297 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,300 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,304 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,309 - INFO -    ✅ 匹配 #13: 第86行, Serial=42, BIN=12
2025-07-07 18:12:15,312 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,315 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,317 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,322 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,326 - INFO -    ✅ 匹配 #14: 第88行, Serial=44, BIN=12
2025-07-07 18:12:15,331 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,335 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,340 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,344 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,348 - INFO -    ✅ 匹配 #15: 第90行, Serial=46, BIN=12
2025-07-07 18:12:15,352 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,356 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,360 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,365 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,369 - INFO -    ✅ 匹配 #16: 第92行, Serial=48, BIN=12
2025-07-07 18:12:15,373 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,377 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,381 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,386 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,390 - INFO -    ✅ 匹配 #17: 第94行, Serial=50, BIN=12
2025-07-07 18:12:15,395 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,400 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,404 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,408 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,413 - INFO -    ✅ 匹配 #18: 第96行, Serial=52, BIN=12
2025-07-07 18:12:15,417 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,421 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,426 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,429 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,433 - INFO -    ✅ 匹配 #19: 第98行, Serial=54, BIN=12
2025-07-07 18:12:15,436 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,440 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,445 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,449 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,454 - INFO -    ✅ 匹配 #20: 第100行, Serial=56, BIN=12
2025-07-07 18:12:15,457 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,462 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,465 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,468 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,471 - INFO -    ✅ 匹配 #21: 第102行, Serial=58, BIN=12
2025-07-07 18:12:15,474 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,478 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,482 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,486 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,491 - INFO -    ✅ 匹配 #22: 第104行, Serial=60, BIN=12
2025-07-07 18:12:15,495 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,499 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,503 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,506 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,509 - INFO -    ✅ 匹配 #23: 第106行, Serial=62, BIN=12
2025-07-07 18:12:15,513 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,517 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,521 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,524 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,528 - INFO -    ✅ 匹配 #24: 第108行, Serial=64, BIN=12
2025-07-07 18:12:15,532 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,536 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,540 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,545 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,549 - INFO -    ✅ 匹配 #25: 第110行, Serial=66, BIN=12
2025-07-07 18:12:15,553 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,557 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,561 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,566 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,570 - INFO -    ✅ 匹配 #26: 第112行, Serial=68, BIN=12
2025-07-07 18:12:15,573 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,577 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,580 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,584 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,587 - INFO -    ✅ 匹配 #27: 第114行, Serial=70, BIN=12
2025-07-07 18:12:15,590 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,595 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,599 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,604 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,608 - INFO -    ✅ 匹配 #28: 第116行, Serial=72, BIN=12
2025-07-07 18:12:15,611 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,615 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,619 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,624 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,627 - INFO -    ✅ 匹配 #29: 第118行, Serial=74, BIN=12
2025-07-07 18:12:15,629 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,633 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,639 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,643 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,648 - INFO -    ✅ 匹配 #30: 第120行, Serial=76, BIN=12
2025-07-07 18:12:15,653 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,657 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,660 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,665 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,670 - INFO -    ✅ 匹配 #31: 第122行, Serial=78, BIN=12
2025-07-07 18:12:15,675 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,680 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,684 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,688 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,692 - INFO -    ✅ 匹配 #32: 第124行, Serial=80, BIN=12
2025-07-07 18:12:15,696 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,700 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,705 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,709 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,714 - INFO -    ✅ 匹配 #33: 第126行, Serial=82, BIN=12
2025-07-07 18:12:15,717 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,722 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,726 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,730 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,734 - INFO -    ✅ 匹配 #34: 第128行, Serial=84, BIN=12
2025-07-07 18:12:15,738 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,742 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,744 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,747 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,750 - INFO -    ✅ 匹配 #35: 第130行, Serial=86, BIN=12
2025-07-07 18:12:15,753 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,758 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,762 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,766 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,770 - INFO -    ✅ 匹配 #36: 第132行, Serial=88, BIN=12
2025-07-07 18:12:15,774 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,781 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,785 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,788 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,793 - INFO -    ✅ 匹配 #37: 第134行, Serial=90, BIN=12
2025-07-07 18:12:15,797 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,801 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,803 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,806 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,810 - INFO -    ✅ 匹配 #38: 第136行, Serial=92, BIN=12
2025-07-07 18:12:15,813 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,816 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,819 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,822 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,825 - INFO -    ✅ 匹配 #39: 第138行, Serial=94, BIN=12
2025-07-07 18:12:15,828 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,831 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,833 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,837 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,840 - INFO -    ✅ 匹配 #40: 第140行, Serial=96, BIN=12
2025-07-07 18:12:15,844 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,847 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,851 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,855 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,858 - INFO -    ✅ 匹配 #41: 第142行, Serial=98, BIN=12
2025-07-07 18:12:15,862 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,865 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,867 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,871 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,874 - INFO -    ✅ 匹配 #42: 第144行, Serial=100, BIN=12
2025-07-07 18:12:15,879 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,882 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,886 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,889 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,894 - INFO -    ✅ 匹配 #43: 第146行, Serial=102, BIN=12
2025-07-07 18:12:15,897 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,901 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,904 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,907 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,911 - INFO -    ✅ 匹配 #44: 第148行, Serial=104, BIN=12
2025-07-07 18:12:15,916 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,919 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,922 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,926 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,930 - INFO -    ✅ 匹配 #45: 第150行, Serial=106, BIN=12
2025-07-07 18:12:15,933 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,936 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,940 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,943 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,947 - INFO -    ✅ 匹配 #46: 第152行, Serial=108, BIN=12
2025-07-07 18:12:15,951 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,954 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,956 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,959 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,963 - INFO -    ✅ 匹配 #47: 第154行, Serial=110, BIN=12
2025-07-07 18:12:15,966 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,969 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,972 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,975 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,979 - INFO -    ✅ 匹配 #48: 第156行, Serial=112, BIN=12
2025-07-07 18:12:15,983 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:15,986 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,990 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:15,993 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:15,996 - INFO -    ✅ 匹配 #49: 第158行, Serial=114, BIN=12
2025-07-07 18:12:15,999 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,002 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,006 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,008 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,012 - INFO -    ✅ 匹配 #50: 第160行, Serial=116, BIN=12
2025-07-07 18:12:16,015 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,018 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,021 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,024 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,028 - INFO -    ✅ 匹配 #51: 第162行, Serial=118, BIN=12
2025-07-07 18:12:16,031 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,035 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,037 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,040 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,042 - INFO -    ✅ 匹配 #52: 第164行, Serial=120, BIN=12
2025-07-07 18:12:16,045 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,048 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,051 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,053 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,056 - INFO -    ✅ 匹配 #53: 第166行, Serial=122, BIN=12
2025-07-07 18:12:16,059 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,061 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,064 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,067 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,070 - INFO -    ✅ 匹配 #54: 第168行, Serial=124, BIN=12
2025-07-07 18:12:16,073 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,077 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,081 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,085 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,089 - INFO -    ✅ 匹配 #55: 第170行, Serial=126, BIN=12
2025-07-07 18:12:16,093 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,096 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,099 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,102 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,105 - INFO -    ✅ 匹配 #56: 第172行, Serial=128, BIN=12
2025-07-07 18:12:16,108 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,111 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,114 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,117 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,120 - INFO -    ✅ 匹配 #57: 第174行, Serial=130, BIN=12
2025-07-07 18:12:16,124 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,126 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,129 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,132 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,135 - INFO -    ✅ 匹配 #58: 第176行, Serial=132, BIN=12
2025-07-07 18:12:16,138 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,141 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,144 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,147 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,149 - INFO -    ✅ 匹配 #59: 第178行, Serial=134, BIN=12
2025-07-07 18:12:16,153 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,156 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,159 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,161 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,164 - INFO -    ✅ 匹配 #60: 第180行, Serial=136, BIN=12
2025-07-07 18:12:16,167 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,170 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,173 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,176 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,179 - INFO -    ✅ 匹配 #61: 第182行, Serial=138, BIN=12
2025-07-07 18:12:16,181 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,184 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,187 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,190 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,193 - INFO -    ✅ 匹配 #62: 第184行, Serial=140, BIN=12
2025-07-07 18:12:16,195 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,198 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,201 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,204 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,207 - INFO -    ✅ 匹配 #63: 第186行, Serial=142, BIN=12
2025-07-07 18:12:16,210 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,213 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,216 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,218 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,221 - INFO -    ✅ 匹配 #64: 第188行, Serial=144, BIN=12
2025-07-07 18:12:16,224 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,227 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,230 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,232 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,235 - INFO -    ✅ 匹配 #65: 第190行, Serial=146, BIN=12
2025-07-07 18:12:16,238 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,241 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,243 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,246 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,248 - INFO -    ✅ 匹配 #66: 第192行, Serial=148, BIN=12
2025-07-07 18:12:16,251 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,254 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,257 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,260 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,263 - INFO -    ✅ 匹配 #67: 第194行, Serial=150, BIN=12
2025-07-07 18:12:16,266 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,268 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,271 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,274 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,277 - INFO -    ✅ 匹配 #68: 第196行, Serial=152, BIN=12
2025-07-07 18:12:16,280 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,282 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,285 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,288 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,291 - INFO -    ✅ 匹配 #69: 第198行, Serial=154, BIN=12
2025-07-07 18:12:16,294 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,297 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,300 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,303 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,306 - INFO -    ✅ 匹配 #70: 第200行, Serial=156, BIN=12
2025-07-07 18:12:16,310 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,313 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,317 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,320 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,323 - INFO -    ✅ 匹配 #71: 第202行, Serial=158, BIN=12
2025-07-07 18:12:16,327 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,330 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,333 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,335 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,338 - INFO -    ✅ 匹配 #72: 第204行, Serial=160, BIN=12
2025-07-07 18:12:16,341 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,344 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,347 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,351 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,353 - INFO -    ✅ 匹配 #73: 第206行, Serial=162, BIN=12
2025-07-07 18:12:16,356 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,359 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,362 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,365 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,368 - INFO -    ✅ 匹配 #74: 第208行, Serial=164, BIN=12
2025-07-07 18:12:16,371 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,373 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,377 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,380 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,383 - INFO -    ✅ 匹配 #75: 第210行, Serial=166, BIN=12
2025-07-07 18:12:16,386 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,389 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,392 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,394 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,398 - INFO -    ✅ 匹配 #76: 第212行, Serial=168, BIN=12
2025-07-07 18:12:16,401 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,404 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,407 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,409 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,412 - INFO -    ✅ 匹配 #77: 第214行, Serial=170, BIN=12
2025-07-07 18:12:16,521 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,525 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,529 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,533 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,537 - INFO -    ✅ 匹配 #78: 第216行, Serial=172, BIN=12
2025-07-07 18:12:16,542 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,545 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,548 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,551 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,555 - INFO -    ✅ 匹配 #79: 第218行, Serial=174, BIN=12
2025-07-07 18:12:16,558 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,561 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,563 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,566 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,570 - INFO -    ✅ 匹配 #80: 第220行, Serial=176, BIN=12
2025-07-07 18:12:16,574 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,577 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,581 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,584 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,588 - INFO -    ✅ 匹配 #81: 第222行, Serial=178, BIN=12
2025-07-07 18:12:16,591 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,594 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,596 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,599 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,602 - INFO -    ✅ 匹配 #82: 第224行, Serial=180, BIN=12
2025-07-07 18:12:16,607 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,610 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,614 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,618 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,621 - INFO -    ✅ 匹配 #83: 第226行, Serial=182, BIN=12
2025-07-07 18:12:16,625 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,630 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,633 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,637 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,641 - INFO -    ✅ 匹配 #84: 第228行, Serial=184, BIN=12
2025-07-07 18:12:16,645 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,648 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,650 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,653 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,655 - INFO -    ✅ 匹配 #85: 第230行, Serial=186, BIN=12
2025-07-07 18:12:16,659 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,662 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,667 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,671 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,675 - INFO -    ✅ 匹配 #86: 第232行, Serial=188, BIN=12
2025-07-07 18:12:16,679 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,684 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,687 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,690 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,694 - INFO -    ✅ 匹配 #87: 第234行, Serial=190, BIN=12
2025-07-07 18:12:16,696 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,700 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,703 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,706 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,710 - INFO -    ✅ 匹配 #88: 第236行, Serial=192, BIN=12
2025-07-07 18:12:16,714 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,717 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,720 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,723 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,727 - INFO -    ✅ 匹配 #89: 第238行, Serial=194, BIN=12
2025-07-07 18:12:16,731 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,735 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,738 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,742 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,745 - INFO -    ✅ 匹配 #90: 第240行, Serial=196, BIN=12
2025-07-07 18:12:16,748 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,751 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,754 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,757 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,761 - INFO -    ✅ 匹配 #91: 第242行, Serial=198, BIN=12
2025-07-07 18:12:16,765 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,769 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,772 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,776 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,780 - INFO -    ✅ 匹配 #92: 第244行, Serial=200, BIN=12
2025-07-07 18:12:16,784 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,787 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,790 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,793 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,797 - INFO -    ✅ 匹配 #93: 第246行, Serial=202, BIN=12
2025-07-07 18:12:16,801 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,805 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,810 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,814 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,819 - INFO -    ✅ 匹配 #94: 第248行, Serial=204, BIN=12
2025-07-07 18:12:16,824 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,827 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,832 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,836 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,840 - INFO -    ✅ 匹配 #95: 第250行, Serial=206, BIN=12
2025-07-07 18:12:16,844 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,848 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,853 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,857 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,861 - INFO -    ✅ 匹配 #96: 第252行, Serial=208, BIN=12
2025-07-07 18:12:16,865 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,871 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,874 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,878 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,883 - INFO -    ✅ 匹配 #97: 第254行, Serial=210, BIN=12
2025-07-07 18:12:16,886 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,890 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,894 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,898 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,902 - INFO -    ✅ 匹配 #98: 第256行, Serial=212, BIN=12
2025-07-07 18:12:16,907 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,911 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,914 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,918 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,921 - INFO -    ✅ 匹配 #99: 第258行, Serial=214, BIN=12
2025-07-07 18:12:16,925 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,929 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,933 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,938 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,942 - INFO -    ✅ 匹配 #100: 第260行, Serial=216, BIN=12
2025-07-07 18:12:16,945 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,949 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,953 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,957 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,960 - INFO -    ✅ 匹配 #101: 第262行, Serial=218, BIN=12
2025-07-07 18:12:16,965 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,970 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,975 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,980 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:16,983 - INFO -    ✅ 匹配 #102: 第264行, Serial=220, BIN=12
2025-07-07 18:12:16,987 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:16,991 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,995 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:16,999 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,002 - INFO -    ✅ 匹配 #103: 第266行, Serial=222, BIN=12
2025-07-07 18:12:17,007 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,011 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,015 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,019 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,023 - INFO -    ✅ 匹配 #104: 第268行, Serial=224, BIN=12
2025-07-07 18:12:17,027 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,031 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,035 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,039 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,043 - INFO -    ✅ 匹配 #105: 第270行, Serial=226, BIN=12
2025-07-07 18:12:17,046 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,048 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,051 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,054 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,057 - INFO -    ✅ 匹配 #106: 第272行, Serial=228, BIN=12
2025-07-07 18:12:17,060 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,063 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,066 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,068 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,072 - INFO -    ✅ 匹配 #107: 第274行, Serial=230, BIN=12
2025-07-07 18:12:17,074 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,078 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,081 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,083 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,086 - INFO -    ✅ 匹配 #108: 第276行, Serial=232, BIN=12
2025-07-07 18:12:17,089 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,092 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,095 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,098 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,101 - INFO -    ✅ 匹配 #109: 第278行, Serial=234, BIN=12
2025-07-07 18:12:17,103 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,107 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,110 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,113 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,116 - INFO -    ✅ 匹配 #110: 第280行, Serial=236, BIN=12
2025-07-07 18:12:17,118 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,121 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,124 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,127 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,131 - INFO -    ✅ 匹配 #111: 第282行, Serial=238, BIN=12
2025-07-07 18:12:17,134 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,137 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,140 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,142 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,146 - INFO -    ✅ 匹配 #112: 第284行, Serial=240, BIN=12
2025-07-07 18:12:17,149 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,151 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,154 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,157 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,160 - INFO -    ✅ 匹配 #113: 第286行, Serial=242, BIN=12
2025-07-07 18:12:17,163 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,167 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,170 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,173 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,177 - INFO -    ✅ 匹配 #114: 第288行, Serial=244, BIN=12
2025-07-07 18:12:17,180 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,183 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,185 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,188 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,192 - INFO -    ✅ 匹配 #115: 第290行, Serial=246, BIN=12
2025-07-07 18:12:17,196 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,199 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,202 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,205 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,208 - INFO -    ✅ 匹配 #116: 第292行, Serial=248, BIN=12
2025-07-07 18:12:17,210 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,213 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,216 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,219 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,222 - INFO -    ✅ 匹配 #117: 第294行, Serial=250, BIN=12
2025-07-07 18:12:17,225 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,228 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,231 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,234 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,237 - INFO -    ✅ 匹配 #118: 第296行, Serial=252, BIN=12
2025-07-07 18:12:17,239 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,242 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,245 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,248 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,252 - INFO -    ✅ 匹配 #119: 第298行, Serial=254, BIN=12
2025-07-07 18:12:17,255 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,257 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,260 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,263 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,267 - INFO -    ✅ 匹配 #120: 第300行, Serial=256, BIN=12
2025-07-07 18:12:17,270 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,273 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,275 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,278 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,282 - INFO -    ✅ 匹配 #121: 第302行, Serial=258, BIN=12
2025-07-07 18:12:17,285 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,288 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,291 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,294 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,298 - INFO -    ✅ 匹配 #122: 第304行, Serial=260, BIN=12
2025-07-07 18:12:17,302 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,305 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,309 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,313 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,317 - INFO -    ✅ 匹配 #123: 第306行, Serial=262, BIN=12
2025-07-07 18:12:17,320 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,323 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,326 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,329 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,332 - INFO -    ✅ 匹配 #124: 第308行, Serial=264, BIN=12
2025-07-07 18:12:17,335 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,338 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,341 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,344 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,347 - INFO -    ✅ 匹配 #125: 第310行, Serial=266, BIN=12
2025-07-07 18:12:17,351 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,354 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,357 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,362 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,367 - INFO -    ✅ 匹配 #126: 第312行, Serial=268, BIN=12
2025-07-07 18:12:17,371 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,375 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,379 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,383 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,386 - INFO -    ✅ 匹配 #127: 第314行, Serial=270, BIN=12
2025-07-07 18:12:17,390 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,394 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,397 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,400 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,404 - INFO -    ✅ 匹配 #128: 第316行, Serial=272, BIN=12
2025-07-07 18:12:17,406 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,409 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,412 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,415 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,419 - INFO -    ✅ 匹配 #129: 第318行, Serial=274, BIN=12
2025-07-07 18:12:17,422 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,425 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,428 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,431 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,433 - INFO -    ✅ 匹配 #130: 第320行, Serial=276, BIN=12
2025-07-07 18:12:17,436 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,439 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,443 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,453 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,459 - INFO -    ✅ 匹配 #131: 第322行, Serial=278, BIN=12
2025-07-07 18:12:17,463 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,467 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,471 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,476 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,480 - INFO -    ✅ 匹配 #132: 第324行, Serial=280, BIN=12
2025-07-07 18:12:17,485 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,489 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,494 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,498 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,503 - INFO -    ✅ 匹配 #133: 第326行, Serial=282, BIN=12
2025-07-07 18:12:17,509 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,514 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,517 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,521 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,525 - INFO -    ✅ 匹配 #134: 第328行, Serial=284, BIN=12
2025-07-07 18:12:17,529 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,533 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,536 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,539 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,543 - INFO -    ✅ 匹配 #135: 第330行, Serial=286, BIN=12
2025-07-07 18:12:17,547 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,550 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,553 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,556 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,560 - INFO -    ✅ 匹配 #136: 第332行, Serial=288, BIN=12
2025-07-07 18:12:17,564 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,567 - INFO -        RT行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,570 - INFO -        FAIL行前5個CODE: ['5', '17', '1', '240', '0']
2025-07-07 18:12:17,574 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,578 - INFO -    🎯 FAIL #6 搜尋結果: 找到 136 個匹配的同一顆 IC
2025-07-07 18:12:17,583 - INFO - 
🎯 【FAIL #7】匹配搜尋:
2025-07-07 18:12:17,586 - INFO -    目標: 第27行, BIN=31
2025-07-07 18:12:17,589 - INFO -    FT行(第26行)備用區間樣本: ['117', '15', '0', '0', '1']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:17,593 - INFO -    CODE 區間樣本: ['117', '15', '0', '0', '1']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:17,597 - INFO -    ✅ 匹配 #1: 第35行, Serial=2, BIN=1
2025-07-07 18:12:17,600 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,604 - INFO -        RT行前5個CODE: ['117', '15', '0', '0', '1']
2025-07-07 18:12:17,607 - INFO -        FAIL行前5個CODE: ['117', '15', '0', '0', '1']
2025-07-07 18:12:17,612 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,630 - INFO -    🎯 FAIL #7 搜尋結果: 找到 1 個匹配的同一顆 IC
2025-07-07 18:12:17,633 - INFO - 
🎯 【FAIL #8】匹配搜尋:
2025-07-07 18:12:17,636 - INFO -    目標: 第29行, BIN=31
2025-07-07 18:12:17,642 - INFO -    FT行(第28行)備用區間樣本: ['31', '0', '0', '0', '1']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:17,648 - INFO -    CODE 區間樣本: ['31', '0', '0', '0', '1']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:17,652 - INFO -    ✅ 匹配 #1: 第36行, Serial=3, BIN=1
2025-07-07 18:12:17,655 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,659 - INFO -        RT行前5個CODE: ['31', '0', '0', '0', '1']
2025-07-07 18:12:17,663 - INFO -        FAIL行前5個CODE: ['31', '0', '0', '0', '1']
2025-07-07 18:12:17,666 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,680 - INFO -    🎯 FAIL #8 搜尋結果: 找到 1 個匹配的同一顆 IC
2025-07-07 18:12:17,683 - INFO - 
🎯 【FAIL #9】匹配搜尋:
2025-07-07 18:12:17,686 - INFO -    目標: 第31行, BIN=31
2025-07-07 18:12:17,690 - INFO -    FT行(第30行)備用區間樣本: ['17', '0', '0', '0', '1']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:17,693 - INFO -    CODE 區間樣本: ['17', '0', '0', '0', '1']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:17,696 - INFO -    ✅ 匹配 #1: 第39行, Serial=6, BIN=9
2025-07-07 18:12:17,700 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,703 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,706 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,709 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,713 - INFO -    ✅ 匹配 #2: 第60行, Serial=16, BIN=9
2025-07-07 18:12:17,717 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,720 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,724 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,728 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,736 - INFO -    ✅ 匹配 #3: 第61行, Serial=17, BIN=9
2025-07-07 18:12:17,740 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,743 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,747 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,751 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,756 - INFO -    ✅ 匹配 #4: 第62行, Serial=18, BIN=9
2025-07-07 18:12:17,761 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,768 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,774 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,777 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,781 - INFO -    ✅ 匹配 #5: 第63行, Serial=19, BIN=9
2025-07-07 18:12:17,785 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,789 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,792 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,795 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,799 - INFO -    ✅ 匹配 #6: 第64行, Serial=20, BIN=9
2025-07-07 18:12:17,804 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,808 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,811 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,814 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,818 - INFO -    ✅ 匹配 #7: 第65行, Serial=21, BIN=9
2025-07-07 18:12:17,823 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,829 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,834 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,839 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,842 - INFO -    ✅ 匹配 #8: 第66行, Serial=22, BIN=9
2025-07-07 18:12:17,846 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,849 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,853 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,857 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,861 - INFO -    ✅ 匹配 #9: 第67行, Serial=23, BIN=9
2025-07-07 18:12:17,866 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,869 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,875 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,878 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,882 - INFO -    ✅ 匹配 #10: 第68行, Serial=24, BIN=9
2025-07-07 18:12:17,885 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,888 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,892 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,895 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,898 - INFO -    ✅ 匹配 #11: 第69行, Serial=25, BIN=9
2025-07-07 18:12:17,901 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,904 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,908 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,911 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,914 - INFO -    ✅ 匹配 #12: 第70行, Serial=26, BIN=9
2025-07-07 18:12:17,918 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,922 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,926 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,929 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,933 - INFO -    ✅ 匹配 #13: 第71行, Serial=27, BIN=9
2025-07-07 18:12:17,937 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,941 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,945 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,948 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,951 - INFO -    ✅ 匹配 #14: 第72行, Serial=28, BIN=9
2025-07-07 18:12:17,955 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,958 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,962 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,965 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,969 - INFO -    ✅ 匹配 #15: 第73行, Serial=29, BIN=9
2025-07-07 18:12:17,972 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,976 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,979 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,982 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:17,985 - INFO -    ✅ 匹配 #16: 第74行, Serial=30, BIN=9
2025-07-07 18:12:17,989 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:17,992 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,996 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:17,999 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,003 - INFO -    ✅ 匹配 #17: 第77行, Serial=33, BIN=9
2025-07-07 18:12:18,006 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,010 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,014 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,018 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,022 - INFO -    ✅ 匹配 #18: 第79行, Serial=35, BIN=9
2025-07-07 18:12:18,026 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,029 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,032 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,036 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,039 - INFO -    ✅ 匹配 #19: 第81行, Serial=37, BIN=9
2025-07-07 18:12:18,042 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,046 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,049 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,052 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,055 - INFO -    ✅ 匹配 #20: 第83行, Serial=39, BIN=9
2025-07-07 18:12:18,059 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,065 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,068 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,074 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,079 - INFO -    ✅ 匹配 #21: 第85行, Serial=41, BIN=9
2025-07-07 18:12:18,083 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,086 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,089 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,092 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,096 - INFO -    ✅ 匹配 #22: 第87行, Serial=43, BIN=9
2025-07-07 18:12:18,100 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,102 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,106 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,109 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,113 - INFO -    ✅ 匹配 #23: 第89行, Serial=45, BIN=9
2025-07-07 18:12:18,116 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,120 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,123 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,126 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,129 - INFO -    ✅ 匹配 #24: 第91行, Serial=47, BIN=9
2025-07-07 18:12:18,133 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,136 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,139 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,143 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,146 - INFO -    ✅ 匹配 #25: 第93行, Serial=49, BIN=9
2025-07-07 18:12:18,149 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,153 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,157 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,161 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,165 - INFO -    ✅ 匹配 #26: 第95行, Serial=51, BIN=9
2025-07-07 18:12:18,169 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,173 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,177 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,180 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,184 - INFO -    ✅ 匹配 #27: 第97行, Serial=53, BIN=9
2025-07-07 18:12:18,188 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,191 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,195 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,198 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,201 - INFO -    ✅ 匹配 #28: 第99行, Serial=55, BIN=9
2025-07-07 18:12:18,205 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,209 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,211 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,215 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,218 - INFO -    ✅ 匹配 #29: 第101行, Serial=57, BIN=9
2025-07-07 18:12:18,222 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,226 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,230 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,233 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,237 - INFO -    ✅ 匹配 #30: 第103行, Serial=59, BIN=9
2025-07-07 18:12:18,241 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,246 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,249 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,253 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,259 - INFO -    ✅ 匹配 #31: 第105行, Serial=61, BIN=9
2025-07-07 18:12:18,262 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,265 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,268 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,273 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,276 - INFO -    ✅ 匹配 #32: 第107行, Serial=63, BIN=9
2025-07-07 18:12:18,280 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,283 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,286 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,291 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,295 - INFO -    ✅ 匹配 #33: 第109行, Serial=65, BIN=9
2025-07-07 18:12:18,298 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,301 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,305 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,310 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,313 - INFO -    ✅ 匹配 #34: 第111行, Serial=67, BIN=9
2025-07-07 18:12:18,317 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,320 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,328 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,344 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,349 - INFO -    ✅ 匹配 #35: 第113行, Serial=69, BIN=9
2025-07-07 18:12:18,353 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,361 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,364 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,367 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,371 - INFO -    ✅ 匹配 #36: 第115行, Serial=71, BIN=9
2025-07-07 18:12:18,376 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,380 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,384 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,387 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,391 - INFO -    ✅ 匹配 #37: 第117行, Serial=73, BIN=9
2025-07-07 18:12:18,395 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,398 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,402 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,406 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,411 - INFO -    ✅ 匹配 #38: 第119行, Serial=75, BIN=9
2025-07-07 18:12:18,415 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,419 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,423 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,427 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,431 - INFO -    ✅ 匹配 #39: 第121行, Serial=77, BIN=9
2025-07-07 18:12:18,434 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,437 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,441 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,445 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,447 - INFO -    ✅ 匹配 #40: 第123行, Serial=79, BIN=9
2025-07-07 18:12:18,450 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,453 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,457 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,461 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,465 - INFO -    ✅ 匹配 #41: 第125行, Serial=81, BIN=9
2025-07-07 18:12:18,468 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,471 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,475 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,479 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,483 - INFO -    ✅ 匹配 #42: 第127行, Serial=83, BIN=9
2025-07-07 18:12:18,486 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,489 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,493 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,498 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,501 - INFO -    ✅ 匹配 #43: 第129行, Serial=85, BIN=9
2025-07-07 18:12:18,504 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,508 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,512 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,516 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,521 - INFO -    ✅ 匹配 #44: 第131行, Serial=87, BIN=9
2025-07-07 18:12:18,524 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,527 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,531 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,534 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,537 - INFO -    ✅ 匹配 #45: 第133行, Serial=89, BIN=9
2025-07-07 18:12:18,542 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,546 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,550 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,554 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,557 - INFO -    ✅ 匹配 #46: 第135行, Serial=91, BIN=9
2025-07-07 18:12:18,560 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,564 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,568 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,571 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,574 - INFO -    ✅ 匹配 #47: 第137行, Serial=93, BIN=9
2025-07-07 18:12:18,577 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,581 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,584 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,587 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,591 - INFO -    ✅ 匹配 #48: 第139行, Serial=95, BIN=9
2025-07-07 18:12:18,593 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,596 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,600 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,604 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,607 - INFO -    ✅ 匹配 #49: 第141行, Serial=97, BIN=9
2025-07-07 18:12:18,610 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,613 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,618 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,622 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,626 - INFO -    ✅ 匹配 #50: 第143行, Serial=99, BIN=9
2025-07-07 18:12:18,631 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,635 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,640 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,644 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,650 - INFO -    ✅ 匹配 #51: 第145行, Serial=101, BIN=9
2025-07-07 18:12:18,656 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,660 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,667 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,672 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,677 - INFO -    ✅ 匹配 #52: 第147行, Serial=103, BIN=9
2025-07-07 18:12:18,682 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,686 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,692 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,696 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,700 - INFO -    ✅ 匹配 #53: 第149行, Serial=105, BIN=9
2025-07-07 18:12:18,704 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,708 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,712 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,716 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,720 - INFO -    ✅ 匹配 #54: 第151行, Serial=107, BIN=9
2025-07-07 18:12:18,724 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,728 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,731 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,735 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,739 - INFO -    ✅ 匹配 #55: 第153行, Serial=109, BIN=9
2025-07-07 18:12:18,742 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,746 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,749 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,753 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,756 - INFO -    ✅ 匹配 #56: 第155行, Serial=111, BIN=9
2025-07-07 18:12:18,759 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,763 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,766 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,770 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,774 - INFO -    ✅ 匹配 #57: 第157行, Serial=113, BIN=9
2025-07-07 18:12:18,777 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,780 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,784 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,787 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,792 - INFO -    ✅ 匹配 #58: 第159行, Serial=115, BIN=9
2025-07-07 18:12:18,796 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,800 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,803 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,807 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,811 - INFO -    ✅ 匹配 #59: 第161行, Serial=117, BIN=9
2025-07-07 18:12:18,815 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,820 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,824 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,828 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,832 - INFO -    ✅ 匹配 #60: 第163行, Serial=119, BIN=9
2025-07-07 18:12:18,836 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,840 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,844 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,848 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,852 - INFO -    ✅ 匹配 #61: 第165行, Serial=121, BIN=9
2025-07-07 18:12:18,856 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,859 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,863 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,867 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,871 - INFO -    ✅ 匹配 #62: 第167行, Serial=123, BIN=9
2025-07-07 18:12:18,874 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,878 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,881 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,883 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,887 - INFO -    ✅ 匹配 #63: 第169行, Serial=125, BIN=9
2025-07-07 18:12:18,889 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,893 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,896 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,899 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,903 - INFO -    ✅ 匹配 #64: 第171行, Serial=127, BIN=9
2025-07-07 18:12:18,907 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,910 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,915 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,918 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,922 - INFO -    ✅ 匹配 #65: 第173行, Serial=129, BIN=9
2025-07-07 18:12:18,925 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,929 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,932 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,936 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,939 - INFO -    ✅ 匹配 #66: 第175行, Serial=131, BIN=9
2025-07-07 18:12:18,942 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,945 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,949 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,952 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,955 - INFO -    ✅ 匹配 #67: 第177行, Serial=133, BIN=9
2025-07-07 18:12:18,959 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,962 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,965 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,968 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,971 - INFO -    ✅ 匹配 #68: 第179行, Serial=135, BIN=9
2025-07-07 18:12:18,975 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,978 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,982 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,985 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:18,988 - INFO -    ✅ 匹配 #69: 第181行, Serial=137, BIN=9
2025-07-07 18:12:18,991 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:18,994 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:18,997 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,000 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,004 - INFO -    ✅ 匹配 #70: 第183行, Serial=139, BIN=9
2025-07-07 18:12:19,008 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,011 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,014 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,018 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,023 - INFO -    ✅ 匹配 #71: 第185行, Serial=141, BIN=9
2025-07-07 18:12:19,027 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,031 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,035 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,039 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,042 - INFO -    ✅ 匹配 #72: 第187行, Serial=143, BIN=9
2025-07-07 18:12:19,047 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,051 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,056 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,060 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,064 - INFO -    ✅ 匹配 #73: 第189行, Serial=145, BIN=9
2025-07-07 18:12:19,068 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,072 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,076 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,079 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,082 - INFO -    ✅ 匹配 #74: 第191行, Serial=147, BIN=9
2025-07-07 18:12:19,086 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,091 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,094 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,098 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,102 - INFO -    ✅ 匹配 #75: 第193行, Serial=149, BIN=9
2025-07-07 18:12:19,105 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,108 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,112 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,115 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,118 - INFO -    ✅ 匹配 #76: 第195行, Serial=151, BIN=9
2025-07-07 18:12:19,121 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,124 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,128 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,132 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,136 - INFO -    ✅ 匹配 #77: 第197行, Serial=153, BIN=9
2025-07-07 18:12:19,139 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,142 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,145 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,148 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,151 - INFO -    ✅ 匹配 #78: 第199行, Serial=155, BIN=9
2025-07-07 18:12:19,155 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,158 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,163 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,165 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,169 - INFO -    ✅ 匹配 #79: 第201行, Serial=157, BIN=9
2025-07-07 18:12:19,172 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,175 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,178 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,181 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,184 - INFO -    ✅ 匹配 #80: 第203行, Serial=159, BIN=9
2025-07-07 18:12:19,187 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,190 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,193 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,197 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,200 - INFO -    ✅ 匹配 #81: 第205行, Serial=161, BIN=9
2025-07-07 18:12:19,204 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,208 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,212 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,216 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,221 - INFO -    ✅ 匹配 #82: 第207行, Serial=163, BIN=9
2025-07-07 18:12:19,227 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,231 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,235 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,240 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,244 - INFO -    ✅ 匹配 #83: 第209行, Serial=165, BIN=9
2025-07-07 18:12:19,249 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,254 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,257 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,262 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,267 - INFO -    ✅ 匹配 #84: 第211行, Serial=167, BIN=9
2025-07-07 18:12:19,271 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,276 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,283 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,288 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,293 - INFO -    ✅ 匹配 #85: 第213行, Serial=169, BIN=9
2025-07-07 18:12:19,298 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,302 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,307 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,312 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,317 - INFO -    ✅ 匹配 #86: 第215行, Serial=171, BIN=9
2025-07-07 18:12:19,321 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,325 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,329 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,333 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,337 - INFO -    ✅ 匹配 #87: 第217行, Serial=173, BIN=9
2025-07-07 18:12:19,342 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,347 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,353 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,358 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,364 - INFO -    ✅ 匹配 #88: 第219行, Serial=175, BIN=9
2025-07-07 18:12:19,376 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,383 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,391 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,400 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,408 - INFO -    ✅ 匹配 #89: 第221行, Serial=177, BIN=9
2025-07-07 18:12:19,414 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,419 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,425 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,432 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,438 - INFO -    ✅ 匹配 #90: 第223行, Serial=179, BIN=9
2025-07-07 18:12:19,448 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,460 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,466 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,473 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,477 - INFO -    ✅ 匹配 #91: 第225行, Serial=181, BIN=9
2025-07-07 18:12:19,481 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,486 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,490 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,495 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,499 - INFO -    ✅ 匹配 #92: 第227行, Serial=183, BIN=9
2025-07-07 18:12:19,504 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,509 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,513 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,517 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,523 - INFO -    ✅ 匹配 #93: 第229行, Serial=185, BIN=9
2025-07-07 18:12:19,527 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,531 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,534 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,539 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,543 - INFO -    ✅ 匹配 #94: 第231行, Serial=187, BIN=9
2025-07-07 18:12:19,546 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,550 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,554 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,558 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,562 - INFO -    ✅ 匹配 #95: 第233行, Serial=189, BIN=9
2025-07-07 18:12:19,566 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,570 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,574 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,578 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,583 - INFO -    ✅ 匹配 #96: 第235行, Serial=191, BIN=9
2025-07-07 18:12:19,586 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,590 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,595 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,599 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,603 - INFO -    ✅ 匹配 #97: 第237行, Serial=193, BIN=9
2025-07-07 18:12:19,607 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,612 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,615 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,620 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,624 - INFO -    ✅ 匹配 #98: 第239行, Serial=195, BIN=9
2025-07-07 18:12:19,629 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,634 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,638 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,642 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,647 - INFO -    ✅ 匹配 #99: 第241行, Serial=197, BIN=9
2025-07-07 18:12:19,651 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,655 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,659 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,663 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,668 - INFO -    ✅ 匹配 #100: 第243行, Serial=199, BIN=9
2025-07-07 18:12:19,672 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,677 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,681 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,685 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,689 - INFO -    ✅ 匹配 #101: 第245行, Serial=201, BIN=9
2025-07-07 18:12:19,693 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,697 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,702 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,707 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,713 - INFO -    ✅ 匹配 #102: 第247行, Serial=203, BIN=9
2025-07-07 18:12:19,718 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,723 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,726 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,730 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,734 - INFO -    ✅ 匹配 #103: 第249行, Serial=205, BIN=9
2025-07-07 18:12:19,738 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,742 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,747 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,751 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,756 - INFO -    ✅ 匹配 #104: 第251行, Serial=207, BIN=9
2025-07-07 18:12:19,760 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,764 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,767 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,771 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,776 - INFO -    ✅ 匹配 #105: 第253行, Serial=209, BIN=9
2025-07-07 18:12:19,786 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,790 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,794 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,799 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,804 - INFO -    ✅ 匹配 #106: 第255行, Serial=211, BIN=9
2025-07-07 18:12:19,807 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,811 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,815 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,818 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,821 - INFO -    ✅ 匹配 #107: 第257行, Serial=213, BIN=9
2025-07-07 18:12:19,824 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,828 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,831 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,835 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,839 - INFO -    ✅ 匹配 #108: 第259行, Serial=215, BIN=9
2025-07-07 18:12:19,842 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,846 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,851 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,854 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,859 - INFO -    ✅ 匹配 #109: 第261行, Serial=217, BIN=9
2025-07-07 18:12:19,863 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,878 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,883 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,887 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,891 - INFO -    ✅ 匹配 #110: 第263行, Serial=219, BIN=9
2025-07-07 18:12:19,895 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,899 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,903 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,907 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,910 - INFO -    ✅ 匹配 #111: 第265行, Serial=221, BIN=9
2025-07-07 18:12:19,914 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,917 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,921 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,925 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,929 - INFO -    ✅ 匹配 #112: 第267行, Serial=223, BIN=9
2025-07-07 18:12:19,934 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,939 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,944 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,949 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,953 - INFO -    ✅ 匹配 #113: 第269行, Serial=225, BIN=9
2025-07-07 18:12:19,957 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,960 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,963 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,966 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,971 - INFO -    ✅ 匹配 #114: 第271行, Serial=227, BIN=9
2025-07-07 18:12:19,974 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:19,978 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,983 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:19,988 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:19,993 - INFO -    ✅ 匹配 #115: 第273行, Serial=229, BIN=9
2025-07-07 18:12:19,998 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,002 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,007 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,011 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,016 - INFO -    ✅ 匹配 #116: 第275行, Serial=231, BIN=9
2025-07-07 18:12:20,021 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,027 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,033 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,038 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,043 - INFO -    ✅ 匹配 #117: 第277行, Serial=233, BIN=9
2025-07-07 18:12:20,049 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,053 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,058 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,065 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,069 - INFO -    ✅ 匹配 #118: 第279行, Serial=235, BIN=9
2025-07-07 18:12:20,075 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,081 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,084 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,088 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,093 - INFO -    ✅ 匹配 #119: 第281行, Serial=237, BIN=9
2025-07-07 18:12:20,096 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,101 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,104 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,109 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,113 - INFO -    ✅ 匹配 #120: 第283行, Serial=239, BIN=9
2025-07-07 18:12:20,116 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,119 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,123 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,126 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,131 - INFO -    ✅ 匹配 #121: 第285行, Serial=241, BIN=9
2025-07-07 18:12:20,134 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,137 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,141 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,145 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,149 - INFO -    ✅ 匹配 #122: 第287行, Serial=243, BIN=9
2025-07-07 18:12:20,153 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,158 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,162 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,166 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,170 - INFO -    ✅ 匹配 #123: 第289行, Serial=245, BIN=9
2025-07-07 18:12:20,174 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,177 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,181 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,184 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,188 - INFO -    ✅ 匹配 #124: 第291行, Serial=247, BIN=9
2025-07-07 18:12:20,192 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,196 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,200 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,203 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,207 - INFO -    ✅ 匹配 #125: 第293行, Serial=249, BIN=9
2025-07-07 18:12:20,211 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,215 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,219 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,222 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,226 - INFO -    ✅ 匹配 #126: 第295行, Serial=251, BIN=9
2025-07-07 18:12:20,229 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,233 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,236 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,240 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,244 - INFO -    ✅ 匹配 #127: 第297行, Serial=253, BIN=9
2025-07-07 18:12:20,248 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,252 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,257 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,261 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,264 - INFO -    ✅ 匹配 #128: 第299行, Serial=255, BIN=9
2025-07-07 18:12:20,269 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,274 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,278 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,282 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,287 - INFO -    ✅ 匹配 #129: 第301行, Serial=257, BIN=9
2025-07-07 18:12:20,291 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,294 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,298 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,302 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,306 - INFO -    ✅ 匹配 #130: 第303行, Serial=259, BIN=9
2025-07-07 18:12:20,309 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,313 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,316 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,320 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,324 - INFO -    ✅ 匹配 #131: 第305行, Serial=261, BIN=9
2025-07-07 18:12:20,327 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,331 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,335 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,340 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,343 - INFO -    ✅ 匹配 #132: 第307行, Serial=263, BIN=9
2025-07-07 18:12:20,347 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,350 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,355 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,358 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,361 - INFO -    ✅ 匹配 #133: 第309行, Serial=265, BIN=9
2025-07-07 18:12:20,364 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,368 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,371 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,375 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,378 - INFO -    ✅ 匹配 #134: 第311行, Serial=267, BIN=9
2025-07-07 18:12:20,381 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,385 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,389 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,392 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,396 - INFO -    ✅ 匹配 #135: 第313行, Serial=269, BIN=9
2025-07-07 18:12:20,400 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,404 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,408 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,411 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,415 - INFO -    ✅ 匹配 #136: 第315行, Serial=271, BIN=9
2025-07-07 18:12:20,419 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,423 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,429 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,433 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,437 - INFO -    ✅ 匹配 #137: 第317行, Serial=273, BIN=9
2025-07-07 18:12:20,441 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,445 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,449 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,453 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,458 - INFO -    ✅ 匹配 #138: 第319行, Serial=275, BIN=9
2025-07-07 18:12:20,463 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,466 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,469 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,473 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,477 - INFO -    ✅ 匹配 #139: 第321行, Serial=277, BIN=9
2025-07-07 18:12:20,480 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,485 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,490 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,496 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,500 - INFO -    ✅ 匹配 #140: 第323行, Serial=279, BIN=9
2025-07-07 18:12:20,504 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,509 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,515 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,519 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,523 - INFO -    ✅ 匹配 #141: 第325行, Serial=281, BIN=9
2025-07-07 18:12:20,527 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,531 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,537 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,542 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,551 - INFO -    ✅ 匹配 #142: 第327行, Serial=283, BIN=9
2025-07-07 18:12:20,556 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,560 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,567 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,572 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,576 - INFO -    ✅ 匹配 #143: 第329行, Serial=285, BIN=9
2025-07-07 18:12:20,582 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,586 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,590 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,594 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,599 - INFO -    ✅ 匹配 #144: 第331行, Serial=287, BIN=9
2025-07-07 18:12:20,602 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,606 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,610 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,614 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,618 - INFO -    ✅ 匹配 #145: 第333行, Serial=289, BIN=9
2025-07-07 18:12:20,623 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,627 - INFO -        RT行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,631 - INFO -        FAIL行前5個CODE: ['17', '0', '0', '0', '1']
2025-07-07 18:12:20,635 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,638 - INFO -    🎯 FAIL #9 搜尋結果: 找到 145 個匹配的同一顆 IC
2025-07-07 18:12:20,642 - INFO - 
🎯 【FAIL #10】匹配搜尋:
2025-07-07 18:12:20,645 - INFO -    目標: 第33行, BIN=31
2025-07-07 18:12:20,650 - INFO -    FT行(第32行)備用區間樣本: ['2', '47', '15', '240', '1']...['129', '19', '1'] (顯示前5+後3欄)
2025-07-07 18:12:20,653 - INFO -    CODE 區間樣本: ['2', '47', '15', '240', '1']...['1', '0', '0'] (顯示前5+後3欄)
2025-07-07 18:12:20,657 - INFO -    ✅ 匹配 #1: 第40行, Serial=7, BIN=1
2025-07-07 18:12:20,660 - INFO -        主要區間: 38/38 欄位匹配 (100%)
2025-07-07 18:12:20,664 - INFO -        RT行前5個CODE: ['2', '47', '15', '240', '1']
2025-07-07 18:12:20,668 - INFO -        FAIL行前5個CODE: ['2', '47', '15', '240', '1']
2025-07-07 18:12:20,673 - INFO -        備用區間(FT行→主區): 36/36 欄位匹配 (100%)
2025-07-07 18:12:20,694 - INFO -    🎯 FAIL #10 搜尋結果: 找到 1 個匹配的同一顆 IC
2025-07-07 18:12:20,698 - INFO - 
📊 Step 4 完整搜尋統計:
2025-07-07 18:12:20,702 - INFO -    總 FAIL 行數: 10
2025-07-07 18:12:20,705 - INFO -    總匹配數量: 300
2025-07-07 18:12:20,709 - INFO -    平均每 FAIL: 30.0 個匹配
2025-07-07 18:12:20,712 - INFO - 
🔍 【DEBUG】指定行數的前5個 CODE 對比:
2025-07-07 18:12:20,716 - INFO -    第38行 [Serial=5, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,719 - INFO -    第43行 [Serial=1, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,723 - INFO -    第46行 [Serial=2, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,726 - INFO -    第47行 [Serial=3, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,729 - INFO -    第48行 [Serial=4, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,732 - INFO -    第49行 [Serial=5, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,738 - INFO -    第50行 [Serial=6, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,741 - INFO -    第51行 [Serial=7, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,744 - INFO -    第52行 [Serial=8, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,747 - INFO -    第53行 [Serial=9, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,751 - INFO -    第54行 [Serial=10, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,755 - INFO -    第55行 [Serial=11, BIN=9] 前5個CODE: ['116', '17', '0', '0', '2']
2025-07-07 18:12:20,758 - INFO -    第339行: 超出檔案範圍 (總行數: 336)
2025-07-07 18:12:20,762 - INFO -    第340行: 超出檔案範圍 (總行數: 336)
2025-07-07 18:12:20,770 - INFO -    ✅ Step 4 DEBUG LOG 已完成: EQCTOTALDATA_Step4_DEBUG.log
   ✅ Step 4 完成: 找到 300 個 CODE 匹配
2025-07-07 18:12:20,771 - INFO -    ✅ Step 4 完成: 找到 300 個 CODE 匹配
   📋 DEBUG LOG: EQCTOTALDATA_Step4_DEBUG.log
2025-07-07 18:12:20,771 - INFO -    📋 DEBUG LOG: EQCTOTALDATA_Step4_DEBUG.log
   ✅ Step 4 CODE匹配: 300 個匹配
2025-07-07 18:12:20,772 - INFO -    ✅ Step 4 CODE匹配: 300 個匹配
   📋 Step 4 DEBUG LOG: doc/20250523/EQCTOTALDATA_Step4_DEBUG.log
2025-07-07 18:12:20,772 - INFO -    📋 Step 4 DEBUG LOG: doc/20250523/EQCTOTALDATA_Step4_DEBUG.log
   ✅ InsEqcRtData2 Step 1-2-3-4 處理成功
2025-07-07 18:12:20,773 - INFO -    ✅ InsEqcRtData2 Step 1-2-3-4 處理成功
🧪 步驟5: 執行 Step 5 測試流程生成
2025-07-07 18:12:20,774 - INFO - 🧪 步驟5: 執行 Step 5 測試流程生成
🧪 Step 5: 測試流程生成...
2025-07-07 18:12:20,774 - INFO - 🧪 Step 5: 測試流程生成...
2025-07-07 18:12:20,776 - INFO - 🚀 Step 5: 開始生成測試流程 CSV...
2025-07-07 18:12:20,844 - INFO -    原始 CSV 總行數: 336
2025-07-07 18:12:20,844 - INFO - 🔍 解析 Step 4 DEBUG LOG 中的 FAIL 對應關係...
2025-07-07 18:12:20,856 - INFO -    找到對應 #1-RT1: FT行14 → OnlineEQC行15 → RT1行37
2025-07-07 18:12:20,857 - INFO -    找到對應 #2-RT1: FT行16 → OnlineEQC行17 → RT1行34
2025-07-07 18:12:20,857 - INFO -    找到對應 #3-RT1: FT行18 → OnlineEQC行19 → RT1行41
2025-07-07 18:12:20,858 - INFO -    找到對應 #4-RT1: FT行20 → OnlineEQC行21 → RT1行38
2025-07-07 18:12:20,858 - INFO -    找到對應 #4-RT2: FT行20 → OnlineEQC行21 → RT2行43
2025-07-07 18:12:20,859 - INFO -    找到對應 #4-RT3: FT行20 → OnlineEQC行21 → RT3行46
2025-07-07 18:12:20,859 - INFO -    找到對應 #4-RT4: FT行20 → OnlineEQC行21 → RT4行47
2025-07-07 18:12:20,860 - INFO -    找到對應 #4-RT5: FT行20 → OnlineEQC行21 → RT5行48
2025-07-07 18:12:20,860 - INFO -    找到對應 #4-RT6: FT行20 → OnlineEQC行21 → RT6行49
2025-07-07 18:12:20,860 - INFO -    找到對應 #4-RT7: FT行20 → OnlineEQC行21 → RT7行50
2025-07-07 18:12:20,861 - INFO -    找到對應 #4-RT8: FT行20 → OnlineEQC行21 → RT8行51
2025-07-07 18:12:20,861 - INFO -    找到對應 #4-RT9: FT行20 → OnlineEQC行21 → RT9行52
2025-07-07 18:12:20,861 - INFO -    找到對應 #4-RT10: FT行20 → OnlineEQC行21 → RT10行53
2025-07-07 18:12:20,862 - INFO -    找到對應 #4-RT11: FT行20 → OnlineEQC行21 → RT11行54
2025-07-07 18:12:20,862 - INFO -    找到對應 #4-RT12: FT行20 → OnlineEQC行21 → RT12行55
2025-07-07 18:12:20,862 - INFO -    找到對應 #5-RT1: FT行22 → OnlineEQC行23 → RT1行45
2025-07-07 18:12:20,863 - INFO -    找到對應 #6-RT1: FT行24 → OnlineEQC行25 → RT1行42
2025-07-07 18:12:20,863 - INFO -    找到對應 #6-RT2: FT行24 → OnlineEQC行25 → RT2行44
2025-07-07 18:12:20,864 - INFO -    找到對應 #6-RT3: FT行24 → OnlineEQC行25 → RT3行56
2025-07-07 18:12:20,864 - INFO -    找到對應 #6-RT4: FT行24 → OnlineEQC行25 → RT4行57
2025-07-07 18:12:20,864 - INFO -    找到對應 #6-RT5: FT行24 → OnlineEQC行25 → RT5行58
2025-07-07 18:12:20,864 - INFO -    找到對應 #6-RT6: FT行24 → OnlineEQC行25 → RT6行59
2025-07-07 18:12:20,865 - INFO -    找到對應 #6-RT7: FT行24 → OnlineEQC行25 → RT7行75
2025-07-07 18:12:20,865 - INFO -    找到對應 #6-RT8: FT行24 → OnlineEQC行25 → RT8行76
2025-07-07 18:12:20,865 - INFO -    找到對應 #6-RT9: FT行24 → OnlineEQC行25 → RT9行78
2025-07-07 18:12:20,866 - INFO -    找到對應 #6-RT10: FT行24 → OnlineEQC行25 → RT10行80
2025-07-07 18:12:20,866 - INFO -    找到對應 #6-RT11: FT行24 → OnlineEQC行25 → RT11行82
2025-07-07 18:12:20,866 - INFO -    找到對應 #6-RT12: FT行24 → OnlineEQC行25 → RT12行84
2025-07-07 18:12:20,867 - INFO -    找到對應 #6-RT13: FT行24 → OnlineEQC行25 → RT13行86
2025-07-07 18:12:20,867 - INFO -    找到對應 #6-RT14: FT行24 → OnlineEQC行25 → RT14行88
2025-07-07 18:12:20,867 - INFO -    找到對應 #6-RT15: FT行24 → OnlineEQC行25 → RT15行90
2025-07-07 18:12:20,868 - INFO -    找到對應 #6-RT16: FT行24 → OnlineEQC行25 → RT16行92
2025-07-07 18:12:20,868 - INFO -    找到對應 #6-RT17: FT行24 → OnlineEQC行25 → RT17行94
2025-07-07 18:12:20,868 - INFO -    找到對應 #6-RT18: FT行24 → OnlineEQC行25 → RT18行96
2025-07-07 18:12:20,869 - INFO -    找到對應 #6-RT19: FT行24 → OnlineEQC行25 → RT19行98
2025-07-07 18:12:20,869 - INFO -    找到對應 #6-RT20: FT行24 → OnlineEQC行25 → RT20行100
2025-07-07 18:12:20,869 - INFO -    找到對應 #6-RT21: FT行24 → OnlineEQC行25 → RT21行102
2025-07-07 18:12:20,869 - INFO -    找到對應 #6-RT22: FT行24 → OnlineEQC行25 → RT22行104
2025-07-07 18:12:20,870 - INFO -    找到對應 #6-RT23: FT行24 → OnlineEQC行25 → RT23行106
2025-07-07 18:12:20,870 - INFO -    找到對應 #6-RT24: FT行24 → OnlineEQC行25 → RT24行108
2025-07-07 18:12:20,870 - INFO -    找到對應 #6-RT25: FT行24 → OnlineEQC行25 → RT25行110
2025-07-07 18:12:20,871 - INFO -    找到對應 #6-RT26: FT行24 → OnlineEQC行25 → RT26行112
2025-07-07 18:12:20,871 - INFO -    找到對應 #6-RT27: FT行24 → OnlineEQC行25 → RT27行114
2025-07-07 18:12:20,871 - INFO -    找到對應 #6-RT28: FT行24 → OnlineEQC行25 → RT28行116
2025-07-07 18:12:20,872 - INFO -    找到對應 #6-RT29: FT行24 → OnlineEQC行25 → RT29行118
2025-07-07 18:12:20,872 - INFO -    找到對應 #6-RT30: FT行24 → OnlineEQC行25 → RT30行120
2025-07-07 18:12:20,873 - INFO -    找到對應 #6-RT31: FT行24 → OnlineEQC行25 → RT31行122
2025-07-07 18:12:20,873 - INFO -    找到對應 #6-RT32: FT行24 → OnlineEQC行25 → RT32行124
2025-07-07 18:12:20,873 - INFO -    找到對應 #6-RT33: FT行24 → OnlineEQC行25 → RT33行126
2025-07-07 18:12:20,873 - INFO -    找到對應 #6-RT34: FT行24 → OnlineEQC行25 → RT34行128
2025-07-07 18:12:20,874 - INFO -    找到對應 #6-RT35: FT行24 → OnlineEQC行25 → RT35行130
2025-07-07 18:12:20,875 - INFO -    找到對應 #6-RT36: FT行24 → OnlineEQC行25 → RT36行132
2025-07-07 18:12:20,875 - INFO -    找到對應 #6-RT37: FT行24 → OnlineEQC行25 → RT37行134
2025-07-07 18:12:20,876 - INFO -    找到對應 #6-RT38: FT行24 → OnlineEQC行25 → RT38行136
2025-07-07 18:12:20,876 - INFO -    找到對應 #6-RT39: FT行24 → OnlineEQC行25 → RT39行138
2025-07-07 18:12:20,876 - INFO -    找到對應 #6-RT40: FT行24 → OnlineEQC行25 → RT40行140
2025-07-07 18:12:20,877 - INFO -    找到對應 #6-RT41: FT行24 → OnlineEQC行25 → RT41行142
2025-07-07 18:12:20,877 - INFO -    找到對應 #6-RT42: FT行24 → OnlineEQC行25 → RT42行144
2025-07-07 18:12:20,877 - INFO -    找到對應 #6-RT43: FT行24 → OnlineEQC行25 → RT43行146
2025-07-07 18:12:20,878 - INFO -    找到對應 #6-RT44: FT行24 → OnlineEQC行25 → RT44行148
2025-07-07 18:12:20,878 - INFO -    找到對應 #6-RT45: FT行24 → OnlineEQC行25 → RT45行150
2025-07-07 18:12:20,878 - INFO -    找到對應 #6-RT46: FT行24 → OnlineEQC行25 → RT46行152
2025-07-07 18:12:20,879 - INFO -    找到對應 #6-RT47: FT行24 → OnlineEQC行25 → RT47行154
2025-07-07 18:12:20,879 - INFO -    找到對應 #6-RT48: FT行24 → OnlineEQC行25 → RT48行156
2025-07-07 18:12:20,879 - INFO -    找到對應 #6-RT49: FT行24 → OnlineEQC行25 → RT49行158
2025-07-07 18:12:20,880 - INFO -    找到對應 #6-RT50: FT行24 → OnlineEQC行25 → RT50行160
2025-07-07 18:12:20,880 - INFO -    找到對應 #6-RT51: FT行24 → OnlineEQC行25 → RT51行162
2025-07-07 18:12:20,880 - INFO -    找到對應 #6-RT52: FT行24 → OnlineEQC行25 → RT52行164
2025-07-07 18:12:20,881 - INFO -    找到對應 #6-RT53: FT行24 → OnlineEQC行25 → RT53行166
2025-07-07 18:12:20,881 - INFO -    找到對應 #6-RT54: FT行24 → OnlineEQC行25 → RT54行168
2025-07-07 18:12:20,881 - INFO -    找到對應 #6-RT55: FT行24 → OnlineEQC行25 → RT55行170
2025-07-07 18:12:20,882 - INFO -    找到對應 #6-RT56: FT行24 → OnlineEQC行25 → RT56行172
2025-07-07 18:12:20,882 - INFO -    找到對應 #6-RT57: FT行24 → OnlineEQC行25 → RT57行174
2025-07-07 18:12:20,882 - INFO -    找到對應 #6-RT58: FT行24 → OnlineEQC行25 → RT58行176
2025-07-07 18:12:20,883 - INFO -    找到對應 #6-RT59: FT行24 → OnlineEQC行25 → RT59行178
2025-07-07 18:12:20,883 - INFO -    找到對應 #6-RT60: FT行24 → OnlineEQC行25 → RT60行180
2025-07-07 18:12:20,883 - INFO -    找到對應 #6-RT61: FT行24 → OnlineEQC行25 → RT61行182
2025-07-07 18:12:20,884 - INFO -    找到對應 #6-RT62: FT行24 → OnlineEQC行25 → RT62行184
2025-07-07 18:12:20,884 - INFO -    找到對應 #6-RT63: FT行24 → OnlineEQC行25 → RT63行186
2025-07-07 18:12:20,884 - INFO -    找到對應 #6-RT64: FT行24 → OnlineEQC行25 → RT64行188
2025-07-07 18:12:20,884 - INFO -    找到對應 #6-RT65: FT行24 → OnlineEQC行25 → RT65行190
2025-07-07 18:12:20,885 - INFO -    找到對應 #6-RT66: FT行24 → OnlineEQC行25 → RT66行192
2025-07-07 18:12:20,885 - INFO -    找到對應 #6-RT67: FT行24 → OnlineEQC行25 → RT67行194
2025-07-07 18:12:20,885 - INFO -    找到對應 #6-RT68: FT行24 → OnlineEQC行25 → RT68行196
2025-07-07 18:12:20,886 - INFO -    找到對應 #6-RT69: FT行24 → OnlineEQC行25 → RT69行198
2025-07-07 18:12:20,886 - INFO -    找到對應 #6-RT70: FT行24 → OnlineEQC行25 → RT70行200
2025-07-07 18:12:20,886 - INFO -    找到對應 #6-RT71: FT行24 → OnlineEQC行25 → RT71行202
2025-07-07 18:12:20,887 - INFO -    找到對應 #6-RT72: FT行24 → OnlineEQC行25 → RT72行204
2025-07-07 18:12:20,887 - INFO -    找到對應 #6-RT73: FT行24 → OnlineEQC行25 → RT73行206
2025-07-07 18:12:20,887 - INFO -    找到對應 #6-RT74: FT行24 → OnlineEQC行25 → RT74行208
2025-07-07 18:12:20,887 - INFO -    找到對應 #6-RT75: FT行24 → OnlineEQC行25 → RT75行210
2025-07-07 18:12:20,888 - INFO -    找到對應 #6-RT76: FT行24 → OnlineEQC行25 → RT76行212
2025-07-07 18:12:20,888 - INFO -    找到對應 #6-RT77: FT行24 → OnlineEQC行25 → RT77行214
2025-07-07 18:12:20,888 - INFO -    找到對應 #6-RT78: FT行24 → OnlineEQC行25 → RT78行216
2025-07-07 18:12:20,888 - INFO -    找到對應 #6-RT79: FT行24 → OnlineEQC行25 → RT79行218
2025-07-07 18:12:20,889 - INFO -    找到對應 #6-RT80: FT行24 → OnlineEQC行25 → RT80行220
2025-07-07 18:12:20,889 - INFO -    找到對應 #6-RT81: FT行24 → OnlineEQC行25 → RT81行222
2025-07-07 18:12:20,890 - INFO -    找到對應 #6-RT82: FT行24 → OnlineEQC行25 → RT82行224
2025-07-07 18:12:20,890 - INFO -    找到對應 #6-RT83: FT行24 → OnlineEQC行25 → RT83行226
2025-07-07 18:12:20,891 - INFO -    找到對應 #6-RT84: FT行24 → OnlineEQC行25 → RT84行228
2025-07-07 18:12:20,891 - INFO -    找到對應 #6-RT85: FT行24 → OnlineEQC行25 → RT85行230
2025-07-07 18:12:20,891 - INFO -    找到對應 #6-RT86: FT行24 → OnlineEQC行25 → RT86行232
2025-07-07 18:12:20,892 - INFO -    找到對應 #6-RT87: FT行24 → OnlineEQC行25 → RT87行234
2025-07-07 18:12:20,892 - INFO -    找到對應 #6-RT88: FT行24 → OnlineEQC行25 → RT88行236
2025-07-07 18:12:20,892 - INFO -    找到對應 #6-RT89: FT行24 → OnlineEQC行25 → RT89行238
2025-07-07 18:12:20,892 - INFO -    找到對應 #6-RT90: FT行24 → OnlineEQC行25 → RT90行240
2025-07-07 18:12:20,893 - INFO -    找到對應 #6-RT91: FT行24 → OnlineEQC行25 → RT91行242
2025-07-07 18:12:20,893 - INFO -    找到對應 #6-RT92: FT行24 → OnlineEQC行25 → RT92行244
2025-07-07 18:12:20,893 - INFO -    找到對應 #6-RT93: FT行24 → OnlineEQC行25 → RT93行246
2025-07-07 18:12:20,894 - INFO -    找到對應 #6-RT94: FT行24 → OnlineEQC行25 → RT94行248
2025-07-07 18:12:20,895 - INFO -    找到對應 #6-RT95: FT行24 → OnlineEQC行25 → RT95行250
2025-07-07 18:12:20,896 - INFO -    找到對應 #6-RT96: FT行24 → OnlineEQC行25 → RT96行252
2025-07-07 18:12:20,896 - INFO -    找到對應 #6-RT97: FT行24 → OnlineEQC行25 → RT97行254
2025-07-07 18:12:20,897 - INFO -    找到對應 #6-RT98: FT行24 → OnlineEQC行25 → RT98行256
2025-07-07 18:12:20,897 - INFO -    找到對應 #6-RT99: FT行24 → OnlineEQC行25 → RT99行258
2025-07-07 18:12:20,898 - INFO -    找到對應 #6-RT100: FT行24 → OnlineEQC行25 → RT100行260
2025-07-07 18:12:20,898 - INFO -    找到對應 #6-RT101: FT行24 → OnlineEQC行25 → RT101行262
2025-07-07 18:12:20,899 - INFO -    找到對應 #6-RT102: FT行24 → OnlineEQC行25 → RT102行264
2025-07-07 18:12:20,899 - INFO -    找到對應 #6-RT103: FT行24 → OnlineEQC行25 → RT103行266
2025-07-07 18:12:20,899 - INFO -    找到對應 #6-RT104: FT行24 → OnlineEQC行25 → RT104行268
2025-07-07 18:12:20,899 - INFO -    找到對應 #6-RT105: FT行24 → OnlineEQC行25 → RT105行270
2025-07-07 18:12:20,903 - INFO -    找到對應 #6-RT106: FT行24 → OnlineEQC行25 → RT106行272
2025-07-07 18:12:20,904 - INFO -    找到對應 #6-RT107: FT行24 → OnlineEQC行25 → RT107行274
2025-07-07 18:12:20,905 - INFO -    找到對應 #6-RT108: FT行24 → OnlineEQC行25 → RT108行276
2025-07-07 18:12:20,905 - INFO -    找到對應 #6-RT109: FT行24 → OnlineEQC行25 → RT109行278
2025-07-07 18:12:20,906 - INFO -    找到對應 #6-RT110: FT行24 → OnlineEQC行25 → RT110行280
2025-07-07 18:12:20,906 - INFO -    找到對應 #6-RT111: FT行24 → OnlineEQC行25 → RT111行282
2025-07-07 18:12:20,907 - INFO -    找到對應 #6-RT112: FT行24 → OnlineEQC行25 → RT112行284
2025-07-07 18:12:20,907 - INFO -    找到對應 #6-RT113: FT行24 → OnlineEQC行25 → RT113行286
2025-07-07 18:12:20,907 - INFO -    找到對應 #6-RT114: FT行24 → OnlineEQC行25 → RT114行288
2025-07-07 18:12:20,908 - INFO -    找到對應 #6-RT115: FT行24 → OnlineEQC行25 → RT115行290
2025-07-07 18:12:20,908 - INFO -    找到對應 #6-RT116: FT行24 → OnlineEQC行25 → RT116行292
2025-07-07 18:12:20,909 - INFO -    找到對應 #6-RT117: FT行24 → OnlineEQC行25 → RT117行294
2025-07-07 18:12:20,909 - INFO -    找到對應 #6-RT118: FT行24 → OnlineEQC行25 → RT118行296
2025-07-07 18:12:20,911 - INFO -    找到對應 #6-RT119: FT行24 → OnlineEQC行25 → RT119行298
2025-07-07 18:12:20,911 - INFO -    找到對應 #6-RT120: FT行24 → OnlineEQC行25 → RT120行300
2025-07-07 18:12:20,912 - INFO -    找到對應 #6-RT121: FT行24 → OnlineEQC行25 → RT121行302
2025-07-07 18:12:20,912 - INFO -    找到對應 #6-RT122: FT行24 → OnlineEQC行25 → RT122行304
2025-07-07 18:12:20,913 - INFO -    找到對應 #6-RT123: FT行24 → OnlineEQC行25 → RT123行306
2025-07-07 18:12:20,913 - INFO -    找到對應 #6-RT124: FT行24 → OnlineEQC行25 → RT124行308
2025-07-07 18:12:20,914 - INFO -    找到對應 #6-RT125: FT行24 → OnlineEQC行25 → RT125行310
2025-07-07 18:12:20,914 - INFO -    找到對應 #6-RT126: FT行24 → OnlineEQC行25 → RT126行312
2025-07-07 18:12:20,914 - INFO -    找到對應 #6-RT127: FT行24 → OnlineEQC行25 → RT127行314
2025-07-07 18:12:20,915 - INFO -    找到對應 #6-RT128: FT行24 → OnlineEQC行25 → RT128行316
2025-07-07 18:12:20,915 - INFO -    找到對應 #6-RT129: FT行24 → OnlineEQC行25 → RT129行318
2025-07-07 18:12:20,917 - INFO -    找到對應 #6-RT130: FT行24 → OnlineEQC行25 → RT130行320
2025-07-07 18:12:20,919 - INFO -    找到對應 #6-RT131: FT行24 → OnlineEQC行25 → RT131行322
2025-07-07 18:12:20,927 - INFO -    找到對應 #6-RT132: FT行24 → OnlineEQC行25 → RT132行324
2025-07-07 18:12:20,934 - INFO -    找到對應 #6-RT133: FT行24 → OnlineEQC行25 → RT133行326
2025-07-07 18:12:20,935 - INFO -    找到對應 #6-RT134: FT行24 → OnlineEQC行25 → RT134行328
2025-07-07 18:12:20,935 - INFO -    找到對應 #6-RT135: FT行24 → OnlineEQC行25 → RT135行330
2025-07-07 18:12:20,937 - INFO -    找到對應 #6-RT136: FT行24 → OnlineEQC行25 → RT136行332
2025-07-07 18:12:20,942 - INFO -    找到對應 #7-RT1: FT行26 → OnlineEQC行27 → RT1行35
2025-07-07 18:12:20,943 - INFO -    找到對應 #8-RT1: FT行28 → OnlineEQC行29 → RT1行36
2025-07-07 18:12:20,943 - INFO -    找到對應 #9-RT1: FT行30 → OnlineEQC行31 → RT1行39
2025-07-07 18:12:20,944 - INFO -    找到對應 #9-RT2: FT行30 → OnlineEQC行31 → RT2行60
2025-07-07 18:12:20,944 - INFO -    找到對應 #9-RT3: FT行30 → OnlineEQC行31 → RT3行61
2025-07-07 18:12:20,945 - INFO -    找到對應 #9-RT4: FT行30 → OnlineEQC行31 → RT4行62
2025-07-07 18:12:20,945 - INFO -    找到對應 #9-RT5: FT行30 → OnlineEQC行31 → RT5行63
2025-07-07 18:12:20,946 - INFO -    找到對應 #9-RT6: FT行30 → OnlineEQC行31 → RT6行64
2025-07-07 18:12:20,946 - INFO -    找到對應 #9-RT7: FT行30 → OnlineEQC行31 → RT7行65
2025-07-07 18:12:20,947 - INFO -    找到對應 #9-RT8: FT行30 → OnlineEQC行31 → RT8行66
2025-07-07 18:12:20,947 - INFO -    找到對應 #9-RT9: FT行30 → OnlineEQC行31 → RT9行67
2025-07-07 18:12:20,947 - INFO -    找到對應 #9-RT10: FT行30 → OnlineEQC行31 → RT10行68
2025-07-07 18:12:20,948 - INFO -    找到對應 #9-RT11: FT行30 → OnlineEQC行31 → RT11行69
2025-07-07 18:12:20,948 - INFO -    找到對應 #9-RT12: FT行30 → OnlineEQC行31 → RT12行70
2025-07-07 18:12:20,949 - INFO -    找到對應 #9-RT13: FT行30 → OnlineEQC行31 → RT13行71
2025-07-07 18:12:20,949 - INFO -    找到對應 #9-RT14: FT行30 → OnlineEQC行31 → RT14行72
2025-07-07 18:12:20,949 - INFO -    找到對應 #9-RT15: FT行30 → OnlineEQC行31 → RT15行73
2025-07-07 18:12:20,950 - INFO -    找到對應 #9-RT16: FT行30 → OnlineEQC行31 → RT16行74
2025-07-07 18:12:20,950 - INFO -    找到對應 #9-RT17: FT行30 → OnlineEQC行31 → RT17行77
2025-07-07 18:12:20,950 - INFO -    找到對應 #9-RT18: FT行30 → OnlineEQC行31 → RT18行79
2025-07-07 18:12:20,951 - INFO -    找到對應 #9-RT19: FT行30 → OnlineEQC行31 → RT19行81
2025-07-07 18:12:20,951 - INFO -    找到對應 #9-RT20: FT行30 → OnlineEQC行31 → RT20行83
2025-07-07 18:12:20,951 - INFO -    找到對應 #9-RT21: FT行30 → OnlineEQC行31 → RT21行85
2025-07-07 18:12:20,952 - INFO -    找到對應 #9-RT22: FT行30 → OnlineEQC行31 → RT22行87
2025-07-07 18:12:20,952 - INFO -    找到對應 #9-RT23: FT行30 → OnlineEQC行31 → RT23行89
2025-07-07 18:12:20,952 - INFO -    找到對應 #9-RT24: FT行30 → OnlineEQC行31 → RT24行91
2025-07-07 18:12:20,953 - INFO -    找到對應 #9-RT25: FT行30 → OnlineEQC行31 → RT25行93
2025-07-07 18:12:20,953 - INFO -    找到對應 #9-RT26: FT行30 → OnlineEQC行31 → RT26行95
2025-07-07 18:12:20,953 - INFO -    找到對應 #9-RT27: FT行30 → OnlineEQC行31 → RT27行97
2025-07-07 18:12:20,954 - INFO -    找到對應 #9-RT28: FT行30 → OnlineEQC行31 → RT28行99
2025-07-07 18:12:20,954 - INFO -    找到對應 #9-RT29: FT行30 → OnlineEQC行31 → RT29行101
2025-07-07 18:12:20,955 - INFO -    找到對應 #9-RT30: FT行30 → OnlineEQC行31 → RT30行103
2025-07-07 18:12:20,955 - INFO -    找到對應 #9-RT31: FT行30 → OnlineEQC行31 → RT31行105
2025-07-07 18:12:20,955 - INFO -    找到對應 #9-RT32: FT行30 → OnlineEQC行31 → RT32行107
2025-07-07 18:12:20,956 - INFO -    找到對應 #9-RT33: FT行30 → OnlineEQC行31 → RT33行109
2025-07-07 18:12:20,956 - INFO -    找到對應 #9-RT34: FT行30 → OnlineEQC行31 → RT34行111
2025-07-07 18:12:20,956 - INFO -    找到對應 #9-RT35: FT行30 → OnlineEQC行31 → RT35行113
2025-07-07 18:12:20,957 - INFO -    找到對應 #9-RT36: FT行30 → OnlineEQC行31 → RT36行115
2025-07-07 18:12:20,957 - INFO -    找到對應 #9-RT37: FT行30 → OnlineEQC行31 → RT37行117
2025-07-07 18:12:20,957 - INFO -    找到對應 #9-RT38: FT行30 → OnlineEQC行31 → RT38行119
2025-07-07 18:12:20,958 - INFO -    找到對應 #9-RT39: FT行30 → OnlineEQC行31 → RT39行121
2025-07-07 18:12:20,959 - INFO -    找到對應 #9-RT40: FT行30 → OnlineEQC行31 → RT40行123
2025-07-07 18:12:20,959 - INFO -    找到對應 #9-RT41: FT行30 → OnlineEQC行31 → RT41行125
2025-07-07 18:12:20,959 - INFO -    找到對應 #9-RT42: FT行30 → OnlineEQC行31 → RT42行127
2025-07-07 18:12:20,960 - INFO -    找到對應 #9-RT43: FT行30 → OnlineEQC行31 → RT43行129
2025-07-07 18:12:20,961 - INFO -    找到對應 #9-RT44: FT行30 → OnlineEQC行31 → RT44行131
2025-07-07 18:12:20,962 - INFO -    找到對應 #9-RT45: FT行30 → OnlineEQC行31 → RT45行133
2025-07-07 18:12:20,962 - INFO -    找到對應 #9-RT46: FT行30 → OnlineEQC行31 → RT46行135
2025-07-07 18:12:20,963 - INFO -    找到對應 #9-RT47: FT行30 → OnlineEQC行31 → RT47行137
2025-07-07 18:12:20,963 - INFO -    找到對應 #9-RT48: FT行30 → OnlineEQC行31 → RT48行139
2025-07-07 18:12:20,963 - INFO -    找到對應 #9-RT49: FT行30 → OnlineEQC行31 → RT49行141
2025-07-07 18:12:20,964 - INFO -    找到對應 #9-RT50: FT行30 → OnlineEQC行31 → RT50行143
2025-07-07 18:12:20,964 - INFO -    找到對應 #9-RT51: FT行30 → OnlineEQC行31 → RT51行145
2025-07-07 18:12:20,964 - INFO -    找到對應 #9-RT52: FT行30 → OnlineEQC行31 → RT52行147
2025-07-07 18:12:20,965 - INFO -    找到對應 #9-RT53: FT行30 → OnlineEQC行31 → RT53行149
2025-07-07 18:12:20,965 - INFO -    找到對應 #9-RT54: FT行30 → OnlineEQC行31 → RT54行151
2025-07-07 18:12:20,965 - INFO -    找到對應 #9-RT55: FT行30 → OnlineEQC行31 → RT55行153
2025-07-07 18:12:20,966 - INFO -    找到對應 #9-RT56: FT行30 → OnlineEQC行31 → RT56行155
2025-07-07 18:12:20,966 - INFO -    找到對應 #9-RT57: FT行30 → OnlineEQC行31 → RT57行157
2025-07-07 18:12:20,966 - INFO -    找到對應 #9-RT58: FT行30 → OnlineEQC行31 → RT58行159
2025-07-07 18:12:20,967 - INFO -    找到對應 #9-RT59: FT行30 → OnlineEQC行31 → RT59行161
2025-07-07 18:12:20,967 - INFO -    找到對應 #9-RT60: FT行30 → OnlineEQC行31 → RT60行163
2025-07-07 18:12:20,967 - INFO -    找到對應 #9-RT61: FT行30 → OnlineEQC行31 → RT61行165
2025-07-07 18:12:20,968 - INFO -    找到對應 #9-RT62: FT行30 → OnlineEQC行31 → RT62行167
2025-07-07 18:12:20,968 - INFO -    找到對應 #9-RT63: FT行30 → OnlineEQC行31 → RT63行169
2025-07-07 18:12:20,968 - INFO -    找到對應 #9-RT64: FT行30 → OnlineEQC行31 → RT64行171
2025-07-07 18:12:20,969 - INFO -    找到對應 #9-RT65: FT行30 → OnlineEQC行31 → RT65行173
2025-07-07 18:12:20,969 - INFO -    找到對應 #9-RT66: FT行30 → OnlineEQC行31 → RT66行175
2025-07-07 18:12:20,970 - INFO -    找到對應 #9-RT67: FT行30 → OnlineEQC行31 → RT67行177
2025-07-07 18:12:20,970 - INFO -    找到對應 #9-RT68: FT行30 → OnlineEQC行31 → RT68行179
2025-07-07 18:12:20,970 - INFO -    找到對應 #9-RT69: FT行30 → OnlineEQC行31 → RT69行181
2025-07-07 18:12:20,971 - INFO -    找到對應 #9-RT70: FT行30 → OnlineEQC行31 → RT70行183
2025-07-07 18:12:20,971 - INFO -    找到對應 #9-RT71: FT行30 → OnlineEQC行31 → RT71行185
2025-07-07 18:12:20,971 - INFO -    找到對應 #9-RT72: FT行30 → OnlineEQC行31 → RT72行187
2025-07-07 18:12:20,972 - INFO -    找到對應 #9-RT73: FT行30 → OnlineEQC行31 → RT73行189
2025-07-07 18:12:20,972 - INFO -    找到對應 #9-RT74: FT行30 → OnlineEQC行31 → RT74行191
2025-07-07 18:12:20,972 - INFO -    找到對應 #9-RT75: FT行30 → OnlineEQC行31 → RT75行193
2025-07-07 18:12:20,972 - INFO -    找到對應 #9-RT76: FT行30 → OnlineEQC行31 → RT76行195
2025-07-07 18:12:20,973 - INFO -    找到對應 #9-RT77: FT行30 → OnlineEQC行31 → RT77行197
2025-07-07 18:12:20,973 - INFO -    找到對應 #9-RT78: FT行30 → OnlineEQC行31 → RT78行199
2025-07-07 18:12:20,976 - INFO -    找到對應 #9-RT79: FT行30 → OnlineEQC行31 → RT79行201
2025-07-07 18:12:20,977 - INFO -    找到對應 #9-RT80: FT行30 → OnlineEQC行31 → RT80行203
2025-07-07 18:12:20,977 - INFO -    找到對應 #9-RT81: FT行30 → OnlineEQC行31 → RT81行205
2025-07-07 18:12:20,978 - INFO -    找到對應 #9-RT82: FT行30 → OnlineEQC行31 → RT82行207
2025-07-07 18:12:20,978 - INFO -    找到對應 #9-RT83: FT行30 → OnlineEQC行31 → RT83行209
2025-07-07 18:12:20,979 - INFO -    找到對應 #9-RT84: FT行30 → OnlineEQC行31 → RT84行211
2025-07-07 18:12:20,979 - INFO -    找到對應 #9-RT85: FT行30 → OnlineEQC行31 → RT85行213
2025-07-07 18:12:20,980 - INFO -    找到對應 #9-RT86: FT行30 → OnlineEQC行31 → RT86行215
2025-07-07 18:12:20,980 - INFO -    找到對應 #9-RT87: FT行30 → OnlineEQC行31 → RT87行217
2025-07-07 18:12:20,980 - INFO -    找到對應 #9-RT88: FT行30 → OnlineEQC行31 → RT88行219
2025-07-07 18:12:20,981 - INFO -    找到對應 #9-RT89: FT行30 → OnlineEQC行31 → RT89行221
2025-07-07 18:12:20,981 - INFO -    找到對應 #9-RT90: FT行30 → OnlineEQC行31 → RT90行223
2025-07-07 18:12:20,981 - INFO -    找到對應 #9-RT91: FT行30 → OnlineEQC行31 → RT91行225
2025-07-07 18:12:20,982 - INFO -    找到對應 #9-RT92: FT行30 → OnlineEQC行31 → RT92行227
2025-07-07 18:12:20,982 - INFO -    找到對應 #9-RT93: FT行30 → OnlineEQC行31 → RT93行229
2025-07-07 18:12:20,983 - INFO -    找到對應 #9-RT94: FT行30 → OnlineEQC行31 → RT94行231
2025-07-07 18:12:20,983 - INFO -    找到對應 #9-RT95: FT行30 → OnlineEQC行31 → RT95行233
2025-07-07 18:12:20,984 - INFO -    找到對應 #9-RT96: FT行30 → OnlineEQC行31 → RT96行235
2025-07-07 18:12:20,984 - INFO -    找到對應 #9-RT97: FT行30 → OnlineEQC行31 → RT97行237
2025-07-07 18:12:20,984 - INFO -    找到對應 #9-RT98: FT行30 → OnlineEQC行31 → RT98行239
2025-07-07 18:12:20,984 - INFO -    找到對應 #9-RT99: FT行30 → OnlineEQC行31 → RT99行241
2025-07-07 18:12:20,985 - INFO -    找到對應 #9-RT100: FT行30 → OnlineEQC行31 → RT100行243
2025-07-07 18:12:20,985 - INFO -    找到對應 #9-RT101: FT行30 → OnlineEQC行31 → RT101行245
2025-07-07 18:12:20,985 - INFO -    找到對應 #9-RT102: FT行30 → OnlineEQC行31 → RT102行247
2025-07-07 18:12:20,985 - INFO -    找到對應 #9-RT103: FT行30 → OnlineEQC行31 → RT103行249
2025-07-07 18:12:20,986 - INFO -    找到對應 #9-RT104: FT行30 → OnlineEQC行31 → RT104行251
2025-07-07 18:12:20,986 - INFO -    找到對應 #9-RT105: FT行30 → OnlineEQC行31 → RT105行253
2025-07-07 18:12:20,986 - INFO -    找到對應 #9-RT106: FT行30 → OnlineEQC行31 → RT106行255
2025-07-07 18:12:20,987 - INFO -    找到對應 #9-RT107: FT行30 → OnlineEQC行31 → RT107行257
2025-07-07 18:12:20,987 - INFO -    找到對應 #9-RT108: FT行30 → OnlineEQC行31 → RT108行259
2025-07-07 18:12:20,987 - INFO -    找到對應 #9-RT109: FT行30 → OnlineEQC行31 → RT109行261
2025-07-07 18:12:20,987 - INFO -    找到對應 #9-RT110: FT行30 → OnlineEQC行31 → RT110行263
2025-07-07 18:12:20,988 - INFO -    找到對應 #9-RT111: FT行30 → OnlineEQC行31 → RT111行265
2025-07-07 18:12:20,988 - INFO -    找到對應 #9-RT112: FT行30 → OnlineEQC行31 → RT112行267
2025-07-07 18:12:20,989 - INFO -    找到對應 #9-RT113: FT行30 → OnlineEQC行31 → RT113行269
2025-07-07 18:12:20,989 - INFO -    找到對應 #9-RT114: FT行30 → OnlineEQC行31 → RT114行271
2025-07-07 18:12:20,989 - INFO -    找到對應 #9-RT115: FT行30 → OnlineEQC行31 → RT115行273
2025-07-07 18:12:20,990 - INFO -    找到對應 #9-RT116: FT行30 → OnlineEQC行31 → RT116行275
2025-07-07 18:12:20,991 - INFO -    找到對應 #9-RT117: FT行30 → OnlineEQC行31 → RT117行277
2025-07-07 18:12:20,992 - INFO -    找到對應 #9-RT118: FT行30 → OnlineEQC行31 → RT118行279
2025-07-07 18:12:20,992 - INFO -    找到對應 #9-RT119: FT行30 → OnlineEQC行31 → RT119行281
2025-07-07 18:12:20,993 - INFO -    找到對應 #9-RT120: FT行30 → OnlineEQC行31 → RT120行283
2025-07-07 18:12:20,994 - INFO -    找到對應 #9-RT121: FT行30 → OnlineEQC行31 → RT121行285
2025-07-07 18:12:20,994 - INFO -    找到對應 #9-RT122: FT行30 → OnlineEQC行31 → RT122行287
2025-07-07 18:12:20,995 - INFO -    找到對應 #9-RT123: FT行30 → OnlineEQC行31 → RT123行289
2025-07-07 18:12:20,996 - INFO -    找到對應 #9-RT124: FT行30 → OnlineEQC行31 → RT124行291
2025-07-07 18:12:20,997 - INFO -    找到對應 #9-RT125: FT行30 → OnlineEQC行31 → RT125行293
2025-07-07 18:12:20,998 - INFO -    找到對應 #9-RT126: FT行30 → OnlineEQC行31 → RT126行295
2025-07-07 18:12:20,998 - INFO -    找到對應 #9-RT127: FT行30 → OnlineEQC行31 → RT127行297
2025-07-07 18:12:20,998 - INFO -    找到對應 #9-RT128: FT行30 → OnlineEQC行31 → RT128行299
2025-07-07 18:12:20,999 - INFO -    找到對應 #9-RT129: FT行30 → OnlineEQC行31 → RT129行301
2025-07-07 18:12:20,999 - INFO -    找到對應 #9-RT130: FT行30 → OnlineEQC行31 → RT130行303
2025-07-07 18:12:20,999 - INFO -    找到對應 #9-RT131: FT行30 → OnlineEQC行31 → RT131行305
2025-07-07 18:12:21,000 - INFO -    找到對應 #9-RT132: FT行30 → OnlineEQC行31 → RT132行307
2025-07-07 18:12:21,000 - INFO -    找到對應 #9-RT133: FT行30 → OnlineEQC行31 → RT133行309
2025-07-07 18:12:21,000 - INFO -    找到對應 #9-RT134: FT行30 → OnlineEQC行31 → RT134行311
2025-07-07 18:12:21,001 - INFO -    找到對應 #9-RT135: FT行30 → OnlineEQC行31 → RT135行313
2025-07-07 18:12:21,001 - INFO -    找到對應 #9-RT136: FT行30 → OnlineEQC行31 → RT136行315
2025-07-07 18:12:21,001 - INFO -    找到對應 #9-RT137: FT行30 → OnlineEQC行31 → RT137行317
2025-07-07 18:12:21,002 - INFO -    找到對應 #9-RT138: FT行30 → OnlineEQC行31 → RT138行319
2025-07-07 18:12:21,002 - INFO -    找到對應 #9-RT139: FT行30 → OnlineEQC行31 → RT139行321
2025-07-07 18:12:21,002 - INFO -    找到對應 #9-RT140: FT行30 → OnlineEQC行31 → RT140行323
2025-07-07 18:12:21,003 - INFO -    找到對應 #9-RT141: FT行30 → OnlineEQC行31 → RT141行325
2025-07-07 18:12:21,003 - INFO -    找到對應 #9-RT142: FT行30 → OnlineEQC行31 → RT142行327
2025-07-07 18:12:21,003 - INFO -    找到對應 #9-RT143: FT行30 → OnlineEQC行31 → RT143行329
2025-07-07 18:12:21,004 - INFO -    找到對應 #9-RT144: FT行30 → OnlineEQC行31 → RT144行331
2025-07-07 18:12:21,004 - INFO -    找到對應 #9-RT145: FT行30 → OnlineEQC行31 → RT145行333
2025-07-07 18:12:21,004 - INFO -    找到對應 #10-RT1: FT行32 → OnlineEQC行33 → RT1行40
2025-07-07 18:12:21,005 - INFO -    解析到 300 個 FAIL 對應關係
2025-07-07 18:12:21,005 - INFO -    標頭行數: 13
2025-07-07 18:12:21,005 - INFO -    資料行數: 323
2025-07-07 18:12:21,006 - INFO - 🔄 重新排列資料行生成測試流程...
2025-07-07 18:12:21,067 - INFO -    🎯 動態邊界計算: B9 FAIL數=10, Online EQC索引範圍=0-19, RT開始索引=20
2025-07-07 18:12:21,068 - INFO -    分析結果: FT區域 0 行, OnlineEQC區域 20 行, RT區域 303 行
2025-07-07 18:12:21,068 - INFO -    線性流程: 第14行 → 第15行 → RT1行37
2025-07-07 18:12:21,068 - INFO -    線性流程: 第16行 → 第17行 → RT1行34
2025-07-07 18:12:21,068 - INFO -    線性流程: 第18行 → 第19行 → RT1行41
2025-07-07 18:12:21,069 - INFO -    線性流程: 第20行 → 第21行 → RT1行38 → RT2行43 → RT3行46 → RT4行47 → RT5行48 → RT6行49 → RT7行50 → RT8行51 → RT9行52 → RT10行53 → RT11行54 → RT12行55
2025-07-07 18:12:21,069 - INFO -    線性流程: 第22行 → 第23行 → RT1行45
2025-07-07 18:12:21,069 - INFO -    線性流程: 第24行 → 第25行 → RT1行42 → RT2行44 → RT3行56 → RT4行57 → RT5行58 → RT6行59 → RT7行75 → RT8行76 → RT9行78 → RT10行80 → RT11行82 → RT12行84 → RT13行86 → RT14行88 → RT15行90 → RT16行92 → RT17行94 → RT18行96 → RT19行98 → RT20行100 → RT21行102 → RT22行104 → RT23行106 → RT24行108 → RT25行110 → RT26行112 → RT27行114 → RT28行116 → RT29行118 → RT30行120 → RT31行122 → RT32行124 → RT33行126 → RT34行128 → RT35行130 → RT36行132 → RT37行134 → RT38行136 → RT39行138 → RT40行140 → RT41行142 → RT42行144 → RT43行146 → RT44行148 → RT45行150 → RT46行152 → RT47行154 → RT48行156 → RT49行158 → RT50行160 → RT51行162 → RT52行164 → RT53行166 → RT54行168 → RT55行170 → RT56行172 → RT57行174 → RT58行176 → RT59行178 → RT60行180 → RT61行182 → RT62行184 → RT63行186 → RT64行188 → RT65行190 → RT66行192 → RT67行194 → RT68行196 → RT69行198 → RT70行200 → RT71行202 → RT72行204 → RT73行206 → RT74行208 → RT75行210 → RT76行212 → RT77行214 → RT78行216 → RT79行218 → RT80行220 → RT81行222 → RT82行224 → RT83行226 → RT84行228 → RT85行230 → RT86行232 → RT87行234 → RT88行236 → RT89行238 → RT90行240 → RT91行242 → RT92行244 → RT93行246 → RT94行248 → RT95行250 → RT96行252 → RT97行254 → RT98行256 → RT99行258 → RT100行260 → RT101行262 → RT102行264 → RT103行266 → RT104行268 → RT105行270 → RT106行272 → RT107行274 → RT108行276 → RT109行278 → RT110行280 → RT111行282 → RT112行284 → RT113行286 → RT114行288 → RT115行290 → RT116行292 → RT117行294 → RT118行296 → RT119行298 → RT120行300 → RT121行302 → RT122行304 → RT123行306 → RT124行308 → RT125行310 → RT126行312 → RT127行314 → RT128行316 → RT129行318 → RT130行320 → RT131行322 → RT132行324 → RT133行326 → RT134行328 → RT135行330 → RT136行332
2025-07-07 18:12:21,070 - INFO -    線性流程: 第26行 → 第27行 → RT1行35
2025-07-07 18:12:21,070 - INFO -    線性流程: 第28行 → 第29行 → RT1行36
2025-07-07 18:12:21,070 - INFO -    線性流程: 第30行 → 第31行 → RT1行39 → RT2行60 → RT3行61 → RT4行62 → RT5行63 → RT6行64 → RT7行65 → RT8行66 → RT9行67 → RT10行68 → RT11行69 → RT12行70 → RT13行71 → RT14行72 → RT15行73 → RT16行74 → RT17行77 → RT18行79 → RT19行81 → RT20行83 → RT21行85 → RT22行87 → RT23行89 → RT24行91 → RT25行93 → RT26行95 → RT27行97 → RT28行99 → RT29行101 → RT30行103 → RT31行105 → RT32行107 → RT33行109 → RT34行111 → RT35行113 → RT36行115 → RT37行117 → RT38行119 → RT39行121 → RT40行123 → RT41行125 → RT42行127 → RT43行129 → RT44行131 → RT45行133 → RT46行135 → RT47行137 → RT48行139 → RT49行141 → RT50行143 → RT51行145 → RT52行147 → RT53行149 → RT54行151 → RT55行153 → RT56行155 → RT57行157 → RT58行159 → RT59行161 → RT60行163 → RT61行165 → RT62行167 → RT63行169 → RT64行171 → RT65行173 → RT66行175 → RT67行177 → RT68行179 → RT69行181 → RT70行183 → RT71行185 → RT72行187 → RT73行189 → RT74行191 → RT75行193 → RT76行195 → RT77行197 → RT78行199 → RT79行201 → RT80行203 → RT81行205 → RT82行207 → RT83行209 → RT84行211 → RT85行213 → RT86行215 → RT87行217 → RT88行219 → RT89行221 → RT90行223 → RT91行225 → RT92行227 → RT93行229 → RT94行231 → RT95行233 → RT96行235 → RT97行237 → RT98行239 → RT99行241 → RT100行243 → RT101行245 → RT102行247 → RT103行249 → RT104行251 → RT105行253 → RT106行255 → RT107行257 → RT108行259 → RT109行261 → RT110行263 → RT111行265 → RT112行267 → RT113行269 → RT114行271 → RT115行273 → RT116行275 → RT117行277 → RT118行279 → RT119行281 → RT120行283 → RT121行285 → RT122行287 → RT123行289 → RT124行291 → RT125行293 → RT126行295 → RT127行297 → RT128行299 → RT129行301 → RT130行303 → RT131行305 → RT132行307 → RT133行309 → RT134行311 → RT135行313 → RT136行315 → RT137行317 → RT138行319 → RT139行321 → RT140行323 → RT141行325 → RT142行327 → RT143行329 → RT144行331 → RT145行333
2025-07-07 18:12:21,071 - INFO -    線性流程: 第32行 → 第33行 → RT1行40
2025-07-07 18:12:21,071 - INFO -    ✅ 重新排列完成: 320 行參與重排, 總計 323 行
2025-07-07 18:12:21,071 - INFO -    📊 新位置統計: Online EQC 10 行, EQC RT 300 行
2025-07-07 18:12:21,072 - INFO -    🎯 final_online_eqc_rt_rows: [15, 16, 18, 19, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 179, 180, 182, 183, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 215, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 260, 261, 262, 263, 264, 265, 266, 267, 268, 269, 270, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 281, 282, 283, 284, 285, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 332, 333]
2025-07-07 18:12:21,153 - INFO -    ✅ 測試流程 CSV 已生成: EQCTOTALDATA_Step5_TestFlow_20250707_181221.csv
2025-07-07 18:12:21,153 - INFO -    輸出總行數: 336
2025-07-07 18:12:21,153 - INFO -    重新排列行數: 320
2025-07-07 18:12:21,157 - INFO -    🗑️ 已刪除舊的 EQCTOTALDATA.csv
2025-07-07 18:12:21,161 - INFO -    📝 已重命名: EQCTOTALDATA_Step5_TestFlow_20250707_181221.csv → EQCTOTALDATA.csv
   ✅ Step 5 完成: 生成 EQCTOTALDATA.csv
2025-07-07 18:12:21,161 - INFO -    ✅ Step 5 完成: 生成 EQCTOTALDATA.csv
   總行數: 336
2025-07-07 18:12:21,163 - INFO -    總行數: 336
   重新排列行數: 320
2025-07-07 18:12:21,163 - INFO -    重新排列行數: 320
   FAIL 對應關係: 300 個
2025-07-07 18:12:21,164 - INFO -    FAIL 對應關係: 300 個
   ✅ Step 5 測試流程生成成功
2025-07-07 18:12:21,164 - INFO -    ✅ Step 5 測試流程生成成功
📊 步驟6: 執行 Step 6 Excel 生成與黃色標記
2025-07-07 18:12:21,165 - INFO - 📊 步驟6: 執行 Step 6 Excel 生成與黃色標記
📊 Step 6: Excel 生成與黃色標記...
2025-07-07 18:12:21,165 - INFO - 📊 Step 6: Excel 生成與黃色標記...
   準備 EQC 格式參數...
2025-07-07 18:12:21,168 - INFO -    準備 EQC 格式參數...
   黃色標記行數: 310 行
2025-07-07 18:12:21,169 - INFO -    黃色標記行數: 310 行
   黃色標記範圍: 第1欄 到 第335欄
2025-07-07 18:12:21,169 - INFO -    黃色標記範圍: 第1欄 到 第335欄
   粗體格式: 第8行，第298-335欄
2025-07-07 18:12:21,170 - INFO -    粗體格式: 第8行，第298-335欄
      區間102: 第1440-1440欄 (1個連續整數) ⚪ (微型區間)
      區間103: 第1442-1442欄 (1個連續整數) ⚪ (微型區間)
      區間104: 第1444-1444欄 (1個連續整數) ⚪ (微型區間)
      區間105: 第1446-1446欄 (1個連續整數) ⚪ (微型區間)
      區間106: 第1448-1448欄 (1個連續整數) ⚪ (微型區間)
      區間107: 第1450-1450欄 (1個連續整數) ⚪ (微型區間)
      區間108: 第1452-1452欄 (1個連續整數) ⚪ (微型區間)
      區間109: 第1454-1454欄 (1個連續整數) ⚪ (微型區間)
      區間110: 第1511-1638欄 (128個連續整數) ✅ (大型區間)
   
   ✅ 採用最長區間: 第146-335欄 (190個連續整數)
      驗證條件: start > 10 ✓, length > 5 ✓
      總計發現: 110 個連續整數區間
🔍 [eqc_simple_detector.py] find_code_region() - 開始執行
🚀 開始 CSV到Excel轉換 (記憶體: 217.2 MB)
🚀 開始核心 7 步驟轉換 (進階性能管理): /mnt/d/download/project/outlook_summary/doc/20250523/EQCTOTALDATA.csv
================================================================================
CSV 讀取成功: 336 行 x 1694 欄
執行步驟 1: CSV 結構補全
結構補全: 行7 C欄 空白 → '0.00.01'
結構補全: 行8 C欄 空白 → 'Test_Time'
結構補全: 行10 C欄 空白 → 'none'
結構補全: 行11 C欄 空白 → 'none'
✅ CSV 結構補全完成
執行步驟 2: BIN 號碼分配
BIN 分配: C6 → BIN 5
BIN 分配: D6 → BIN 6
BIN 分配: E6 → BIN 7
BIN 分配: F6 → BIN 8
BIN 分配: G6 → BIN 9
BIN 分配: H6 → BIN 10
BIN 分配: I6 → BIN 11
BIN 分配: J6 → BIN 12
BIN 分配: K6 → BIN 13
BIN 分配: L6 → BIN 14
  ... (省略中間 BIN 分配) ...
BIN 分配: Col16896 → BIN 1691
BIN 分配: Col16906 → BIN 1692
BIN 分配: Col16916 → BIN 1693
BIN 分配: Col16926 → BIN 1694
BIN 分配: Col16936 → BIN 1695
BIN 分配: Col16946 → BIN 1696
✅ BIN 號碼分配完成
執行步驟 3: BIN1 保護機制 (向量化優化版本)
🔍 發現 18 個 BIN1 設備，識別時間: 0.002 秒
📊 批量資料提取完成: 0.002 秒，18 設備 × 1692 測試項目
✅ 限值有效性檢查完成: 0.006 秒，1269 個有效測試項目
🛡️ 保護邏輯檢查完成: 0.082 秒，保護 28 個測試項目

🎉 向量化 BIN1 保護完成統計:
  ✅ 檢查 18 個 BIN1 設備
  ⚡ 總處理時間: 0.091 秒
  📊 檢查次數: 22,842 次
  🚀 處理速度: 250,383 次檢查/秒
  🛡️ 保護項目: 28 個
  📈 性能提升: 向量化優化相比逐一檢查

📝 BIN1 保護記錄已附加到: datalog.txt
✅ BIN1 保護機制完成，共 56 個位置需要紅色標記
執行步驟 4: 全設備上下限檢查與 BIN 分類 (向量化優化版本)
📊 向量化預處理階段...
  資料規模: 324 設備 × 1692 測試項目 = 548,208 次比較
  預處理完成: 0.426 秒
⚡ 向量化測試比較階段...
  向量化比較完成: 0.007 秒
🎯 向量化 BIN 分配階段...
  BIN 分配完成: 0.001 秒
📝 失敗位置記錄階段...
  位置記錄完成: 0.492 秒

🎉 向量化 BIN 分配完成統計:
  ✅ 完成 324 個設備的 BIN 分配
  ⚡ 總處理時間: 0.926 秒
  📊 比較次數: 548,208 次
  🚀 處理速度: 592,177 次比較/秒
  🔴 需要標紅色的位置: 139291 個
  📈 性能提升: 向量化優化相比逐一處理
🔍 執行 Site 欄位動態查找
📝 Site 查找記錄已附加到: datalog.txt
✅ 找到 Site 欄位: E欄 (index 4) = 'Site_No'
\n🎯 執行策略 B - Site 統計分析 (存檔前)
📊 執行 Site 統計分析 - 純統計模式
📁 檔案: EQCTOTALDATA.csv
🔍 使用指定的 Site 欄位: 第5欄
📊 策略 B - 步驟 3: 掃描 B 欄 BIN + Site 統計
🔍 開始掃描 B 欄 BIN 號碼...
  設備 13: Site 2, BIN 1
  設備 14: Site 1, BIN 1
  設備 15: Site 1, BIN 601
  設備 16: Site 1, BIN 1
  設備 17: Site 1, BIN 1
✅ 完成 324 個設備的 B 欄掃描
📊 發現 2 個不同的 Site
🎯 總計: PASS 18 個, FAIL 306 個

📋 各 Site 統計摘要:
  Site 2: 總計 157, PASS 8, FAIL 149
  Site 1: 總計 167, PASS 10, FAIL 157
📝 Site 統計記錄已附加到: datalog.txt
✅ Site 統計分析完成！處理時間: 0.01 秒
\n📊 生成 Summary 工作表
🎯 生成 Summary 工作表: EQCTOTALDATA.csv
📊 BIN 統計計算: 設備資料從第13行開始，BIN 欄位在第2欄
📊 BIN 統計: 總設備 324 個，BIN 分佈 {1: 18, 601: 141, 1367: 1, 1565: 2, 298: 157, 1277: 1, 1333: 1, 102: 2, 127: 1}
📊 Site 統計計算: 設備資料從第13行開始，Site 欄位在第5欄，BIN 欄位在第2欄
📊 Site 統計: 2 個 Site，詳細資料 {2: {'total': 157, 'pass': 8, 'fail': 149, 'pass_rate': 5.095541401273886, 'bins': {1: 8, 1565: 2, 298: 141, 1277: 1, 601: 2, 102: 2, 127: 1}}, 1: {'total': 167, 'pass': 10, 'fail': 157, 'pass_rate': 5.9880239520958085, 'bins': {1: 10, 601: 139, 1367: 1, 298: 16, 1333: 1}}}
📋 BIN Definition 對應: 1684 個 BIN 定義
✅ Summary 生成完成: 9 個 BIN, 2 個 Site
🚀 使用 xlsxwriter 建立包含 Summary 的 Excel 檔案: /mnt/d/download/project/outlook_summary/doc/20250523/EQCTOTALDATA_Step6_HighlightedEQCRT_20250707_181221.xlsx
📋 步驟 1: 創建主資料工作表...
  📊 寫入主資料工作表數據...
🚀 執行向量化智慧轉換...
  📊 處理第13行往下 324 行資料...
  ✅ 向量化智慧轉換完成: 2.060 秒
  🔍 紅色位置分析: BIN1保護 56 個, 測試失敗 139291 個, 總計 139347 個
    🚀 混合模式寫入 336 行 × 1694 欄的數據...
    ⚡ 批量寫入完成: 1.706 秒
    🔴 紅色格式重寫完成: 4.369 秒，處理 139347 個位置
      🟡 套用黃色標記: 310 行，範圍 1-335 (跳過 B 欄和 C 欄超連結)
      🔥 套用粗體格式: 第8行，欄位 298-335
      🔗 套用 C 欄超連結: 從第14行開始
    🎨 EQC 自訂格式完成: 2.655 秒，處理 103901 個位置
    🔍 分析 139347 個失敗位置...
    🎯 發現 306 個設備有失敗項目
        調試 1: 設備行14 失敗欄位[598, 601, 602]...
        調試 2: 設備行17 失敗欄位[598, 601, 602]...
        調試 3: 設備行20 失敗欄位[1364, 1386, 1388]...
        調試超連結: 設備行12 BIN=1 在失敗列表=False
        調試超連結: 設備行13 BIN=1 在失敗列表=False
        調試超連結: 設備行14 BIN=601 在失敗列表=True
      🔗 創建超連結 1: 設備15 BIN 601 → internal:'EQCTOTALDATA_Step6_HighlightedE'!WA15
        調試超連結: 設備行15 BIN=1 在失敗列表=False
        調試超連結: 設備行16 BIN=1 在失敗列表=False
        調試超連結: 設備行17 BIN=601 在失敗列表=True
      🔗 創建超連結 2: 設備18 BIN 601 → internal:'EQCTOTALDATA_Step6_HighlightedE'!WA18
        調試超連結: 設備行18 BIN=1 在失敗列表=False
        調試超連結: 設備行19 BIN=1 在失敗列表=False
        調試超連結: 設備行20 BIN=1367 在失敗列表=True
      🔗 創建超連結 3: 設備21 BIN 1367 → internal:'EQCTOTALDATA_Step6_HighlightedE'!AZM21
      🔗 創建超連結 4: 設備24 BIN 1565 → internal:'EQCTOTALDATA_Step6_HighlightedE'!BHC24
      🔗 創建超連結 5: 設備25 BIN 298 → internal:'EQCTOTALDATA_Step6_HighlightedE'!KJ25
    ✅ 超連結創建完成，共 306 個
    🔗 BIN 超連結完成: 0.088 秒，處理 306 個連結
    🧊 凍結視窗完成: 0.000 秒 (凍結 C12 到 Site 欄位第5欄，左上角顯示 C6)
    ✅ 主資料寫入完成，總時間: 6.163 秒
✅ 主資料工作表創建完成
📋 步驟 2: 創建 Summary 工作表...
  📊 寫入 Summary 工作表數據...
    批量填入基本統計行 (第1-4行)...
    批量填入 Site 總計行 (第5行)...
    批量填入標頭行 (第6行)...
    批量填入 BIN 資料行...
    ⚡ 批量數據寫入完成: 0.000 秒
    批量填入完整 BIN 列表...
    📊 空 BIN 列表填入完成: 0.112 秒，添加 1675 個空 BIN
    調整欄位寬度...
    📐 欄位寬度調整完成: 0.000 秒
    ✅ Summary 數據寫入完成，總時間: 0.112 秒，Definition 欄寬度: 23
✅ Summary 工作表創建完成
   ✅ Step 6 完成: 生成 EQCTOTALDATA_Step6_HighlightedEQCRT_20250707_181221.xlsx
2025-07-07 18:12:36,027 - INFO -    ✅ Step 6 完成: 生成 EQCTOTALDATA_Step6_HighlightedEQCRT_20250707_181221.xlsx
   標記行數: 310 行
2025-07-07 18:12:36,028 - INFO -    標記行數: 310 行
   標記範圍: 第1欄 到 第335欄
2025-07-07 18:12:36,029 - INFO -    標記範圍: 第1欄 到 第335欄
   處理時間: 14.632 秒
2025-07-07 18:12:36,030 - INFO -    處理時間: 14.632 秒
   🗑️ 已刪除舊的 EQCTOTALDATA.xlsx
2025-07-07 18:12:36,040 - INFO -    🗑️ 已刪除舊的 EQCTOTALDATA.xlsx
   📝 已重命名: EQCTOTALDATA_Step6_HighlightedEQCRT_20250707_181221.xlsx → EQCTOTALDATA.xlsx
2025-07-07 18:12:36,050 - INFO -    📝 已重命名: EQCTOTALDATA_Step6_HighlightedEQCRT_20250707_181221.xlsx → EQCTOTALDATA.xlsx
   ✅ 重命名成功，檔案大小: 2273050 bytes
2025-07-07 18:12:36,057 - INFO -    ✅ 重命名成功，檔案大小: 2273050 bytes
   ✅ Step 6 Excel 生成成功
2025-07-07 18:12:36,058 - INFO -    ✅ Step 6 Excel 生成成功
📈 步驟7: 執行最終 CSV 到 Excel 轉換
2025-07-07 18:12:36,058 - INFO - 📈 步驟7: 執行最終 CSV 到 Excel 轉換
⏩ 暫時跳過最終 Excel 轉換（功能已註解，測試中）
2025-07-07 18:12:36,059 - INFO - ⏩ 暫時跳過最終 Excel 轉換（功能已註解，測試中）
   ⚠️ 最終 Excel 轉換失敗: None
2025-07-07 18:12:36,060 - WARNING -    ⚠️ 最終 Excel 轉換失敗: None
📝 生成最終處理報告...
2025-07-07 18:12:36,060 - INFO - 📝 生成最終處理報告...
📝 生成最終處理報告...
2025-07-07 18:12:36,061 - INFO - 📝 生成最終處理報告...
📋 報告已生成: EQC_Stage2_Report_20250707_181236.txt
2025-07-07 18:12:36,067 - INFO - 📋 報告已生成: EQC_Stage2_Report_20250707_181236.txt
✅ 第二階段EQC處理流程完成
2025-07-07 18:12:36,068 - INFO - ✅ 第二階段EQC處理流程完成
2025-07-07 18:12:36.068 | INFO     | src.presentation.api.services.api_utils:log_api_success:543 - ✅ API 端點成功: process_eqc_advanced
2025-07-07 18:12:36.069 | INFO     | src.presentation.api.services.api_utils:log_api_success:545 -    📊 結果摘要: 第二階段處理完成，耗時 22.07秒
2025-07-07 18:12:36.069 | INFO     | src.presentation.api.services.api_utils:format_eqc_standard_response:301 - 🎯 API 使用動態檢測的備用區間: 第1565-1600欄 (36個欄位)
📋 步驟 3: 保存 Excel 檔案...
✅ xlsxwriter Excel 檔案保存完成

🔍 驗證 Excel 檔案: /mnt/d/download/project/outlook_summary/doc/20250523/EQCTOTALDATA_Step6_HighlightedEQCRT_20250707_181221.xlsx
✅ Excel 檔案已成功創建，大小: 2,273,050 bytes
\n================================================================================
⚡ 詳細性能分析報告
================================================================================
📊 各步驟處理時間 (按耗時排序):
   1. 步驟8_Excel輸出 :   13.248 秒 ( 90.5%)
   2. 步驟5_設備BIN分類 :    0.926 秒 (  6.3%)
   3. 步驟1_CSV讀取   :    0.135 秒 (  0.9%)
   4. 步驟4_BIN1保護  :    0.107 秒 (  0.7%)
   5. 步驟7_Summary生成:    0.099 秒 (  0.7%)
   6. 步驟3_BIN分配   :    0.078 秒 (  0.5%)
   7. 步驟6_Site統計  :    0.038 秒 (  0.3%)
   8. 步驟2_結構補全    :    0.001 秒 (  0.0%)
\n🎯 總處理時間: 14.632 秒
\n🔍 性能瓶頸分析:
  前 3 大耗時步驟佔總時間: 97.8%
    🎯 瓶頸 1: 步驟8_Excel輸出 (90.5%)
    🎯 瓶頸 2: 步驟5_設備BIN分類 (6.3%)
    🎯 瓶頸 3: 步驟1_CSV讀取 (0.9%)
\n💡 進一步優化建議:
  🔧 Excel 輸出是主要瓶頸，建議優化 openpyxl 操作
================================================================================

📝 性能記錄已附加到: logs/datalog.txt
✅ CSV到Excel轉換 完成 - 耗時: 14.745秒, 記憶體: 236.1MB
INFO:     127.0.0.1:44996 - "POST /api/process_eqc_advanced HTTP/1.1" 200 OK
2025-07-07 18:12:36.669 | INFO     | src.presentation.api.services.api_utils:log_api_start:536 - 🚀 API 端點開始: analyze_real_data
2025-07-07 18:12:36.670 | DEBUG    | src.presentation.api.services.api_utils:log_api_start:538 -    📋 請求數據: {'folder_path': 'D:\\download\\project\\outlook_summary\\doc\\20250523'}
2025-07-07 18:12:36.670 | INFO     | src.presentation.api.services.api_utils:process_folder_path:65 - 🔄 路徑轉換: D:\download\project\outlook_summary\doc\20250523 -> /mnt/d/download/project/outlook_summary/doc/20250523
2025-07-07 18:12:41.256 | INFO     | src.presentation.api.services.api_utils:parse_summary_sheet_data:201 - 📋 Summary 解析完成: 9 個 BIN 項目
2025-07-07 18:12:41.264 | INFO     | src.presentation.api.services.api_utils:count_debug_matches:225 - 📋 Debug 日誌匹配記錄: 300 個
2025-07-07 18:12:41.268 | INFO     | src.presentation.api.services.api_utils:log_api_success:543 - ✅ API 端點成功: analyze_real_data
2025-07-07 18:12:41.268 | INFO     | src.presentation.api.services.api_utils:log_api_success:545 -    📊 結果摘要: 分析完成: FAIL=10, PASS=7
🔍 [eqc_processing_service.py] analyze_real_data() - 開始執行
INFO:     127.0.0.1:44996 - "POST /api/analyze_eqc_real_data HTTP/1.1" 200 OK
2025-07-07 18:12:41.280 | INFO     | src.presentation.api.services.api_utils:log_api_start:536 - 🚀 API 端點開始: get_today_processed_files
2025-07-07 18:12:41.280 | INFO     | src.presentation.api.services.file_management_service:get_today_processed_files:280 - 掃描範圍: 2025-07-07 00:00:00 到 2025-07-07 23:59:59.999999
2025-07-07 18:12:41.380 | INFO     | src.presentation.api.services.api_utils:log_api_success:543 - ✅ API 端點成功: get_today_processed_files
2025-07-07 18:12:41.381 | INFO     | src.presentation.api.services.api_utils:log_api_success:545 -    📊 結果摘要: 找到 1 個記錄
INFO:     127.0.0.1:44996 - "GET /api/today_processed_files HTTP/1.1" 200 OK
INFO:     Shutting down
INFO:     Waiting for application shutdown.
2025-07-07 19:12:57.513 | INFO     | src.presentation.api.ft_eqc_api:shutdown_event:949 - 🛑 FT-EQC API 服務正在關閉...
2025-07-07 19:12:57.515 | INFO     | src.presentation.api.services.file_cleanup_scheduler:stop_scheduler:135 - ⏹️ 檔案清理調度器已停止
2025-07-07 19:12:57.516 | INFO     | src.presentation.api.ft_eqc_api:shutdown_event:956 - ✅ 背景清理調度器已優雅關閉
2025-07-07 19:12:57.516 | INFO     | src.presentation.api.ft_eqc_api:shutdown_event:962 - ✅ 服務關閉完成
INFO:     Application shutdown complete.
INFO:     Finished server process [34434]
