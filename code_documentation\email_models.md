# email_models.py

郵件數據模型模組，使用 Pydantic 建立強型別的數據模型，支援郵件解析、廠商識別、任務管理。

## ProcessingStatus

處理狀態枚舉類別，定義郵件處理的各種狀態。

### 枚舉值
- `PENDING`: 等待處理
- `PROCESSING`: 處理中
- `COMPLETED`: 已完成
- `FAILED`: 處理失敗

## EmailAttachment

郵件附件模型，用於表示郵件中的附件資訊。

### 屬性
- `filename` (str): 附件檔名，必填，最小長度為1
- `content_type` (str): MIME 類型，必填
- `size_bytes` (int): 檔案大小（位元組），必填，必須大於0
- `file_path` (Path): 檔案路徑，必填
- `is_processed` (bool): 是否已處理，預設為False
- `checksum` (Optional[str]): 檔案校驗和，可選

### 類別常數
- `ALLOWED_EXTENSIONS`: 允許的副檔名集合 {".csv", ".xlsx", ".xls", ".zip", ".txt"}
- `MAX_FILE_SIZE`: 檔案大小限制 (100MB)

### validate_file_size

驗證檔案大小是否超過限制。

**參數:**
- `v` (int): 檔案大小（位元組）

**返回值:**
- int: 驗證通過的檔案大小

**異常:**
- ValueError: 當檔案大小超過100MB時拋出

### validate_filename

驗證檔名和副檔名是否符合規範。

**參數:**
- `v` (str): 檔案名稱

**返回值:**
- str: 驗證通過的檔案名稱

**異常:**
- ValueError: 當檔名為空或副檔名不被允許時拋出

## EmailData

郵件數據模型，表示完整的郵件資訊。

### 屬性
- `message_id` (str): 郵件 ID，必填
- `subject` (str): 郵件主旨，必填，最小長度為1
- `sender` (str): 寄件者，必填
- `recipient` (Optional[str]): 收件者，可選
- `received_time` (datetime): 接收時間，必填
- `body` (Optional[str]): 郵件內容，可選
- `attachments` (List[EmailAttachment]): 附件列表，預設為空列表
- `headers` (Dict[str, str]): 郵件標頭，預設為空字典
- `raw_data` (Dict[str, Any]): 原始數據，預設為空字典

### validate_subject

驗證郵件主旨不能為空。

**參數:**
- `v` (str): 郵件主旨

**返回值:**
- str: 去[EXCEPT_CHAR]前後空白的郵件主旨

**異常:**
- ValueError: 當主旨為空時拋出

### validate_sender

驗證寄件者 email 格式（支援中文字元）。

**參數:**
- `v` (str): 寄件者 email

**返回值:**
- str: 驗證通過的 email

**異常:**
- ValueError: 當 email 格式不正確時拋出

### add_attachment

新增附件到郵件中。

**參數:**
- `attachment` (EmailAttachment): 要新增的附件

**返回值:**
- None

### get_attachment_count

取得附件數量。

**返回值:**
- int: 附件數量

### get_total_attachment_size

取得所有附件的總大小。

**返回值:**
- int: 所有附件的總大小（位元組）

## VendorIdentificationResult

廠商識別結果模型，用於記錄廠商識別的結果。

### 屬性
- `vendor_code` (Optional[str]): 廠商代碼，可選
- `vendor_name` (Optional[str]): 廠商名稱，可選
- `confidence_score` (float): 信心分數 (0-1)，必填
- `matching_patterns` (List[str]): 匹配的模式，預設為空列表
- `is_identified` (bool): 是否成功識別，預設為False
- `identification_method` (Optional[str]): 識別方法，可選

### validate_confidence_score

驗證信心分數範圍。

**參數:**
- `v` (float): 信心分數

**返回值:**
- float: 驗證通過的信心分數

**異常:**
- ValueError: 當信心分數不在0.0到1.0之間時拋出

### validate_identification_consistency

驗證識別結果的一致性。

**返回值:**
- VendorIdentificationResult: 驗證通過的實例

**異常:**
- ValueError: 當識別成功但未提供廠商代碼時拋出

## EmailParsingResult

郵件解析結果模型，記錄郵件解析的結果和相關資訊。

### 屬性
- `is_success` (bool): 是否解析成功，必填
- `vendor_code` (Optional[str]): 廠商代碼，可選
- `mo_number` (Optional[str]): MO 編號，可選
- `lot_number` (Optional[str]): 批次編號，可選
- `test_type` (Optional[str]): 測試類型，可選
- `extracted_data` (Dict[str, Any]): 解析出的數據，預設為空字典
- `error_message` (Optional[str]): 錯誤訊息，可選
- `validation_errors` (List[str]): 驗證錯誤，預設為空列表
- `parsing_method` (Optional[str]): 解析方法，可選

### 類別常數
- `MO_NUMBER_PATTERN`: MO 編號格式模式 (r'^[A-Z]\d{6}$')

### validate_mo_number

驗證 MO 編號格式。

**參數:**
- `v` (Optional[str]): MO 編號

**返回值:**
- Optional[str]: 驗證通過的 MO 編號

**異常:**
- ValueError: 當 MO 編號格式不正確時拋出

### validate_parsing_consistency

驗證解析結果的一致性。

**返回值:**
- EmailParsingResult: 驗證通過的實例

**異常:**
- ValueError: 當解析成功但未提供廠商代碼，或解析失敗但未提供錯誤訊息時拋出

### add_validation_error

新增驗證錯誤到錯誤列表中。

**參數:**
- `error` (str): 錯誤訊息

**返回值:**
- None

### has_validation_errors

檢查是否有驗證錯誤。

**返回值:**
- bool: 如果有驗證錯誤返回True，否則返回False

## EmailMetadata

郵件元數據模型，用於儲存郵件的額外資訊。

### 屬性
- `message_size_bytes` (Optional[int]): 郵件大小，可選，必須大於等於0
- `attachment_count` (Optional[int]): 附件數量，可選，必須大於等於0
- `processing_priority` (int): 處理優先級 (1-10)，預設為5
- `source_folder` (Optional[str]): 來源資料夾，可選
- `backup_location` (Optional[Path]): 備份位置，可選
- `tags` (List[str]): 標籤，預設為空列表
- `custom_fields` (Dict[str, Any]): 自訂欄位，預設為空字典

### validate_priority

驗證處理優先級範圍。

**參數:**
- `v` (int): 處理優先級

**返回值:**
- int: 驗證通過的處理優先級

**異常:**
- ValueError: 當優先級不在1到10之間時拋出

## TaskData

任務數據模型，用於管理郵件處理任務的生命週期。

### 屬性
- `task_id` (str): 任務 ID，必填
- `email_id` (str): 關聯的郵件 ID，必填
- `vendor_code` (str): 廠商代碼，必填
- `mo_number` (Optional[str]): MO 編號，可選
- `lot_number` (Optional[str]): 批次編號，可選
- `status` (ProcessingStatus): 處理狀態，預設為PENDING
- `created_time` (datetime): 建立時間，必填
- `started_time` (Optional[datetime]): 開始時間，可選
- `completed_time` (Optional[datetime]): 完成時間，可選
- `error_message` (Optional[str]): 錯誤訊息，可選
- `result_data` (Dict[str, Any]): 處理結果，預設為空字典
- `retry_count` (int): 重試次數，預設為0，必須大於等於0
- `max_retries` (int): 最大重試次數，預設為3，必須大於等於0

### is_completed

檢查任務是否已完成。

**返回值:**
- bool: 如果任務狀態為COMPLETED返回True，否則返回False

### is_failed

檢查任務是否失敗。

**返回值:**
- bool: 如果任務狀態為FAILED返回True，否則返回False

### can_retry

檢查任務是否可以重試。

**返回值:**
- bool: 如果重試次數未達上限且任務失敗返回True，否則返回False

### mark_started

標記任務為開始處理狀態。

**返回值:**
- None

### mark_completed

標記任務為完成狀態。

**參數:**
- `result_data` (Optional[Dict[str, Any]]): 處理結果數據，可選

**返回值:**
- None

### mark_failed

標記任務為失敗狀態。

**參數:**
- `error_message` (str): 錯誤訊息

**返回值:**
- None

## FileProcessingInfo

檔案處理資訊模型，用於追蹤檔案處理的詳細資訊。

### 屬性
- `source_file` (Path): 來源檔案路徑，必填
- `processed_file` (Optional[Path]): 處理後檔案路徑，可選
- `file_type` (str): 檔案類型，必填
- `file_size_bytes` (int): 檔案大小，必填，必須大於0
- `processing_steps` (List[str]): 處理步驟，必填，最小長度為1
- `started_time` (Optional[datetime]): 處理開始時間，可選
- `completed_time` (Optional[datetime]): 處理完成時間，可選
- `is_success` (bool): 是否處理成功，預設為False
- `error_message` (Optional[str]): 錯誤訊息，可選
- `metadata` (Dict[str, Any]): 檔案元數據，預設為空字典

### validate_processing_steps

驗證處理步驟不能為空。

**參數:**
- `v` (List[str]): 處理步驟列表

**返回值:**
- List[str]: 驗證通過的處理步驟列表

**異常:**
- ValueError: 當處理步驟列表為空時拋出

### add_processing_step

新增處理步驟到步驟列表中。

**參數:**
- `step` (str): 處理步驟描述

**返回值:**
- None

### mark_started

標記檔案處理開始。

**返回值:**
- None

### mark_completed

標記檔案處理完成。

**參數:**
- `processed_file` (Optional[Path]): 處理後的檔案路徑，可選

**返回值:**
- None

### mark_failed

標記檔案處理失敗。

**參數:**
- `error_message` (str): 錯誤訊息

**返回值:**
- None

## EmailProcessingContext

郵件處理上下文模型，整合所有郵件處理相關的資訊。

### 屬性
- `email_data` (EmailData): 郵件數據，必填
- `vendor_identification` (Optional[VendorIdentificationResult]): 廠商識別結果，可選
- `parsing_result` (Optional[EmailParsingResult]): 解析結果，可選
- `task_data` (Optional[TaskData]): 任務數據，可選
- `file_processing_info` (List[FileProcessingInfo]): 檔案處理資訊，預設為空列表
- `metadata` (EmailMetadata): 郵件元數據，預設為新實例
- `processing_start_time` (datetime): 處理開始時間，必填
- `processing_end_time` (Optional[datetime]): 處理結束時間，可選
- `context_data` (Dict[str, Any]): 上下文數據，預設為空字典

### is_vendor_identified

檢查是否已成功識別廠商。

**返回值:**
- bool: 如果廠商識別成功返回True，否則返回False

### is_parsing_successful

檢查郵件解析是否成功。

**返回值:**
- bool: 如果郵件解析成功返回True，否則返回False

### get_vendor_code

取得廠商代碼，優先從廠商識別結果取得，其次從解析結果取得。

**返回值:**
- Optional[str]: 廠商代碼，如果都沒有則返回None

### add_file_processing_info

新增檔案處理資訊到列表中。

**參數:**
- `info` (FileProcessingInfo): 檔案處理資訊

**返回值:**
- None

### mark_processing_completed

標記整個處理流程完成。

**返回值:**
- None

### get_processing_duration

取得處理時長（秒）。

**返回值:**
- Optional[float]: 處理時長（秒），如果尚未完成則返回None

### to_summary_dict

轉換為摘要字典格式，便於序列化和報告。

**返回值:**
- Dict[str, Any]: 包含處理摘要資訊的字典，包含以下欄位：
  - email_id: 郵件ID
  - subject: 郵件主旨
  - sender: 寄件者
  - vendor_code: 廠商代碼
  - vendor_identified: 是否識別廠商
  - parsing_successful: 是否解析成功
  - attachment_count: 附件數量
  - processing_duration: 處理時長
  - start_time: 開始時間（ISO格式）
  - end_time: 結束時間（ISO格式，如果已完成）
