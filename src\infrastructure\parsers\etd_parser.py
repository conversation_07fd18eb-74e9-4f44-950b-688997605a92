"""
ETD 廠商解析器實作
基於 VBA ANF 邏輯，保持功能不變

VBA 邏輯參考：
- If InStr(1, LCase(subject), "anf", vbTextCompare) > 0 Then 'ETD
- myArray = Split(subject, "/")
- product = myArray(1), moString = Left(myArray(6), Len(myArray(6)) - 1), lotString = myArray(4)
- GetQtyFromMailBody(body), GetYieldFromMail(body), FindLineContainingString(body, "異常")
"""

import re
from typing import List, Optional, Dict, Any
from dataclasses import dataclass

from src.infrastructure.parsers.base_parser import VendorParser, ParsingError, ParsingContext
from src.data_models.email_models import EmailData, EmailParsingResult


class ETDParser(VendorParser):
    """
    ETD 廠商郵件解析器
    
    識別條件：主旨包含 "anf"（不區分大小寫）
    提取資料：使用 "/" 分隔主旨，從內文提取數量、良率、異常資訊
    """
    
    def __init__(self):
        """初始化 ETD 解析器"""
        super().__init__()
        self._vendor_code = "ETD"
        self._vendor_name = "ETD"
        self._identification_patterns = [
            "anf"
        ]
        self.set_confidence_threshold(0.5)  # 降低閾值，因為 ETD 郵件可能從內部轉發
        
        # ETD 特有的資料提取模式
        self.qty_patterns = [
            r'input\s+quantity\s*:\s*(\d+)',
            r'quantity\s*:\s*(\d+)\s+units',
            r'total\s+input\s*:\s*(\d+)',
            r'input\s*:\s*(\d+)',
            r'qty\s*:\s*(\d+)'
        ]
        
        self.yield_patterns = [
            r'yield\s*:\s*(\d+\.?\d*%?)',
            r'良率\s*:\s*(\d+\.?\d*%?)',
            r'pass\s+rate\s*:\s*(\d+\.?\d*%?)',
            r'(\d+\.?\d*)\s*%'
        ]

    @property
    def vendor_code(self) -> str:
        """廠商代碼"""
        return self._vendor_code
    
    @property  
    def vendor_name(self) -> str:
        """廠商名稱"""
        return self._vendor_name
    
    @property
    def supported_patterns(self) -> List[str]:
        """支援的模式列表"""
        return self._identification_patterns
    
    def identify_vendor(self, email_data: EmailData) -> 'VendorIdentificationResult':
        """識別廠商 - 實作抽象方法"""
        from src.data_models.email_models import VendorIdentificationResult
        
        subject_lower = email_data.subject.lower()
        sender_lower = email_data.sender.lower()
        matched_patterns = []
        confidence_score = 0.0
        
        # 檢查主要識別模式：ANF 關鍵字
        for pattern in self._identification_patterns:
            if pattern in subject_lower:
                matched_patterns.append(pattern)
                confidence_score += 0.5  # ANF 模式匹配
        
        # 額外的信心分數計算
        if "etrendtech" in sender_lower:
            confidence_score += 0.4  # ETD 官方寄件者
            
        # 檢查主旨是否有斜線分隔格式
        if "/" in email_data.subject and len(email_data.subject.split("/")) >= 3:
            confidence_score += 0.2  # 符合 ANF 格式結構
            
        # 檢查是否有 ETD 相關關鍵字
        if any(keyword in subject_lower for keyword in ['etd', 'etrend']):
            confidence_score += 0.1
            
        # 確保信心分數不超過 1.0
        confidence_score = min(confidence_score, 1.0)
        
        is_identified = len(matched_patterns) > 0 and confidence_score >= self.get_confidence_threshold()
        
        return VendorIdentificationResult(
            vendor_code=self.vendor_code,
            vendor_name=self.vendor_name,
            confidence_score=confidence_score,
            matching_patterns=matched_patterns,
            is_identified=is_identified,
            identification_method="anf_pattern_matching"
        )
    
    def parse_email(self, context: ParsingContext) -> EmailParsingResult:
        """解析郵件 - 實作抽象方法"""
        email_data = context.email_data
        
        if not email_data.subject:
            raise ParsingError("Empty subject line", vendor_code=self.vendor_code)
        
        try:
            # 解析 ANF 主旨格式
            anf_data = self.parse_anf_subject(email_data.subject)
            
            # 從郵件內文提取數量
            in_qty = self.extract_qty_from_body(email_data.body or "")
            
            # 從郵件內文提取良率
            yield_value = self.extract_yield_from_body(email_data.body or "")
            
            # 查找異常資訊
            issue_description = self.find_line_containing(email_data.body or "", "異常")
            
            # 清理和驗證 MO 編號格式
            mo_number = anf_data["mo_number"]
            if mo_number != "?":
                # ETD MO 編號通常有前綴，嘗試提取純編號部分
                mo_match = re.search(r'([A-Z]\d{6})', mo_number)
                if mo_match:
                    mo_number = mo_match.group(1)
                else:
                    mo_number = None  # 格式不匹配時設為 None
            else:
                mo_number = None
            
            # 建立解析結果
            result = EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=mo_number,
                lot_number=anf_data["lot_number"] if anf_data["lot_number"] != "?" else None,
                is_success=True,
                error_message=None,
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'product': anf_data["product"],
                    'product_name': anf_data["product"],  # 同時設置 product_name 以相容 API
                    'lot_number': anf_data["lot_number"],  # LOT 編號
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'in_qty': in_qty,
                    'yield_value': yield_value,
                    'issue_description': issue_description,
                    'anf_parts': anf_data["raw_parts"],
                    'raw_mo_number': anf_data["mo_number"],  # 保存原始 MO 編號
                    'parser_version': '1.0',
                    'patterns_matched': self.identify_vendor(email_data).matching_patterns,
                    'extraction_method': 'anf_slash_separated'
                }
            )
            
            return result
            
        except Exception as e:
            # 發生錯誤時返回失敗結果
            return EmailParsingResult(
                vendor_code=self.vendor_code,
                mo_number=None,
                lot_number=None,
                is_success=False,
                error_message=f"ETD parsing failed: {str(e)}",
                extracted_data={
                    'vendor_name': self.vendor_name,
                    'subject': email_data.subject,
                    'sender': email_data.sender,
                    'received_time': email_data.received_time.isoformat(),
                    'parser_version': '1.0',
                    'error_type': type(e).__name__
                }
            )

    def parse_anf_subject(self, subject: str) -> Dict[str, Any]:
        """
        解析 ANF 主旨格式
        基於 VBA Split(subject, "/") 邏輯
        
        VBA 邏輯：
        - myArray = Split(subject, "/")
        - product = myArray(1)
        - moString = Left(myArray(6), Len(myArray(6)) - 1)  # 去[EXCEPT_CHAR]最後一個字元
        - lotString = myArray(4)
        """
        parts = subject.split("/")
        
        # 安全提取各部分資料
        product = parts[1] if len(parts) > 1 else "?"
        lot_number = parts[4] if len(parts) > 4 else "?"
        
        # MO 編號：取第 6 個部分
        # 注意：原 VBA 邏輯是去[EXCEPT_CHAR]最後一個字元，但實際上第 6 部分可能已經是完整的 MO 編號
        if len(parts) > 6 and parts[6]:
            mo_number = parts[6]
            # 如果結尾有右括號，則移[EXCEPT_CHAR]它
            if mo_number.endswith(')'):
                mo_number = mo_number[:-1]
        else:
            mo_number = "?"
        
        return {
            "product": product,
            "lot_number": lot_number,
            "mo_number": mo_number,
            "raw_parts": parts
        }

    def extract_qty_from_body(self, body: str) -> Optional[str]:
        """
        從郵件內文提取數量
        基於 VBA GetQtyFromMailBody 函數邏輯
        """
        if not body:
            return None
            
        body_lower = body.lower()
        
        for pattern in self.qty_patterns:
            match = re.search(pattern, body_lower)
            if match:
                return match.group(1)
        
        return None

    def extract_yield_from_body(self, body: str) -> Optional[str]:
        """
        從郵件內文提取良率
        基於 VBA GetYieldFromMail 函數邏輯
        """
        if not body:
            return None
            
        body_lower = body.lower()
        
        for pattern in self.yield_patterns:
            match = re.search(pattern, body_lower)
            if match:
                yield_value = match.group(1)
                # 確保有百分號
                if not yield_value.endswith('%'):
                    yield_value += '%'
                return yield_value
        
        return None

    def find_line_containing(self, body: str, search_term: str) -> Optional[str]:
        """
        查找包含指定字串的行
        基於 VBA FindLineContainingString(body, "異常") 邏輯
        """
        if not body:
            return None
            
        lines = body.split('\n')
        
        for line in lines:
            if search_term in line:
                return line.strip()
        
        return None

    def validate_anf_format(self, subject: str) -> bool:
        """
        驗證 ANF 格式是否正確
        """
        if not subject:
            return False
            
        parts = subject.split("/")
        
        # ANF 格式至少需要 3 個部分
        if len(parts) < 3:
            return False
            
        # 檢查是否包含 ANF 關鍵字
        if "anf" not in subject.lower():
            return False
            
        return True

    def get_parser_info(self) -> dict:
        """獲取解析器資訊"""
        return {
            'vendor': self.vendor_name,
            'version': '1.0',
            'identification_patterns': self._identification_patterns,
            'supported_format': 'ANF slash-separated format',
            'confidence_threshold': self.get_confidence_threshold(),
            'based_on': 'VBA ANF parsing logic with Split(subject, "/")',
            'extraction_capabilities': [
                'Product name from position [1]',
                'LOT number from position [4]', 
                'MO number from position [6] (last char removed)',
                'Quantity from email body',
                'Yield from email body',
                'Issue description (異常) from email body'
            ]
        }