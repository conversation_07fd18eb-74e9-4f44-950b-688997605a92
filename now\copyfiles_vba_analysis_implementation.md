# CopyFiles VBA 函數分析與 Python 實作方案

## VBA 函數分析總結

### 發現的 CopyFiles 函數清單

1. **CopyFilesXAHT** - XAHT 廠商檔案處理
2. **CopyFilesJCET** - JCET 廠商檔案處理
3. **CopyFilesNFME** - NFME 廠商檔案處理
4. **CopyFilesMSECZD** - MSECZD 廠商（已註解）
5. **CopyFilesGTK** - GTK 廠商檔案處理（主要）
6. **CopyFilesGTK2** - GTK 廠商資料夾複製
7. **CopyFilesETD** - ETD 廠商檔案處理
8. **CopyFilesETD2** - ETD 廠商資料夾複製

### 共同處理模式分析

```vba
' 通用處理流程
Function CopyFilesXXX(fileName As String, filetemp As String, pd As String, lot As String) As Boolean
    1. 設定來源路徑 (sourcePathXXX)
    2. 設定目標路徑 (destinationPath)
    3. 呼叫 CreateFolders 建立目標資料夾
    4. 搜尋檔案：
       - 優先用 MO (fileName) 搜尋壓縮檔
       - 找不到則用 LOT 搜尋
       - 選擇最新的檔案
    5. 檢查檔案是否已存在且大小相同
    6. 複製檔案並驗證
    7. 回傳成功/失敗
End Function
```

### 各廠商特定路徑與模式

| 廠商 | 主要路徑 | 備用路徑 | 檔案模式 |
|------|---------|----------|----------|
| XAHT | `\XAHT\temp\` | - | `*{MO}*.zip/rar/7z` |
| JCET | `\JCET\JCET\` | - | `*{MO}*.zip/rar/7z` |
| NFME | `\NFME\FT\{PD}\` | - | `*lsr*`, `*data.csv` |
| GTK | `\GTK\temp\` | `\GTK\FT\{PD}\{LOT}` | `{MO}*.zip/rar/7z` 或 `{LOT}*` |
| ETD | `\ETD\FT\{MODEL}\{LOT}\` | `\Etrend\FT\{PD}\{LOT}` | 整個資料夾 |

## Python 實作方案

### 1. 基礎架構設計

```python
# src/infrastructure/adapters/file_handlers/base_file_handler.py
import os
import shutil
import tempfile
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import time

from src.infrastructure.logging.logger_manager import LoggerManager


class BaseFileHandler(ABC):
    """檔案處理器基礎類別 - 對應 VBA CopyFiles 系列函數"""
    
    def __init__(self, source_base_path: str, vendor_code: str):
        """
        Args:
            source_base_path: 來源基礎路徑 (對應 VBA sourcePath)
            vendor_code: 廠商代碼
        """
        self.source_base_path = Path(source_base_path)
        self.vendor_code = vendor_code
        self.logger = LoggerManager().get_logger(f"{vendor_code}FileHandler")
        
        # 支援的壓縮檔副檔名
        self.archive_extensions = {'.zip', '.rar', '.7z'}
        
    @abstractmethod
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """取得廠商特定的來源路徑列表（可能有多個）"""
        pass
        
    @abstractmethod
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """取得廠商特定的檔案搜尋模式"""
        pass
        
    def copy_files(self, file_name: str, file_temp: str, 
                  pd: str = "default", lot: str = "default") -> bool:
        """
        主要複製函數 - 對應 VBA CopyFilesXXX
        
        Args:
            file_name: MO 編號（對應 VBA fileName）
            file_temp: 目標暫存路徑（對應 VBA filetemp）
            pd: 產品名稱
            lot: 批號
            
        Returns:
            bool: 複製是否成功
        """
        try:
            # 1. 建立目標資料夾
            destination_path = Path(file_temp)
            if not self._create_folders(destination_path, pd, file_name.upper()):
                return False
                
            # 2. 取得來源路徑
            source_paths = self.get_source_paths(pd, lot, file_name)
            
            # 3. 嘗試從每個來源路徑複製檔案
            for source_path in source_paths:
                if not source_path.exists():
                    self.logger.warning(f"來源路徑不存在: {source_path}")
                    continue
                    
                # 嘗試不同的搜尋策略
                success = (
                    self._copy_by_mo(source_path, destination_path, file_name) or
                    self._copy_by_lot(source_path, destination_path, lot) or
                    self._copy_entire_folder(source_path, destination_path, pd, lot)
                )
                
                if success:
                    return True
                    
            self.logger.error(f"無法從任何來源複製檔案: MO={file_name}, LOT={lot}")
            return False
            
        except Exception as e:
            self.logger.error(f"複製檔案時發生錯誤: {e}")
            return False
            
    def _create_folders(self, base_path: Path, pd: str, mo: str) -> bool:
        """建立目標資料夾結構 - 對應 VBA CreateFolders"""
        try:
            # 建立 PD/MO 結構
            target_dir = base_path / pd / mo
            target_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"建立目標資料夾: {target_dir}")
            return True
        except Exception as e:
            self.logger.error(f"建立資料夾失敗: {e}")
            return False
            
    def _copy_by_mo(self, source_path: Path, dest_path: Path, mo: str) -> bool:
        """使用 MO 搜尋並複製檔案（優先搜尋壓縮檔）"""
        try:
            # 搜尋包含 MO 的檔案
            pattern = f"*{mo}*"
            newest_file = self._find_newest_archive(source_path, pattern)
            
            if newest_file:
                return self._copy_file_with_check(newest_file, dest_path)
                
            return False
            
        except Exception as e:
            self.logger.error(f"使用 MO 複製失敗: {e}")
            return False
            
    def _copy_by_lot(self, source_path: Path, dest_path: Path, lot: str) -> bool:
        """使用 LOT 搜尋並複製檔案"""
        try:
            # 搜尋包含 LOT 的檔案
            pattern = f"{lot}*"
            files = list(source_path.glob(pattern))
            
            if not files:
                return False
                
            # 複製所有符合的檔案
            success = False
            for file_path in files:
                if file_path.is_file():
                    if self._copy_file_with_check(file_path, dest_path):
                        success = True
                        
            return success
            
        except Exception as e:
            self.logger.error(f"使用 LOT 複製失敗: {e}")
            return False
            
    def _copy_entire_folder(self, source_base: Path, dest_base: Path, 
                           pd: str, lot: str) -> bool:
        """複製整個資料夾 - 對應 VBA CopyFolder"""
        try:
            # 建構資料夾路徑
            source_folder = source_base / pd / lot
            dest_folder = dest_base / pd / lot
            
            if source_folder.exists() and source_folder.is_dir():
                shutil.copytree(source_folder, dest_folder, dirs_exist_ok=True)
                self.logger.info(f"已複製整個資料夾: {source_folder} -> {dest_folder}")
                return True
                
            return False
            
        except Exception as e:
            self.logger.error(f"複製資料夾失敗: {e}")
            return False
            
    def _find_newest_archive(self, directory: Path, pattern: str) -> Optional[Path]:
        """尋找最新的壓縮檔案 - 對應 VBA 的檔案日期比較邏輯"""
        newest_file = None
        newest_date = datetime.min
        
        for file_path in directory.glob(pattern):
            if file_path.suffix.lower() in self.archive_extensions:
                file_date = datetime.fromtimestamp(file_path.stat().st_mtime)
                if file_date > newest_date:
                    newest_date = file_date
                    newest_file = file_path
                    
        return newest_file
        
    def _copy_file_with_check(self, source: Path, dest_dir: Path) -> bool:
        """複製檔案並檢查 - 對應 VBA 的檔案大小檢查邏輯"""
        try:
            dest_file = dest_dir / source.name
            
            # 檢查目標檔案是否已存在且大小相同
            if dest_file.exists():
                if dest_file.stat().st_size == source.stat().st_size:
                    self.logger.info(f"檔案已存在且大小相同，跳過: {dest_file}")
                    return True
                    
            # 延遲一秒（對應 VBA DelayOneSecond）
            time.sleep(1)
            
            # 複製檔案
            shutil.copy2(source, dest_file)
            
            # 驗證複製
            if dest_file.stat().st_size == source.stat().st_size:
                self.logger.info(f"檔案複製成功: {source} -> {dest_file}")
                return True
            else:
                self.logger.error(f"檔案複製驗證失敗: 大小不符")
                return False
                
        except Exception as e:
            self.logger.error(f"複製檔案失敗 {source}: {e}")
            return False
```

### 2. 各廠商實作

```python
# src/infrastructure/adapters/file_handlers/gtk_file_handler.py
class GTKFileHandler(BaseFileHandler):
    """GTK 廠商檔案處理器 - 對應 CopyFilesGTK"""
    
    def __init__(self, source_base_path: str):
        super().__init__(source_base_path, "GTK")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """GTK 有兩個可能的來源路徑"""
        return [
            self.source_base_path / "GTK" / "temp",
            self.source_base_path / "GTK" / "FT" / pd / lot
        ]
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """GTK 的檔案搜尋模式"""
        return [
            f"{mo}*",      # 用 MO 搜尋
            f"*{mo}*",     # 包含 MO
            f"{lot}*",     # 用 LOT 搜尋
            f"*{lot}*"     # 包含 LOT
        ]


# src/infrastructure/adapters/file_handlers/xaht_file_handler.py
class XAHTFileHandler(BaseFileHandler):
    """XAHT 廠商檔案處理器 - 對應 CopyFilesXAHT"""
    
    def __init__(self, source_base_path: str):
        super().__init__(source_base_path, "XAHT")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """XAHT 只有一個來源路徑"""
        return [self.source_base_path / "XAHT" / "temp"]
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """XAHT 的檔案搜尋模式"""
        return [f"*{mo}*"]  # 只用 MO 搜尋


# src/infrastructure/adapters/file_handlers/nfme_file_handler.py
class NFMEFileHandler(BaseFileHandler):
    """NFME 廠商檔案處理器 - 對應 CopyFilesNFME"""
    
    def __init__(self, source_base_path: str):
        super().__init__(source_base_path, "NFME")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """NFME 使用 PD 建構路徑"""
        return [self.source_base_path / "NFME" / "FT" / pd]
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """NFME 特殊：只複製特定類型檔案"""
        return ["*lsr*", "*data.csv"]
        
    def _copy_by_mo(self, source_path: Path, dest_path: Path, mo: str) -> bool:
        """NFME 覆寫：不搜尋壓縮檔，而是特定檔案類型"""
        try:
            success = False
            for pattern in self.get_file_patterns(mo, "", ""):
                for file_path in source_path.glob(pattern):
                    if file_path.is_file():
                        if self._copy_file_with_check(file_path, dest_path):
                            success = True
            return success
        except Exception as e:
            self.logger.error(f"NFME 檔案複製失敗: {e}")
            return False
```

### 3. 檔案處理器工廠

```python
# src/infrastructure/adapters/file_handlers/file_handler_factory.py
from typing import Optional, Dict, Type
from .base_file_handler import BaseFileHandler
from .gtk_file_handler import GTKFileHandler
from .xaht_file_handler import XAHTFileHandler
from .jcet_file_handler import JCETFileHandler
from .nfme_file_handler import NFMEFileHandler
from .etd_file_handler import ETDFileHandler


class FileHandlerFactory:
    """檔案處理器工廠"""
    
    _handlers: Dict[str, Type[BaseFileHandler]] = {
        'GTK': GTKFileHandler,
        'XAHT': XAHTFileHandler,
        'JCET': JCETFileHandler,
        'NFME': NFMEFileHandler,
        'ETD': ETDFileHandler,
    }
    
    @classmethod
    def create_handler(cls, vendor_code: str, 
                      source_base_path: str) -> Optional[BaseFileHandler]:
        """
        建立檔案處理器
        
        Args:
            vendor_code: 廠商代碼
            source_base_path: 來源基礎路徑
            
        Returns:
            檔案處理器實例，如果廠商不支援則返回 None
        """
        handler_class = cls._handlers.get(vendor_code.upper())
        if handler_class:
            return handler_class(source_base_path)
        return None
        
    @classmethod
    def get_supported_vendors(cls) -> List[str]:
        """取得支援的廠商列表"""
        return list(cls._handlers.keys())
```

### 4. 整合到郵件同步服務

```python
# 修改 src/infrastructure/adapters/email_inbox/email_sync_service.py

from src.infrastructure.adapters.file_handlers.file_handler_factory import FileHandlerFactory

class EmailSyncService:
    
    def __init__(self, database: EmailDatabase = None):
        # ... 現有初始化 ...
        
        # 初始化檔案處理器設定
        self.source_base_path = os.getenv('FILE_SOURCE_BASE_PATH', '/mnt/share')
        self.temp_base_path = os.getenv('FILE_TEMP_BASE_PATH', tempfile.gettempdir())
        
    def _process_vendor_files(self, email_id: int, parse_result: Dict[str, Any]):
        """處理廠商相關檔案"""
        
        if not parse_result.get('success'):
            return
            
        vendor = parse_result.get('vendor')
        mo = parse_result.get('mo') or parse_result.get('mo_number')
        lot = parse_result.get('lot') or parse_result.get('lot_number')
        pd = parse_result.get('pd') or parse_result.get('product_name', 'default')
        
        if not vendor or not (mo or lot):
            self.logger.warning(f"缺少必要資訊，無法處理檔案: vendor={vendor}, mo={mo}, lot={lot}")
            return
            
        # 建立檔案處理器
        file_handler = FileHandlerFactory.create_handler(vendor, self.source_base_path)
        
        if not file_handler:
            self.logger.warning(f"不支援的廠商: {vendor}")
            return
            
        # 建立暫存目錄
        temp_dir = Path(self.temp_base_path) / f"email_{email_id}" / vendor
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 複製檔案
        success = file_handler.copy_files(
            file_name=mo or lot,  # MO 優先，沒有則用 LOT
            file_temp=str(temp_dir),
            pd=pd,
            lot=lot
        )
        
        if success:
            self.logger.info(f"成功複製 {vendor} 檔案到: {temp_dir}")
            # 更新資料庫記錄
            self._update_file_copy_status(email_id, temp_dir)
        else:
            self.logger.error(f"複製 {vendor} 檔案失敗")
```

## 環境設定

```bash
# .env
# 檔案來源基礎路徑（對應 VBA sourcePath）
FILE_SOURCE_BASE_PATH=/mnt/share

# 暫存檔案基礎路徑（對應 VBA tempPath）
FILE_TEMP_BASE_PATH=/tmp/email_processor

# 各廠商特定設定（可選）
GTK_ARCHIVE_PRIORITY=true  # 優先搜尋壓縮檔
NFME_FILE_TYPES=lsr,data.csv  # NFME 特定檔案類型
```

## 測試範例

```python
# tests/test_file_handlers.py
import pytest
from pathlib import Path
import tempfile
from src.infrastructure.adapters.file_handlers.gtk_file_handler import GTKFileHandler


def test_gtk_file_handler():
    """測試 GTK 檔案處理器"""
    
    # 準備測試環境
    with tempfile.TemporaryDirectory() as temp_dir:
        # 建立模擬來源結構
        source_base = Path(temp_dir) / "source"
        gtk_temp = source_base / "GTK" / "temp"
        gtk_temp.mkdir(parents=True)
        
        # 建立測試檔案
        test_file = gtk_temp / "MO123456.zip"
        test_file.write_text("test content")
        
        # 建立處理器
        handler = GTKFileHandler(str(source_base))
        
        # 測試複製
        dest_dir = Path(temp_dir) / "dest"
        success = handler.copy_files(
            file_name="MO123456",
            file_temp=str(dest_dir),
            pd="TEST_PD",
            lot="LOT789"
        )
        
        # 驗證
        assert success
        expected_file = dest_dir / "TEST_PD" / "MO123456" / "MO123456.zip"
        assert expected_file.exists()
        assert expected_file.read_text() == "test content"
```

## 實作優勢

1. **保持 VBA 邏輯一致性** - 完整對應原始 VBA 函數行為
2. **模組化設計** - 每個廠商獨立實作，易於維護
3. **可擴展性** - 透過工廠模式輕鬆添加新廠商
4. **錯誤處理** - 完整的日誌和異常處理
5. **測試友善** - 每個處理器可獨立測試

## 下一步

1. 實作各廠商的檔案處理器
2. 整合到現有的郵件同步流程
3. 添加檔案複製的進度追蹤
4. 實作檔案快取機制避免重複複製