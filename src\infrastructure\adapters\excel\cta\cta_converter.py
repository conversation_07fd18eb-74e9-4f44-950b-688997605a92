#!/usr/bin/env python3
"""
CTA 核心轉換器
實作 convertOtherDatalog 的 Python 版本，處理到 Data11 階段
"""

import pandas as pd
import logging
from typing import Dict, List, Optional, Union
from pathlib import Path

from .cta_enums import TesterType, DataSectionState
from .cta_state import CTAProcessingState, CTAConstants
from .worksheet_manager import WorksheetManager

logger = logging.getLogger(__name__)


class CTAConverter:
    """CTA 轉換器 - convertOtherDatalog 的 Python 實作"""
    
    def __init__(self):
        self.state = CTAProcessingState()
        self.worksheet_manager = WorksheetManager()
        self.current_file_path = None
        
    def convert_other_datalog(self, excel_file_path: str) -> bool:
        """
        convertOtherDatalog 的完整 Python 實作 (到 Data11 階段)
        
        Args:
            excel_file_path: CTA Excel 檔案路徑
            
        Returns:
            True 如果轉換成功
        """
        try:
            logger.info(f"開始處理 CTA 檔案: {excel_file_path}")
            self.current_file_path = excel_file_path
            
            # 步驟 1: 載入和初始化
            self._load_excel_file(excel_file_path)
            
            # 步驟 2: 測試器類型識別
            self._identify_tester_type()
            
            # 步驟 3: 動態掃描和處理
            if self.state.tester_type == TesterType.UNKNOWN:
                self._dynamic_scanning_process()
            
            # 步驟 4: 最終驗證
            success = self._final_validation()
            
            if success:
                # 驗證成功後，創建 Data11 工作表
                self._create_data11_worksheet()
                logger.info(f"CTA 轉換成功，測試器類型: {self.state.tester_type.name}")
            else:
                logger.error("CTA 轉換失敗：最終驗證未通過")
                
            return success
            
        except Exception as e:
            logger.error(f"convertOtherDatalog 處理失敗: {e}")
            return False
    
    def _load_excel_file(self, file_path: str) -> None:
        """
        載入 CTA 檔案 (支援 CSV 和 Excel 格式)
        """
        try:
            if file_path.lower().endswith('.csv'):
                # CSV 格式 - 需要特殊處理成 Excel 多工作表結構
                self._load_csv_as_excel_structure(file_path)
            elif file_path.lower().endswith(('.xlsx', '.xls')):
                # Excel 格式 - 直接載入所有工作表
                excel_file = pd.ExcelFile(file_path)
                for sheet_name in excel_file.sheet_names:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name, header=None)
                    self.worksheet_manager.add_worksheet(sheet_name, df)
                    logger.debug(f"載入工作表: {sheet_name}, 大小: {len(df)} x {len(df.columns)}")
            else:
                raise ValueError(f"不支援的檔案格式: {file_path}")
                
            logger.info(f"成功載入檔案: {file_path}")
            
        except Exception as e:
            raise ValueError(f"載入檔案失敗: {e}")
    
    def _load_csv_as_excel_structure(self, csv_file_path: str) -> None:
        """
        將 CTA CSV 檔案載入並轉換為 Excel 多工作表結構
        """
        try:
            # 首先按原方式讀取 CSV 做為原始資料
            with open(csv_file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
            
            # 分析檔案結構，找到主要區段
            sections = self._parse_cta_sections(lines)
            
            # 創建主工作表 (包含配置資訊)
            config_data = []
            for line in lines[:1979]:  # [Data] 之前的配置資訊
                line = line.strip()
                if line:
                    parts = line.split(',', 1)
                    if len(parts) == 2:
                        config_data.append([parts[0].strip(), parts[1].rstrip(',').strip()])
                    else:
                        config_data.append([parts[0].rstrip(',').strip(), ''])
            
            config_df = pd.DataFrame(config_data, columns=['Key', 'Value'])
            self.worksheet_manager.add_worksheet("Sheet1", config_df, 'first')
            
            # 如果有 [Data] 區段，創建原始測試數據工作表
            if 'Data' in sections:
                data_lines = sections['Data']
                # 解析測試數據為表格格式
                if len(data_lines) > 1:
                    # 第一行是欄位名稱
                    headers = [h.strip() for h in data_lines[0].split(',')]
                    data_rows = []
                    
                    for line in data_lines[1:]:
                        row_data = [d.strip() for d in line.split(',')]
                        # 確保行長度與標題匹配
                        while len(row_data) < len(headers):
                            row_data.append('')
                        data_rows.append(row_data[:len(headers)])
                    
                    if data_rows:
                        data_df = pd.DataFrame(data_rows, columns=headers)
                        self.worksheet_manager.add_worksheet("RawData", data_df)
                        logger.debug(f"創建 RawData 工作表: {len(data_df)} 行 x {len(headers)} 列")
            
            logger.debug(f"CSV 檔案已轉換為多工作表結構")
            
        except Exception as e:
            raise ValueError(f"CSV 轉換失敗: {e}")
    
    def _parse_cta_sections(self, lines: list) -> dict:
        """解析 CTA 檔案的不同區段"""
        sections = {}
        
        # 查找 [Data] 開始的測試數據區段
        data_start_line = None
        for i, line in enumerate(lines):
            if line.strip().startswith('[Data]'):
                data_start_line = i
                break
        
        if data_start_line is not None:
            # [Data] 後面的行是測試數據
            data_lines = []
            for line in lines[data_start_line + 1:]:  # 跳過 [Data] 標記行
                line = line.strip()
                if line and not line.startswith('['):  # 直到下一個區段或檔案結尾
                    data_lines.append(line)
                elif line.startswith('['):
                    break  # 遇到下一個區段停止
            
            if data_lines:
                sections['Data'] = data_lines
        
        return sections
    
    def _identify_tester_type(self) -> None:
        """
        測試器類型識別 - 對應 VBA 的測試器檢測邏輯
        """
        main_sheet = self.worksheet_manager.get_worksheet("Sheet1")
        
        if main_sheet is None or main_sheet.empty:
            return
            
        # 檢查第二行的 Tester_Type 鍵值
        if len(main_sheet) > 1:
            tester_type_row = main_sheet.iloc[1]  # 第二行 (索引1)
            if tester_type_row['Key'] == 'Tester_Type':
                tester_value = tester_type_row['Value'].upper()
                
                # CTA8290DPlus 或類似的 CTA8280 系列
                if 'CTA82' in tester_value:
                    self.state.tester_type = TesterType.CTA8280
                    logger.debug(f"檢測到 CTA8280 測試器: {tester_value}")
                    return
                elif 'STS8200' in tester_value:
                    self.state.tester_type = TesterType.STS8200
                    logger.debug(f"檢測到 STS8200 測試器: {tester_value}")
                    return
                    
        # 檢查其他特徵
        key_column = main_sheet['Key'] if 'Key' in main_sheet.columns else pd.Series()
        
        # EAGLE 檢測: ETS 標記
        if any('ETS' in str(key) for key in key_column):
            self.state.tester_type = TesterType.EAGLE
            logger.debug("檢測到 EAGLE 測試器")
            return
            
        # YS 檢測: .PRG Line# 標記  
        if any('.PRG Line#' in str(key) for key in key_column):
            self.state.tester_type = TesterType.YS
            logger.debug("檢測到 YS 測試器")
            return
            
        # 檢查工作表名稱 (對於多工作表的 Excel 檔案)
        for sheet_name in self.worksheet_manager.get_worksheet_names():
            if sheet_name in ['Data11', 'QAData', 'DataInfo']:
                self.state.tester_type = TesterType.CTA8280
                self._move_sheet_to_front(sheet_name)
                logger.debug("檢測到 CTA8280 測試器 (工作表名稱)")
                return
            elif sheet_name in ['DUT_DATA', 'QA_Data']:
                self.state.tester_type = TesterType.STS8200
                self._move_sheet_to_front(sheet_name)
                logger.debug("檢測到 STS8200 測試器 (工作表名稱)")
                return
        
        logger.debug("測試器類型尚未確定，將進行動態掃描")
    
    def _move_sheet_to_front(self, sheet_name: str) -> None:
        """移動工作表到前面"""
        if sheet_name in self.worksheet_manager.get_worksheet_names():
            self.worksheet_manager.move_worksheet(sheet_name, 'first')
    
    def _dynamic_scanning_process(self) -> None:
        """
        動態掃描處理 - 對 CTA 格式的鍵值對掃描
        """
        # 計算總設備數
        self.state.total_device_number = self._get_total_device_number()
        logger.debug(f"總設備數量: {self.state.total_device_number}")
        
        if self.state.total_device_number == 0:
            logger.warning("未找到設備資料")
            return
            
        main_sheet = self.worksheet_manager.get_worksheet("Sheet1")
        if main_sheet is None:
            return
            
        # 掃描 CTA 格式的鍵值對
        data_section_found = False
        qadata_section_found = False
        
        for index, row in main_sheet.iterrows():
            key = str(row.get('Key', '')).strip()
            value = str(row.get('Value', '')).strip()
            
            # 檢查區段標記
            if key == "[Data]":
                self._handle_data_section()
                data_section_found = True
                self.state.current_row = index + 1
                logger.debug(f"找到 [Data] 標記，行號: {index + 1}")
                
            elif key == "[QAData]":
                self._handle_qadata_section()
                qadata_section_found = True
                self.state.current_row = index + 1
                logger.debug(f"找到 [QAData] 標記，行號: {index + 1}")
                
            elif key == "SITE_NUM" or key == "Site_Num":
                self._handle_site_num_section()
                logger.debug(f"找到 SITE_NUM 標記，行號: {index + 1}")
                
        # 如果沒有找到特定區段，但已經識別了測試器類型，則認為成功
        if not data_section_found and not qadata_section_found:
            if self.state.tester_type != TesterType.UNKNOWN:
                # 對於 CTA8280 格式，直接建立 Data11 工作表
                self.state.data_section_state = DataSectionState.DATA_FOUND
                logger.debug("CTA8280 格式：直接設定為 DATA_FOUND 狀態")
                
        logger.debug(f"動態掃描完成，狀態: {self.state.data_section_state.name}")
    
    def _handle_data_section(self) -> None:
        """處理 [Data] 區段"""
        self.state.tester_type = TesterType.CTA8280
        self.state.start_row = self.state.current_row + 1
        self.state.data_section_state = DataSectionState.DATA_FOUND
    
    def _handle_qadata_section(self) -> None:
        """處理 [QAData] 區段"""
        if self.state.data_section_state == DataSectionState.DATA_FOUND:
            self.state.stop_row = self.state.current_row - 1
            
            # 建立新工作表結構
            self._create_data11_worksheet()
            
        self.state.start_row = self.state.current_row + 1
        self.state.data_section_state = DataSectionState.QADATA_FOUND
    
    def _handle_site_num_section(self) -> None:
        """處理 SITE_NUM 區段 (STS8200 格式)"""
        self.state.tester_type = TesterType.STS8200
        self.state.start_row = self.state.current_row
        self.state.data_section_state = DataSectionState.DATA_FOUND
    
    def _handle_end_of_file(self) -> None:
        """處理檔案結尾邏輯"""
        self.state.stop_row = self.state.current_row
        
        if self.state.data_section_state == DataSectionState.DATA_FOUND:
            if self.state.tester_type == TesterType.CTA8280:
                self._create_data11_worksheet()
                self._finalize_data11_worksheet()
                logger.debug("已建立 Data11 工作表 (CTA8280)")
            else:  # STS8200
                self._create_dut_data_worksheet()
                logger.debug("已建立 DUT_DATA 工作表 (STS8200)")
                
        elif self.state.data_section_state == DataSectionState.QADATA_FOUND:
            # 注意：這裡只到 Data11 階段，QAData 處理留待後續實作
            self._finalize_data11_worksheet()
            logger.debug("已完成 Data11 工作表處理")
    
    def _create_data11_worksheet(self) -> None:
        """
        建立 Data11 工作表 - 根據 CTA 分析文件的標準處理邏輯
        """
        # 檢查是否有 RawData 工作表（包含測試數據）
        raw_data = self.worksheet_manager.get_worksheet("RawData")
        
        if raw_data is not None and not raw_data.empty:
            # 使用 RawData 工作表作為 Data11 的基礎
            data11_df = raw_data.copy()
            
            # 根據 VBA 邏輯進行數據處理
            # 1. 移動到第一個位置（Data11 工作表應該是主要數據工作表）
            self.worksheet_manager.add_worksheet("Data11", data11_df, 'first')
            
            # 2. 清空第一個儲存格（對應 VBA: Cells(1,1) = ""）
            if len(data11_df) > 0 and len(data11_df.columns) > 0:
                data11_df.iloc[0, 0] = ""
            
            logger.debug(f"已建立 Data11 工作表，包含 {len(data11_df)} 行測試數據，{len(data11_df.columns)} 個測試項目")
            
        else:
            # 回退到基本配置資料
            logger.warning("未找到 RawData，建立基本配置資料")
            source_data = self.worksheet_manager.get_worksheet("Sheet1")
            if source_data is not None:
                self._create_basic_data11_worksheet(source_data)
            else:
                logger.error("無法建立 Data11 工作表：缺少來源數據")
            
    def _create_basic_data11_worksheet(self, source_data: pd.DataFrame) -> None:
        """建立基本配置資料的 Data11 工作表（回退選項）"""
        test_related_keys = [
            'Tester_Type', 'Tester_Name', 'Software_Name', 'Software_Ver',
            'Site_Count', 'Site_Num', 'Device_Name', 'Part_No',
            'Program', 'TestFile', 'Job_Revision'
        ]
        
        data11_data = []
        data11_data.append(['Parameter', 'Value'])
        
        for _, row in source_data.iterrows():
            key = str(row.get('Key', '')).strip()
            value = str(row.get('Value', '')).strip()
            
            if key in test_related_keys or key.startswith('['):
                data11_data.append([key, value])
        
        data11_df = pd.DataFrame(data11_data[1:], columns=data11_data[0])
        self.worksheet_manager.add_worksheet("Data11", data11_df, 'after', "Sheet1")
        logger.debug(f"已建立基本 Data11 工作表，包含 {len(data11_df)} 行配置資料")
    
    def _create_dut_data_worksheet(self) -> None:
        """建立 DUT_DATA 工作表 (STS8200 格式)"""
        source_sheet_name = self.worksheet_manager.get_worksheet_names()[self.state.source_sheet_index]
        source_data = self.worksheet_manager.get_worksheet(source_sheet_name)
        
        if source_data is None:
            return
            
        # 提取 DUT_DATA 區段
        start_idx = max(0, self.state.start_row - 1)
        end_idx = min(len(source_data), self.state.stop_row)
        
        if start_idx < end_idx:
            dut_data = source_data.iloc[start_idx:end_idx].copy()
            self.worksheet_manager.add_worksheet("DUT_DATA", dut_data, 'before', source_sheet_name)
            
            # 重命名原工作表
            self.worksheet_manager.rename_worksheet(source_sheet_name, "Summary information")
            
            logger.debug("已建立 DUT_DATA 工作表")
    
    def _finalize_data11_worksheet(self) -> None:
        """完成 Data11 工作表的最終處理"""
        if "Data11" not in self.worksheet_manager.get_worksheet_names():
            return
            
        # 移動 Data11 到第一個位置
        self.worksheet_manager.move_worksheet("Data11", "first")
        
        # 刪[EXCEPT_CHAR]標題行
        if self.state.sum_end_row > 0:
            self.worksheet_manager.delete_rows("Data11", 1, self.state.sum_end_row)
            
        # 清空第一個儲存格
        self.worksheet_manager.set_cell_value("Data11", 1, 1, "")
        
        logger.debug("Data11 工作表最終處理完成")
    
    def _get_total_device_number(self, sheet_index: int = 0) -> int:
        """
        計算總設備數 - 對應 VBA: GetTotalDeviceNumber
        """
        sheet_names = self.worksheet_manager.get_worksheet_names()
        if not sheet_names:
            return 0
            
        df = self.worksheet_manager.get_worksheet(sheet_names[sheet_index])
        if df is None:
            return 0
        
        # 對於 CTA 格式檔案，檢查特定鍵來計算設備數
        # 可能的設備相關鍵: Site_Count, Site_Num 等
        for _, row in df.iterrows():
            key = str(row.get('Key', '')).strip()
            value = str(row.get('Value', '')).strip()
            
            if key == 'Site_Count' and value.isdigit():
                return int(value)
            elif key == 'Site_Num':
                # Site_Num 可能是 "1_2_3_4" 這樣的格式
                sites = value.split('_')
                return len([s for s in sites if s.strip()])
        
        # 如果沒有找到明確的設備數，嘗試計算包含數字ID的行數
        device_count = 0
        for _, row in df.iterrows():
            key = str(row.get('Key', '')).strip()
            if key.startswith('[') and key.endswith(']'):
                # 跳過區段標記如 [GENERAL], [Data] 等
                continue
            if key.isdigit() or ('_' in key and any(part.isdigit() for part in key.split('_'))):
                device_count += 1
        
        return max(device_count, 1)  # 至少返回 1
    
    def _check_cell_content(self, df: pd.DataFrame, row: int, col: int, 
                           expected: str) -> bool:
        """檢查儲存格內容是否包含預期字串 (適應鍵值對格式)"""
        try:
            if df is None or row > len(df):
                return False
            
            # 對於鍵值對格式，檢查 Key 或 Value 欄位
            if row <= len(df):
                data_row = df.iloc[row-1]  # 轉換為 0-based index
                key_value = str(data_row.get('Key', '')) if 'Key' in df.columns else ""
                value_value = str(data_row.get('Value', '')) if 'Value' in df.columns else ""
                
                # 根據列數決定檢查哪個欄位
                if col == 1:  # 第一列檢查 Key
                    return expected in key_value
                elif col == 2:  # 第二列檢查 Value
                    return expected in value_value
                else:
                    # 其他列檢查兩個欄位
                    return expected in key_value or expected in value_value
                    
            return False
        except Exception:
            return False
    
    def _get_cell_content(self, df: pd.DataFrame, row: int, col: int) -> str:
        """獲取儲存格內容 (適應鍵值對格式)"""
        try:
            if df is None or row > len(df):
                return ""
            
            # 對於鍵值對格式，根據列數返回對應內容
            if row <= len(df):
                data_row = df.iloc[row-1]  # 轉換為 0-based index
                
                if col == 1 and 'Key' in df.columns:  # 第一列返回 Key
                    value = data_row.get('Key', '')
                elif col == 2 and 'Value' in df.columns:  # 第二列返回 Value
                    value = data_row.get('Value', '')
                else:
                    # 其他情況返回 Key (主要內容)
                    value = data_row.get('Key', '') if 'Key' in df.columns else ""
                    
                return str(value).strip() if pd.notna(value) else ""
                
            return ""
        except Exception:
            return ""
    
    def _final_validation(self) -> bool:
        """
        最終驗證 - 對 CTA 格式的成功檢查邏輯
        """
        sheet_names = self.worksheet_manager.get_worksheet_names()
        if not sheet_names:
            return False
            
        main_sheet = self.worksheet_manager.get_worksheet("Sheet1")
        if main_sheet is None:
            return False
        
        # 對於 CTA 格式，檢查是否有有效的測試器類型和基本結構
        if self.state.tester_type == TesterType.UNKNOWN:
            return False
            
        # 檢查必要的 CTA 格式鍵
        required_keys = ['Tester_Type', 'Software_Name']
        found_keys = 0
        
        for _, row in main_sheet.iterrows():
            key = str(row.get('Key', '')).strip()
            if key in required_keys:
                found_keys += 1
                
        # 至少要找到一個必要的鍵
        if found_keys == 0:
            logger.warning("未找到必要的 CTA 格式鍵")
            return False
            
        # 重新計算設備數（可能在動態掃描時沒有正確設定）
        if self.state.total_device_number <= 0:
            self.state.total_device_number = self._get_total_device_number()
            
        # 檢查是否有設備資料
        if self.state.total_device_number <= 0:
            logger.warning("未找到有效的設備資料")
            return False
            
        # 設定關鍵欄位位置 (對於 CTA 格式，這是固定的)
        self.state.finded_row = 1
        self.state.data_cnt_column = 1  # Key 欄位
        
        logger.debug(f"最終驗證通過：測試器類型={self.state.tester_type.name}, 設備數={self.state.total_device_number}")
        return True
    
    def get_processing_summary(self) -> Dict:
        """獲取處理摘要"""
        return {
            'tester_type': self.state.tester_type.name,
            'data_section_state': self.state.data_section_state.name,
            'total_devices': self.state.total_device_number,
            'worksheets_created': self.worksheet_manager.get_worksheet_names(),
            'key_column_position': self.state.data_cnt_column,
            'processing_complete': True
        }