#!/usr/bin/env python3
"""
FT Summary Excel 上色器
專門處理 FT Summary 檔案的智慧上色功能

功能：
1. 穩固的 Definition 欄位定位（第6行由左至右掃描）
2. 最大 TOTAL 區段識別（排[EXCEPT_CHAR] Multiple Files）
3. BIN 色彩映射（All Pass 綠色，Fail Bins 不同顏色）
4. Excel 上色應用（相同 BIN NAME 一致顏色）
"""

import pandas as pd
import xlsxwriter
from typing import Dict, List, Tuple, Any


class ColorConfig:
    """色彩配置管理"""
    
    ALL_PASS_COLOR = '#90EE90'  # 綠色 - All Pass
    
    FAIL_BIN_COLORS = [
        '#FF69B4',  # 深粉色
        '#4682B4',  # 鋼藍色
        '#9370DB',  # 中紫色
        '#DAA520',  # 金黃色
        '#FF6347',  # 番茄色
        '#32CD32',  # 綠黃色
        '#DEB887',  # 淺黃褐色
        '#F4A460',  # 沙棕色
        '#708090',  # 石板灰
        '#20B2AA'   # 淺海綠色
    ]


class FTSummaryColorizer:
    """
    FT Summary Excel 上色器
    
    提供穩固的 FT Summary 上色功能，包括：
    - 智慧 Definition 欄位定位
    - BIN 色彩分配和映射
    - Excel 檔案上色應用
    """
    
    def __init__(self):
        """初始化上色器"""
        self.color_config = self._init_color_config()
        print("[ART] FT Summary 上色器已初始化")
    
    def _init_color_config(self) -> Dict[str, Any]:
        """初始化色彩配置"""
        return {
            'all_pass': ColorConfig.ALL_PASS_COLOR,
            'fail_bins': ColorConfig.FAIL_BIN_COLORS
        }
    
    def colorize_excel(self, workbook: xlsxwriter.Workbook, worksheet, df: pd.DataFrame) -> bool:
        """
        主要上色方法 - 對外接口
        
        Args:
            workbook: xlsxwriter Workbook 物件
            worksheet: xlsxwriter Worksheet 物件
            df: 處理後的 DataFrame
            
        Returns:
            bool: 上色是否成功
        """
        try:
            print("[ART] 開始 FT Summary 上色處理...")
            
            # 1. 先找到最大 TOTAL 區段
            max_total_section = self._find_max_total_section(df)
            if max_total_section < 0:
                print("[WARNING] 未找到有效的最大 TOTAL 區段")
                return False
            
            # 2. 穩固的 Definition 欄位定位
            definition_columns = self._find_definition_columns_robust(df)
            if not definition_columns:
                print("[WARNING] 未找到 Definition 欄位，跳過上色")
                return False
            
            # 3. 找到最大 TOTAL 區段對應的 Definition 欄位
            target_definition_column = self._find_target_definition_column(max_total_section, definition_columns)
            if target_definition_column < 0:
                print("[WARNING] 未找到最大 TOTAL 區段對應的 Definition 欄位")
                return False
            
            print(f"[TARGET] 目標上色區段：最大 TOTAL 在第{max_total_section+1}欄，對應 Definition 在第{target_definition_column+1}欄")
            
            # 4. 建立 BIN 色彩映射
            bin_color_mapping = self._create_bin_color_mapping(df, definition_columns)
            if not bin_color_mapping:
                print("[WARNING] 未建立有效的 BIN 色彩映射")
                return False
            
            # 5. 應用色彩到 Excel
            self._apply_bin_coloring_to_excel(workbook, worksheet, df, bin_color_mapping)
            
            print(f"[OK] FT Summary 上色完成，共處理 {len(bin_color_mapping)} 個 BIN")
            return True
            
        except Exception as e:
            print(f"[ERROR] 上色處理失敗: {str(e)}")
            return False
    
    def _find_definition_columns_robust(self, df: pd.DataFrame) -> List[int]:
        """
        穩固的 Definition 欄位定位方法
        直接在第6行（索引5）由左至右掃描所有儲存格，找到值為 "Definition" 的儲存格
        
        這樣可以避免因未來報表結構在 "Definition" 欄位左側增加或減少欄位而導致的定位失敗
        
        Args:
            df: DataFrame
            
        Returns:
            List[int]: Definition 欄位的欄位索引列表
        """
        definition_columns = []
        header_row_idx = 5  # 第6行（索引從0開始）
        
        if len(df) > header_row_idx:
            for col_idx in range(len(df.columns)):
                cell_value = str(df.iloc[header_row_idx, col_idx]).strip()
                if cell_value.lower() == 'definition':
                    definition_columns.append(col_idx)
                    print(f"  [LOCATION] 找到 Definition 欄位: 第{col_idx+1}欄")
        
        print(f"  [CHART] 共找到 {len(definition_columns)} 個 Definition 欄位")
        return definition_columns
    
    def _find_max_total_section(self, df: pd.DataFrame) -> int:
        """
        找到第1行最大 TOTAL 數量的區段，排[EXCEPT_CHAR] Multiple Files
        
        Args:
            df: DataFrame
            
        Returns:
            int: 最大 TOTAL 區段的起始欄位索引，未找到返回 -1
        """
        if len(df) == 0:
            return -1
        
        max_total = 0
        max_section_start = -1
        
        # 掃描第1行找到所有 TOTAL 欄位
        for col_idx in range(len(df.columns)):
            cell_value = str(df.iloc[0, col_idx]).strip()
            if cell_value.lower() == 'total':
                # 檢查右邊3欄是否為 Multiple Files
                if col_idx + 3 < len(df.columns):
                    right_value = str(df.iloc[0, col_idx + 3]).strip()
                    if 'multiple files' in right_value.lower():
                        print(f"  [NO_ENTRY] 跳過 Multiple Files 區段: 第{col_idx+1}欄")
                        continue
                
                # 取得 TOTAL 數值
                if col_idx + 1 < len(df.columns):
                    try:
                        total_value = int(df.iloc[0, col_idx + 1])
                        print(f"  [CHART] 找到 TOTAL: 第{col_idx+1}欄 = {total_value}")
                        if total_value > max_total:
                            max_total = total_value
                            max_section_start = col_idx
                    except (ValueError, TypeError):
                        continue
        
        if max_section_start >= 0:
            print(f"  [TARGET] 最大 TOTAL 區段: 第{max_section_start+1}欄，數量 = {max_total}")
        else:
            print("  [WARNING] 未找到有效的 TOTAL 區段")
        
        return max_section_start
    
    def _create_bin_color_mapping(self, df: pd.DataFrame, definition_columns: List[int]) -> Dict[str, str]:
        """
        建立 BIN 名稱到色彩的映射表
        
        邏輯：
        1. 找到最大 TOTAL 區段對應的 Definition 欄位
        2. 按順序掃描該 Definition 欄位，找前 10 個 Fail BIN
        3. All Pass -> 綠色，前 10 個 Fail BIN -> 依序分配顏色
        4. 相同 BIN NAME 在所有區段使用相同顏色
        
        Args:
            df: DataFrame
            definition_columns: Definition 欄位索引列表
            
        Returns:
            Dict[str, str]: BIN名稱 -> 色彩代碼的映射字典
        """
        bin_color_mapping = {}
        fail_color_index = 0
        
        print("  [SEARCH] 建立基於最大 TOTAL 區段的 BIN 色彩映射...")
        
        # 1. 找到最大 TOTAL 區段
        max_total_section = self._find_max_total_section(df)
        if max_total_section < 0:
            print("  [ERROR] 未找到最大 TOTAL 區段")
            return {}
        
        # 2. 找到對應的 Definition 欄位
        target_definition_column = self._find_target_definition_column(max_total_section, definition_columns)
        if target_definition_column < 0:
            print("  [ERROR] 未找到目標 Definition 欄位")
            return {}
        
        print(f"  [TARGET] 目標 Definition 欄位: 第{target_definition_column+1}欄")
        
        # 3. 按順序掃描該 Definition 欄位，建立色彩映射
        for row_idx in range(6, len(df)):  # 從第7行開始
            if target_definition_column < len(df.columns):
                definition_value = str(df.iloc[row_idx, target_definition_column]).strip()
                if definition_value and definition_value != '':
                    # 取得對應的 BIN 名稱（Definition 欄位左邊3格）
                    bin_col = target_definition_column - 3
                    if bin_col >= 0 and bin_col < len(df.columns):
                        bin_name = str(df.iloc[row_idx, bin_col]).strip()
                        
                        if bin_name and bin_name not in bin_color_mapping:
                            if 'all pass' in definition_value.lower():
                                bin_color_mapping[bin_name] = self.color_config['all_pass']
                                print(f"    [GREEN_CIRCLE] BIN={bin_name} -> All Pass (綠色)")
                            else:
                                # 只為前 10 個 Fail BIN 分配顏色
                                if fail_color_index < len(self.color_config['fail_bins']):
                                    color = self.color_config['fail_bins'][fail_color_index]
                                    bin_color_mapping[bin_name] = color
                                    print(f"    [ART] BIN={bin_name} -> {definition_value[:20]}... ({color}) [第{fail_color_index+1}個Fail BIN]")
                                    fail_color_index += 1
                                else:
                                    # 第 11 個開始不分配顏色
                                    print(f"    [WHITE] BIN={bin_name} -> 不上色 (超過前10個Fail BIN)")
                                    # 不加入 bin_color_mapping，這樣就不會上色
        
        print(f"  [BOARD] BIN 色彩映射建立完成，共 {len(bin_color_mapping)} 個 BIN (1個All Pass + {fail_color_index}個Fail BIN)")
        return bin_color_mapping
    
    def _find_target_definition_column(self, max_total_section: int, definition_columns: List[int]) -> int:
        """
        找到最大 TOTAL 區段對應的 Definition 欄位
        
        邏輯：找到最接近且在最大 TOTAL 區段右邊的 Definition 欄位
        
        Args:
            max_total_section: 最大 TOTAL 區段的起始欄位索引
            definition_columns: 所有 Definition 欄位索引列表
            
        Returns:
            int: 目標 Definition 欄位索引，未找到返回 -1
        """
        # 找到最接近且在最大 TOTAL 區段右邊的 Definition 欄位
        target_definition = -1
        min_distance = float('inf')
        
        for def_col in definition_columns:
            if def_col > max_total_section:  # Definition 必須在 TOTAL 右邊
                distance = def_col - max_total_section
                if distance < min_distance:
                    min_distance = distance
                    target_definition = def_col
        
        if target_definition >= 0:
            print(f"  [TARGET] 最大 TOTAL 第{max_total_section+1}欄 → 最近 Definition 第{target_definition+1}欄（距離 {min_distance} 欄）")
        else:
            print(f"  [ERROR] 最大 TOTAL 第{max_total_section+1}欄右邊沒有找到 Definition 欄位")
        
        return target_definition
    
    def _show_definition_content(self, df: pd.DataFrame, definition_column: int):
        """
        顯示指定 Definition 欄位的內容供檢查
        
        Args:
            df: DataFrame
            definition_column: Definition 欄位索引
        """
        print(f"[BOARD] Definition 欄位（第{definition_column+1}欄）內容：")
        print("-" * 50)
        
        # 顯示從第7行開始的 Definition 內容（前10行）
        for row_idx in range(6, min(16, len(df))):  # 第7-16行
            if definition_column < len(df.columns):
                definition_value = str(df.iloc[row_idx, definition_column]).strip()
                if definition_value and definition_value != '':
                    # 同時顯示對應的 BIN 名稱
                    bin_col = definition_column - 3  # Definition 左邊3格是 BIN
                    bin_name = ""
                    if bin_col >= 0 and bin_col < len(df.columns):
                        bin_name = str(df.iloc[row_idx, bin_col]).strip()
                    
                    print(f"  第{row_idx+1}行: BIN={bin_name} → Definition={definition_value}")
        
        print("-" * 50)
    
    def _apply_bin_coloring_to_excel(self, workbook: xlsxwriter.Workbook, worksheet, 
                                    df: pd.DataFrame, bin_color_mapping: Dict[str, str]):
        """
        對 Excel 檔案的 Definition 欄位應用 BIN 色彩
        
        邏輯：
        1. 建立色彩格式物件
        2. 找到所有 Definition 欄位
        3. 根據 Definition 內容（All Pass 或 Fail BIN 名稱）應用對應色彩
        
        Args:
            workbook: xlsxwriter Workbook 物件
            worksheet: xlsxwriter Worksheet 物件
            df: DataFrame
            bin_color_mapping: BIN 色彩映射字典
        """
        print("  🖌 開始對 Definition 欄位應用色彩...")
        
        # 建立色彩格式物件
        definition_color_formats = {}
        
        # 為 All Pass 建立綠色格式
        definition_color_formats['All Pass'] = workbook.add_format({'bg_color': self.color_config['all_pass']})
        
        # 為每個 Fail BIN 的 Definition 建立對應色彩格式
        fail_color_index = 0
        for bin_name, color_hex in bin_color_mapping.items():
            if bin_name != '1':  # 跳過 All Pass（BIN=1）
                definition_color_formats[bin_name] = workbook.add_format({'bg_color': color_hex})
        
        # 找到所有 Definition 欄位
        definition_columns = self._find_definition_columns_robust(df)
        print(f"    [LOCATION] Definition 欄位: {[col+1 for col in definition_columns]}")
        
        colored_cells = 0
        
        # 對所有 Definition 欄位的內容應用色彩
        for row_idx in range(6, len(df)):  # 從第7行開始（數據行）
            for col_idx in definition_columns:
                if col_idx < len(df.columns):
                    definition_value = str(df.iloc[row_idx, col_idx]).strip()
                    
                    if definition_value and definition_value != '':
                        format_obj = None
                        
                        # 檢查是否為 All Pass
                        if 'all pass' in definition_value.lower():
                            format_obj = definition_color_formats['All Pass']
                            print(f"    [GREEN_CIRCLE] 第{row_idx+1}行，第{col_idx+1}欄: {definition_value} -> 綠色")
                        else:
                            # 取得對應的 BIN 名稱（Definition 左邊3格）
                            bin_col = col_idx - 3
                            if bin_col >= 0 and bin_col < len(df.columns):
                                bin_name = str(df.iloc[row_idx, bin_col]).strip()
                                if bin_name in definition_color_formats:
                                    format_obj = definition_color_formats[bin_name]
                                    color_hex = bin_color_mapping.get(bin_name, '')
                                    print(f"    [ART] 第{row_idx+1}行，第{col_idx+1}欄: {definition_value} -> {color_hex}")
                        
                        # 應用格式
                        if format_obj:
                            original_value = df.iloc[row_idx, col_idx]
                            if pd.isna(original_value):
                                worksheet.write(row_idx, col_idx, "", format_obj)
                            else:
                                worksheet.write(row_idx, col_idx, original_value, format_obj)
                            colored_cells += 1
        
        print(f"    [OK] 共為 {colored_cells} 個 Definition 儲存格應用了色彩")
    
    def get_bin_color_mapping(self, df: pd.DataFrame) -> Dict[str, str]:
        """
        取得 BIN 色彩映射（用於外部查詢）
        
        Args:
            df: DataFrame
            
        Returns:
            Dict[str, str]: BIN 色彩映射字典
        """
        definition_columns = self._find_definition_columns_robust(df)
        if not definition_columns:
            return {}
        
        return self._create_bin_color_mapping(df, definition_columns)


# 測試功能
def test_colorizer():
    """測試 FT Summary 上色器功能"""
    print("[TEST_TUBE] 開始測試 FT Summary 上色器...")
    
    # 模擬測試數據
    test_data = [
        ['Total', '899', 'Date:', '05/22/25 18:43:52', '', '', '', '', 'Total', '1059', 'Date:', 'Multiple Files'],
        ['Pass', '877', 'Computer:', 'ASLX-06', '', '', '', '', 'Pass', '964', 'Computer:', 'Batch Process'],
        ['Fail', '22', 'Lot ID:', 'Test.xlsx', '', '', '', '', 'Fail', '95', 'Lot ID:', 'FT_SUMMARY.csv'],
        ['Yield', '97.553%', '', '', '', '', '', '', 'Yield', '91.029%', '', ''],
        ['', '', '', '', 'Total', '449', 'Total', '450', '', '', '', ''],
        ['Bin', 'Count', '%', 'Definition', 'Note', 'Site 1', '%', 'Site 2', 'Bin', 'Count', '%', 'Definition'],
        ['1', '877', '97.553', 'All Pass', '', '439', '97.773', '438', '1', '964', '91.029', 'All Pass'],
        ['148', '2', '0.222', '0X02(w0xAA)', '', '2', '0.445', '0', '148', '12', '1.133', '0X02(w0xAA)']
    ]
    
    df = pd.DataFrame(test_data)
    colorizer = FTSummaryColorizer()
    
    # 測試 Definition 欄位定位
    definition_columns = colorizer._find_definition_columns_robust(df)
    print(f"Definition 欄位: {definition_columns}")
    
    # 測試色彩映射
    bin_mapping = colorizer.get_bin_color_mapping(df)
    print(f"BIN 色彩映射: {bin_mapping}")
    
    print("[OK] 測試完成")


if __name__ == "__main__":
    test_colorizer()