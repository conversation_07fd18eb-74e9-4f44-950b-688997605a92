# base_parser.py

基礎解析器架構模組，實現支援多廠商的解析器基礎架構，使用抽象基類和工廠模式。

## ParsingStrategy

解析策略枚舉類別，定義不同的解析策略。

### 枚舉值
- `SUBJECT_PATTERN`: 主旨模式解析
- `BODY_CONTENT`: 內文內容解析
- `SENDER_ANALYSIS`: 寄件者分析
- `ATTACHMENT_BASED`: 基於附件的解析
- `HYBRID`: 混合策略

## ParsingError

解析錯誤類別，繼承自 Exception，用於處理解析過程中的錯誤。

### 屬性
- `message` (str): 錯誤訊息
- `error_code` (str): 錯誤代碼
- `vendor_code` (str): 廠商代碼
- `context_data` (Dict[str, Any]): 上下文資料
- `timestamp` (datetime): 錯誤發生時間

### __init__

初始化解析錯誤。

**參數:**
- `message` (str): 錯誤訊息
- `error_code` (str): 錯誤代碼，預設為 "PARSE_ERROR"
- `vendor_code` (str): 廠商代碼，可選
- `context_data` (Optional[Dict[str, Any]]): 上下文資料，可選

**返回值:**
- None

### __str__

字串表示方法。

**返回值:**
- str: 格式化的錯誤訊息

### to_dict

轉換為字典格式。

**返回值:**
- Dict[str, Any]: 包含錯誤資訊的字典

## ParsingContext

解析上下文類別，包含解析過程中需要的所有資訊。

### 屬性
- `email_data` (EmailData): 郵件數據
- `vendor_code` (str): 廠商代碼
- `parsing_strategy` (ParsingStrategy): 解析策略
- `metadata` (Dict[str, Any]): 元數據
- `created_time` (datetime): 建立時間

### __init__

初始化解析上下文。

**參數:**
- `email_data` (EmailData): 郵件數據
- `vendor_code` (str): 廠商代碼
- `parsing_strategy` (ParsingStrategy): 解析策略，預設為 HYBRID
- `metadata` (Optional[Dict[str, Any]]): 元數據，可選

**返回值:**
- None

### add_metadata

新增元數據。

**參數:**
- `key` (str): 鍵名
- `value` (Any): 值

**返回值:**
- None

### get_metadata

取得元數據。

**參數:**
- `key` (str): 鍵名
- `default` (Any): 預設值，預設為 None

**返回值:**
- Any: 元數據值

## BaseParser

基礎解析器抽象類別，定義所有解析器必須實作的介面。

### vendor_code

廠商代碼屬性（抽象）。

**返回值:**
- str: 廠商代碼

### vendor_name

廠商名稱屬性（抽象）。

**返回值:**
- str: 廠商名稱

### supported_patterns

支援的模式列表屬性（抽象）。

**返回值:**
- List[str]: 支援的模式列表

### identify_vendor

識別廠商的抽象方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- VendorIdentificationResult: 廠商識別結果

### parse_email

解析郵件的抽象方法。

**參數:**
- `context` (ParsingContext): 解析上下文

**返回值:**
- EmailParsingResult: 解析結果

### can_parse

檢查是否可以解析此郵件。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- bool: 是否可以解析

**功能:**
- 呼叫 identify_vendor 方法
- 檢查識別結果和信心分數
- 信心分數需大於 0.5

### get_confidence_score

取得信心分數。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- float: 信心分數（0.0-1.0）

### validate_parsing_result

驗證解析結果。

**參數:**
- `result` (EmailParsingResult): 解析結果

**返回值:**
- bool: 驗證是否通過

**驗證規則:**
- 失敗結果總是有效
- 成功結果必須有廠商代碼
- MO 編號格式檢查（如果存在）

## VendorParser

廠商解析器基類，繼承自 BaseParser，提供通用的解析功能。

### 屬性
- `_confidence_threshold` (float): 信心分數閾值
- `_logger` (logging.Logger): 日誌記錄器

### __init__

初始化廠商解析器。

**返回值:**
- None

**功能:**
- 設定預設信心分數閾值為 0.7
- 初始化日誌記錄器

### set_confidence_threshold

設定信心分數閾值。

**參數:**
- `threshold` (float): 閾值（0.0-1.0）

**返回值:**
- None

**異常:**
- ValueError: 當閾值不在有效範圍時拋出

### get_confidence_threshold

取得信心分數閾值。

**返回值:**
- float: 當前閾值

### extract_mo_number

從文字中提取 MO 編號。

**參數:**
- `text` (str): 要搜尋的文字

**返回值:**
- Optional[str]: MO 編號，如果未找到則返回 None

**功能:**
- 使用正則表達式搜尋 MO 編號格式
- 支援多種 MO 編號模式
- 記錄提取過程

### extract_lot_number

從文字中提取批次編號。

**參數:**
- `text` (str): 要搜尋的文字

**返回值:**
- Optional[str]: 批次編號，如果未找到則返回 None

### extract_test_type

從文字中提取測試類型。

**參數:**
- `text` (str): 要搜尋的文字

**返回值:**
- Optional[str]: 測試類型，如果未找到則返回 None

### create_parsing_result

建立解析結果。

**參數:**
- `is_success` (bool): 是否成功
- `vendor_code` (Optional[str]): 廠商代碼，可選
- `mo_number` (Optional[str]): MO 編號，可選
- `lot_number` (Optional[str]): 批次編號，可選
- `test_type` (Optional[str]): 測試類型，可選
- `extracted_data` (Optional[Dict[str, Any]]): 提取的數據，可選
- `error_message` (Optional[str]): 錯誤訊息，可選
- `parsing_method` (Optional[str]): 解析方法，可選

**返回值:**
- EmailParsingResult: 解析結果物件

### log_parsing_attempt

記錄解析嘗試。

**參數:**
- `email_data` (EmailData): 郵件數據
- `success` (bool): 是否成功
- `details` (Optional[str]): 詳細資訊，可選

**返回值:**
- None

## ParserFactory

解析器工廠類別，負責管理和建立解析器實例。

### 屬性
- `_parsers` (Dict[str, BaseParser]): 已註冊的解析器
- `_logger` (logging.Logger): 日誌記錄器

### __init__

初始化解析器工廠。

**返回值:**
- None

### register_parser

註冊解析器。

**參數:**
- `parser` (BaseParser): 解析器實例

**返回值:**
- None

**異常:**
- ValueError: 當解析器已存在時拋出

### get_parser

取得指定廠商的解析器。

**參數:**
- `vendor_code` (str): 廠商代碼

**返回值:**
- Optional[BaseParser]: 解析器實例，如果不存在則返回 None

### get_all_parsers

取得所有已註冊的解析器。

**返回值:**
- List[BaseParser]: 解析器列表

### find_best_parser

找到最適合的解析器。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- Optional[BaseParser]: 最適合的解析器，如果沒有則返回 None

**功能:**
- 計算所有解析器的信心分數
- 選擇信心分數最高的解析器
- 確保信心分數超過閾值

### parse_with_best_parser

使用最適合的解析器解析郵件。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- EmailParsingResult: 解析結果

**功能:**
- 自動選擇最適合的解析器
- 建立解析上下文
- 執行解析並返回結果
- 處理解析過程中的異常

## 設計特點

### 抽象基類設計
- **介面統一**: 所有解析器實作相同介面
- **強制實作**: 抽象方法確保關鍵功能被實作
- **擴展性**: 易於新增新的廠商解析器

### 工廠模式
- **集中管理**: 統一管理所有解析器
- **動態選擇**: 根據郵件內容自動選擇解析器
- **解耦合**: 使用者無需直接操作具體解析器

### 錯誤處理
- **自定義異常**: 提供詳細的錯誤資訊
- **上下文保存**: 保留錯誤發生時的上下文
- **結構化資料**: 錯誤資訊可序列化

### 信心分數機制
- **量化評估**: 數值化表示解析器的適用性
- **閾值控制**: 可調整的信心分數閾值
- **競爭選擇**: 多個解析器競爭，選擇最佳者
