# email_processor.py

郵件處理引擎模組，整合所有解析器，提供統一的郵件處理服務。支援 Outlook 2019+，採用非同步處理和現代架構模式。

## EmailProcessingConfig

郵件處理配置資料類別，用於設定郵件處理的各種參數。

### 屬性
- `max_concurrent_processing` (int): 最大並發處理數量，預設為5
- `retry_attempts` (int): 重試次數，預設為3
- `processing_timeout` (float): 處理超時時間（秒），預設為300.0
- `enable_monitoring` (bool): 是否啟用監控，預設為True
- `outlook_version` (str): Outlook 版本，預設為"2019"
- `batch_size` (int): 批次大小，預設為10
- `enable_statistics` (bool): 是否啟用統計，預設為True
- `temp_directory` (str): 暫存目錄，預設為"/tmp/outlook_processing"
- `supported_versions` (List[str]): 支援的版本列表，預設為["2016", "2019", "2021", "365"]

## ProcessingMetrics

處理指標資料類別，用於追蹤處理效能和統計資訊。

### 屬性
- `total_processed` (int): 總處理數量，預設為0
- `total_failed` (int): 總失敗數量，預設為0
- `total_success` (int): 總成功數量，預設為0
- `processing_start_time` (float): 處理開始時間戳，預設為當前時間
- `average_processing_time` (float): 平均處理時間，預設為0.0
- `last_processing_time` (Optional[float]): 最後處理時間，可選

### success_rate

成功率屬性，計算處理成功的百分比。

**返回值:**
- float: 成功率百分比（0.0-100.0）

### uptime

運行時間屬性，計算從開始處理到現在的時間。

**返回值:**
- float: 運行時間（秒）

## EmailProcessor

郵件處理引擎類別，整合解析器、佇列、監控等組件，提供完整的郵件處理服務。

### __init__

初始化郵件處理器。

**參數:**
- `email_reader` (EmailReader): 郵件讀取器
- `task_queue` (TaskQueue): 任務佇列
- `outlook_adapter` (OutlookAdapter): Outlook 適配器
- `config` (EmailProcessingConfig): 處理配置

**返回值:**
- None

### is_running

檢查是否運行中的屬性。

**返回值:**
- bool: 是否運行中

### processed_count

已處理郵件數量的屬性。

**返回值:**
- int: 已處理郵件數量

### failed_count

失敗郵件數量的屬性。

**返回值:**
- int: 失敗郵件數量

### process_email

處理單一郵件的方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- EmailParsingResult: 處理結果

**異常:**
- Exception: 處理過程中的各種異常

### process_batch

批次處理郵件的方法。

**參數:**
- `emails` (List[EmailData]): 郵件列表

**返回值:**
- List[EmailParsingResult]: 處理結果列表

**異常:**
- Exception: 批次處理過程中的各種異常

### process_email_with_retry

帶重試的郵件處理方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- EmailParsingResult: 處理結果

**異常:**
- Exception: 重試處理過程中的各種異常

### validate_email_data

驗證郵件數據的方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- bool: 驗證是否通過

**異常:**
- Exception: 驗證過程中的異常（會被捕獲並返回False）

### is_outlook_version_supported

檢查 Outlook 版本是否支援的方法。

**返回值:**
- bool: 版本是否支援

### validate_configuration

驗證配置的方法。

**返回值:**
- bool: 配置是否有效

**異常:**
- Exception: 驗證過程中的異常（會被捕獲並返回False）

### connect_to_outlook

連接到 Outlook 的方法。

**返回值:**
- bool: 連接是否成功

**異常:**
- Exception: 連接過程中的異常（會被記錄並返回False）

### fetch_new_emails

獲取新郵件的方法。

**返回值:**
- List[EmailData]: 新郵件列表

**異常:**
- Exception: 獲取過程中的異常（會被記錄並返回空列表）

### enqueue_email_processing

將郵件加入處理佇列的方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- str: 任務 ID 或空字符串（如果失敗）

**異常:**
- Exception: 加入佇列過程中的異常（會被記錄並返回空字符串）

### get_processing_status

獲取處理狀態的方法。

**參數:**
- `task_id` (str): 任務 ID

**返回值:**
- str: 處理狀態或"unknown"（如果失敗）

**異常:**
- Exception: 獲取狀態過程中的異常（會被捕獲並返回"unknown"）

### monitor_inbox_once

執行一次收件夾監控的方法。

**返回值:**
- List[EmailData]: 未讀郵件列表

**異常:**
- Exception: 監控過程中的異常（會被記錄並返回空列表）

### process_attachments

處理郵件附件的方法。

**參數:**
- `email_data` (EmailData): 郵件數據

**返回值:**
- List[str]: 附件路徑列表

**異常:**
- Exception: 處理附件過程中的異常（會被記錄並返回空列表）

### start_continuous_monitoring

開始持續監控的方法。

**參數:**
- `callback` (Optional[Callable]): 新郵件回調函數，可選

**返回值:**
- None

**異常:**
- Exception: 監控過程中的異常（會被記錄並設定運行狀態為False）

### stop_monitoring

停止監控的方法。

**返回值:**
- None

**異常:**
- Exception: 停止監控過程中的異常（會被記錄）

### graceful_shutdown

優雅關閉的方法。

**參數:**
- `timeout` (float): 超時時間（秒），預設為30.0

**返回值:**
- None

**異常:**
- asyncio.TimeoutError: 關閉超時（會被記錄為警告）
- Exception: 關閉過程中的其他異常（會被記錄）

### get_processing_metrics

獲取處理指標的方法。

**返回值:**
- Dict[str, Any]: 包含各種處理指標的字典

### log_processing_event

記錄處理事件的方法。

**參數:**
- `event` (str): 事件描述
- `context` (Dict[str, Any]): 事件上下文

**返回值:**
- None

## 私有方法

### _is_permanent_error

判斷是否為永久性錯誤的私有方法。

**參數:**
- `error_message` (str): 錯誤訊息

**返回值:**
- bool: 是否為永久性錯誤

### _update_metrics

更新處理指標的私有方法。

**參數:**
- `success` (bool): 是否成功
- `processing_time` (float): 處理時間

**返回值:**
- None
