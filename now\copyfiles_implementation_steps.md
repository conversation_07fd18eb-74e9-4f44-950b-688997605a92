# CopyFiles 功能實作執行步驟

## [CALENDAR] 實作時程表

### Week 1: 基礎架構建立

#### Day 1-2: 建立檔案處理器框架
```bash
# 1. 建立目錄結構
mkdir -p src/infrastructure/adapters/file_handlers

# 2. 建立基礎檔案
touch src/infrastructure/adapters/file_handlers/__init__.py
touch src/infrastructure/adapters/file_handlers/base_file_handler.py
touch src/infrastructure/adapters/file_handlers/file_handler_factory.py
```

#### Day 3-4: 實作 BaseFileHandler
- 實作基礎抽象類別
- 包含通用的檔案搜尋和複製邏輯
- 添加單元測試

#### Day 5: 實作 FileHandlerFactory
- 工廠模式實作
- 廠商註冊機制

### Week 2: 廠商實作

#### Day 1-2: GTK 檔案處理器
```python
# src/infrastructure/adapters/file_handlers/gtk_file_handler.py
- 實作 GTKFileHandler
- 支援雙重路徑搜尋
- 壓縮檔優先邏輯
```

#### Day 3: XAHT & JCET 檔案處理器
```python
# src/infrastructure/adapters/file_handlers/xaht_file_handler.py
# src/infrastructure/adapters/file_handlers/jcet_file_handler.py
- 實作較簡單的單路徑搜尋
```

#### Day 4: NFME & ETD 檔案處理器
```python
# src/infrastructure/adapters/file_handlers/nfme_file_handler.py
# src/infrastructure/adapters/file_handlers/etd_file_handler.py
- NFME: 特殊檔案類型處理
- ETD: 資料夾複製功能
```

#### Day 5: 整合測試
- 各廠商處理器的整合測試
- 錯誤處理測試

### Week 3: 系統整合

#### Day 1-2: 整合到郵件同步服務
```python
# 修改 src/infrastructure/adapters/email_inbox/email_sync_service.py

def _process_vendor_files(self, email_id: int, parse_result: Dict[str, Any]):
    """在郵件解析後自動處理廠商檔案"""
    # 實作整合邏輯
```

#### Day 3: 資料庫更新
```sql
-- 添加檔案處理相關欄位
ALTER TABLE emails ADD COLUMN vendor_files_copied BOOLEAN DEFAULT FALSE;
ALTER TABLE emails ADD COLUMN vendor_files_path TEXT;
ALTER TABLE emails ADD COLUMN files_copied_at TIMESTAMP;
```

#### Day 4-5: 端到端測試
- 完整流程測試
- 效能測試
- 文件更新

## [ROCKET] 快速開始實作

### Step 1: 建立基礎檔案 (今天可完成)

```python
# src/infrastructure/adapters/file_handlers/base_file_handler.py
"""開始實作基礎類別"""

import os
import shutil
import tempfile
from abc import ABC, abstractmethod
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
import time

from src.infrastructure.logging.logger_manager import LoggerManager


class BaseFileHandler(ABC):
    """檔案處理器基礎類別"""
    
    def __init__(self, source_base_path: str, vendor_code: str):
        self.source_base_path = Path(source_base_path)
        self.vendor_code = vendor_code
        self.logger = LoggerManager().get_logger(f"{vendor_code}FileHandler")
        self.archive_extensions = {'.zip', '.rar', '.7z'}
        
    # ... 實作其他方法
```

### Step 2: 實作第一個廠商 (GTK)

```python
# src/infrastructure/adapters/file_handlers/gtk_file_handler.py
"""GTK 是最複雜的，先實作它"""

from .base_file_handler import BaseFileHandler

class GTKFileHandler(BaseFileHandler):
    """GTK 廠商檔案處理器"""
    
    def __init__(self, source_base_path: str):
        super().__init__(source_base_path, "GTK")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        return [
            self.source_base_path / "GTK" / "temp",
            self.source_base_path / "GTK" / "FT" / pd / lot
        ]
```

### Step 3: 建立測試

```python
# tests/test_file_handlers.py
"""測試檔案處理器"""

import pytest
from pathlib import Path
import tempfile

def test_gtk_file_handler_with_mo():
    """測試 GTK 使用 MO 搜尋"""
    # 建立測試環境
    # 執行測試
    # 驗證結果
```

## [CHART] 實作優先順序

### 高優先級 (必須先實作)
1. **BaseFileHandler** - 核心基礎類別
2. **GTKFileHandler** - 最常用的廠商
3. **FileHandlerFactory** - 管理所有處理器

### 中優先級
4. **XAHTFileHandler** - 第二常用
5. **JCETFileHandler** - 第三常用
6. 整合到 email_sync_service.py

### 低優先級
7. **NFMEFileHandler** - 特殊檔案類型
8. **ETDFileHandler** - 較少使用
9. 效能優化和快取機制

## [TOOL] 環境準備

### 1. 設定環境變數
```bash
# .env
FILE_SOURCE_BASE_PATH=/mnt/share  # 或實際的網路磁碟路徑
FILE_TEMP_BASE_PATH=/tmp/email_processor
GTK_SOURCE_PATH=/mnt/share/GTK
XAHT_SOURCE_PATH=/mnt/share/XAHT
```

### 2. 建立測試資料結構
```bash
# 建立測試用的目錄結構
mkdir -p test_data/source/{GTK,XAHT,JCET}/{temp,FT}
mkdir -p test_data/temp
```

### 3. 準備測試檔案
```bash
# 建立測試用的壓縮檔
echo "test content" > test_file.txt
zip test_data/source/GTK/temp/MO123456.zip test_file.txt
```

## [OK] 實作檢查清單

- [ ] BaseFileHandler 類別完成
- [ ] 單元測試框架建立
- [ ] GTKFileHandler 實作
- [ ] GTK 測試通過
- [ ] FileHandlerFactory 實作
- [ ] 至少 3 個廠商處理器完成
- [ ] 整合到郵件同步服務
- [ ] 端到端測試通過
- [ ] 文件更新完成
- [ ] Code Review 通過

## [IDEA] 實作提示

1. **先簡單後複雜** - 先實作基本的檔案複製，再加入進階功能
2. **重視測試** - 每個處理器都要有對應的測試
3. **保持 VBA 邏輯** - 確保行為與原始 VBA 一致
4. **注意錯誤處理** - 網路磁碟可能不穩定
5. **記錄日誌** - 方便追蹤問題

## [UP] 預期成果

完成後，系統將能夠：
- [OK] 自動識別廠商並複製相關檔案
- [OK] 支援 5+ 個廠商的檔案處理
- [OK] 智能避免重複複製
- [OK] 完整的錯誤處理和日誌
- [OK] 與現有郵件處理流程無縫整合

開始實作吧！建議從 BaseFileHandler 開始。