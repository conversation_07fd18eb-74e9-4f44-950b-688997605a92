/**
 * 郵件收件夾 UI 工具和通用功能
 * 提供載入狀態、通知、對話框等 UI 組件
 */

/**
 * UI 工具類 - 提供通用的 UI 操作方法
 */
class EmailUIUtils {
    constructor() {}
    
    /**
     * 顯示載入狀態
     */
    static showLoading(elements) {
        if (elements && elements.loadingOverlay) {
            elements.loadingOverlay.classList.remove('hidden');
        }
    }
    
    /**
     * 隱藏載入狀態
     */
    static hideLoading(elements) {
        if (elements && elements.loadingOverlay) {
            elements.loadingOverlay.classList.add('hidden');
        }
    }
    
    /**
     * 顯示同步狀態
     */
    static showSyncStatus(elements, message) {
        if (elements && elements.syncStatus) {
            const statusText = elements.syncStatus.querySelector('.status-text');
            if (statusText) {
                statusText.textContent = message;
            }
            elements.syncStatus.classList.remove('hidden');
        }
    }
    
    /**
     * 隱藏同步狀態
     */
    static hideSyncStatus(elements) {
        if (elements && elements.syncStatus) {
            elements.syncStatus.classList.add('hidden');
        }
    }
    
    /**
     * 顯示確認對話框
     */
    static showConfirmDialog(elements, title, message) {
        return new Promise((resolve) => {
            const dialog = elements && elements.confirmDialog;
            if (!dialog) {
                resolve(false);
                return;
            }
            
            const titleElement = dialog.querySelector('#dialog-title');
            const messageElement = dialog.querySelector('#dialog-message');
            
            if (titleElement) titleElement.textContent = title;
            if (messageElement) messageElement.textContent = message;
            
            dialog.classList.remove('hidden');
            
            const confirmBtn = dialog.querySelector('#dialog-confirm');
            const cancelBtn = dialog.querySelector('#dialog-cancel');
            
            const handleConfirm = () => {
                dialog.classList.add('hidden');
                confirmBtn?.removeEventListener('click', handleConfirm);
                cancelBtn?.removeEventListener('click', handleCancel);
                resolve(true);
            };
            
            const handleCancel = () => {
                dialog.classList.add('hidden');
                confirmBtn?.removeEventListener('click', handleConfirm);
                cancelBtn?.removeEventListener('click', handleCancel);
                resolve(false);
            };
            
            confirmBtn?.addEventListener('click', handleConfirm);
            cancelBtn?.addEventListener('click', handleCancel);
        });
    }
    
    /**
     * 關閉對話框
     */
    static closeDialog(elements) {
        if (elements && elements.confirmDialog) {
            elements.confirmDialog.classList.add('hidden');
        }
    }
    
    /**
     * 顯示通知
     */
    static showNotification(elements, message, type = 'info') {
        if (!elements || !elements.notification) return;
        
        const notification = elements.notification;
        notification.className = `notification ${type}`;
        
        const textElement = notification.querySelector('.notification-text');
        if (textElement) {
            textElement.textContent = message;
        }
        
        notification.classList.remove('hidden');
        
        // 自動隱藏
        setTimeout(() => {
            EmailUIUtils.closeNotification(elements);
        }, 5000);
    }
    
    /**
     * 關閉通知
     */
    static closeNotification(elements) {
        if (elements && elements.notification) {
            elements.notification.classList.add('hidden');
        }
    }
    
    /**
     * 顯示成功訊息
     */
    static showSuccessMessage(message) {
        EmailUIUtils.showMessage(message, 'success');
    }
    
    /**
     * 顯示錯誤訊息
     */
    static showErrorMessage(message) {
        EmailUIUtils.showMessage(message, 'error');
    }
    
    /**
     * 顯示訊息
     */
    static showMessage(message, type) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message-notification ${type}`;
        messageDiv.innerHTML = `
            <div class="message-content">
                <div class="message-icon">${type === 'success' ? '✅' : '❌'}</div>
                <div class="message-text">${EmailUIUtils.escapeHtml(message)}</div>
            </div>
        `;
        
        const bgColor = type === 'success' ? '#4CAF50' : '#F44336';
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${bgColor};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            min-width: 300px;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(messageDiv);
        
        // 3秒後自動移除
        setTimeout(() => {
            messageDiv.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                messageDiv.remove();
            }, 300);
        }, 3000);
    }
    
    /**
     * 工具方法：截斷文字
     */
    static truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
    
    /**
     * 工具方法：HTML轉義
     */
    static escapeHtml(text) {
        if (!text) return '';
        return String(text)
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }
    
    /**
     * 工具方法：格式化檔案大小
     */
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 更新統計資訊到 DOM
     */
    static renderStatistics(elements, statistics) {
        if (!elements || !statistics) return;
        
        if (elements.totalEmails) {
            elements.totalEmails.textContent = statistics.total_emails || 0;
        }
        if (elements.unreadEmails) {
            elements.unreadEmails.textContent = statistics.unread_emails || 0;
        }
        if (elements.totalSenders) {
            elements.totalSenders.textContent = statistics.total_senders || 0;
        }
        if (elements.allCount) {
            elements.allCount.textContent = statistics.total_emails || 0;
        }
    }
    
    /**
     * 更新分頁顯示
     */
    static updatePaginationDisplay(elements, currentPage, pageSize, totalCount) {
        if (!elements) return;
        
        const start = (currentPage - 1) * pageSize + 1;
        const end = Math.min(currentPage * pageSize, totalCount);
        
        if (elements.pageStart) elements.pageStart.textContent = start;
        if (elements.pageEnd) elements.pageEnd.textContent = end;
        if (elements.totalCountSpan) elements.totalCountSpan.textContent = totalCount;
        
        // 更新分頁按鈕狀態
        if (elements.prevPageBtn) {
            elements.prevPageBtn.disabled = currentPage <= 1;
        }
        if (elements.nextPageBtn) {
            elements.nextPageBtn.disabled = end >= totalCount;
        }
    }
    
    /**
     * 添加動畫樣式到頁面
     */
    static addAnimationStyles() {
        if (document.querySelector('#message-animation-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'message-animation-styles';
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
            .message-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }
        `;
        document.head.appendChild(style);
    }
}

// 在頁面載入時添加必要的樣式
document.addEventListener('DOMContentLoaded', function() {
    EmailUIUtils.addAnimationStyles();
});

// 全域可用
window.EmailUIUtils = EmailUIUtils;