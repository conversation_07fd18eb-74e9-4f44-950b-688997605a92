<!DOCTYPE html>
<html>
<head>
    <title>同步功能測試</title>
</head>
<body>
    <h1>同步功能測試</h1>
    <button id="testSync">測試同步</button>
    <div id="result"></div>
    
    <script>
        document.getElementById('testSync').addEventListener('click', async () => {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '測試中...';
            
            try {
                const response = await fetch('/api/sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    },
                    body: JSON.stringify({
                        max_emails: 100
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    resultDiv.innerHTML = `<span style="color: green;">✅ 同步成功: ${result.message}</span>`;
                } else {
                    resultDiv.innerHTML = `<span style="color: red;">❌ 同步失敗: ${result.message}</span>`;
                }
                
            } catch (error) {
                resultDiv.innerHTML = `<span style="color: red;">❌ 請求失敗: ${error.message}</span>`;
            }
        });
    </script>
</body>
</html>