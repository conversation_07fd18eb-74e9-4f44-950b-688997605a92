/* 
 * 布局樣式模組
 * 包含主要內容區域、控制面板、時間軸等布局樣式
 */

/* ==================== 主要內容區域 ==================== */
.main-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    min-height: 600px;
}

/* ==================== 控制區域布局 ==================== */
.controls-container {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    align-items: start;
}

/* ==================== 資料夾輸入區域樣式 ==================== */
.folder-input-card {
    background: var(--bg-light);
    border: 2px dashed #dee2e6;
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition);
}

.folder-input-card:hover {
    border-color: var(--primary-color);
    background: #f0f4ff;
}

.folder-input-card .folder-icon {
    font-size: 2.5em;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.folder-input-card .folder-input {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.folder-input-card .primary-btn {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.folder-input-card .primary-btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* ==================== 詳細資料容器 ==================== */
.details-container {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.detail-container {
    min-height: 300px;
}

.detail-item {
    background: white;
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    margin-bottom: 15px;
    box-shadow: var(--shadow-xs);
    transition: var(--transition);
    overflow: hidden;
}

.detail-item:hover {
    box-shadow: var(--shadow-md);
}

.detail-header {
    padding: 18px 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: background 0.3s;
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
    background: var(--gradient-light);
}

.detail-header:hover {
    background: linear-gradient(135deg, #e9ecff 0%, var(--bg-light) 100%);
}

.detail-header i {
    color: var(--primary-color);
    transition: transform 0.3s;
    font-size: 16px;
}

.detail-header.expanded i {
    transform: rotate(90deg);
}

.detail-content {
    padding: 0 20px 20px;
    border-top: 1px solid #f0f0f0;
    margin-top: -1px;
    animation: slideDown 0.3s ease-out;
}

/* ==================== 時間軸布局 ==================== */
.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.timeline-header h3 {
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-controls {
    display: flex;
    gap: 10px;
}

.filter-btn {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 20px;
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: var(--transition);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-primary);
}

.timeline-item {
    position: relative;
    margin-bottom: 25px;
    background: white;
    border-radius: var(--radius-lg);
    padding: 20px;
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
    transition: transform 0.3s;
}

.timeline-item:hover {
    transform: translateX(5px);
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -37px;
    top: 25px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 3px solid white;
    box-shadow: 0 0 0 3px var(--primary-color);
}

.timeline-item.matched::before {
    background: var(--success-color);
    box-shadow: 0 0 0 3px var(--success-color);
}

.timeline-item.unmatched::before {
    background: var(--warning-color);
    box-shadow: 0 0 0 3px var(--warning-color);
}

.timeline-item.cta {
    border-left-color: var(--error-color);
}

.timeline-item.cta::before {
    background: var(--error-color);
    box-shadow: 0 0 0 3px var(--error-color);
}

.timeline-content {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 15px;
    align-items: start;
}

/* ==================== 檔案資訊布局 ==================== */
.file-info {
    flex: 1;
}

.file-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.file-name {
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 8px;
    word-break: break-all;
}

.file-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.file-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
}

.action-btn:hover {
    background: var(--bg-light);
    border-color: var(--primary-color);
}

.timestamp {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 10px;
}
