"""
JCET 廠商檔案處理器
對應 VBA 的 CopyFilesJCET 函數
"""

from pathlib import Path
from typing import List

from .base_file_handler import BaseFileHandler


class JCETFileHandler(BaseFileHandler):
    """
    JCET 廠商檔案處理器
    
    VBA 邏輯：
    - 從 \JCET\JCET\ 搜尋包含 MO 的壓縮檔
    - 邏輯與 XAHT 幾乎相同，只是路徑不同
    """
    
    def __init__(self, source_base_path: str):
        """初始化 JCET 檔案處理器"""
        super().__init__(source_base_path, "JCET")
        
    def get_source_paths(self, pd: str = "default", lot: str = "default", 
                        mo: str = "") -> List[Path]:
        """
        JCET 的來源路徑
        
        VBA: sourcePathJCET = sourcePath & "\JCET\JCET\"
        """
        return [self.source_base_path / "JCET" / "JCET"]
        
    def get_file_patterns(self, mo: str, lot: str, pd: str) -> List[str]:
        """
        JCET 的檔案搜尋模式
        
        VBA: file = Dir(sourcePathJCET & "*" & fileName & "*")
        """
        patterns = []
        
        if mo:
            patterns.append(f"*{mo}*")  # VBA 模式：包含 MO
            
        return patterns
        
    def _supports_folder_copy(self) -> bool:
        """JCET 不支援資料夾複製"""
        return False