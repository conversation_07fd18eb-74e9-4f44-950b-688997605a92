# 一鍵完成程式碼對比處理 - 命令列工具使用說明書

## 概述

`code_comparison.py` 是一個命令列工具，用於執行「一鍵完成程式碼對比處理」功能。此工具重用 `EQCBin1FinalProcessor.one_click_complete_comparison()` 方法，提供完整的4階段程式碼對比流程。

## 系統要求

### Python 檔案依賴

確保以下 Python 檔案存在於專案目錄中：

```
專案目錄/
├── code_comparison.py                              # 主執行檔案
└── src/
    └── infrastructure/
        └── adapters/
            ├── excel/
            │   ├── eqc/
            │   │   ├── eqc_bin1_final_processor.py     # 核心處理器
            │   │   ├── utils/
            │   │   │   └── timestamp_extractor.py      # 時間戳提取器
            │   │   ├── hyperlinks/
            │   │   │   └── hyperlink_processor.py      # 超連結處理器
            │   │   ├── monitoring/
            │   │   │   ├── progress_monitor.py         # 進度監控
            │   │   │   └── debug_logger.py             # [EXCEPT_CHAR]錯記錄器
            │   │   └── processors/
            │   │       ├── eqc_statistics_calculator.py # 統計計算器
            │   │       ├── eqc_file_scanner.py          # 檔案掃描器
            │   │       └── eqc_bin1_processor.py        # BIN1 處理器
            │   ├── cta/
            │   │   └── cta_integrated_processor.py     # CTA 整合處理器
            │   └── ft_eqc_grouping_processor.py        # FT-EQC 分組處理器
            └── filesystem/
                └── chinese_path_processor.py           # 中文路徑處理器
```

### Python 套件依賴

安裝必要的 Python 套件：

```bash
pip install pandas numpy openpyxl xlsxwriter python-dotenv pytz
```

或使用虛擬環境：

```bash
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

pip install pandas numpy openpyxl xlsxwriter python-dotenv pytz
```

## 環境變數設定 (.env)

在專案根目錄創建 `.env` 檔案，並設定以下變數：

```env
# BIN1 保護機制
BIN1_PROTECTION=true

# 檔案自動刪[EXCEPT_CHAR]設定
DELETE_FILE_EXTENSIONS=dlx,mdb

# 檔案過濾關鍵字
EXCLUDED_FILE_KEYWORDS=eqctotaldata,eqcfaildata,summary,correlation

# FT 處理關鍵字
FT_PROCESSING_KEYWORDS=auto_qc,ft,final_test

# QC 處理關鍵字  
QC_PROCESSING_KEYWORDS=qc,quality_control,eqc

# 中文路徑處理
CHINESE_PATH_PROCESSING=true

# AI 提供者設定 (可選)
AI_PROVIDER=ollama
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EXPECTED_VECTOR_DIMENSION=384
```

## 使用方法

### 基本語法

```bash
python3 code_comparison.py <folder_path> [options]
```

### 參數說明

| 參數 | 說明 |
|------|------|
| `folder_path` | CSV 檔案所在的資料夾路徑（必填） |
| `--code-region` | 程式碼區間設定，格式：main_start,main_end,backup_start,backup_end |
| `--verbose`, `-v` | 詳細輸出模式 |
| `--version` | 顯示版本資訊 |
| `--help`, `-h` | 顯示說明 |

### 程式碼區間設定格式

程式碼區間參數格式：`main_start,main_end,backup_start,backup_end`

範例：`298,335,1565,1600`
- 主要區間：第298行到第335行
- 備用區間：第1565行到第1600行

## 4階段處理流程

### Stage 1: 自動生成 EQCTOTALDATA
- 自動發現並整合目錄下所有 CSV 檔案
- 生成完整的 EQCTOTALDATA.csv 檔案
- SPD 檔案自動轉換為 CSV 格式
- 執行 FT-EQC 配對處理

### Stage 2: 程式碼區間檢測
- 智慧檢測程式碼區間
- 識別主要和備用程式碼區間
- 驗證程式碼區間的有效性

### Stage 3: 雙重搜尋機制
- 執行雙重搜尋演算法
- 程式碼對比分析
- 區間對比分析

### Stage 4: 完整報告生成
- 生成完整的分析報告
- 產生 Excel 格式檔案
- 整合所有處理結果

## 使用範例

### 範例 1：基本處理（使用預設程式碼區間）
```bash
python3 code_comparison.py doc/20250523
```

### 範例 2：自訂程式碼區間
```bash
python3 code_comparison.py doc/20250523 --code-region 298,335,1565,1600
```

### 範例 3：詳細輸出模式
```bash
python3 code_comparison.py doc/20250523 --verbose
```

### 範例 4：完整模式（自訂區間 + 詳細輸出）
```bash
python3 code_comparison.py doc/20250523 --code-region 298,335,1565,1600 --verbose
```

### 範例 5：處理不同路徑
```bash
# 使用相對路徑
python3 code_comparison.py ./data/test_folder --verbose

# 使用絕對路徑
python3 code_comparison.py "/path/to/csv/files" --code-region 300,400,1600,1700

# Windows 路徑
python3 code_comparison.py "C:\Data\CSV Files" --verbose
```

## 輸出結果說明

### 生成檔案

工具會在指定的資料夾中生成以下檔案：

| 檔案名稱 | 說明 |
|---------|------|
| `EQCTOTALDATA.csv` | 主要整合資料檔案 |
| `EQCTOTALDATA_RAW.csv` | 原始資料備份 |
| `EQCTOTALDATA.xlsx` | Excel 格式報告 |
| `EQC_BIN1_WITH_STATISTICS.csv` | 統計資料檔案 |
| `code_comparison_result_YYYYMMDD_HHMMSS.json` | 處理結果 JSON 檔案 |

### 處理結果顯示

工具會顯示以下資訊：

#### [CHART] 處理結果摘要
- 整體執行狀態（成功/失敗）
- 總執行時間
- 生成檔案清單和大小

#### [REFRESH] 4階段執行狀態
- Stage 1: 自動生成 EQCTOTALDATA
- Stage 2: 程式碼區間檢測
- Stage 3: 雙重搜尋機制
- Stage 4: 完整報告生成

#### [UP] 統計資訊
- 處理的檔案數量
- 資料處理統計
- 錯誤和警告訊息

## 錯誤處理

### 常見錯誤及解決方法

#### 1. 匯入錯誤
```
[ERROR] 錯誤: 無法匯入核心處理器
```
**解決方法：**
- 確認 `src/infrastructure/adapters/excel/eqc/eqc_bin1_final_processor.py` 存在
- 檢查所有依賴的 Python 檔案是否完整
- 安裝必要的 Python 套件

#### 2. 資料夾路徑錯誤
```
[ERROR] 執行失敗: 資料夾不存在: /path/to/folder
```
**解決方法：**
- 檢查路徑是否正確
- 確認資料夾確實存在
- 使用絕對路徑或正確的相對路徑

#### 3. 程式碼區間格式錯誤
```
[ERROR] 執行失敗: 程式碼區間解析失敗: 程式碼區間格式錯誤，需要4個數字
```
**解決方法：**
- 確認格式為：`main_start,main_end,backup_start,backup_end`
- 使用正整數
- 確保起始位置小於結束位置

#### 4. 權限問題
```
[ERROR] 權限錯誤
```
**解決方法：**
- 確認對資料夾有讀取權限
- 確認對輸出位置有寫入權限
- 檢查檔案是否被其他程式占用

#### 5. 記憶體不足
```
[ERROR] 記憶體錯誤
```
**解決方法：**
- 確保系統有足夠的可用記憶體
- 關閉不必要的程式
- 分批處理大量檔案

## 效能最佳化建議

### 處理大量檔案的建議

1. **足夠的記憶體**：確保系統有足夠記憶體處理大型檔案

2. **使用 SSD**：將檔案放在 SSD 上可提升處理速度

3. **關閉不必要的程式**：釋放系統資源

4. **分批處理**：對於極大量的檔案，建議分批處理

## 與網頁版本的差異

| 功能 | 網頁版本 | 命令列版本 |
|------|---------|-----------|
| 使用方式 | 瀏覽器操作 | 命令列執行 |
| 互動性 | 即時互動 | 批次執行 |
| 進度顯示 | 圖形界面 | 文字輸出 |
| 自動化 | 手動觸發 | 可腳本化 |
| 結果查看 | 網頁預覽 | 檔案輸出 |
| 程式碼區間設定 | 視覺化介面 | 命令列參數 |

## 自動化腳本範例

### Bash 腳本範例
```bash
#!/bin/bash
# 批量處理多個資料夾的程式碼對比

folders=("data/batch1" "data/batch2" "data/batch3")
code_region="298,335,1565,1600"

for folder in "${folders[@]}"; do
    echo "處理資料夾: $folder"
    python3 code_comparison.py "$folder" --code-region "$code_region" --verbose
    
    if [ $? -eq 0 ]; then
        echo "[OK] 成功完成: $folder"
    else
        echo "[ERROR] 處理失敗: $folder"
    fi
    echo "---"
done
```

### Python 腳本範例
```python
#!/usr/bin/env python3
import subprocess
import os
import sys

def process_folder(folder_path, code_region=None, verbose=False):
    """處理單個資料夾"""
    cmd = ["python3", "code_comparison.py", folder_path]
    
    if code_region:
        cmd.extend(["--code-region", code_region])
    
    if verbose:
        cmd.append("--verbose")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def main():
    folders = [
        "data/batch1",
        "data/batch2", 
        "data/batch3"
    ]
    
    code_region = "298,335,1565,1600"
    
    for folder in folders:
        if not os.path.exists(folder):
            print(f"[WARNING] 資料夾不存在: {folder}")
            continue
            
        print(f"[REFRESH] 處理資料夾: {folder}")
        success, stdout, stderr = process_folder(folder, code_region, verbose=True)
        
        if success:
            print(f"[OK] 成功完成: {folder}")
        else:
            print(f"[ERROR] 處理失敗: {folder}")
            if stderr:
                print(f"   錯誤: {stderr}")
        
        print("-" * 50)

if __name__ == "__main__":
    main()
```

## 進階使用技巧

### 1. 結合其他工具
```bash
# 先執行 CSV to Summary，再執行程式碼對比
python3 csv_to_summary.py doc/20250523 --excel
python3 code_comparison.py doc/20250523 --code-region 298,335,1565,1600 --verbose
```

### 2. 日誌記錄
```bash
# 將輸出記錄到日誌檔案
python3 code_comparison.py doc/20250523 --verbose > process_log.txt 2>&1
```

### 3. 條件式處理
```bash
# 只有在資料夾存在時才處理
if [ -d "doc/20250523" ]; then
    python3 code_comparison.py doc/20250523 --code-region 298,335,1565,1600 --verbose
else
    echo "資料夾不存在"
fi
```

## 技術架構

### 核心組件
- **code_comparison.py**: 命令列介面和參數解析
- **EQCBin1FinalProcessor**: 核心4階段處理邏輯
- **各種處理器模組**: 專門的功能處理器
- **監控和記錄**: 進度追蹤和[EXCEPT_CHAR]錯支援

### 處理流程
1. 中文路徑處理和檔案清理
2. 自動生成 EQCTOTALDATA
3. 程式碼區間檢測
4. 雙重搜尋機制
5. 完整報告生成
6. 結果整合和輸出

## 疑難排解

### 常見問題檢查清單

- [ ] 是否安裝了所有必要的 Python 套件？
- [ ] 是否正確設定了 .env 檔案？
- [ ] 資料夾路徑是否正確？
- [ ] 是否有足夠的磁碟空間？
- [ ] 是否有必要的檔案讀寫權限？
- [ ] 程式碼區間格式是否正確？

### 取得更多說明

如果遇到問題，可以：
1. 使用 `--help` 參數查看說明
2. 使用 `--verbose` 參數取得詳細輸出
3. 檢查生成的結果 JSON 檔案
4. 查看相關的日誌檔案

## 版本資訊

- **版本**: v1.0.0
- **相容性**: Python 3.7+
- **依賴**: pandas, numpy, openpyxl, xlsxwriter, python-dotenv, pytz
- **核心處理器**: EQCBin1FinalProcessor

## 授權與支援

此工具為內部專案工具，如有問題請聯繫開發團隊。

---

**最後更新**: 2025-07-11
**作者**: AI Assistant
**相關檔案**: code_comparison.py, eqc_bin1_final_processor.py