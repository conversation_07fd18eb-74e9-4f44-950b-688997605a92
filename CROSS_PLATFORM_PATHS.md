# 跨平台路徑自動切換系統

## 概述

本系統提供了完整的跨平台路徑自動檢測和轉換機制，支援 Windows、Linux、WSL 環境的無縫切換。

## 功能特點

### [REFRESH] 自動路徑轉換
- **Windows 環境**：WSL 路徑 → Windows 路徑
- **Linux/WSL 環境**：Windows 路徑 → WSL/Linux 路徑
- **智能檢測**：自動識別當前執行環境

### [TARGET] 智能路徑解析
- 嘗試多種路徑格式
- 自動尋找存在的路徑
- 相對路徑回退機制

### [GLOBE_WITH_MERIDIANS] 前端 URL 自動配置
- 自動檢測作業系統
- 動態生成 API URL
- 服務連接性測試

## 核心模組

### 1. PathManager (`src/utils/path_manager.py`)

#### 主要方法

```python
# 自動正規化路徑
normalized_path = PathManager.normalize_path("/mnt/d/project/...")

# 智能路徑解析
original, resolved, exists = PathManager.smart_path_resolution(path)

# 環境檢測
env_info = PathManager.get_environment_info()
```

#### 支援的轉換格式

| 來源格式 | 目標格式 | 範例 |
|---------|---------|------|
| Windows | WSL | `D:\project\...` → `/mnt/d/project/...` |
| WSL | Windows | `/mnt/d/project/...` → `D:\project\...` |
| WSL | Linux | `/mnt/d/project/...` → `/project/...` |

### 2. UrlConfig (`src/presentation/web/static/js/utils/url-config.js`)

#### 主要功能

```javascript
// 獲取適合當前環境的 URL 配置
const config = UrlConfig.getConfig();

// 自動檢測作業系統
const os = UrlConfig.detectOS(); // 'windows' | 'linux' | 'macos'

// 生成適當的 API URL
const apiUrl = UrlConfig.getApiBaseUrl();
```

#### URL 配置規則

| 環境 | 主機名 | 範例 URL |
|------|--------|----------|
| Windows | 127.0.0.1 | `http://127.0.0.1:8010/ft-summary-ui#` |
| Linux/macOS | localhost | `http://localhost:8010/ft-summary-ui#` |

### 3. 整合的 API 工具 (`src/presentation/api/services/api_utils.py`)

```python
# 自動處理路徑轉換和驗證
original, processed = APIUtils.process_folder_path(input_path)
```

## 使用範例

### 後端路徑處理

```python
from src.utils.path_manager import PathManager

# 智能路徑解析 - 自動尋找存在的路徑
input_path = "/mnt/d/project/python/outlook_summary/doc/20250523"
original, resolved, exists = PathManager.smart_path_resolution(input_path)

if exists:
    print(f"成功解析路徑: {resolved}")
else:
    print(f"路徑不存在，使用: {resolved}")
```

### 前端 URL 配置

```html
<!-- 載入 URL 配置模組 -->
<script src="js/utils/url-config.js"></script>
<script src="js/core/api-client.js"></script>

<script>
// 自動設置動態連結
document.addEventListener('DOMContentLoaded', function() {
    const config = UrlConfig.getConfig();
    
    // FT-Summary UI 連結自動適配
    document.getElementById('ft-summary-link').href = config.ui.ftSummary;
    
    // API 呼叫自動使用正確的 baseUrl
    ApiClient.post('endpoint', data); // 自動使用適當的主機名
});
</script>
```

## 環境支援

### [OK] 完全支援

| 環境 | 路徑轉換 | URL 配置 | 自動檢測 |
|------|---------|----------|----------|
| Windows (Native) | [OK] | [OK] | [OK] |
| Linux (WSL) | [OK] | [OK] | [OK] |
| Linux (Native) | [OK] | [OK] | [OK] |
| macOS | [OK] | [OK] | [OK] |

### [REFRESH] 轉換範例

#### Windows 環境執行
```
輸入: /mnt/d/project/python/outlook_summary/doc/20250523
輸出: D:\project\python\outlook_summary\doc\20250523
```

#### Linux/WSL 環境執行
```
輸入: D:\project\python\outlook_summary\doc\20250523
輸出: /mnt/d/project/python/outlook_summary/doc/20250523
```

## 故障排[EXCEPT_CHAR]

### 路徑找不到
系統會自動嘗試以下候選路徑：
1. 原始路徑
2. 正規化路徑（環境轉換）
3. 相對路徑
4. `doc/` 前綴路徑

### URL 連接失敗
系統會自動：
1. 回退到預設 URL
2. 測試服務連接性
3. 顯示詳細錯誤信息

## 配置選項

### 環境變數
- 無需額外配置
- 自動檢測執行環境

### 自訂配置
```python
# 強制使用特定路徑格式
PathManager.normalize_path(path, force_windows=True)

# 自訂路徑候選列表
existing_path = PathManager.find_existing_path(
    "/mnt/d/project/...",
    "D:\\project\\...",
    "./doc/..."
)
```

## 更新日誌

### v1.0 (2025-07-16)
- [OK] 實現完整的跨平台路徑管理
- [OK] 前端 URL 自動配置
- [OK] 智能路徑解析和回退機制
- [OK] 環境自動檢測
- [OK] WSL、Windows、Linux 完全支援

## 注意事項

1. **編碼問題**：已移[EXCEPT_CHAR] Unicode emoji 避免 Windows cp950 編碼問題
2. **權限問題**：確保程式有權限讀取 `/proc/version`（Linux 環境檢測）
3. **路徑驗證**：系統會驗證路徑存在性並記錄警告
4. **向下相容**：保留原有 API 介面，無需修改現有代碼