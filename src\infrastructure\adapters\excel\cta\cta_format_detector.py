#!/usr/bin/env python3
"""
CTA 格式檢測器
識別和驗證 CTA (Chip Test Assistant) 格式的 CSV 檔案
"""

import os
from typing import List, Optional


class CTAFormatDetector:
    """CTA 格式檢測器"""
    
    def __init__(self):
        """初始化檢測器"""
        # CTA 格式的關鍵特徵
        self.cta_sections = ['[GENERAL]', '[Data]']
        self.cta_headers = ['Serial_No', 'SW_Bin', 'Site_No']
        
    def is_cta_format(self, file_path: str) -> bool:
        """
        檢測檔案是否為 CTA 格式
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            True 如果是 CTA 格式，False 否則
        """
        try:
            # 1. 檢查檔案是否存在
            if not os.path.exists(file_path):
                return False
                
            # 2. 檢查副檔名
            if not file_path.lower().endswith('.csv'):
                return False
                
            # 3. 檢查檔案內容
            return self._check_cta_content(file_path)
            
        except Exception:
            return False
    
    def _check_cta_content(self, file_path: str) -> bool:
        """
        檢查檔案內容是否符合 CTA 格式
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            True 如果內容符合 CTA 格式
        """
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            # 檢查必要的區段
            has_sections = all(section in content for section in self.cta_sections)
            
            # 檢查是否有標準的資料標頭
            has_headers = all(header in content for header in self.cta_headers)
            
            return has_sections and has_headers
            
        except Exception:
            return False
    
    def validate_cta_structure(self, file_path: str) -> bool:
        """
        驗證 CTA 檔案結構完整性
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            True 如果結構有效
        """
        try:
            if not self.is_cta_format(file_path):
                return False
                
            # 進一步檢查結構
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            # 檢查是否有足夠的行數 (至少要有標頭和一些資料)
            if len(lines) < 10:
                return False
                
            # 尋找 [Data] 區段
            data_section_found = False
            header_line_found = False
            
            for i, line in enumerate(lines):
                if '[Data]' in line:
                    data_section_found = True
                    # 檢查後續是否有標頭行
                    for j in range(i + 1, min(i + 5, len(lines))):
                        if all(header in lines[j] for header in self.cta_headers):
                            header_line_found = True
                            break
                    break
                    
            return data_section_found and header_line_found
            
        except Exception:
            return False
    
    def get_cta_info(self, file_path: str) -> Optional[dict]:
        """
        獲取 CTA 檔案的基本資訊
        
        Args:
            file_path: 檔案路徑
            
        Returns:
            包含檔案資訊的字典，或 None
        """
        if not self.is_cta_format(file_path):
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                
            info = {
                'file_path': file_path,
                'total_lines': len(lines),
                'is_cta_format': True,
                'sections_found': [],
                'data_section_line': None,
                'header_line': None
            }
            
            # 找出各區段位置
            for i, line in enumerate(lines):
                if '[GENERAL]' in line:
                    info['sections_found'].append(('GENERAL', i))
                elif '[Data]' in line:
                    info['sections_found'].append(('Data', i))
                    info['data_section_line'] = i
                    
                    # 尋找標頭行
                    for j in range(i + 1, min(i + 5, len(lines))):
                        if all(header in lines[j] for header in self.cta_headers):
                            info['header_line'] = j
                            break
                            
            return info
            
        except Exception:
            return None