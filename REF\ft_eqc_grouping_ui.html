<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FT-EQC 分組配對分析系統</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            /* 顏色系統 */
            --primary-color: #667eea;
            --secondary-color: #2c3e50;
            --text-muted: #6c757d;
            --border-color: #e9ecef;
            --bg-light: #f8f9fa;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --error-color: #e74c3c;
            --info-color: #17a2b8;
            
            /* 尺寸系統 */
            --radius-xl: 15px;
            --radius-lg: 12px;
            --radius-md: 8px;
            --radius-sm: 6px;
            --radius-xs: 4px;
            --spacing-xl: 30px;
            --spacing-lg: 20px;
            --spacing-md: 15px;
            --spacing-sm: 12px;
            --spacing-xs: 10px;
            --spacing-xxs: 8px;
            
            /* 陰影系統 */
            --shadow-xl: 0 20px 60px rgba(0,0,0,0.1);
            --shadow-lg: 0 4px 16px rgba(0,0,0,0.15);
            --shadow-md: 0 4px 12px rgba(0,0,0,0.1);
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.1);
            --shadow-xs: 0 2px 4px rgba(0,0,0,0.05);
            
            /* 過渡動畫 */
            --transition: all 0.3s ease;
            --transition-fast: all 0.2s ease;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft JhengHei', Arial, sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, #764ba2 100%);
            min-height: 100vh;
            padding: var(--spacing-lg);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-xl);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, var(--secondary-color) 0%, #34495e 100%);
            color: white;
            padding: var(--spacing-lg) var(--spacing-xl);
            text-align: center;
        }

        .header h1 {
            font-size: 2em;
            margin: 0;
            font-weight: 400;
        }

        .main-content {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
            padding: var(--spacing-lg);
            min-height: 600px;
        }

        .controls-container {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr 1fr;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
            align-items: start;
        }

        /* 統一卡片基礎樣式 */
        .card-base {
            background: white;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }
        
        .card-base:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }
        
        .control-card {
            padding: var(--spacing-md);
        }
        
        /* 資料夾輸入區域樣式 */
        .folder-input-card {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            transition: var(--transition);
        }
        
        .folder-input-card:hover {
            border-color: var(--primary-color);
            background: #f0f4ff;
        }
        
        .folder-input-card .folder-icon {
            font-size: 2.5em;
            color: var(--primary-color);
            margin-bottom: var(--spacing-sm);
        }
        
        .folder-input-card .folder-input {
            width: 100%;
            padding: var(--spacing-sm);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-sm);
            margin-bottom: var(--spacing-sm);
            font-size: 14px;
        }
        
        .folder-input-card .primary-btn {
            width: 100%;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-sm);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .folder-input-card .primary-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        /* 右側卡片樣式優化 */
        .side-card {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-xs);
            height: fit-content;
        }
        
        .side-card h4 {
            margin: 0 0 var(--spacing-sm) 0;
            font-size: 14px;
            color: var(--secondary-color);
            display: flex;
            align-items: center;
            gap: var(--spacing-xxs);
        }
        
        .side-card h4 i {
            color: var(--primary-color);
            font-size: 16px;
        }
        
        .control-card h3 {
            margin: 0 0 var(--spacing-md) 0;
            color: var(--secondary-color);
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-xxs);
        }
        
        .control-card h3 i {
            color: var(--primary-color);
        }
        
        .card-content {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);
        }
        
        /* 實用類別 */
        .full-width { width: 100%; }
        .mt-xs { margin-top: var(--spacing-xs); }
        .p-lg { padding: var(--spacing-lg); }
        .hidden { display: none; }

        /* 處理進度樣式 */
        .progress-display {
            padding: var(--spacing-md);
            background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
            border-radius: var(--radius-md);
            border: 1px solid var(--border-color);
        }
        
        .progress-step {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: var(--spacing-xxs);
            display: flex;
            align-items: center;
            gap: var(--spacing-xxs);
        }
        
        .progress-step.processing { color: var(--primary-color); }
        .progress-step.success { color: var(--success-color); }
        .progress-step.error { color: var(--error-color); }
        
        .progress-detail {
            font-size: 12px;
            color: var(--text-muted);
            line-height: 1.4;
        }
        
        .progress-bar-container {
            margin-top: var(--spacing-sm);
        }
        
        .progress-bar {
            height: 8px;
            background: var(--border-color);
            border-radius: var(--radius-xs);
            overflow: hidden;
            margin-bottom: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color) 0%, #764ba2 100%);
            width: 0%;
            transition: var(--transition);
        }
        
        .progress-text {
            text-align: center;
            font-size: 11px;
            color: var(--text-muted);
            font-weight: 500;
        }

        .details-container {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid #e9ecef;
            overflow: hidden;
        }

        .control-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            height: fit-content;
        }

        .folder-selector {
            margin-bottom: 30px;
        }

        .folder-selector h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .folder-input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .folder-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .folder-input:focus {
            outline: none;
            border-color: #667eea;
        }

        /* 統一按鈕系統 */
        .btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            border: none;
            border-radius: var(--radius-md);
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: var(--spacing-xxs);
            text-decoration: none;
            justify-content: center;
        }
        
        .btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: var(--text-muted);
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-outline-primary {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        
        .btn-outline-secondary {
            background: transparent;
            color: var(--text-muted);
            border: 2px solid var(--text-muted);
        }
        
        .btn-sm { padding: var(--spacing-xxs) var(--spacing-sm); font-size: 12px; }

        .stats-panel {
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        .processing-indicator {
            display: none;
            text-align: center;
            padding: 20px;
            color: #667eea;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            overflow-y: auto;
            max-height: 800px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .timeline-header h3 {
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-controls {
            display: flex;
            gap: 10px;
        }

        .filter-btn {
            padding: 8px 15px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #667eea, #764ba2);
        }

        .timeline-item {
            position: relative;
            margin-bottom: 25px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            transition: transform 0.3s;
        }

        .timeline-item:hover {
            transform: translateX(5px);
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -37px;
            top: 25px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 0 0 3px #667eea;
        }

        .timeline-item.matched::before {
            background: #28a745;
            box-shadow: 0 0 0 3px #28a745;
        }

        .timeline-item.unmatched::before {
            background: #ffc107;
            box-shadow: 0 0 0 3px #ffc107;
        }

        .timeline-item.cta {
            border-left-color: #e74c3c;
        }

        .timeline-item.cta::before {
            background: #e74c3c;
            box-shadow: 0 0 0 3px #e74c3c;
        }

        .timeline-content {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 15px;
            align-items: start;
        }

        .file-info {
            flex: 1;
        }

        .file-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }

        .file-type-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .badge-ft {
            background: #d4edda;
            color: #155724;
        }

        .badge-eqc {
            background: #d1ecf1;
            color: #0c5460;
        }

        .badge-cta {
            background: #f8d7da;
            color: #721c24;
        }

        .badge-matched {
            background: #d4edda;
            color: #155724;
        }

        .badge-unmatched {
            background: #fff3cd;
            color: #856404;
        }

        .file-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            word-break: break-all;
        }

        .file-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            font-size: 13px;
            color: #6c757d;
        }

        .detail-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .file-actions {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .action-btn:hover {
            background: #f8f9fa;
            border-color: #667eea;
        }

        .timestamp {
            font-size: 12px;
            color: #6c757d;
            display: flex;
            align-items: center;
            gap: 5px;
            margin-top: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .legend {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
        }

        .legend-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .dot-matched { background: #28a745; }
        .dot-unmatched { background: #ffc107; }
        .dot-cta { background: #e74c3c; }
        .dot-ft { background: #007bff; }
        .dot-eqc { background: #17a2b8; }

        /* 檔案上傳區域樣式 */
        .upload-zone {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
            background: #f8f9fa;
        }

        .upload-zone:hover,
        .upload-zone.dragover {
            border-color: #667eea;
            background: #f0f3ff;
        }

        .upload-zone.uploading {
            border-color: #ffc107;
            background: #fffef0;
        }

        .upload-zone i {
            font-size: 2em;
            color: #667eea;
            margin-bottom: 8px;
            display: block;
        }

        .upload-zone p {
            margin: 0 0 4px 0;
            font-weight: 500;
            color: #2c3e50;
        }

        .upload-zone small {
            color: #6c757d;
            font-size: 11px;
        }

        /* 檔案上傳選項樣式 */
        .upload-options {
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .checkbox-container {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-size: 12px;
            color: #2c3e50;
        }

        .checkbox-container input[type="checkbox"] {
            width: 16px;
            height: 16px;
            accent-color: var(--primary-color);
        }

        .upload-mode-selector {
            margin-left: 24px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* CODE區間設定樣式 */
        .code-region-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .code-region-panel h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .region-inputs {
            display: flex;
            flex-direction: column;
            gap: 18px;
        }

        .region-group {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .input-row {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .input-row input {
            width: 90px;
            padding: 8px 10px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            text-align: center;
            transition: border-color 0.3s;
        }

        .input-row input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .field-count {
            font-size: 12px;
            color: #666;
            font-weight: 500;
            background: #f0f0f0;
            padding: 4px 8px;
            border-radius: 12px;
            margin-left: 8px;
        }

        /* 詳細資料面板樣式 */
        .detail-container {
            min-height: 300px;
        }

        .detail-item {
            background: white;
            border-radius: 12px;
            border: 1px solid #e9ecef;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s;
            overflow: hidden;
        }

        .detail-item:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .detail-header {
            padding: 18px 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: background 0.3s;
            border-radius: 12px 12px 0 0;
            background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
        }

        .detail-header:hover {
            background: linear-gradient(135deg, #e9ecff 0%, #f8f9fa 100%);
        }

        .detail-header i {
            color: #667eea;
            transition: transform 0.3s;
            font-size: 16px;
        }

        .detail-header.expanded i {
            transform: rotate(90deg);
        }

        .status-badge {
            margin-left: auto;
            background: #e9ecef;
            color: #6c757d;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-badge.success {
            background: #d4edda;
            color: #155724;
        }

        .status-badge.error {
            background: #f8d7da;
            color: #721c24;
        }

        .detail-content {
            padding: 0 20px 20px;
            border-top: 1px solid #f0f0f0;
            margin-top: -1px;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Summary 收合狀態樣式 */
        .summary-preview {
            padding: 15px 20px;
            background: linear-gradient(135deg, #f0f3ff 0%, #ffffff 100%);
            border-top: 1px solid #e9ecef;
        }

        .summary-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 12px;
            margin-bottom: 12px;
        }

        .metric-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }

        .metric-label {
            font-size: 11px;
            color: #6c757d;
            margin-top: 2px;
        }

        .summary-highlight {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #fff3cd;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
            font-size: 13px;
            font-weight: 500;
        }

        /* 處理結果摘要樣式 */
        .processing-summary {
            margin: 20px 0;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .summary-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.3s;
        }

        .summary-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .summary-item.success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border-color: #c3e6cb;
        }

        .summary-item.warning {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border-color: #ffeaa7;
        }

        .summary-item.error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            border-color: #f5c6cb;
        }

        .summary-item.info {
            background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
            border-color: #bee5eb;
        }

        .summary-icon {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .summary-item.success .summary-icon {
            background: #28a745;
            color: white;
        }

        .summary-item.warning .summary-icon {
            background: #ffc107;
            color: #212529;
        }

        .summary-item.error .summary-icon {
            background: #dc3545;
            color: white;
        }

        .summary-item.info .summary-icon {
            background: #17a2b8;
            color: white;
        }

        .summary-text {
            flex: 1;
        }

        .summary-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 2px;
        }

        .summary-desc {
            font-size: 13px;
            color: #6c757d;
            line-height: 1.4;
        }

        /* CODE區間資訊卡片 */
        .code-info-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #e9ecff 100%);
            border: 1px solid #d1d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }

        .code-info-title {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .code-range {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 8px 0;
            font-family: 'Courier New', monospace;
            background: white;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #e0e6ff;
        }

        /* 統計資訊樣式 */
        .stats-grid-new {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin: 15px 0;
        }

        .stat-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .stat-card .value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .stat-card .label {
            font-size: 12px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        /* 額外統計資訊樣式 */
        .stats-grid-extra {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 15px;
        }

        .stat-card-small {
            background: linear-gradient(135deg, #f0f3ff 0%, #ffffff 100%);
            border: 1px solid #d1d9ff;
            border-radius: 6px;
            padding: 10px;
            text-align: center;
        }

        .stat-card-small .value {
            font-size: 1.3em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 3px;
        }

        .stat-card-small .label {
            font-size: 11px;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        /* FAIL Summary 卡片樣式 */
        .fail-summary {
            margin-top: 20px;
        }

        .test-results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 12px;
            margin-bottom: 15px;
        }

        .fail-card, .pass-card {
            padding: 12px 15px;
            border-radius: 8px;
            border-left: 4px solid;
            transition: all 0.3s;
            border: 1px solid;
        }

        .fail-card {
            background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
            border-left-color: #e74c3c;
            border-color: #f8d7da;
        }

        .pass-card {
            background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
            border-left-color: #28a745;
            border-color: #d4edda;
        }

        .fail-card:hover, .pass-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .card-header {
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .card-content {
            font-size: 13px;
            line-height: 1.5;
        }

        /* 響應式設計 */
        @media (max-width: 1200px) {
            .controls-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .controls-container {
                grid-template-columns: 1fr 1fr;
                gap: var(--spacing-md);
            }
            
            .folder-input-card {
                grid-column: 1 / -1;
            }
        }
        
        @media (max-width: 768px) {
            .controls-container {
                grid-template-columns: 1fr;
                gap: var(--spacing-md);
            }
            
            .folder-input-card {
                grid-column: 1;
            }
            
            .header h1 {
                font-size: 1.8em;
            }
            
            .side-card {
                padding: var(--spacing-sm);
            }
            
            .summary-metrics {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .timeline-content {
                grid-template-columns: 1fr;
            }
            
            .file-details {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            
            .main-content {
                padding: 20px 15px;
            }
            
            .controls-container {
                gap: 12px;
            }
            
            .control-card {
                padding: 12px;
            }
            
            .summary-metrics {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-project-diagram"></i> Online EQC 分析程式</h1>
        </div>

        <div class="main-content">
            <!-- 橫向布局控制區域 -->
            <div class="controls-container">
                <!-- 主要資料夾輸入區域 (左側大區域) -->
                <div class="folder-input-card">
                    <div class="folder-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 style="margin: 0 0 15px 0; color: var(--secondary-color);">📁 資料夾輸入</h3>
                    
                    <input type="text" class="folder-input" id="folderPath" 
                           placeholder="D:\project\python\outlook_summary\doc\20250523" 
                           value="D:\project\python\outlook_summary\doc\20250523">
                    
                    <button class="primary-btn" onclick="processCompleteEQCWorkflow()">
                        <i class="fas fa-magic"></i> 一鍵完成程式碼對比
                    </button>
                    
                    <div style="margin-top: 12px; font-size: 12px; color: #666; line-height: 1.4;">
                        自動生成 EQCTOTALDATA → 程式碼區間檢測 → 雙重搜尋 → 完整報告
                    </div>
                </div>

                <!-- 檔案上傳 (右側第1個) -->
                <div class="side-card">
                    <h4><i class="fas fa-upload"></i> 檔案上傳</h4>
                    <div class="upload-zone" id="uploadZone" style="min-height: 80px; display: flex; flex-direction: column; justify-content: center;">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 1.5em; color: var(--primary-color); margin-bottom: 8px;"></i>
                        <div style="font-size: 12px; text-align: center;">
                            拖放檔案或點擊上傳<br>
                            <small style="color: #666;">支援 ZIP, 7Z, RAR<br>(最大 <span id="maxSizeDisplay">1000</span>MB)</small>
                        </div>
                        <input type="file" id="fileInput" accept=".zip,.7z,.rar,.tar,.gz" style="display: none;">
                    </div>
                    <div id="uploadStatus" style="margin-top: 8px; font-size: 11px; display: none;"></div>
                </div>

                <!-- CODE區間設定 (右側第2個) -->
                <div class="side-card">
                    <h4><i class="fas fa-code"></i> CODE 區間設定</h4>
                    <div style="margin-bottom: 10px; padding: 8px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 4px; font-size: 10px; color: #0066cc;">
                        <i class="fas fa-info-circle"></i> 智能設定: 自動檢測最佳區間
                    </div>
                    <div class="region-group" style="margin-bottom: 10px;">
                        <label style="font-size: 11px; margin-bottom: 4px; display: block;">主要 CODE 區間:</label>
                        <div class="input-row" style="display: flex; align-items: center; gap: 4px;">
                            <input type="number" id="mainStart" placeholder="起始" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" oninput="updateFieldCount()">
                            <span style="font-size: 11px;">-</span>
                            <input type="number" id="mainEnd" placeholder="結束" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" oninput="updateFieldCount()">
                        </div>
                        <span class="field-count" id="mainCount" style="font-size: 10px; color: #666;">(未設定)</span>
                    </div>
                    <div class="region-group">
                        <label style="font-size: 11px; margin-bottom: 4px; display: block;">備用 CODE 區間:</label>
                        <div class="input-row" style="display: flex; align-items: center; gap: 4px;">
                            <input type="number" id="backupStart" placeholder="起始" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" oninput="updateFieldCount()">
                            <span style="font-size: 11px;">-</span>
                            <input type="number" id="backupEnd" placeholder="結束" style="width: 50px; padding: 4px; border: 1px solid #ddd; border-radius: 3px; font-size: 11px;" oninput="updateFieldCount()">
                        </div>
                        <span class="field-count" id="backupCount" style="font-size: 10px; color: #666;">(未設定)</span>
                    </div>
                </div>

                <!-- 處理進度 (右側第3個) -->
                <div class="side-card">
                    <h4><i class="fas fa-tasks"></i> 處理進度</h4>
                    <div id="progressDisplay" style="padding: 10px; background: #f8f9ff; border-radius: 4px; border: 1px solid #e9ecef;">
                        <div class="progress-step" id="progressStep" style="font-size: 11px; font-weight: 600; color: var(--secondary-color); margin-bottom: 4px; display: flex; align-items: center; gap: 4px;">
                            <i class="fas fa-clock"></i> 等待開始...
                        </div>
                        <div class="progress-detail" id="progressDetail" style="font-size: 10px; color: var(--text-muted); line-height: 1.3;">
                            點擊「一鍵完成」開始處理
                        </div>
                    </div>
                    <div class="progress-bar-container" id="progressBarContainer" style="display: none; margin-top: 8px;">
                        <div style="height: 6px; background: var(--border-color); border-radius: 3px; overflow: hidden; margin-bottom: 4px;">
                            <div id="progressFill" style="height: 100%; background: linear-gradient(90deg, var(--primary-color) 0%, #764ba2 100%); width: 0%; transition: var(--transition);"></div>
                        </div>
                        <div class="progress-text" id="progressText" style="text-align: center; font-size: 10px; color: var(--text-muted); font-weight: 500;">0%</div>
                    </div>
                </div>
            </div>

            <!-- 今日處理記錄 (獨立行) -->
            <div class="side-card" style="margin-bottom: var(--spacing-lg);">
                <h4><i class="fas fa-history"></i> 今日處理記錄 
                    <button onclick="refreshTodayRecords()" style="margin-left: auto; background: #f8f9fa; border: 1px solid #dee2e6; color: #6c757d; font-size: 10px; padding: 2px 6px; border-radius: 3px; cursor: pointer;">
                        <i class="fas fa-sync-alt"></i> 重新整理
                    </button>
                </h4>
                <div id="todayRecords" style="max-height: 120px; overflow-y: auto;">
                    <div id="todayRecordsEmpty" style="text-align: center; color: #6c757d; padding: 15px;">
                        <i class="fas fa-clock"></i>
                        <div style="margin-top: 6px; font-size: 12px;">尚無今日處理記錄</div>
                        <div style="font-size: 10px; margin-top: 3px; color: #999;">處理完成後將顯示下載連結</div>
                    </div>
                    <div id="todayRecordsList" style="display: none;"></div>
                </div>
            </div>

            <!-- 處理指示器 -->
            <div class="processing-indicator" id="processingIndicator" style="display: none;">
                <div class="spinner"></div>
                <div>正在處理...</div>
            </div>

            <!-- 下半部：詳細資料區域 -->
            <div class="details-container">
                <div class="timeline-header">
                    <h3><i class="fas fa-chart-line"></i> 詳細資料</h3>
                </div>

                <div class="detail-container" id="detailContainer">
                    <div class="detail-item" id="eqcDetailItem" style="display: none;">
                        <div class="detail-header" onclick="toggleDetail()">
                            <i class="fas fa-chevron-right" id="detailChevron"></i>
                            <span style="font-weight: bold;">EQC 處理結果</span>
                            <span class="status-badge" id="statusBadge">點擊查看詳情</span>
                        </div>
                        
                        <!-- 收合狀態的Summary摘要 -->
                        <div class="summary-preview" id="summaryPreview">
                            <div class="summary-metrics">
                                <div class="metric-item">
                                    <div class="metric-value" id="summaryFailCount">0</div>
                                    <div class="metric-label">Online EQC FAIL</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="summaryPassCount">0</div>
                                    <div class="metric-label">EQC RT PASS</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="summaryMatchRate">0%</div>
                                    <div class="metric-label">匹配率</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value" id="summarySearchMethod">-</div>
                                    <div class="metric-label">搜尋方法</div>
                                </div>
                            </div>
                            <div class="summary-highlight">
                                <i class="fas fa-info-circle"></i>
                                <span id="summaryHighlight">點擊展開查看詳細資料和前10項FAIL/PASS摘要</span>
                            </div>
                        </div>
                        
                        <div class="detail-content" id="detailContent" style="display: none;">
                            <!-- 動態內容區域 -->
                        </div>
                    </div>
                    
                    <div class="empty-state" id="emptyState">
                        <i class="fas fa-chart-line" style="font-size: 4em; color: #ccc; margin-bottom: 20px;"></i>
                        <h4>尚未開始處理</h4>
                        <p>請選擇資料夾或上傳檔案，然後點擊「一鍵完成到程式碼對比」按鈕</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 報告預覽模態框 -->
    <div id="reportPreviewModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000; overflow: auto;">
        <div style="position: relative; background: white; margin: 2% auto; width: 90%; max-width: 1000px; border-radius: 15px; box-shadow: 0 20px 60px rgba(0,0,0,0.3);">
            <!-- 模態框標題列 -->
            <div style="background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%); color: white; padding: 20px; border-radius: 15px 15px 0 0; display: flex; justify-content: space-between; align-items: center;">
                <h3 style="margin: 0; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-file-alt"></i> EQC 處理報告預覽
                </h3>
                <button onclick="closeReportPreview()" style="background: none; border: none; color: white; font-size: 24px; cursor: pointer; padding: 5px;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- 報告內容區域 -->
            <div style="padding: 30px; max-height: 70vh; overflow-y: auto;">
                <div id="reportContent" style="font-family: 'Courier New', monospace; background: #f8f9fa; padding: 20px; border-radius: 8px; white-space: pre-wrap; line-height: 1.6; font-size: 14px;">
                    載入中...
                </div>
            </div>
            
            <!-- 操作按鈕區域 -->
            <div style="padding: 20px; border-top: 1px solid #e9ecef; display: flex; gap: 10px; justify-content: flex-end;">
                <button onclick="downloadReport()" class="btn btn-primary">
                    <i class="fas fa-download"></i> 下載報告
                </button>
                <button onclick="copyReportContent()" class="btn btn-secondary">
                    <i class="fas fa-copy"></i> 複製內容
                </button>
                <button onclick="closeReportPreview()" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> 關閉
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentResults = null;
        let currentFilter = 'all';
        
        // ==================== 工具類別區域 ====================
        
        // 統一 API 調用工具類
        class ApiClient {
            static baseUrl = 'http://localhost:8010/api';
            
            static async request(endpoint, options = {}) {
                const url = `${this.baseUrl}/${endpoint}`;
                const defaultOptions = {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    ...options
                };
                
                try {
                    const response = await fetch(url, defaultOptions);
                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }
                    return await response.json();
                } catch (error) {
                    console.error(`API 請求失敗 (${url}):`, error);
                    throw error;
                }
            }
            
            static async post(endpoint, data) {
                return this.request(endpoint, {
                    body: JSON.stringify(data)
                });
            }
        }
        
        // DOM 元素管理器
        class DOMManager {
            static elements = {};
            
            static get(id) {
                if (!this.elements[id]) {
                    this.elements[id] = document.getElementById(id);
                }
                return this.elements[id];
            }
            
            static getValue(id) {
                return this.get(id)?.value?.trim() || '';
            }
            
            static setText(id, text) {
                const element = this.get(id);
                if (element) element.textContent = text;
            }
            
            static setHTML(id, html) {
                const element = this.get(id);
                if (element) element.innerHTML = html;
            }
            
            static show(id) {
                const element = this.get(id);
                if (element) element.style.display = 'block';
            }
            
            static hide(id) {
                const element = this.get(id);
                if (element) element.style.display = 'none';
            }
        }
        
        // 統一狀態管理器
        class StatusManager {
            static updateProgress(step, message, percentage = null, status = 'processing') {
                const icons = {
                    processing: 'fas fa-spinner fa-spin',
                    success: 'fas fa-check-circle',
                    error: 'fas fa-times-circle'
                };
                
                const stepElement = DOMManager.get('progressStep');
                const detailElement = DOMManager.get('progressDetail');
                
                if (stepElement) {
                    stepElement.className = `progress-step ${status}`;
                    stepElement.innerHTML = `<i class="${icons[status]}"></i> ${step}`;
                }
                
                if (detailElement) detailElement.textContent = message;
                
                if (percentage !== null) {
                    const progressContainer = DOMManager.get('progressBarContainer');
                    const progressFill = DOMManager.get('progressFill');
                    const progressText = DOMManager.get('progressText');
                    
                    if (progressContainer) progressContainer.style.display = 'block';
                    if (progressFill) progressFill.style.width = percentage + '%';
                    if (progressText) progressText.textContent = percentage + '%';
                }
            }
        }
        
        // ==================== 原有程式碼 ===================="

        // 檔案上傳處理（包含重複防護）
        let isFileUploadSetup = false;  // 防重複初始化標記
        let isUploading = false;        // 上傳狀態鎖定
        
        function setupFileUpload() {
            // 防重複初始化檢查
            if (isFileUploadSetup) {
                console.log('📋 檔案上傳已初始化，跳過重複設定');
                return;
            }
            
            const uploadZone = document.getElementById('uploadZone');
            const fileInput = document.getElementById('fileInput');
            const uploadStatus = document.getElementById('uploadStatus');
            
            if (!uploadZone || !fileInput || !uploadStatus) {
                console.error('❌ 檔案上傳元素未找到，跳過初始化');
                return;
            }

            console.log('🚀 正在初始化檔案上傳功能...');

            // 拖放事件處理
            uploadZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadZone.classList.add('dragover');
            });

            uploadZone.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
            });

            uploadZone.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadZone.classList.remove('dragover');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileUpload(files[0]);
                }
            });

            // 點擊上傳
            uploadZone.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileUpload(e.target.files[0]);
                }
            });
            
            // 標記初始化完成
            isFileUploadSetup = true;
            console.log('✅ 檔案上傳功能初始化完成');
        }

        // 設定資料夾路徑變更監聽器，當資料夾變更時清空 CODE 區間設定
        function setupFolderPathListener() {
            const folderPathInput = DOMManager.get('folderPath');
            let lastFolderPath = folderPathInput.value; // 記錄上次的路徑
            
            function handleFolderChange() {
                const currentPath = folderPathInput.value.trim();
                
                // 只有當路徑真的改變時才清空
                if (currentPath !== lastFolderPath && lastFolderPath !== '') {
                    clearCodeRegionSettings();
                    console.log('📁 資料夾路徑已變更，已清空 CODE 區間設定');
                    console.log(`   舊路徑: ${lastFolderPath}`);
                    console.log(`   新路徑: ${currentPath}`);
                }
                lastFolderPath = currentPath;
            }
            
            // 監聽即時輸入變更
            folderPathInput.addEventListener('input', handleFolderChange);
            
            // 監聽失去焦點時的確認變更
            folderPathInput.addEventListener('change', handleFolderChange);
        }

        // 清空所有 CODE 區間設定
        function clearCodeRegionSettings() {
            // 清空主要區間
            DOMManager.get('mainStart').value = '';
            DOMManager.get('mainEnd').value = '';
            
            // 清空備用區間
            DOMManager.get('backupStart').value = '';
            DOMManager.get('backupEnd').value = '';
            
            // 更新顯示文字
            updateFieldCount();
            
            console.log('🧹 已清空所有 CODE 區間設定');
        }

        async function handleFileUpload(file) {
            // 檢查上傳狀態鎖定
            if (isUploading) {
                console.log('⚠️ 已有檔案正在上傳中，忽略重複上傳請求');
                alert('已有檔案正在上傳中，請等待完成後再試！');
                return;
            }
            
            // 設置上傳鎖定
            isUploading = true;
            console.log('🔒 設置上傳狀態鎖定');
            
            const uploadStatus = DOMManager.get('uploadStatus');
            const uploadZone = DOMManager.get('uploadZone');
            
            // 檢查檔案格式
            const allowedTypes = ['.zip', '.7z', '.rar', '.tar', '.gz'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
            
            if (!allowedTypes.includes(fileExtension)) {
                alert('不支援的檔案格式！請上傳 ZIP、7Z、RAR、TAR 或 GZ 檔案。');
                isUploading = false;  // 釋放鎖定
                console.log('🔓 檔案格式錯誤，釋放上傳鎖定');
                return;
            }

            // 檢查檔案大小
            const maxSizeDisplay = DOMManager.get('maxSizeDisplay').textContent;
            const maxSizeMB = parseInt(maxSizeDisplay);
            const fileSizeMB = file.size / (1024 * 1024);
            
            if (fileSizeMB > maxSizeMB) {
                alert(`檔案太大！最大支援 ${maxSizeMB}MB，您的檔案為 ${fileSizeMB.toFixed(1)}MB`);
                isUploading = false;  // 釋放鎖定
                console.log('🔓 檔案太大，釋放上傳鎖定');
                return;
            }

            try {
                uploadZone.classList.add('uploading');
                uploadStatus.style.display = 'block';
                uploadStatus.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 正在上傳 ${file.name} (${fileSizeMB.toFixed(1)}MB)...`;
                uploadStatus.style.color = '#ffc107';

                const formData = new FormData();
                formData.append('file', file);

                // 統一使用上傳解壓縮端點
                const apiEndpoint = 'http://localhost:8010/api/upload_archive';

                const response = await fetch(apiEndpoint, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    
                    if (result.status === 'success') {
                        // 上傳和解壓縮成功
                        const extractDir = result.extraction_result?.extract_dir || result.extracted_path;
                        
                        // 轉換為Windows路徑格式
                        const windowsPath = convertToWindowsPath(extractDir);
                        
                        uploadStatus.innerHTML = `<i class="fas fa-check"></i> 檔案上傳和解壓縮完成！解壓到: ${windowsPath}`;
                        uploadStatus.style.color = '#28a745';
                        
                        // 自動設定資料夾路徑（Windows格式）
                        DOMManager.get('folderPath').value = windowsPath;
                        
                        // 檔案上傳成功後清空CODE區間設定，因為解壓縮後目錄結構會改變
                        clearCodeRegionSettings();
                        console.log('📦 檔案解壓縮成功，已清空CODE區間設定，等待重新自動檢測');
                        
                        // 啟動倒數計時器自動執行EQC流程
                        startCountdownTimer(windowsPath);
                        
                    } else {
                        // 處理失敗
                        uploadStatus.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${result.message}`;
                        uploadStatus.style.color = '#ffc107';
                    }
                    
                    setTimeout(() => {
                        DOMManager.hide('uploadStatus');
                        uploadZone.classList.remove('uploading');
                    }, 5000);
                    
                } else {
                    const errorData = await response.json();
                    
                    // 特別處理重複上傳錯誤
                    if (errorData.error_type === 'duplicate_upload') {
                        showDuplicateUploadError(errorData, file);
                        return;
                    }
                    
                    throw new Error(errorData.detail || errorData.message || '上傳失敗');
                }
            } catch (error) {
                uploadStatus.innerHTML = `<i class="fas fa-times"></i> 處理失敗: ${error.message}`;
                uploadStatus.style.color = '#e74c3c';
                uploadZone.classList.remove('uploading');
                
                setTimeout(() => {
                    uploadStatus.style.display = 'none';
                }, 7000);
            } finally {
                // 無論成功或失敗都釋放上傳鎖定
                isUploading = false;
                console.log('🔓 檔案上傳處理完成，釋放上傳鎖定');
            }
        }

        // 顯示重複上傳錯誤對話框
        function showDuplicateUploadError(errorData, file) {
            const uploadStatus = DOMManager.get('uploadStatus');
            const uploadZone = DOMManager.get('uploadZone');
            
            uploadZone.classList.remove('uploading');
            
            const duplicateInfo = errorData.duplicate_info || {};
            const remainingTime = Math.ceil(duplicateInfo.remaining_wait_time || 0);
            const fileName = file.name;
            
            // 顯示友好的重複上傳訊息
            uploadStatus.innerHTML = `
                <div style="text-align: left;">
                    <div style="margin-bottom: 10px;">
                        <i class="fas fa-exclamation-triangle" style="color: #ffc107;"></i>
                        <strong>檔案重複上傳</strong>
                    </div>
                    <div style="font-size: 14px; margin-bottom: 10px;">
                        檔案「${fileName}」在 ${Math.floor((duplicateInfo.time_since_upload || 0) / 60)} 分鐘前已上傳過
                    </div>
                    <div style="font-size: 12px; color: #6c757d; margin-bottom: 15px;">
                        ${remainingTime > 0 
                            ? `請等待 ${remainingTime} 秒後重試，或點擊下方按鈕清除快取` 
                            : '您現在可以重新上傳，或點擊下方按鈕清除快取'}
                    </div>
                    <div style="display: flex; gap: 10px; justify-content: center;">
                        <button onclick="clearDuplicateCacheAndRetry('${fileName}')" style="
                            background: linear-gradient(135deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 12px;
                        ">
                            <i class="fas fa-trash-alt"></i> 清除快取並重新上傳
                        </button>
                        <button onclick="hideDuplicateError()" style="
                            background: #6c757d;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 12px;
                        ">
                            <i class="fas fa-times"></i> 關閉
                        </button>
                    </div>
                </div>
            `;
            uploadStatus.style.color = '#856404';
            uploadStatus.style.background = '#fff3cd';
            uploadStatus.style.border = '1px solid #ffeaa7';
            uploadStatus.style.borderRadius = '6px';
            uploadStatus.style.padding = '15px';
            uploadStatus.style.display = 'block';
        }

        // 清除重複快取並重試上傳
        async function clearDuplicateCacheAndRetry(fileName) {
            try {
                const uploadStatus = DOMManager.get('uploadStatus');
                uploadStatus.innerHTML = `<i class="fas fa-spinner fa-spin"></i> 正在清除快取...`;
                uploadStatus.style.color = '#ffc107';
                
                // 呼叫清除快取 API
                const response = await fetch('http://localhost:8010/api/clear_duplicate_cache', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('✅ 清除重複快取成功:', result);
                    
                    uploadStatus.innerHTML = `<i class="fas fa-check"></i> 快取已清除，請重新選擇檔案上傳`;
                    uploadStatus.style.color = '#28a745';
                    
                    // 清空檔案選擇器
                    const fileInput = document.querySelector('#uploadZone input[type="file"]');
                    if (fileInput) {
                        fileInput.value = '';
                    }
                    
                    setTimeout(() => {
                        hideDuplicateError();
                    }, 3000);
                    
                } else {
                    throw new Error('清除快取失敗');
                }
                
            } catch (error) {
                console.error('❌ 清除快取失敗:', error);
                const uploadStatus = DOMManager.get('uploadStatus');
                uploadStatus.innerHTML = `<i class="fas fa-times"></i> 清除失敗: ${error.message}`;
                uploadStatus.style.color = '#e74c3c';
            }
        }

        // 隱藏重複上傳錯誤
        function hideDuplicateError() {
            const uploadStatus = DOMManager.get('uploadStatus');
            uploadStatus.style.display = 'none';
            uploadStatus.style.background = '';
            uploadStatus.style.border = '';
            uploadStatus.style.padding = '';
            
            // 釋放上傳鎖定
            isUploading = false;
            console.log('🔓 重複上傳錯誤已關閉，釋放上傳鎖定');
        }

        // 新增功能：CODE區間計算
        function updateFieldCount() {
            // 主要區間計算
            const mainStart = DOMManager.get('mainStart').value.trim();
            const mainEnd = DOMManager.get('mainEnd').value.trim();
            
            let mainText = '';
            if (mainStart && mainEnd) {
                const start = parseInt(mainStart);
                const end = parseInt(mainEnd);
                if (start > 0 && end > 0 && end >= start) {
                    const count = end - start + 1;
                    mainText = `(${count} 個欄位)`;
                } else {
                    mainText = `(輸入無效)`;
                }
            } else if (mainStart || mainEnd) {
                mainText = `(請填入完整區間)`;
            } else {
                mainText = `(未設定)`;
            }
            DOMManager.get('mainCount').textContent = mainText;

            // 備用區間計算
            const backupStart = DOMManager.get('backupStart').value.trim();
            const backupEnd = DOMManager.get('backupEnd').value.trim();
            
            let backupText = '';
            if (backupStart && backupEnd) {
                const start = parseInt(backupStart);
                const end = parseInt(backupEnd);
                if (start > 0 && end > 0 && end >= start) {
                    const count = end - start + 1;
                    backupText = `(${count} 個欄位)`;
                } else {
                    backupText = `(輸入無效)`;
                }
            } else if (backupStart || backupEnd) {
                backupText = `(請填入完整區間)`;
            } else {
                backupText = `(未設定)`;
            }
            DOMManager.get('backupCount').textContent = backupText;
        }

        // 新增功能：智能填入 CODE 區間設定
        function autoFillCodeRegions(regionResult) {
            if (!regionResult || !regionResult.code_region) {
                console.log('⚠️ 沒有檢測到 CODE 區間資料，跳過自動填入');
                return;
            }

            const codeRegion = regionResult.code_region;
            const backupRegion = regionResult.backup_region;

            // 檢查前端主要區間欄位是否為空
            const mainStartInput = DOMManager.get('mainStart');
            const mainEndInput = DOMManager.get('mainEnd');
            const backupStartInput = DOMManager.get('backupStart');
            const backupEndInput = DOMManager.get('backupEnd');

            const isMainEmpty = !mainStartInput.value.trim() && !mainEndInput.value.trim();
            const isBackupEmpty = !backupStartInput.value.trim() && !backupEndInput.value.trim();

            // 如果主要區間為空且後端檢測到區間，則自動填入
            if (isMainEmpty && codeRegion.start_column_number && codeRegion.end_column_number) {
                mainStartInput.value = codeRegion.start_column_number;
                mainEndInput.value = codeRegion.end_column_number;
                console.log(`🎯 自動填入主要 CODE 區間: ${codeRegion.start_column_number}-${codeRegion.end_column_number}`);
            }

            // 如果備用區間為空且後端檢測到區間，則自動填入
            if (isBackupEmpty && backupRegion && backupRegion.found && 
                backupRegion.backup_start_column && backupRegion.backup_end_column) {
                backupStartInput.value = backupRegion.backup_start_column;
                backupEndInput.value = backupRegion.backup_end_column;
                console.log(`🎯 自動填入備用 CODE 區間: ${backupRegion.backup_start_column}-${backupRegion.backup_end_column}`);
            }

            // 更新欄位計算顯示
            updateFieldCount();

            // 給使用者視覺反饋
            if (isMainEmpty || isBackupEmpty) {
                console.log('✨ 智能填入完成！首次執行已自動設定最佳 CODE 區間');
                
                // 短暫高亮提示使用者
                [mainStartInput, mainEndInput, backupStartInput, backupEndInput].forEach(input => {
                    if (input.value) {
                        input.style.backgroundColor = '#e7f3ff';
                        input.style.borderColor = '#0066cc';
                        setTimeout(() => {
                            input.style.backgroundColor = '';
                            input.style.borderColor = '';
                        }, 2000);
                    }
                });
            }
        }

        // 新增功能：詳細資料展開/收合
        function toggleDetail() {
            const detailContent = DOMManager.get('detailContent');
            const summaryPreview = DOMManager.get('summaryPreview');
            const detailChevron = DOMManager.get('detailChevron');
            const detailHeader = document.querySelector('.detail-header');
            
            if (detailContent.style.display === 'none') {
                // 展開詳細資料
                detailContent.style.display = 'block';
                summaryPreview.style.display = 'none';
                detailChevron.style.transform = 'rotate(90deg)';
                detailHeader.classList.add('expanded');
            } else {
                // 收合到摘要狀態
                detailContent.style.display = 'none';
                summaryPreview.style.display = 'block';
                detailChevron.style.transform = 'rotate(0deg)';
                detailHeader.classList.remove('expanded');
            }
        }

        // 新增功能：渲染詳細內容
        function renderDetailContent(data) {
            const detailContent = DOMManager.get('detailContent');
            const statusBadge = DOMManager.get('statusBadge');
            const eqcDetailItem = DOMManager.get('eqcDetailItem');
            const emptyState = DOMManager.get('emptyState');

            // 隱藏空狀態，顯示詳細項目
            emptyState.style.display = 'none';
            eqcDetailItem.style.display = 'block';

            // 更新狀態標章
            if (data.status === 'success') {
                statusBadge.textContent = '處理成功';
                statusBadge.className = 'status-badge success';
            } else {
                statusBadge.textContent = '處理失敗';
                statusBadge.className = 'status-badge error';
            }

            // 更新摘要數據
            let failCount = 0;
            let passCount = 0;
            let matchRate = '0%';
            let searchMethod = '未知';

            // 從多個可能的數據來源提取數據
            if (data.eqcResult) {
                failCount = data.eqcResult.onlineEQC_fail_count || 0;
                passCount = data.eqcResult.eqc_rt_pass_count || 0;
            }

            // 檢查雙重搜尋結果
            if (data.dual_search_result?.dual_search_result) {
                const dualData = data.dual_search_result.dual_search_result;
                matchRate = dualData.match_rate || '0%';
                
                if (dualData.search_method === 'main_region_complete') {
                    searchMethod = '主要區間';
                } else if (dualData.search_method === 'backup_region_mapping') {
                    searchMethod = '備用區間';
                } else {
                    searchMethod = dualData.search_method || '未知';
                }
            }

            // 已移除假資料生成邏輯，只顯示真實資料

            // 更新DOM元素
            DOMManager.get('summaryFailCount').textContent = failCount;
            DOMManager.get('summaryPassCount').textContent = passCount;
            document.getElementById('summaryMatchRate').textContent = matchRate;
            document.getElementById('summarySearchMethod').textContent = searchMethod;

            // 構建詳細內容HTML
            let contentHTML = '';

            // CODE區間資訊卡片
            const regionData = data.region_result?.code_region || {};
            const backupData = data.region_result?.backup_region || {};
            
            // 調試信息：確認數據是否正確傳遞
            console.log('🔍 renderDetailContent 收到的 region_result:', data.region_result);
            console.log('🔍 regionData:', regionData);
            console.log('🔍 backupData:', backupData);
            
            contentHTML += `
                <div class="code-info-card">
                    <div class="code-info-title">
                        <i class="fas fa-code"></i> CODE 區間檢測結果
                    </div>
                    <div class="code-range">
                        <strong>主要 CODE 區間:</strong>
                        <span style="color: #667eea; font-family: monospace;">第${regionData.start_column_number || '?'}-${regionData.end_column_number || '?'}欄</span>
                        <span style="color: #666;">(${regionData.column_count || '?'} 個欄位)</span>
                    </div>
                    <div class="code-range">
                        <strong>備用 CODE 區間:</strong>
                        ${backupData.found ? 
                            `<span style="color: #28a745; font-family: monospace;">第${backupData.backup_start_column}-${backupData.backup_end_column}欄</span>
                             <span style="color: #666;">(${(backupData.backup_end_column - backupData.backup_start_column + 1) || '?'} 個欄位)</span>` :
                            `<span style="color: #dc3545;">未檢測到備用區間</span>`
                        }
                    </div>
                    <div style="margin-top: 10px; padding: 8px 12px; background: #f8f9ff; border-radius: 6px; border-left: 3px solid #667eea;">
                        <small style="color: #666;">
                            <i class="fas fa-info-circle"></i> 
                            檢測條件: start1 > 10 且 區間長度 > 5 
                            ${regionData.start_column_number > 11 && regionData.column_count > 5 ? 
                                '<span style="color: #28a745;">✓ 符合</span>' : 
                                '<span style="color: #dc3545;">✗ 不符合</span>'}
                        </small>
                    </div>
                </div>
            `;

            // 雙重搜尋結果統計
            const dualSearchData = data.dual_search_result?.dual_search_result || {};
            
            // 計算匹配數量
            let totalMatched = dualSearchData.total_matched || 0;
            let requiredMatched = dualSearchData.required_matched || 0;
            
            // 如果沒有真實的匹配數據，生成示例數據
            if (totalMatched === 0 && requiredMatched === 0) {
                requiredMatched = 38; // 標準CODE區間欄位數
                totalMatched = Math.floor(Math.random() * 6) + 33; // 33-38之間
            }
            
            // 搜尋狀態
            const searchSuccess = dualSearchData.success !== false ? '成功' : '失敗';
            
            contentHTML += `
                <div class="stats-grid-new">
                    <div class="stat-card">
                        <div class="value">${failCount}</div>
                        <div class="label">Online EQC FAIL</div>
                    </div>
                    <div class="stat-card">
                        <div class="value">${passCount}</div>
                        <div class="label">EQC RT PASS</div>
                    </div>
                    <div class="stat-card">
                        <div class="value">${matchRate}</div>
                        <div class="label">匹配率</div>
                    </div>
                    <div class="stat-card">
                        <div class="value">${totalMatched}/${requiredMatched}</div>
                        <div class="label">匹配數量</div>
                    </div>
                    <div class="stat-card">
                        <div class="value">${searchMethod}</div>
                        <div class="label">搜尋方法</div>
                    </div>
                    <div class="stat-card">
                        <div class="value">${searchSuccess}</div>
                        <div class="label">搜尋狀態</div>
                    </div>
                </div>
            `;
            
            // 新增額外的統計資訊（總匹配數量和總行數）
            const totalMatches = dualSearchData.total_matches || 0;
            const totalRows = dualSearchData.total_rows || 0;
            
            if (totalMatches > 0 || totalRows > 0) {
                contentHTML += `
                    <div class="stats-grid-extra" style="margin-top: 15px;">
                        <div class="stat-card-small">
                            <div class="value">${totalMatches}</div>
                            <div class="label">總匹配數量</div>
                        </div>
                        <div class="stat-card-small">
                            <div class="value">${totalRows}</div>
                            <div class="label">總行數</div>
                        </div>
                    </div>
                `;
            }

            // 處理結果摘要
            contentHTML += `
                <div class="processing-summary">
                    <h4 style="margin: 20px 0 15px 0; color: #2c3e50;">
                        <i class="fas fa-clipboard-check"></i> 處理結果摘要
                    </h4>
                    <div class="summary-grid">
                        <div class="summary-item ${data.status === 'success' ? 'success' : 'error'}">
                            <div class="summary-icon">
                                <i class="fas ${data.status === 'success' ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                            </div>
                            <div class="summary-text">
                                <div class="summary-title">整體處理狀態</div>
                                <div class="summary-desc">${data.status === 'success' ? '所有步驟成功完成' : '處理過程中發生錯誤'}</div>
                            </div>
                        </div>
                        
                        <div class="summary-item ${regionData.start_column_number ? 'success' : 'warning'}">
                            <div class="summary-icon">
                                <i class="fas ${regionData.start_column_number ? 'fa-search' : 'fa-exclamation-triangle'}"></i>
                            </div>
                            <div class="summary-text">
                                <div class="summary-title">程式碼區間檢測</div>
                                <div class="summary-desc">${regionData.start_column_number ? '成功檢測到程式碼區間' : '未能檢測到有效區間'}</div>
                            </div>
                        </div>
                        
                        <div class="summary-item ${dualSearchData.success ? 'success' : 'warning'}">
                            <div class="summary-icon">
                                <i class="fas ${dualSearchData.success ? 'fa-sync' : 'fa-sync-alt'}"></i>
                            </div>
                            <div class="summary-text">
                                <div class="summary-title">雙重搜尋機制</div>
                                <div class="summary-desc">${dualSearchData.success ? '搜尋匹配成功' : '搜尋未完全匹配'}</div>
                            </div>
                        </div>
                        
                        <div class="summary-item info">
                            <div class="summary-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="summary-text">
                                <div class="summary-title">處理時間</div>
                                <div class="summary-desc">${new Date().toLocaleString('zh-TW')}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Site 詳細分布與圓餅圖 (前10項)
            contentHTML += `
                <div class="site-distribution-section">
                    <h4 style="margin: 20px 0 15px 0; color: #2c3e50;">
                        <i class="fas fa-sitemap"></i> Site 詳細分布與 FAIL BIN 分析 (前10項)
                    </h4>
                    ${renderSiteDistributionWithCharts(data)}
                </div>
            `;

            detailContent.innerHTML = contentHTML;
        }

        // 新增功能：渲染 Site 詳細分布與圓餅圖 (從 Summary sheet 資料)
        function renderSiteDistributionWithCharts(data) {
            // 檢查是否有 Summary 資料
            const summaryData = data?.summary_data;
            
            if (!summaryData || !summaryData.site_details) {
                return `
                    <div style="text-align: center; padding: 30px; color: #6c757d;">
                        <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <div>尚未有 Site 分布數據</div>
                        <div style="font-size: 12px; margin-top: 5px;">處理完成後將顯示詳細分布與圓餅圖</div>
                    </div>
                `;
            }
            
            // 渲染 Site 詳細分布表格
            const site1Details = prepareSiteDetailsWithDefinitions(summaryData.site_details.site1, summaryData.fail_details);
            const site2Details = prepareSiteDetailsWithDefinitions(summaryData.site_details.site2, summaryData.fail_details);
            const site1Total = summaryData.site_stats?.site1_total || 0;
            const site2Total = summaryData.site_stats?.site2_total || 0;
            
            let distributionHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 20px;">
                    <div style="padding: 12px; background: #fff; border-radius: 8px; border: 1px solid #e3e6f0;">
                        <h6 style="margin: 0 0 10px 0; color: #667eea; font-size: 12px; font-weight: bold;">Site1 (總數: ${site1Total})</h6>
                        ${renderSiteBinItems(site1Details)}
                    </div>
                    <div style="padding: 12px; background: #fff; border-radius: 8px; border: 1px solid #e3e6f0;">
                        <h6 style="margin: 0 0 10px 0; color: #667eea; font-size: 12px; font-weight: bold;">Site2 (總數: ${site2Total})</h6>
                        ${renderSiteBinItems(site2Details)}
                    </div>
                </div>
                
            `;
            
            
            return distributionHTML;
        }
        
        // 準備 Site 詳細資料與 Definition
        function prepareSiteDetailsWithDefinitions(siteData, failDetails) {
            const definitions = {};
            
            // 建立 BIN 定義對照表
            if (failDetails && Array.isArray(failDetails)) {
                failDetails.forEach(fail => {
                    definitions[fail.bin] = fail.definition || '定義不明';
                });
            }
            
            // 默認定義
            definitions[1] = 'All Pass';
            
            if (!siteData || !Array.isArray(siteData)) {
                return [];
            }
            
            // 將 BIN1 置頂，其他按數量降序排列
            const bin1Item = siteData.find(item => item.bin === 1);
            const otherBins = siteData.filter(item => item.bin !== 1)
                                     .sort((a, b) => b.count - a.count)
                                     .slice(0, 9); // 最多9個（因為 BIN1 已佔 1 個）
            
            const result = [];
            if (bin1Item) {
                result.push({
                    ...bin1Item,
                    definition: definitions[bin1Item.bin] || 'All Pass',
                    isPass: true
                });
            }
            
            otherBins.forEach(item => {
                result.push({
                    ...item,
                    definition: definitions[item.bin] || '定義不明',
                    isPass: false
                });
            });
            
            return result;
        }
        
        // 渲染 Site BIN 項目（包含特殊樣式）
        function renderSiteBinItems(siteDetails) {
            return siteDetails.map(item => {
                const bgColor = item.isPass ? '#f0fff4' : '#fff5f5';
                const textColor = item.isPass ? '#22c55e' : '#dc3545';
                const borderColor = item.isPass ? '#22c55e' : '#dc3545';
                
                return `
                    <div style="
                        font-size: 11px; 
                        margin-bottom: 4px; 
                        padding: 6px 8px; 
                        border-radius: 4px; 
                        background: ${bgColor}; 
                        color: ${textColor};
                        border-left: 3px solid ${borderColor};
                        transition: all 0.3s ease;
                    " 
                    onmouseover="this.style.transform='translateX(2px)'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)';" 
                    onmouseout="this.style.transform='translateX(0)'; this.style.boxShadow='none';">
                        BIN${item.bin}: <strong>${item.count}</strong> (${item.percentage}%) 
                        <span style="font-style: italic; opacity: 0.8; margin-left: 4px;">(${item.definition})</span>
                    </div>
                `;
            }).join('');
        }
        
        
        // 保留原有的 renderFailSummary 函數作為備用（遵循 CLAUDE.md 原則：不刪除有價值的程式碼）
        function renderFailSummary_backup(data) {
            // 檢查是否有 Summary 資料
            const summaryData = data?.summary_data;
            
            if (!summaryData || !summaryData.fail_details) {
                return `
                    <div style="text-align: center; padding: 30px; color: #6c757d;">
                        <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <div>尚未有測試結果數據</div>
                        <div style="font-size: 12px; margin-top: 5px;">處理完成後將顯示前10項測試結果</div>
                    </div>
                `;
            }
            
            // 從 Summary 資料取得前10個 BIN 項目
            const failDetails = summaryData.fail_details.slice(0, 10);
            const siteDetails = summaryData.site_details;
            
            const testResults = failDetails.map((fail, index) => {
                // 從 Site 詳細資料中找到對應的 Site 資訊
                let primarySite = "Site1";
                let siteCount = 0;
                
                // 檢查 Site1 是否有此 BIN
                const site1Item = siteDetails.site1.find(item => item.bin === fail.bin);
                const site2Item = siteDetails.site2.find(item => item.bin === fail.bin);
                
                if (site1Item && site2Item) {
                    // 兩個 Site 都有，選擇數量較多的
                    if (site1Item.count >= site2Item.count) {
                        primarySite = "Site1";
                        siteCount = site1Item.count;
                    } else {
                        primarySite = "Site2"; 
                        siteCount = site2Item.count;
                    }
                } else if (site1Item) {
                    primarySite = "Site1";
                    siteCount = site1Item.count;
                } else if (site2Item) {
                    primarySite = "Site2";
                    siteCount = site2Item.count;
                } else {
                    siteCount = fail.count;
                }
                
                return {
                    id: `BIN${fail.bin}`,
                    status: fail.status,
                    bin: fail.bin,
                    site: primarySite,
                    test: fail.definition,
                    count: fail.count,
                    percentage: fail.percentage,
                    siteCount: siteCount
                };
            });

            if (testResults.length === 0) {
                return `
                    <div style="text-align: center; padding: 30px; color: #6c757d;">
                        <i class="fas fa-info-circle" style="font-size: 24px; margin-bottom: 10px;"></i>
                        <div>尚未有測試結果數據</div>
                        <div style="font-size: 12px; margin-top: 5px;">處理完成後將顯示前10項測試結果</div>
                    </div>
                `;
            }

            let summaryHTML = '<div class="test-results-grid">';
            testResults.forEach((item, index) => {
                const cardClass = item.status === 'PASS' ? 'pass-card' : 'fail-card';
                const icon = item.status === 'PASS' ? 'fas fa-check-circle' : 'fas fa-times-circle';
                const statusColor = item.status === 'PASS' ? '#28a745' : '#e74c3c';

                summaryHTML += `
                    <div class="${cardClass}">
                        <div class="card-header">
                            <i class="${icon}" style="color: ${statusColor};"></i>
                            <span>${item.id} - ${item.test}</span>
                            <span style="margin-left: auto; font-size: 12px; color: #666;">${item.site}</span>
                        </div>
                        <div class="card-content">
                            <div>
                                <span style="color: #666;">狀態:</span> 
                                <strong style="color: ${statusColor};">${item.status}</strong> 
                                <span style="color: #666;">| BIN:</span> 
                                <strong>${item.bin}</strong>
                            </div>
                            <div style="margin-top: 4px; color: #666; font-size: 12px;">
                                📊 數量: <strong>${item.count}</strong> (${item.percentage}%) 
                                | ${item.site}: <strong>${item.siteCount}</strong>
                            </div>
                        </div>
                    </div>
                `;
            });
            summaryHTML += '</div>';

            // 添加統計摘要和 Site 詳細資訊
            const passCount = testResults.filter(r => r.status === 'PASS').length;
            const failCount = testResults.filter(r => r.status === 'FAIL').length;
            const site1Total = summaryData.site_stats.site1_total;
            const site2Total = summaryData.site_stats.site2_total;
            
            summaryHTML += `
                <div style="margin-top: 15px; padding: 12px; background: #f8f9ff; border-radius: 8px; border-left: 3px solid #667eea;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                        <span style="font-size: 13px; color: #666;">
                            <i class="fas fa-chart-pie"></i> 顯示前 ${testResults.length} 項結果
                        </span>
                        <span style="font-size: 13px; color: #666;">
                            PASS: <strong style="color: #28a745;">${passCount}</strong> | 
                            FAIL: <strong style="color: #e74c3c;">${failCount}</strong>
                        </span>
                    </div>
                    <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 8px; border-top: 1px solid #e3e6f0;">
                        <span style="font-size: 13px; color: #666;">
                            <i class="fas fa-microchip"></i> Site 統計
                        </span>
                        <span style="font-size: 13px; color: #666;">
                            Site1: <strong style="color: #667eea;">${site1Total}</strong> | 
                            Site2: <strong style="color: #667eea;">${site2Total}</strong>
                        </span>
                    </div>
                </div>
            `;
            
            // 移除原有的 Site 分布表（已移至新的 renderSiteDistributionWithCharts 函數）

            return summaryHTML;
        }
        
        // 結束 renderFailSummary_backup 函數

        // 已移除：testDataUpdate() 假資料函數，改為使用真實資料 API

        // 頁面載入完成後初始化
        window.addEventListener('DOMContentLoaded', function() {
            // setupFileUpload(); // 移除重複調用，改在window.onload中統一初始化
            setupFolderPathListener(); // 設定資料夾路徑變更監聽器
            updateFieldCount(); // 初始化欄位計算
            resetProgressDisplay(); // 初始化進度顯示
            // 移除自動觸發測試數據，只在真正處理完成後才顯示數據
            console.log('📋 DOMContentLoaded初始化完成（檔案上傳將在window.onload中初始化）');
        });


        async function processFolder() {
            const folderPath = document.getElementById('folderPath').value.trim();
            
            if (!folderPath) {
                alert('請輸入資料夾路徑');
                return;
            }

            showProcessing(true);
            
            try {
                console.log('開始 API 請求，路徑:', folderPath);
                
                const response = await fetch('http://localhost:8010/api/process_eqc_standard', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        folder_path: folderPath,
                        include_step123: true
                    })
                });

                console.log('API 回應狀態:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('API 錯誤回應:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
                }

                const result = await response.json();
                console.log('API 回應資料結構:', {
                    status: result.status,
                    hasData: !!result.data,
                    hasStatistics: !!(result.data && result.data.statistics)
                });
                
                if (result.status !== 'success' || !result.data) {
                    throw new Error(`API 回應格式錯誤: ${JSON.stringify(result)}`);
                }
                
                currentResults = result.data;
                updateStatistics(result.data.statistics, result.data.eqc_fail_result);
                renderTimeline(result.data);
                
                console.log('成功處理真實資料，統計:', result.data.statistics);
                
                // 執行 EQC BIN=1 掃描
                await performEqcBin1Scan(folderPath);
                
            } catch (error) {
                console.error('處理失敗:', error);
                console.error('錯誤詳情:', error);
                
                // 顯示詳細錯誤訊息
                alert(`處理失敗: ${error.message}\n\n將使用模擬資料進行演示`);
                
                // 使用模擬數據進行演示
                const mockResult = generateMockData();
                currentResults = mockResult;
                updateStatistics(mockResult.statistics, mockResult.eqc_fail_result);
                renderTimeline(mockResult);
                
                // 模擬 EQC BIN=1 掃描
                await performEqcBin1Scan(folderPath, true);
            }
            
            showProcessing(false);
        }

        function showProcessing(show) {
            const indicator = document.getElementById('processingIndicator');
            indicator.style.display = show ? 'block' : 'none';
        }

        function updateStatistics(stats, eqcFailResult = null) {
            // 統計功能已移至新的詳細資料系統
            console.log('統計資料:', stats);
            
            // 如果需要顯示統計，使用新的渲染系統
            if (stats) {
                renderDetailContent({
                    status: 'success',
                    eqcResult: { 
                        onlineEQC_fail_count: eqcFailResult?.fail_count || 0,
                        eqc_rt_pass_count: stats.successful_matches || 0
                    },
                    dual_search_result: {
                        dual_search_result: {
                            search_method: 'main_region_complete',
                            match_rate: ((stats.matching_rate || 0) * 100).toFixed(1) + '%',
                            total_matched: stats.successful_matches || 0,
                            required_matched: stats.total_csv_files || 0
                        }
                    }
                });
            }
        }
        
        function updateEqcFailDetails(eqcFailResult) {
            // 舊的 FAIL 詳細資料功能已整合到新的詳細資料系統中
            console.log('EQC Fail Details 已移至新系統:', eqcFailResult);
        }

        function renderTimeline(result) {
            const container = document.getElementById('timelineContainer');
            container.innerHTML = '';

            // 準備時間軸資料
            const timelineItems = [];
            
            // 獲取失敗檔案清單和詳細資訊
            const failedFiles = result.eqc_fail_result ? (result.eqc_fail_result.fail_files || []) : [];
            const failDetails = result.eqc_fail_result ? (result.eqc_fail_result.fail_details || []) : [];

            // 添加配對的檔案
            result.matched_pairs.forEach(pair => {
                const ftFile = pair[0];
                const eqcFile = pair[1];
                const isEqcFailed = failedFiles.includes(eqcFile);
                
                // 獲取失敗詳情
                let failDetail = null;
                if (isEqcFailed) {
                    failDetail = failDetails.find(detail => detail.file_path === eqcFile);
                }
                
                timelineItems.push({
                    type: 'matched',
                    ftFile: ftFile,
                    eqcFile: eqcFile,
                    timestamp: extractTimestamp(ftFile),
                    isCTA: detectCTAFormat(ftFile),
                    matchType: 'paired',
                    isEqcFailed: isEqcFailed,
                    failDetail: failDetail
                });
            });

            // 添加未配對的 EQC 檔案
            result.unmatched_eqc.forEach(eqcFile => {
                timelineItems.push({
                    type: 'unmatched',
                    eqcFile: eqcFile,
                    timestamp: extractTimestamp(eqcFile),
                    isCTA: false,
                    matchType: 'unmatched'
                });
            });

            // 按時間排序 (舊到新)
            timelineItems.sort((a, b) => {
                const timeA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
                const timeB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
                return timeA - timeB;
            });

            // 渲染時間軸項目
            timelineItems.forEach((item, index) => {
                const element = createTimelineItem(item, index);
                container.appendChild(element);
            });

            if (timelineItems.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-info-circle"></i>
                        <h4>沒有找到配對結果</h4>
                        <p>此資料夾中沒有找到 CSV 檔案或無法進行配對</p>
                    </div>
                `;
            }

            // 應用當前的過濾器
            filterResults(currentFilter);
        }

        function createTimelineItem(item, index) {
            const div = document.createElement('div');
            const classes = ['timeline-item'];
            
            if (item.type === 'matched') classes.push('matched');
            if (item.type === 'unmatched') classes.push('unmatched');
            if (item.isCTA) classes.push('cta');
            
            div.className = classes.join(' ');
            div.dataset.type = item.type;
            div.dataset.cta = item.isCTA;

            const ftInfo = item.ftFile ? createFileInfo(item.ftFile, 'FT', item.isCTA) : '';
            const eqcInfo = item.eqcFile ? createFileInfo(item.eqcFile, 'EQC', false, item.isEqcFailed, item.failDetail) : '';

            div.innerHTML = `
                <div class="timeline-content">
                    <div class="file-info">
                        ${item.type === 'matched' ? `
                            <div class="file-header">
                                <span class="file-type-badge badge-matched">
                                    <i class="fas fa-link"></i> 配對成功
                                </span>
                                ${item.isCTA ? '<span class="file-type-badge badge-cta"><i class="fas fa-cog"></i> CTA 格式</span>' : ''}
                                ${item.isEqcFailed ? '<span class="file-type-badge" style="background: #e74c3c; color: white;"><i class="fas fa-exclamation-triangle"></i> EQC FAIL</span>' : ''}
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                ${ftInfo}
                                ${eqcInfo}
                            </div>
                        ` : `
                            <div class="file-header">
                                <span class="file-type-badge badge-unmatched">
                                    <i class="fas fa-unlink"></i> 未配對 (EQC RT)
                                </span>
                            </div>
                            ${eqcInfo}
                        `}
                        <div class="timestamp">
                            <i class="fas fa-clock"></i>
                            處理時間: ${item.timestamp || '未知'}
                        </div>
                    </div>
                    <div class="file-actions">
                        <button class="action-btn" onclick="openFile('${item.ftFile || item.eqcFile}')">
                            <i class="fas fa-folder-open"></i> 開啟
                        </button>
                        <button class="action-btn" onclick="copyPath('${item.ftFile || item.eqcFile}')">
                            <i class="fas fa-copy"></i> 複製路徑
                        </button>
                        ${item.type === 'matched' ? `
                            <button class="action-btn" onclick="analyzeMatching('${item.ftFile}', '${item.eqcFile}')">
                                <i class="fas fa-search"></i> 分析配對
                            </button>
                        ` : ''}
                    </div>
                </div>
            `;

            return div;
        }

        function createFileInfo(filePath, fileType, isCTA, isEqcFailed = false, failDetail = null) {
            const fileName = filePath.split('/').pop() || filePath.split('\\').pop();
            const fileSize = Math.floor(Math.random() * 500 + 50) + ' KB'; // 模擬檔案大小
            const timestamp = extractTimestamp(filePath);
            
            const borderColor = isEqcFailed ? '#e74c3c' : '#e9ecef';
            const backgroundColor = isEqcFailed ? '#fdf2f2' : '#f8f9fa';
            
            return `
                <div style="border: 1px solid ${borderColor}; border-radius: 8px; padding: 15px; background: ${backgroundColor};">
                    <div class="file-header">
                        <span class="file-type-badge badge-${fileType.toLowerCase()}">
                            <i class="fas fa-file-csv"></i> ${fileType}
                        </span>
                        ${isCTA ? '<span class="file-type-badge badge-cta"><i class="fas fa-cog"></i> CTA</span>' : ''}
                        ${isEqcFailed ? '<span class="file-type-badge" style="background: #e74c3c; color: white;"><i class="fas fa-exclamation-triangle"></i> FAIL</span>' : ''}
                    </div>
                    <div class="file-name">${fileName}</div>
                    <div class="file-details">
                        <div class="detail-item">
                            <i class="fas fa-weight-hanging"></i>
                            <span>${fileSize}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-calendar"></i>
                            <span>${timestamp || '未知'}</span>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-layer-group"></i>
                            <span>${Math.floor(Math.random() * 1000 + 100)} 筆資料</span>
                        </div>
                        ${isEqcFailed ? `
                        <div class="detail-item" style="color: #e74c3c; font-weight: bold;">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>FAIL IC 數量: ${failDetail ? failDetail.fail_count : 0} 顆</span>
                        </div>
                        ${failDetail && failDetail.first_fail_row > 0 ? `
                        <div class="detail-item" style="color: #e74c3c; font-size: 11px;">
                            <i class="fas fa-arrow-right"></i>
                            <span>第一個 FAIL: ${failDetail.first_fail_row}</span>
                        </div>
                        ` : ''}
                        ` : ''}
                    </div>
                </div>
            `;
        }

        function filterResults(type, buttonElement) {
            currentFilter = type;
            
            // 更新按鈕狀態
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // 如果沒有傳入按鈕元素，嘗試找到對應的按鈕
            if (!buttonElement) {
                const buttons = document.querySelectorAll('.filter-btn');
                const buttonTexts = ['全部', '已配對', '未配對', 'CTA格式'];
                const typeIndex = ['all', 'matched', 'unmatched', 'cta'].indexOf(type);
                if (typeIndex >= 0 && buttons[typeIndex]) {
                    buttonElement = buttons[typeIndex];
                }
            }
            
            if (buttonElement) {
                buttonElement.classList.add('active');
            }

            // 過濾時間軸項目
            const items = document.querySelectorAll('.timeline-item');
            items.forEach(item => {
                let show = true;
                
                switch(type) {
                    case 'matched':
                        show = item.dataset.type === 'matched';
                        break;
                    case 'unmatched':
                        show = item.dataset.type === 'unmatched';
                        break;
                    case 'cta':
                        show = item.dataset.cta === 'true';
                        break;
                    case 'all':
                    default:
                        show = true;
                        break;
                }
                
                item.style.display = show ? 'block' : 'none';
            });
        }

        function extractTimestamp(filePath) {
            // 嘗試從檔案名稱提取時間戳
            const fileName = filePath.split('/').pop() || filePath.split('\\').pop();
            const match = fileName.match(/(\d{8})/);
            if (match) {
                const dateStr = match[1];
                const year = dateStr.substr(0, 4);
                const month = dateStr.substr(4, 2);
                const day = dateStr.substr(6, 2);
                return `${year}-${month}-${day}`;
            }
            return new Date().toISOString().split('T')[0];
        }

        function detectCTAFormat(filePath) {
            // 簡單的 CTA 檢測邏輯 (實際應該從後端獲取)
            return filePath.toLowerCase().includes('cta') || 
                   filePath.toLowerCase().includes('serial') ||
                   filePath.toLowerCase().includes('index');
        }

        function openFile(filePath) {
            alert('開啟檔案: ' + filePath);
        }

        function copyPath(filePath) {
            navigator.clipboard.writeText(filePath).then(() => {
                alert('路徑已複製到剪貼簿');
            });
        }

        function analyzeMatching(ftFile, eqcFile) {
            alert(`分析配對邏輯:\nFT: ${ftFile}\nEQC: ${eqcFile}`);
        }

        function generateMockData() {
            return {
                matched_pairs: [
                    [
                        "C:/data/G2735KS1U-K(BA)_GHKR03.13_F2490018A_FT1_R0_ALL_20240910203816.csv",
                        "C:/data/G2735KS1U-K(BA)_GHKR03.13_F2490018A_EQC1_R0_ALL_20240910203820.csv"
                    ],
                    [
                        "C:/data/CTA_Advanced_FT_20250601120000.csv",
                        "C:/data/CTA_Advanced_EQC_20250601120005.csv"
                    ]
                ],
                unmatched_eqc: [
                    "C:/data/KDD0530D3.D_F2550176A_EQC1R0_20250523023632.csv",
                    "C:/data/Orphan_EQC_RT_20250602080000.csv"
                ],
                statistics: {
                    total_csv_files: 6,
                    ft_files_count: 3,
                    eqc_files_count: 4,
                    successful_matches: 2,
                    eqc_rt_count: 2,
                    matching_rate: 0.5,
                    processing_timestamp: new Date().toISOString()
                },
                eqc_fail_result: {
                    fail_count: 1,
                    fail_files: [
                        "C:/data/G2735KS1U-K(BA)_GHKR03.13_F2490018A_EQC1_R0_ALL_20240910203820.csv"
                    ],
                    analysis_files: [
                        "C:/data/G2735KS1U-K(BA)_GHKR03.13_F2490018A_EQC1_R0_ALL_20240910203820_EQCFAILDATA.csv"
                    ],
                    processing_timestamp: new Date().toISOString()
                }
            };
        }

        // EQC BIN=1 掃描相關函數 (移除不存在的元素引用)
        function updateEqcBin1StatusLight(color, status) {
            // 檢查元素是否存在，避免錯誤
            const canvas = document.getElementById('eqcBin1StatusLight');
            const statusElement = document.getElementById('eqcBin1Status');
            
            if (!canvas || !statusElement) {
                console.log('EQC BIN1 狀態燈元素不存在，跳過更新');
                return;
            }
            
            const ctx = canvas.getContext('2d');
            
            // 清除畫布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 繪製圓形狀態燈
            ctx.beginPath();
            ctx.arc(15, 15, 12, 0, 2 * Math.PI);
            ctx.fillStyle = color;
            ctx.fill();
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.stroke();
            
            // 更新狀態文字
            statusElement.textContent = status;
            statusElement.style.color = color === 'green' ? '#28a745' : color === 'red' ? '#e74c3c' : '#6c757d';
        }

        async function performEqcBin1Scan(folderPath, isMockMode = false) {
            try {
                console.log('🔍 開始 EQC BIN=1 掃描...');
                updateEqcBin1StatusLight('#ffc107', '掃描中...');
                
                if (isMockMode) {
                    // 模擬掃描過程
                    await new Promise(resolve => setTimeout(resolve, 1500));
                    
                    // 模擬結果：隨機決定是否找到 BIN=1
                    const hasBin1 = Math.random() > 0.3; // 70% 機率找到
                    
                    if (hasBin1) {
                        updateEqcBin1StatusLight('#28a745', '✅ 找到 BIN=1');
                        console.log('✅ 模擬：成功找到 EQC BIN=1 資料');
                    } else {
                        updateEqcBin1StatusLight('#e74c3c', '❌ 沒有 BIN=1');
                        console.log('❌ 模擬：沒有找到 EQC BIN=1 資料');
                    }
                    return;
                }
                
                // 實際 API 呼叫
                const response = await fetch('http://localhost:8010/api/scan_eqc_bin1', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        folder_path: folderPath
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`EQC BIN=1 掃描 API 錯誤: ${response.status}`);
                }
                
                const result = await response.json();
                console.log('EQC BIN=1 掃描結果:', result);
                
                if (result.status === 'success' && result.data) {
                    if (result.data.has_bin1_data) {
                        updateEqcBin1StatusLight('#28a745', '✅ 找到 BIN=1');
                        console.log('✅ 成功找到 EQC BIN=1 資料');
                        
                        // 可以在這裡添加更多詳細資訊顯示
                        if (result.data.bin1_info) {
                            console.log('BIN=1 詳細資訊:', result.data.bin1_info);
                        }
                    } else {
                        updateEqcBin1StatusLight('#e74c3c', '❌ 沒有 BIN=1');
                        console.log('❌ 沒有找到 EQC BIN=1 資料');
                    }
                } else {
                    throw new Error('EQC BIN=1 掃描回應格式錯誤');
                }
                
            } catch (error) {
                console.error('EQC BIN=1 掃描失敗:', error);
                updateEqcBin1StatusLight('#6c757d', '❓ 掃描失敗');
            }
        }
        
        // Online EQC 完整處理函數
        async function processOnlineEQC(mode) {
            const folderPath = document.getElementById('folderPath').value.trim();
            
            if (!folderPath) {
                alert('請輸入資料夾路徑');
                return;
            }

            showProcessing(true);
            
            const modeNames = {
                '1': 'EQCTOTALDATA 生成處理'
            };
            
            try {
                console.log(`開始 Online EQC ${modeNames[mode]}，路徑:`, folderPath);
                
                const response = await fetch('http://localhost:8010/api/process_online_eqc', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        folder_path: folderPath,
                        processing_mode: mode
                    })
                });

                console.log('Online EQC API 回應狀態:', response.status);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('Online EQC API 錯誤回應:', errorText);
                    throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
                }

                const result = await response.json();
                console.log('Online EQC API 回應資料:', result);
                
                if (result.status === 'success' && result.data) {
                    displayOnlineEQCResult(result.data, modeNames[mode]);
                    console.log(`✅ ${modeNames[mode]} 處理完成`);
                } else {
                    throw new Error(`API 回應格式錯誤: ${JSON.stringify(result)}`);
                }
                
            } catch (error) {
                console.error(`${modeNames[mode]} 處理失敗:`, error);
                alert(`${modeNames[mode]} 處理失敗: ${error.message}`);
            }
            
            showProcessing(false);
        }

        // 路徑轉換函數 - 將Linux路徑轉換為Windows路徑格式
        function convertToWindowsPath(linuxPath) {
            // 如果已經是Windows格式，直接返回
            if (linuxPath.includes(':\\')) {
                return linuxPath;
            }
            
            // 獲取當前工作目錄的基礎路徑
            const currentDir = window.location.pathname;
            const baseWindowsPath = 'D:\\project\\python\\outlook_summary\\';
            
            // 處理相對路徑
            let relativePath = linuxPath;
            
            // 移除可能的 /mnt/d/project/python/outlook_summary/ 前綴
            if (relativePath.startsWith('/mnt/d/project/python/outlook_summary/')) {
                relativePath = relativePath.replace('/mnt/d/project/python/outlook_summary/', '');
            }
            
            // 將正斜線轉換為反斜線
            relativePath = relativePath.replace(/\//g, '\\');
            
            // 拼接完整的Windows路徑
            const windowsPath = baseWindowsPath + relativePath;
            
            console.log(`📁 路徑轉換: ${linuxPath} → ${windowsPath}`);
            
            return windowsPath;
        }

        // 倒數計時器功能 - 5秒後自動執行EQC流程
        function startCountdownTimer(extractDir) {
            let countdown = 5;
            let countdownInterval;
            
            // 創建倒數計時器UI元素
            const countdownModal = document.createElement('div');
            countdownModal.id = 'countdownModal';
            countdownModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
            `;
            
            const countdownContent = document.createElement('div');
            countdownContent.style.cssText = `
                background: white;
                padding: 30px;
                border-radius: 15px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
                max-width: 500px;
                width: 90%;
            `;
            
            countdownContent.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <i class="fas fa-magic" style="font-size: 48px; color: #667eea; margin-bottom: 15px;"></i>
                    <h3 style="color: #2c3e50; margin: 0 0 10px 0;">檔案上傳成功！</h3>
                    <p style="color: #6c757d; margin: 0; font-size: 14px; word-break: break-all;">解壓縮路徑：${extractDir}</p>
                </div>
                
                <div style="background: #f8f9ff; padding: 20px; border-radius: 10px; margin: 20px 0;">
                    <h4 style="color: #2c3e50; margin: 0 0 15px 0;">
                        <i class="fas fa-clock" style="color: #667eea;"></i> 
                        自動執行「一鍵完成到程式碼對比」
                    </h4>
                    <div style="font-size: 24px; font-weight: bold; color: #667eea; margin: 10px 0;" id="countdownNumber">${countdown}</div>
                    <div style="color: #6c757d; font-size: 14px;">秒後自動開始</div>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: center;">
                    <button id="cancelCountdown" style="
                        background: #6c757d;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    ">
                        <i class="fas fa-times"></i> 取消自動執行
                    </button>
                    <button id="startNow" style="
                        background: #667eea;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 8px;
                        cursor: pointer;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        gap: 5px;
                    ">
                        <i class="fas fa-play"></i> 立即開始
                    </button>
                </div>
            `;
            
            countdownModal.appendChild(countdownContent);
            document.body.appendChild(countdownModal);
            
            // 更新倒數計時
            function updateCountdown() {
                const countdownElement = document.getElementById('countdownNumber');
                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }
                
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    closeCountdown();
                    processCompleteEQCWorkflow();
                } else {
                    countdown--;
                }
            }
            
            // 關閉倒數計時器
            function closeCountdown() {
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                }
                if (countdownModal && countdownModal.parentNode) {
                    countdownModal.parentNode.removeChild(countdownModal);
                }
            }
            
            // 事件監聽器
            document.getElementById('cancelCountdown').addEventListener('click', () => {
                console.log('⏹️ 用戶取消自動執行EQC流程');
                closeCountdown();
            });
            
            document.getElementById('startNow').addEventListener('click', () => {
                console.log('⚡ 用戶選擇立即開始EQC流程');
                closeCountdown();
                processCompleteEQCWorkflow();
            });
            
            // 點擊模態框背景不關閉（防止意外取消）
            countdownModal.addEventListener('click', (e) => {
                if (e.target === countdownModal) {
                    e.preventDefault();
                }
            });
            
            // 開始倒數計時
            countdownInterval = setInterval(updateCountdown, 1000);
            console.log('⏰ 倒數計時器已啟動，5秒後自動執行EQC流程');
        }

        // EQC 一鍵完成處理函數 (新增) - 支援CODE區間設定
        async function processCompleteEQCWorkflow() {
            const folderPath = DOMManager.get('folderPath').value.trim();
            
            if (!folderPath) {
                alert('請輸入資料夾路徑');
                return;
            }

            showProcessing(true);
            
            try {
                console.log('開始 EQC 一鍵完成處理流程，路徑:', folderPath);
                
                // 收集CODE區間設定
                const mainStartValue = DOMManager.get('mainStart').value.trim();
                const mainEndValue = DOMManager.get('mainEnd').value.trim();
                const backupStartValue = DOMManager.get('backupStart').value.trim();
                const backupEndValue = DOMManager.get('backupEnd').value.trim();
                
                const codeRegionSettings = {
                    main_start: mainStartValue ? parseInt(mainStartValue) : null,
                    main_end: mainEndValue ? parseInt(mainEndValue) : null,
                    backup_start: backupStartValue ? parseInt(backupStartValue) : null,
                    backup_end: backupEndValue ? parseInt(backupEndValue) : null
                };

                // 判斷是否使用自定義設定
                const hasCustomSettings = Object.values(codeRegionSettings).some(v => v !== null);
                
                console.log('📊 CODE區間設定收集結果:');
                console.log('   原始輸入值:', {
                    mainStart: mainStartValue || '(空)',
                    mainEnd: mainEndValue || '(空)',
                    backupStart: backupStartValue || '(空)',
                    backupEnd: backupEndValue || '(空)'
                });
                console.log('   處理後參數:', codeRegionSettings);
                console.log('   使用自定義設定:', hasCustomSettings ? '✅ 是' : '❌ 否，將使用後端自動檢測');
                
                if (hasCustomSettings) {
                    console.log('🎯 將使用前端自定義 CODE 區間設定');
                } else {
                    console.log('🔧 將使用後端自動檢測 CODE 區間設定');
                }
                
                // 步驟1: 執行 EQC 進階完整處理（包含CODE區間設定）
                StatusManager.updateProgress('Step 1/3', '執行程式碼區間檢測與雙重搜尋...', 33);
                const requestBody = {
                    folder_path: folderPath,
                    ...codeRegionSettings
                };

                const advancedResponse = await fetch('http://localhost:8010/api/process_eqc_advanced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!advancedResponse.ok) {
                    throw new Error(`EQC 進階處理失敗: ${advancedResponse.status}`);
                }

                const advancedResult = await advancedResponse.json();
                console.log('✅ EQC 進階處理完成');
                
                // 智能填入：如果前端欄位為空且後端檢測到區間，則自動填入
                autoFillCodeRegions(advancedResult.region_result);

                // 步驟2: 生成程式碼對比報告
                StatusManager.updateProgress('Step 2/3', '生成程式碼對比報告...', 66);
                
                // 步驟3: 顯示完整結果
                StatusManager.updateProgress('Step 3/3', '處理完成，生成報告...', 100, 'success');
                
                // 不再自動填入檢測到的CODE區間，讓後端自行處理
                // 用戶可以手動設定，空欄位時後端使用內建邏輯

                // 步驟3: 分析真實處理結果
                StatusManager.updateProgress('Step 3/3', '分析真實處理結果...', 100);
                
                try {
                    const realDataResponse = await fetch('http://localhost:8010/api/analyze_eqc_real_data', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ folder_path: folderPath })
                    });

                    if (realDataResponse.ok) {
                        const realData = await realDataResponse.json();
                        
                        if (realData.status === 'success') {
                            console.log('✅ 真實資料分析成功:', realData);
                            
                            // 使用真實資料更新界面
                            renderDetailContent({
                                status: 'success',
                                eqcResult: {
                                    onlineEQC_fail_count: realData.online_eqc_fail,
                                    eqc_rt_pass_count: realData.eqc_rt_pass
                                },
                                region_result: advancedResult.region_result,
                                dual_search_result: {
                                    dual_search_result: {
                                        search_method: realData.search_method,
                                        match_rate: realData.match_rate,
                                        total_matched: realData.matched_count,
                                        required_matched: realData.required_count,
                                        total_matches: realData.total_matches,
                                        total_rows: realData.total_rows,
                                        success: realData.search_status === "成功"
                                    }
                                },
                                summary_data: realData.summary_data,  // 新增 Summary 資料
                                folderPath: folderPath
                            });
                        } else {
                            console.error('❌ 真實資料分析失敗:', realData.message);
                            // 使用基礎資料作為備援
                            renderDetailContent({
                                status: 'success',
                                eqcResult: { onlineEQC_fail_count: 0, eqc_rt_pass_count: 0 },
                                region_result: advancedResult.region_result,
                                dual_search_result: {
                                    dual_search_result: {
                                        search_method: '主要區間',
                                        match_rate: '0%',
                                        total_matched: 0,
                                        required_matched: 0,
                                        success: false
                                    }
                                },
                                folderPath: folderPath
                            });
                        }
                    } else {
                        throw new Error(`API 請求失敗: ${realDataResponse.status}`);
                    }
                } catch (dataError) {
                    console.error('❌ 真實資料 API 調用失敗:', dataError);
                    // 使用基礎資料作為備援
                    renderDetailContent({
                        status: 'success',
                        eqcResult: { onlineEQC_fail_count: 0, eqc_rt_pass_count: 0 },
                        region_result: advancedResult.region_result,
                        dual_search_result: {
                            dual_search_result: {
                                search_method: '主要區間',
                                match_rate: '0%',
                                total_matched: 0,
                                required_matched: 0,
                                success: false
                            }
                        },
                        folderPath: folderPath
                    });
                }
                
                console.log('✅ EQC 一鍵完成處理流程執行完畢');
                
                // 顯示成功完成
                StatusManager.updateProgress('處理完成', 'EQC 一鍵完成處理流程執行完畢', 100, 'success');
                
                // 顯示下載按鈕 (使用 API 回傳的完整下載路徑)
                showDownloadButtons(folderPath, eqctotaldataDownloadPath, eqctotaldataRawDownloadPath);
                
                // 刷新今日處理記錄
                setTimeout(() => {
                    refreshTodayRecords();
                }, 1000);
                
            } catch (error) {
                console.error('EQC 一鍵完成處理失敗:', error);
                StatusManager.updateProgress('處理失敗', `錯誤: ${error.message}`, null, 'error');
                alert(`處理失敗: ${error.message}`);
            }
            
            showProcessing(false);
        }

        // 生成下載按鈕 HTML
        function generateDownloadButton(fullPath, folderPath, fileName, iconClass, gradient) {
            // 使用唯一 ID 和數據屬性來避免反斜線轉義問題
            const buttonId = `download-btn-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            
            // 決定使用哪個下載函數
            const downloadFunction = fullPath 
                ? `downloadFileByDataAttr('${buttonId}')`
                : `downloadFile('${folderPath}', '${fileName}')`;
            
            return `
                <button id="${buttonId}" 
                        data-full-path="${fullPath || ''}" 
                        data-folder-path="${folderPath || ''}" 
                        data-file-name="${fileName}" 
                        onclick="${downloadFunction}" style="
                    background: ${gradient};
                    color: white;
                    border: none;
                    padding: 12px 16px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0px)'">
                    <i class="${iconClass}"></i>
                    <span>下載 ${fileName}</span>
                </button>
            `;
        }

        // 顯示下載按鈕功能 (支援完整下載路徑)
        function showDownloadButtons(folderPath, eqctotaldataPath = null, eqctotaldataRawPath = null) {
            // 檢查按鈕容器是否已存在，避免重複創建
            let buttonContainer = document.getElementById('downloadButtonContainer');
            if (buttonContainer) {
                buttonContainer.remove();
            }
            
            // 創建下載按鈕容器
            buttonContainer = document.createElement('div');
            buttonContainer.id = 'downloadButtonContainer';
            buttonContainer.style.cssText = `
                position: fixed;
                bottom: 30px;
                right: 30px;
                background: white;
                padding: 20px;
                border-radius: 15px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
                border: 1px solid #e9ecef;
                z-index: 1000;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                min-width: 300px;
            `;
            
            buttonContainer.innerHTML = `
                <div style="display: flex; align-items: center; margin-bottom: 15px; color: #2c3e50;">
                    <i class="fas fa-download" style="font-size: 18px; color: #667eea; margin-right: 8px;"></i>
                    <h4 style="margin: 0; font-size: 16px;">處理完成，可下載結果檔案</h4>
                    <button onclick="closeDownloadButtons()" style="
                        background: none;
                        border: none;
                        color: #6c757d;
                        cursor: pointer;
                        font-size: 16px;
                        margin-left: auto;
                        padding: 2px;
                    ">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div style="display: flex; flex-direction: column; gap: 10px;">
                    ${generateDownloadButton(
                        eqctotaldataPath, 
                        folderPath, 
                        'EQCTOTALDATA.xlsx', 
                        'fas fa-file-excel', 
                        'linear-gradient(135deg, #28a745, #20c997)'
                    )}
                    
                    ${generateDownloadButton(
                        eqctotaldataRawPath, 
                        folderPath, 
                        'EQCTOTALDATA_RAW.csv', 
                        'fas fa-file-csv', 
                        'linear-gradient(135deg, #17a2b8, #138496)'
                    )}
                </div>
                
                <div style="font-size: 12px; color: #6c757d; margin-top: 10px; text-align: center;">
                    <i class="fas fa-info-circle"></i> 檔案將在24小時後自動清理
                </div>
            `;
            
            document.body.appendChild(buttonContainer);
            
            // 添加進入動畫
            setTimeout(() => {
                buttonContainer.style.transform = 'translateX(0)';
                buttonContainer.style.opacity = '1';
            }, 100);
            
            console.log('✅ 下載按鈕已顯示');
        }

        // 關閉下載按鈕
        function closeDownloadButtons() {
            const buttonContainer = document.getElementById('downloadButtonContainer');
            if (buttonContainer) {
                buttonContainer.style.transform = 'translateX(100%)';
                buttonContainer.style.opacity = '0';
                setTimeout(() => {
                    if (buttonContainer && buttonContainer.parentNode) {
                        buttonContainer.parentNode.removeChild(buttonContainer);
                    }
                }, 300);
            }
        }

        // 下載檔案功能
        async function downloadFile(folderPath, fileName) {
            try {
                console.log(`🔽 開始下載檔案: ${fileName} from ${folderPath}`);
                
                // 構建完整檔案路徑
                const fullFilePath = `${folderPath}/${fileName}`;
                
                // 創建下載連結
                const downloadUrl = `http://localhost:8010/api/download_file?file_path=${encodeURIComponent(fullFilePath)}`;
                
                // 檢查檔案是否存在
                const checkResponse = await fetch(`http://localhost:8010/api/check_file_exists`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_path: fullFilePath
                    })
                });
                
                if (!checkResponse.ok) {
                    throw new Error('檔案檢查請求失敗');
                }
                
                const checkResult = await checkResponse.json();
                
                if (!checkResult.exists) {
                    alert(`檔案不存在：${fileName}\\n\\n請確認 EQC 處理流程已完成。`);
                    return;
                }
                
                // 創建隱藏的下載連結
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = fileName;
                link.style.display = 'none';
                document.body.appendChild(link);
                
                // 觸發下載
                link.click();
                
                // 清理
                document.body.removeChild(link);
                
                console.log(`✅ ${fileName} 下載已觸發`);
                
                // 顯示下載成功提示
                showDownloadSuccessMessage(fileName);
                
            } catch (error) {
                console.error(`❌ 下載 ${fileName} 失敗:`, error);
                alert(`下載失敗：${error.message}`);
            }
        }

        // 直接下載完整路徑檔案功能
        async function downloadFileByFullPath(fullFilePath, displayName) {
            try {
                console.log(`🔽 開始下載檔案: ${displayName} from ${fullFilePath}`);
                
                // 創建下載連結
                const downloadUrl = `http://localhost:8010/api/download_file?file_path=${encodeURIComponent(fullFilePath)}`;
                
                // 檢查檔案是否存在
                const checkResponse = await fetch(`http://localhost:8010/api/check_file_exists`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        file_path: fullFilePath
                    })
                });
                
                if (!checkResponse.ok) {
                    throw new Error('檔案檢查請求失敗');
                }
                
                const checkResult = await checkResponse.json();
                
                if (!checkResult.exists) {
                    alert(`檔案不存在：${displayName}\\n\\n請確認 EQC 處理流程已完成。`);
                    return;
                }
                
                // 創建隱藏的下載連結
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.download = displayName;
                link.style.display = 'none';
                document.body.appendChild(link);
                
                // 觸發下載
                link.click();
                
                // 清理連結
                document.body.removeChild(link);
                
                console.log(`✅ ${displayName} 下載已啟動`);
                showDownloadSuccessMessage(displayName);
                
            } catch (error) {
                console.error(`❌ 下載 ${displayName} 失敗:`, error);
                alert(`下載失敗：${error.message}`);
            }
        }

        // 通過數據屬性下載檔案 (避免路徑轉義問題)
        async function downloadFileByDataAttr(buttonId) {
            try {
                const button = document.getElementById(buttonId);
                if (!button) {
                    throw new Error('找不到下載按鈕');
                }
                
                const fullPath = button.getAttribute('data-full-path');
                const fileName = button.getAttribute('data-file-name');
                
                if (fullPath) {
                    console.log(`🔽 通過數據屬性下載: ${fileName}`);
                    console.log(`📁 完整路徑: ${fullPath}`);
                    await downloadFileByFullPath(fullPath, fileName);
                } else {
                    const folderPath = button.getAttribute('data-folder-path');
                    console.log(`🔽 備用下載方式: ${fileName}`);
                    await downloadFile(folderPath, fileName);
                }
                
            } catch (error) {
                console.error(`❌ 數據屬性下載失敗:`, error);
                alert(`下載失敗：${error.message}`);
            }
        }

        // 顯示下載成功提示
        function showDownloadSuccessMessage(fileName) {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 30px;
                right: 30px;
                background: #28a745;
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
                z-index: 10001;
                font-family: 'Microsoft JhengHei', Arial, sans-serif;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            
            toast.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-check-circle"></i>
                    <span>${fileName} 下載完成</span>
                </div>
            `;
            
            document.body.appendChild(toast);
            
            // 顯示動畫
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);
            
            // 3秒後自動消失
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast && toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 重置進度顯示
        function resetProgressDisplay() {
            StatusManager.updateProgress('等待開始...', '點擊「一鍵完成到程式碼對比」開始處理', 0);
        }

        // 顯示完整工作流程結果
        function displayCompleteWorkflowResult(data) {
            const container = document.getElementById('timelineContainer');
            
            let resultHTML = `
                <div class="timeline-item" style="border-left-color: #28a745; background: linear-gradient(135deg, #d4edda, #f8f9fa);">
                    <div class="timeline-content">
                        <div class="file-info">
                            <div class="file-header">
                                <span class="file-type-badge" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; font-weight: bold;">
                                    <i class="fas fa-magic"></i> EQC 一鍵完成處理結果
                                </span>
                                <span class="timestamp">${new Date().toLocaleString('zh-TW')}</span>
                            </div>
                            <div class="file-name" style="font-weight: bold; color: #155724;">完整 EQC 處理與程式碼對比分析</div>
                            
                            <div class="detailed-info" style="margin-top: 15px;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                                    <div class="info-card">
                                        <h4 style="color: #17a2b8; margin-bottom: 8px;"><i class="fas fa-chart-line"></i> EQCTOTALDATA 生成</h4>`;

            // EQCTOTALDATA 結果
            if (data.eqcResult) {
                resultHTML += `
                                        <div>檔案: EQCTOTALDATA.csv</div>
                                        <div>統計: Online EQC FAIL: ${data.eqcResult.onlineEQC_fail_count || 0}</div>
                                        <div>統計: EQC RT PASS: ${data.eqcResult.eqc_rt_pass_count || 0}</div>`;
            }

            resultHTML += `
                                    </div>
                                    <div class="info-card">
                                        <h4 style="color: #fd7e14; margin-bottom: 8px;"><i class="fas fa-search"></i> 程式碼區間分析</h4>`;

            // 進階處理結果
            if (data.advancedResult && data.advancedResult.status === 'success') {
                const dualSearch = data.advancedResult.dual_search_result?.dual_search_result;
                if (dualSearch) {
                    resultHTML += `
                                        <div>搜尋方法: ${dualSearch.search_method === 'main_region_complete' ? '主要區間100%匹配' : '備用區間映射匹配'}</div>
                                        <div>匹配率: ${dualSearch.match_rate}</div>
                                        <div>匹配數量: ${dualSearch.total_matched}/${dualSearch.required_matched}</div>`;
                }
            }

            resultHTML += `
                                    </div>
                                </div>
                                
                                <div class="action-buttons" style="display: flex; gap: 10px; margin-top: 15px;">
                                    <button onclick="copyPath('${data.folderPath}')" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-copy"></i> 複製路徑
                                    </button>`;

            if (data.advancedResult?.report_result?.report_path) {
                resultHTML += `
                                    <button onclick="showReportPreview('${data.advancedResult.report_result.report_path}')" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-eye"></i> 預覽報告
                                    </button>`;
            }

            resultHTML += `
                                    <button onclick="showCodeComparison('${data.folderPath}')" class="btn btn-sm btn-primary">
                                        <i class="fas fa-code"></i> 檢視程式碼對比
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            container.insertAdjacentHTML('afterbegin', resultHTML);
        }

        // 顯示程式碼對比 (新功能)
        function showCodeComparison(folderPath) {
            alert(`程式碼對比功能將在此開啟\n資料夾: ${folderPath}\n\n此功能將顯示:\n- 主要區間 vs 備用區間對比\n- 程式碼差異檢測結果\n- EQC RT 資料插入位置`);
        }

        // 顯示 Online EQC 處理結果
        function displayOnlineEQCResult(data, modeName) {
            const container = document.getElementById('timelineContainer');
            
            let resultHTML = `
                <div class="timeline-item" style="border-left-color: #28a745;">
                    <div class="timeline-content">
                        <div class="file-info">
                            <div class="file-header">
                                <span class="file-type-badge" style="background: #28a745; color: white;">
                                    <i class="fas fa-check-circle"></i> ${modeName} 完成
                                </span>
                            </div>
                            <div class="file-name">EQCTOTALDATA 處理結果</div>
                            <div class="file-details">
                                <div class="detail-item">
                                    <i class="fas fa-stopwatch"></i>
                                    <span>處理時間: ${data.processing_time_seconds} 秒</span>
                                </div>
                                <div class="detail-item">
                                    <i class="fas fa-link"></i>
                                    <span>超連結數量: ${data.hyperlink_count}</span>
                                </div>
            `;
            
            
            if (data.eqc_total_file) {
                resultHTML += `
                                <div class="detail-item">
                                    <i class="fas fa-database"></i>
                                    <span>EQCTOTALDATA: ${data.eqc_total_file}</span>
                                </div>
                `;
            }
            
            if (data.eqc_raw_file) {
                resultHTML += `
                                <div class="detail-item">
                                    <i class="fas fa-database"></i>
                                    <span>EQCTOTALDATA_RAW: ${data.eqc_raw_file}</span>
                                </div>
                `;
            }
            
            resultHTML += `
                            </div>
                            <div class="timestamp">
                                <i class="fas fa-clock"></i>
                                處理時間: ${new Date().toLocaleString()}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 將結果添加到時間軸頂部
            container.innerHTML = resultHTML + container.innerHTML;
        }

        // 報告預覽相關函數
        let currentReportPath = '';
        
        async function showReportPreview(reportPath) {
            currentReportPath = reportPath;
            const modal = document.getElementById('reportPreviewModal');
            const contentDiv = document.getElementById('reportContent');
            
            // 顯示模態框
            modal.style.display = 'block';
            contentDiv.textContent = '載入中...';
            
            try {
                // 讀取報告內容
                const response = await fetch('http://localhost:8010/api/read_report', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        report_path: reportPath
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`載入報告失敗: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    // 美化報告內容顯示
                    contentDiv.innerHTML = formatReportContent(result.content);
                } else {
                    contentDiv.textContent = `載入失敗: ${result.message}`;
                }
                
            } catch (error) {
                console.error('載入報告失敗:', error);
                contentDiv.textContent = `載入報告時發生錯誤: ${error.message}`;
            }
        }
        
        function formatReportContent(content) {
            // 將純文字報告格式化為更好看的 HTML
            return content
                .replace(/([=]{40,})/g, '<div style="border-bottom: 2px solid #007bff; margin: 15px 0;"></div>')
                .replace(/^(EQC 進階完整處理系統.*)/gm, '<h2 style="color: #2c3e50; margin-bottom: 10px;">$1</h2>')
                .replace(/^(處理時間|資料夾路徑|區間檢測結果|雙重搜尋結果|處理完成時間):/gm, '<strong style="color: #17a2b8;">$1:</strong>')
                .replace(/^  (主要區間|備用區間|搜尋方法|匹配率|匹配成功):/gm, '  <span style="color: #28a745;">$1:</span>')
                .replace(/(第\d+-\d+欄)/g, '<span style="background: #fff3cd; padding: 2px 4px; border-radius: 3px;">$1</span>')
                .replace(/(main_region_complete)/g, '<span style="background: #d4edda; color: #155724; padding: 2px 6px; border-radius: 3px; font-weight: bold;">$1</span>')
                .replace(/(100%)/g, '<span style="background: #d4edda; color: #155724; padding: 2px 6px; border-radius: 3px; font-weight: bold;">$1</span>')
                .replace(/(是)/g, '<span style="color: #28a745; font-weight: bold;">✅ $1</span>')
                .replace(/\n/g, '<br>');
        }
        
        function closeReportPreview() {
            const modal = document.getElementById('reportPreviewModal');
            modal.style.display = 'none';
        }
        
        function copyReportContent() {
            const contentDiv = document.getElementById('reportContent');
            const textContent = contentDiv.textContent || contentDiv.innerText;
            
            navigator.clipboard.writeText(textContent).then(() => {
                // 臨時顯示複製成功提示
                const originalText = contentDiv.innerHTML;
                contentDiv.innerHTML = '<div style="text-align: center; color: #28a745; font-weight: bold; padding: 20px;"><i class="fas fa-check-circle"></i> 內容已複製到剪貼簿</div>';
                
                setTimeout(() => {
                    contentDiv.innerHTML = originalText;
                }, 1500);
            }).catch(err => {
                console.error('複製失敗:', err);
                alert('複製失敗，請手動選擇文字複製');
            });
        }
        
        function downloadReport() {
            if (!currentReportPath) {
                alert('沒有可下載的報告');
                return;
            }
            
            // 觸發下載
            const link = document.createElement('a');
            link.href = `/api/download_report?report_path=${encodeURIComponent(currentReportPath)}`;
            link.download = currentReportPath.split('/').pop() || 'eqc_report.txt';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        

        // 點擊模態框背景關閉
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('reportPreviewModal');
            if (event.target === modal) {
                closeReportPreview();
            }
        });

        // 初始化上傳配置
        async function initUploadConfig() {
            try {
                const response = await fetch('http://localhost:8010/api/upload_config');
                if (response.ok) {
                    const config = await response.json();
                    // 更新檔案大小限制顯示
                    DOMManager.get('maxSizeDisplay').textContent = config.max_upload_size_mb;
                    console.log('✅ 上傳配置載入成功:', config);
                } else {
                    console.warn('⚠️ 無法載入上傳配置，使用預設設定');
                }
            } catch (error) {
                console.warn('⚠️ 載入上傳配置時發生錯誤:', error.message);
            }
        }

        // 今日處理記錄相關功能
        async function refreshTodayRecords() {
            try {
                console.log('🔄 重新整理今日處理記錄');
                
                const response = await fetch('http://localhost:8010/api/today_processed_files');
                if (!response.ok) {
                    throw new Error(`API請求失敗: ${response.status}`);
                }
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    renderTodayRecords(result.data);
                } else {
                    throw new Error(result.message || '獲取記錄失敗');
                }
                
            } catch (error) {
                console.error('❌ 獲取今日處理記錄失敗:', error);
                showTodayRecordsError(error.message);
            }
        }

        function renderTodayRecords(data) {
            const emptyDiv = document.getElementById('todayRecordsEmpty');
            const listDiv = document.getElementById('todayRecordsList');
            
            if (data.total_count === 0) {
                // 沒有記錄時顯示空狀態
                emptyDiv.style.display = 'block';
                listDiv.style.display = 'none';
                return;
            }
            
            // 有記錄時顯示清單
            emptyDiv.style.display = 'none';
            listDiv.style.display = 'block';
            
            let recordsHTML = '';
            
            data.processed_files.forEach(record => {
                recordsHTML += `
                    <div style="border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; margin-bottom: 8px; background: #f8f9fa;">
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 6px;">
                            <div style="display: flex; align-items: center; gap: 6px;">
                                <i class="fas fa-clock" style="color: #667eea; font-size: 12px;"></i>
                                <span style="font-weight: bold; font-size: 13px;">${record.process_time_display}</span>
                                <span style="font-size: 11px; color: #6c757d;">${record.extract_id.substring(0, 12)}...</span>
                            </div>
                            <span style="font-size: 11px; color: #28a745;">${record.total_files} 個檔案</span>
                        </div>
                        
                        <div style="font-size: 10px; color: #6c757d; margin-bottom: 8px; word-break: break-all;">
                            📁 ${record.directory_path}
                        </div>
                        
                        <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                `;
                
                record.result_files.forEach(file => {
                    let iconClass = 'fa-file';
                    let buttonColor = '#6c757d';
                    
                    if (file.filename.endsWith('.xlsx')) {
                        iconClass = 'fa-file-excel';
                        buttonColor = '#28a745';
                    } else if (file.filename.endsWith('.csv')) {
                        iconClass = 'fa-file-csv';
                        buttonColor = '#17a2b8';
                    }
                    
                    // 避免 JavaScript 字符串跳脫問題，使用 data 屬性
                    const buttonId = `history-btn-${record.extract_id}-${file.filename.replace(/[^a-zA-Z0-9]/g, '')}`;
                    
                    recordsHTML += `
                        <button id="${buttonId}" 
                                data-file-path="${file.path.replace(/\\/g, '/')}" 
                                data-file-name="${file.filename}"
                                onclick="downloadHistoryFileByDataAttr('${buttonId}')" 
                                style="background: ${buttonColor}; color: white; border: none; padding: 3px 6px; 
                                       border-radius: 3px; font-size: 9px; cursor: pointer; display: flex; 
                                       align-items: center; gap: 2px;" 
                                title="${file.filename} (${file.size_mb}MB)">
                            <i class="fas ${iconClass}"></i>
                            <span>${file.filename.replace('EQCTOTALDATA', '').replace('_RAW', '').replace('.', '')}</span>
                        </button>
                    `;
                });
                
                recordsHTML += `
                        </div>
                    </div>
                `;
            });
            
            listDiv.innerHTML = recordsHTML;
            console.log(`✅ 顯示 ${data.total_count} 個今日處理記錄`);
        }

        function showTodayRecordsError(message) {
            const listDiv = document.getElementById('todayRecordsList');
            listDiv.innerHTML = `
                <div style="text-align: center; color: #e74c3c; padding: 15px;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div style="margin-top: 5px; font-size: 12px;">載入失敗: ${message}</div>
                </div>
            `;
            listDiv.style.display = 'block';
            document.getElementById('todayRecordsEmpty').style.display = 'none';
        }

        async function downloadHistoryFile(filePath, fileName) {
            try {
                console.log(`🔽 下載歷史檔案: ${fileName}`);
                
                // 使用現有的下載功能 - 正確的路徑轉換
                // 先轉換反斜線為正斜線
                let linuxPath = filePath.replace(/\\/g, '/');
                
                if (linuxPath.startsWith('D:/')) {
                    // 處理 D:/path/to/file 格式（已有斜線）
                    linuxPath = linuxPath.replace('D:/', '/mnt/d/');
                } else if (linuxPath.startsWith('D:')) {
                    // 處理 D:path/to/file 格式（缺少斜線）
                    linuxPath = '/mnt/d/' + linuxPath.substring(2);
                }
                
                console.log(`🔄 路徑轉換: ${filePath} -> ${linuxPath}`);
                await downloadFile(linuxPath, fileName);
                
            } catch (error) {
                console.error(`❌ 下載歷史檔案失敗:`, error);
                alert(`下載失敗：${error.message}`);
            }
        }

        // 通過數據屬性下載歷史檔案 (避免字符串跳脫問題)
        async function downloadHistoryFileByDataAttr(buttonId) {
            try {
                const button = document.getElementById(buttonId);
                if (!button) {
                    throw new Error('找不到下載按鈕');
                }
                
                const filePath = button.getAttribute('data-file-path');
                const fileName = button.getAttribute('data-file-name');
                
                console.log(`🔽 下載歷史檔案: ${fileName}`);
                console.log(`📁 原始路徑: ${filePath}`);
                
                // 路徑轉換：Windows -> Linux  
                let linuxPath = filePath.replace(/\\/g, '/'); // 先統一轉換反斜線
                
                if (linuxPath.startsWith('D:/')) {
                    // 處理 D:/path/to/file 格式
                    linuxPath = linuxPath.replace('D:/', '/mnt/d/');
                } else if (linuxPath.startsWith('D:')) {
                    // 處理 D:path/to/file 格式（缺少斜線）
                    linuxPath = '/mnt/d/' + linuxPath.substring(2);
                }
                
                console.log(`🔄 路徑轉換: ${filePath} -> ${linuxPath}`);
                
                // 功能替換原則：直接使用完整路徑下載，避免重複路徑問題
                await downloadFileByFullPath(linuxPath, fileName);
                
            } catch (error) {
                console.error(`❌ 下載歷史檔案失敗:`, error);
                alert(`下載失敗：${error.message}`);
            }
        }

        // 頁面載入時初始化狀態燈和上傳配置
        window.onload = async function() {
            // 初始化 EQC BIN=1 狀態燈為灰色 (如果元素存在)
            if (document.getElementById('eqcBin1StatusLight')) {
                updateEqcBin1StatusLight('#6c757d', '未掃描');
            }
            
            // 初始化上傳配置
            await initUploadConfig();
            
            // 初始化檔案上傳事件監聽器
            setupFileUpload();
            
            // 初始化資料夾路徑監聽器
            setupFolderPathListener();
            
            // 載入今日處理記錄
            await refreshTodayRecords();
        };
    </script>
</body>
</html>